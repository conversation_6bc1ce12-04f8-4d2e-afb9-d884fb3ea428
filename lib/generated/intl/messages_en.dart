// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(phone) =>
      "After your account【${phone}】cancellation, you can not login using that account and use【Bcloud platform】related products and service,and automatically exit the enterprise/organization you joined on the Bcloud Platform. All data information of your account on the Bcloud platform will be cleared and cannot be restored.\n If you have any questions, please contact Bcloud customer service email: <EMAIL>";

  static String m1(platformId) => "${platformId} Platform SIP ID View";

  static String m2(maxChannel, remainChannel) =>
      "GB cascade authorization ${maxChannel} channels, ${remainChannel} channels left";

  static String m3(name) => "“${name}”\'s rectification tasks";

  static String m4(permission) =>
      "In order to add device normally, need【${permission}】permission";

  static String m5(total, allocatedNum, unallocatedNum) =>
      "${total} sub-accounts are authorized, ${allocatedNum} have been allocated, and ${unallocatedNum} are left.";

  static String m6(maxChannel, remainChannel) =>
      "Device channel access authorization ${maxChannel} paths, remaining ${remainChannel} paths, <NAME_EMAIL> to expand authorization.";

  static String m7(maxChannel, remainChannel) =>
      "The device channel is authorized ${maxChannel} channels，remain ${remainChannel}channels";

  static String m8(time) => "Add time ${time}";

  static String m9(quantity) => "Total (${quantity} devices)";

  static String m10(name) => "“${name}”arithmetic，channel insufficient";

  static String m11(name) =>
      "Before after business hours ${name} minutes considered staff";

  static String m12(count) => "${count} channels";

  static String m13(serialNum) => "Channel ${serialNum} information";

  static String m14(channel) =>
      "Same as the serial number of channel ${channel}, please modify the serial number code";

  static String m15(name) => "Max select ${name} algorithm";

  static String m16(name) => "Select a maximum of ${name} departments";

  static String m17(name) => "Choose at most ${name} person";

  static String m18(maxCount) =>
      "Up to ${maxCount} departments can be selected";

  static String m19(deviceMaxCount) =>
      "Max can select ${deviceMaxCount} resources";

  static String m20(period) => "Please select ${period} inspection date";

  static String m21(maxCount) => "Max can select ${maxCount} users";

  static String m22(name) => "Max can select ${name} stores";

  static String m23(quantity) => "Total ${quantity} devices";

  static String m24(name) => "End time cannot be greater than ${name}";

  static String m25(name) => "Monthly:${name}";

  static String m26(name) => "Weekly:${name}";

  static String m27(name) => "Female percentage:${name}%";

  static String m28(name) => "Male percentage:${name}%";

  static String m29(name) => "Maximum duration cannot be greater than ${name}";

  static String m30(name) => "Maximum ${name} tasks download limit";

  static String m31(day) => "Time supports up to ${day} days";

  static String m32(name) => "Meet above ${name} considered staff";

  static String m33(name) => "${name} channel exception";

  static String m34(name) => "${name} devices";

  static String m35(name) => "${name} hours or more";

  static String m36(name) => "${name} minutes or more";

  static String m37(name) => "${name}，pleaseRetry";

  static String m38(nodeCount) => "Total ${nodeCount} nodes";

  static String m39(quantity) => "Device traffic (${quantity} devices)";

  static String m40(name) => "“${name}“ to be accepted";

  static String m41(name) => "“${name}“ to be rectified";

  static String m42(name) => "“${name}“ acceptance has passed";

  static String m43(quantity) => "Precise traffic flow (${quantity} devices)";

  static String m44(name) => "Remaining ${name} channels";

  static String m45(name) => "The ${name} times retry is in progress";

  static String m46(quantity) => "${quantity} devices found, ";

  static String m47(name) => "Set up to ${name} time periods";

  static String m48(name) => "Single stay duration above ${name} minutes";

  static String m49(channels) => "Total ${channels} devices";

  static String m50(name) =>
      "Entry and exit within ${name} minutes before and after business hours are considered staff";

  static String m51(name) => "Left ${name}channels";

  static String m52(max) => "Can be set up to ${max}";

  static String m53(name) => "Total ${name} channels";

  static String m54(name) => "Total stay duration above ${name} hours";

  static String m55(name) => "Used ${name} channels";

  static String m56(days) =>
      "Platform will expire in ${days} days. <NAME_EMAIL> for renewal/expansion.";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "local": MessageLookupByLibrary.simpleMessage("en"),
        "openCameraPermissionTip": MessageLookupByLibrary.simpleMessage(
            "Please open camera access permissions in system settings"),
        "openLocationPermissionTip": MessageLookupByLibrary.simpleMessage(
            "Please grant location permission to read the currently connected WiFi, and enable location permission in the system settings. If not authorized, please manually enter"),
        "openLocationPermissionTip1": MessageLookupByLibrary.simpleMessage(
            "Please open location permissions in system settings"),
        "openPhotoPermissionTip": MessageLookupByLibrary.simpleMessage(
            "Please open album access permissions in system settings"),
        "openStoragePermissionTip": MessageLookupByLibrary.simpleMessage(
            "Please open storage permissions in system settings"),
        "tr_AIDetailInspection":
            MessageLookupByLibrary.simpleMessage("AI Inspection details"),
        "tr_AIInitiate": MessageLookupByLibrary.simpleMessage("AI initiate"),
        "tr_AIInspectionStatistics":
            MessageLookupByLibrary.simpleMessage("AI inspection statistics"),
        "tr_AIPlanInspectionEdit":
            MessageLookupByLibrary.simpleMessage("AI Inspection plan Editor"),
        "tr_About": MessageLookupByLibrary.simpleMessage("About"),
        "tr_AccessIdPwdTips": MessageLookupByLibrary.simpleMessage(
            "Password length should be 8-64 characters, consisting of letters and numbers"),
        "tr_AccessIds": MessageLookupByLibrary.simpleMessage("Access ID"),
        "tr_AccessNationalStandardDevice":
            MessageLookupByLibrary.simpleMessage("National device access"),
        "tr_AccessNode": MessageLookupByLibrary.simpleMessage("Access Node"),
        "tr_AccessPassword":
            MessageLookupByLibrary.simpleMessage("Access password"),
        "tr_AccessProtocol":
            MessageLookupByLibrary.simpleMessage("Access protocol"),
        "tr_AccessPwdConfig": MessageLookupByLibrary.simpleMessage(
            "Access Password Configuration"),
        "tr_AccessTips": MessageLookupByLibrary.simpleMessage(
            "NOTE:\nNational standard protocol currently does not support H.265 encoding. Please ensure that the device\'s bitstream is H.264. The device needs to use the GB28181-2016 protocol to support TCP."),
        "tr_AccessType": MessageLookupByLibrary.simpleMessage("Access type"),
        "tr_Account": MessageLookupByLibrary.simpleMessage("Account"),
        "tr_AccountCancellation":
            MessageLookupByLibrary.simpleMessage("Cancel account"),
        "tr_AccountCancellation1":
            MessageLookupByLibrary.simpleMessage("Cancel account"),
        "tr_AccountPasswordRule": MessageLookupByLibrary.simpleMessage(
            "The password is 8-16 characters and must contain letters, numbers and special characters"),
        "tr_AccountPwdRuleSimple": MessageLookupByLibrary.simpleMessage(
            "8-16 characters, letters and numbers"),
        "tr_AccountSafe":
            MessageLookupByLibrary.simpleMessage("Account and safety"),
        "tr_AccountSet":
            MessageLookupByLibrary.simpleMessage("Persoal setting"),
        "tr_Add4GDevice": MessageLookupByLibrary.simpleMessage("4G camera"),
        "tr_Add4GDeviceNotes": MessageLookupByLibrary.simpleMessage(
            "Add by scan the device QR code"),
        "tr_AddChildDeviceNodeTips": MessageLookupByLibrary.simpleMessage(
            "There are no devices or sub-nodes under the current node"),
        "tr_AddDepartment":
            MessageLookupByLibrary.simpleMessage("Add Department"),
        "tr_AddDepartmentTips": MessageLookupByLibrary.simpleMessage(
            "There are currently no sub-departments under this department. Please click the add button in the top right corner to add a department."),
        "tr_AddDepartmentTipsSimple": MessageLookupByLibrary.simpleMessage(
            "There are currently no sub-departments under this department."),
        "tr_AddDevice": MessageLookupByLibrary.simpleMessage("Add device"),
        "tr_AddDeviceByManual":
            MessageLookupByLibrary.simpleMessage("Add device manually"),
        "tr_AddDeviceByWIFI":
            MessageLookupByLibrary.simpleMessage("WiFi distribution network"),
        "tr_AddDeviceByWIFINotes": MessageLookupByLibrary.simpleMessage(
            "Supports QR code distribution and fast distribution"),
        "tr_AddDeviceFailed":
            MessageLookupByLibrary.simpleMessage("Add failed"),
        "tr_AddDirectNetConnectionNotes": MessageLookupByLibrary.simpleMessage(
            "NOTE： \n1.Make sure the device is powered on \n2.Connect the network cable to the network cable port of the device, and the DVR/NVR is connected to the wired network. After the device is successfully started, try to connect to the network. When you hear the device successfully connected to the network, it means that the network is connected"),
        "tr_AddJFDevice": MessageLookupByLibrary.simpleMessage("Device add"),
        "tr_AddNode": MessageLookupByLibrary.simpleMessage("Add node"),
        "tr_AddNodeTips": MessageLookupByLibrary.simpleMessage(
            "There are currently no devices or child nodes under the current node. Please click the add button in the upper right corner to add devices and child nodes"),
        "tr_AddPerson": MessageLookupByLibrary.simpleMessage("Add New User"),
        "tr_AddPreset":
            MessageLookupByLibrary.simpleMessage("Create new preset"),
        "tr_AddRecordDevice": MessageLookupByLibrary.simpleMessage(
            "Add by direct network cable connection"),
        "tr_AddRecordDeviceNotes": MessageLookupByLibrary.simpleMessage(
            "Suitable for devices with Ethernet cable sockets"),
        "tr_AddRecordNotes": MessageLookupByLibrary.simpleMessage(
            "NOTE： \n· Mare sure to power on device \n· Connect network cable and device network cable"),
        "tr_AddRole": MessageLookupByLibrary.simpleMessage("Add Role"),
        "tr_AddSuccess": MessageLookupByLibrary.simpleMessage("Add success"),
        "tr_AgreeAndContinue":
            MessageLookupByLibrary.simpleMessage("Agree to continue"),
        "tr_AlarmAI": MessageLookupByLibrary.simpleMessage("AI alarm"),
        "tr_AlarmDevice": MessageLookupByLibrary.simpleMessage("Device alarm"),
        "tr_AlarmMessage":
            MessageLookupByLibrary.simpleMessage("Alarm Message"),
        "tr_AlarmRecording":
            MessageLookupByLibrary.simpleMessage("Alarm recording"),
        "tr_AlarmRecordingTip": MessageLookupByLibrary.simpleMessage(
            "Only record when an alarm is detected, long recording time"),
        "tr_AlarmSetting":
            MessageLookupByLibrary.simpleMessage("Alarm Setting"),
        "tr_AlarmTotalNum":
            MessageLookupByLibrary.simpleMessage("Total alarms"),
        "tr_Album": MessageLookupByLibrary.simpleMessage("Album"),
        "tr_AlertPreRecordTime":
            MessageLookupByLibrary.simpleMessage("Alert pre-record time"),
        "tr_AlertPreRecordTimeTip":
            MessageLookupByLibrary.simpleMessage("Range 1-30 seconds"),
        "tr_Algorithm": MessageLookupByLibrary.simpleMessage("Algorithm"),
        "tr_AlgorithmAlarm":
            MessageLookupByLibrary.simpleMessage("Algorithm Alarm"),
        "tr_AlgorithmConfiguration":
            MessageLookupByLibrary.simpleMessage("Set algorithm"),
        "tr_AllDate": MessageLookupByLibrary.simpleMessage("All Date"),
        "tr_AllDayRecording":
            MessageLookupByLibrary.simpleMessage("All-day recording"),
        "tr_AllDayRecordingTip": MessageLookupByLibrary.simpleMessage(
            "All-day recording, 24 hours non-stop recording"),
        "tr_AllProtocols":
            MessageLookupByLibrary.simpleMessage("All Protocols"),
        "tr_AllRightsReserved":
            MessageLookupByLibrary.simpleMessage("Copyright：Hangzhou JFTECH"),
        "tr_AllTypes": MessageLookupByLibrary.simpleMessage("All Types"),
        "tr_And": MessageLookupByLibrary.simpleMessage("And"),
        "tr_AppName": MessageLookupByLibrary.simpleMessage("BcloudSaaS"),
        "tr_AppPolicyTip1": MessageLookupByLibrary.simpleMessage(
            "Summary of Privacy Protection Policy"),
        "tr_AppPolicyTip2": MessageLookupByLibrary.simpleMessage(
            "Thank you for trusting and using Bcloud! Bcloud attaches great importance to your privacy and personal information protection. Before using the Bcloud service, please read carefully"),
        "tr_AppPolicyTip3": MessageLookupByLibrary.simpleMessage(
            "We agree and accept all terms and conditions before starting to use your services.\n        e will take corresponding security measures in accordance with legal and regulatory requirements, and do our best to protect the security and controllability of your personal information."),
        "tr_AppWelcome":
            MessageLookupByLibrary.simpleMessage("Welcome to use BcloudSaaS"),
        "tr_Avatar": MessageLookupByLibrary.simpleMessage("Head sculpture"),
        "tr_BasicInfo":
            MessageLookupByLibrary.simpleMessage("Basic Information"),
        "tr_BindEmail": MessageLookupByLibrary.simpleMessage("Bind email"),
        "tr_BindNewPhone":
            MessageLookupByLibrary.simpleMessage("Bind to new mobile phone"),
        "tr_BindPhone": MessageLookupByLibrary.simpleMessage("Bind phone"),
        "tr_Cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "tr_Cancellation": MessageLookupByLibrary.simpleMessage("Cancellation"),
        "tr_CancellationProtocol": MessageLookupByLibrary.simpleMessage(
            "《BcloudSaaS Account Cancellation Agreement》"),
        "tr_CancellationSuccess":
            MessageLookupByLibrary.simpleMessage("Cancel success"),
        "tr_CancellationValidityPeriod":
            MessageLookupByLibrary.simpleMessage("THE TERM OF VALIDITY"),
        "tr_CannotChangeSameEmail": MessageLookupByLibrary.simpleMessage(
            "New email cannot be same as the original email"),
        "tr_CannotChangeSamePhone": MessageLookupByLibrary.simpleMessage(
            "Nw phone number cannot be same as the original phone number"),
        "tr_Capacity": MessageLookupByLibrary.simpleMessage("Capacity"),
        "tr_CheckAddBTDeviceFailed": MessageLookupByLibrary.simpleMessage(
            "Device verification failed, please confirm on the device. If you need to continue adding, reset the new device and try again."),
        "tr_CheckAddDeviceFailed": MessageLookupByLibrary.simpleMessage(
            "The device cannot be found. Please reset the device and add it again."),
        "tr_CheckCurrentEmail": MessageLookupByLibrary.simpleMessage(
            "Detected that the email you are currently bound to is："),
        "tr_CheckCurrentPhone": MessageLookupByLibrary.simpleMessage(
            "Detected that the phone number you are currently bound to is:"),
        "tr_CheckDeviceLoginInfo":
            MessageLookupByLibrary.simpleMessage("Device name and password"),
        "tr_CheckView": MessageLookupByLibrary.simpleMessage("View"),
        "tr_ChooseDepartment":
            MessageLookupByLibrary.simpleMessage("Select Department"),
        "tr_ChooseNetwork": MessageLookupByLibrary.simpleMessage(
            "Please choose the WIFI network"),
        "tr_ChoosePrettyWiFi": MessageLookupByLibrary.simpleMessage(
            "Please choose a WiFi with strong signal that can quickly connect devices to the internet"),
        "tr_ClickToUploadTheBusinessLicense":
            MessageLookupByLibrary.simpleMessage(
                "Click to upload business license"),
        "tr_CloudRecord":
            MessageLookupByLibrary.simpleMessage("Cloud Playback"),
        "tr_CommandSuccess":
            MessageLookupByLibrary.simpleMessage("Operation Successful"),
        "tr_CommonSave": MessageLookupByLibrary.simpleMessage("Save"),
        "tr_Common_Add": MessageLookupByLibrary.simpleMessage("Add"),
        "tr_Common_AdministrativeArea":
            MessageLookupByLibrary.simpleMessage("Administrative Area"),
        "tr_Common_All": MessageLookupByLibrary.simpleMessage("All"),
        "tr_Common_Channel": MessageLookupByLibrary.simpleMessage("Channel"),
        "tr_Common_ChooseAdministrativeArea":
            MessageLookupByLibrary.simpleMessage(
                "Administrative Region Selection"),
        "tr_Common_ContentPairingFailed":
            MessageLookupByLibrary.simpleMessage("Searched nothing"),
        "tr_Common_Custom": MessageLookupByLibrary.simpleMessage("Custom"),
        "tr_Common_Delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "tr_Common_DeleteSuccess":
            MessageLookupByLibrary.simpleMessage("Delete success"),
        "tr_Common_Device": MessageLookupByLibrary.simpleMessage("Device"),
        "tr_Common_Disconnect":
            MessageLookupByLibrary.simpleMessage("Disconnect"),
        "tr_Common_Edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "tr_Common_Function": MessageLookupByLibrary.simpleMessage("Function"),
        "tr_Common_GrassrootsUnitNum":
            MessageLookupByLibrary.simpleMessage("Grassroots unit number"),
        "tr_Common_InputPassword":
            MessageLookupByLibrary.simpleMessage("Please input login password"),
        "tr_Common_Loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "tr_Common_Message": MessageLookupByLibrary.simpleMessage("Message"),
        "tr_Common_Mine": MessageLookupByLibrary.simpleMessage("Mine"),
        "tr_Common_Monitor": MessageLookupByLibrary.simpleMessage("Monitor"),
        "tr_Common_More": MessageLookupByLibrary.simpleMessage("More"),
        "tr_Common_Password": MessageLookupByLibrary.simpleMessage("Password"),
        "tr_Common_PlsFillInfo":
            MessageLookupByLibrary.simpleMessage("Please choose"),
        "tr_Common_Random": MessageLookupByLibrary.simpleMessage("Random"),
        "tr_Common_Record": MessageLookupByLibrary.simpleMessage("Record"),
        "tr_Common_Rename": MessageLookupByLibrary.simpleMessage("Rename"),
        "tr_Common_Reset": MessageLookupByLibrary.simpleMessage("Reset"),
        "tr_Common_Search": MessageLookupByLibrary.simpleMessage("Search"),
        "tr_Common_SelectAll":
            MessageLookupByLibrary.simpleMessage("Select all"),
        "tr_Common_Snap": MessageLookupByLibrary.simpleMessage("Snapshot"),
        "tr_Common_Store": MessageLookupByLibrary.simpleMessage("Store"),
        "tr_Common_Voice": MessageLookupByLibrary.simpleMessage("Voice"),
        "tr_Common_Workbench":
            MessageLookupByLibrary.simpleMessage("Workbench"),
        "tr_Common_definition":
            MessageLookupByLibrary.simpleMessage("Definition"),
        "tr_Common_export": MessageLookupByLibrary.simpleMessage("Export"),
        "tr_Confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "tr_ConfirmCancellation":
            MessageLookupByLibrary.simpleMessage("Confirm cancellation"),
        "tr_ConfirmTips":
            MessageLookupByLibrary.simpleMessage("Confirm to logout?"),
        "tr_ContactUs": MessageLookupByLibrary.simpleMessage("Contact us"),
        "tr_ContactUsContent": MessageLookupByLibrary.simpleMessage(
            "Thank you for using our products and services. If you have any questions, comments or suggestions during the use of our products or services, please send your <NAME_EMAIL> We will reply to you as soon as possible. Thank you for your cooperation"),
        "tr_CopySuccess":
            MessageLookupByLibrary.simpleMessage("Copied to the clipboard"),
        "tr_DVR": MessageLookupByLibrary.simpleMessage("DVR"),
        "tr_DearUser": MessageLookupByLibrary.simpleMessage("Dear user："),
        "tr_DearUserDetail": m0,
        "tr_Delete": MessageLookupByLibrary.simpleMessage("Detele"),
        "tr_DeleteDevice":
            MessageLookupByLibrary.simpleMessage("Delete Device"),
        "tr_DeleteDeviceTips": MessageLookupByLibrary.simpleMessage(
            "After deleting the camera, any photos and videos generated in the cloud will also be cleared."),
        "tr_Department": MessageLookupByLibrary.simpleMessage("Department"),
        "tr_DepartmentManage":
            MessageLookupByLibrary.simpleMessage("Department Management"),
        "tr_Detailed": MessageLookupByLibrary.simpleMessage("Detailed info"),
        "tr_Device": MessageLookupByLibrary.simpleMessage("Device"),
        "tr_DeviceAccessChannelNum":
            MessageLookupByLibrary.simpleMessage("Access Channels"),
        "tr_DeviceAccessIDPwd":
            MessageLookupByLibrary.simpleMessage("Access ID Password"),
        "tr_DeviceAdd": MessageLookupByLibrary.simpleMessage("Add device"),
        "tr_DeviceChannelID":
            MessageLookupByLibrary.simpleMessage("Channel ID"),
        "tr_DeviceChannelNum":
            MessageLookupByLibrary.simpleMessage("Channel Number"),
        "tr_DeviceEdit": MessageLookupByLibrary.simpleMessage("Edit device"),
        "tr_DeviceInfo":
            MessageLookupByLibrary.simpleMessage("Device Information"),
        "tr_DeviceLoginName":
            MessageLookupByLibrary.simpleMessage("Device login name"),
        "tr_DeviceName": MessageLookupByLibrary.simpleMessage("Device Name"),
        "tr_DeviceNode": MessageLookupByLibrary.simpleMessage("Node"),
        "tr_DeviceParams":
            MessageLookupByLibrary.simpleMessage("Device parameter"),
        "tr_DevicePassword":
            MessageLookupByLibrary.simpleMessage("Please input password"),
        "tr_DeviceResource":
            MessageLookupByLibrary.simpleMessage("Device Resource"),
        "tr_DeviceSN": MessageLookupByLibrary.simpleMessage("Serial number"),
        "tr_DeviceScanTips": MessageLookupByLibrary.simpleMessage(
            "Quick network configuration and QR code configuration are both enabled. Please check if the manual supports QR code configuration. If the device does not support QR code configuration, there is no need to perform QR code matching"),
        "tr_DeviceState": MessageLookupByLibrary.simpleMessage("Device State"),
        "tr_DeviceStateChangeOffline":
            MessageLookupByLibrary.simpleMessage("Offline"),
        "tr_DeviceStateChangeOnline":
            MessageLookupByLibrary.simpleMessage("Online"),
        "tr_DeviceStateOffline":
            MessageLookupByLibrary.simpleMessage("Offline"),
        "tr_DeviceStateOnline": MessageLookupByLibrary.simpleMessage("Online"),
        "tr_DeviceStateUnRegister":
            MessageLookupByLibrary.simpleMessage("Unregistered"),
        "tr_DeviceUuid": MessageLookupByLibrary.simpleMessage("Device SN"),
        "tr_Done": MessageLookupByLibrary.simpleMessage("Done"),
        "tr_Download": MessageLookupByLibrary.simpleMessage("Download"),
        "tr_DownloadSuccess":
            MessageLookupByLibrary.simpleMessage("Download Successful"),
        "tr_Downloaded": MessageLookupByLibrary.simpleMessage("Downloaded"),
        "tr_EditDepartment":
            MessageLookupByLibrary.simpleMessage("Edit Department"),
        "tr_EditPreset": MessageLookupByLibrary.simpleMessage("Edit preset"),
        "tr_EncodingMethod":
            MessageLookupByLibrary.simpleMessage("Encode method"),
        "tr_EncodingMethodNationalStandard":
            MessageLookupByLibrary.simpleMessage("National encode"),
        "tr_EncodingMethodRandom":
            MessageLookupByLibrary.simpleMessage("Random encode"),
        "tr_EnterNoteDepartment":
            MessageLookupByLibrary.simpleMessage("Enter Department Name"),
        "tr_EnterNoteNickName":
            MessageLookupByLibrary.simpleMessage("Add node name"),
        "tr_EnterWiFiPassword":
            MessageLookupByLibrary.simpleMessage("Please enter WIFI password"),
        "tr_ErrorCode": MessageLookupByLibrary.simpleMessage("Error code"),
        "tr_ErrorCode_Minus_1":
            MessageLookupByLibrary.simpleMessage("Data parsing failed"),
        "tr_ErrorCode_Minus_1000":
            MessageLookupByLibrary.simpleMessage("Network error"),
        "tr_ErrorCode_Minus_10000":
            MessageLookupByLibrary.simpleMessage("Your request is illegal"),
        "tr_ErrorCode_Minus_100000":
            MessageLookupByLibrary.simpleMessage("Error"),
        "tr_ErrorCode_Minus_10001":
            MessageLookupByLibrary.simpleMessage("System is not initialised"),
        "tr_ErrorCode_Minus_10002":
            MessageLookupByLibrary.simpleMessage("Incorrect parameters"),
        "tr_ErrorCode_Minus_10003":
            MessageLookupByLibrary.simpleMessage("Invalid handle"),
        "tr_ErrorCode_Minus_10004":
            MessageLookupByLibrary.simpleMessage("SDK cleaning error"),
        "tr_ErrorCode_Minus_10005": MessageLookupByLibrary.simpleMessage(
            "Your network connection timed out,please try again"),
        "tr_ErrorCode_Minus_10006":
            MessageLookupByLibrary.simpleMessage("Insufficient storage space"),
        "tr_ErrorCode_Minus_10007":
            MessageLookupByLibrary.simpleMessage("Network connection failed"),
        "tr_ErrorCode_Minus_10008":
            MessageLookupByLibrary.simpleMessage("Failed to open the file"),
        "tr_ErrorCode_Minus_10009":
            MessageLookupByLibrary.simpleMessage("Unknown error"),
        "tr_ErrorCode_Minus_1001":
            MessageLookupByLibrary.simpleMessage("Sending buffer is full"),
        "tr_ErrorCode_Minus_1002":
            MessageLookupByLibrary.simpleMessage("Network sending failed"),
        "tr_ErrorCode_Minus_1003":
            MessageLookupByLibrary.simpleMessage("Network receiving failed"),
        "tr_ErrorCode_Minus_1004":
            MessageLookupByLibrary.simpleMessage("Network timeout"),
        "tr_ErrorCode_Minus_1005":
            MessageLookupByLibrary.simpleMessage("No object"),
        "tr_ErrorCode_Minus_1006":
            MessageLookupByLibrary.simpleMessage("Fail to creat"),
        "tr_ErrorCode_Minus_1007":
            MessageLookupByLibrary.simpleMessage("Connecting failed"),
        "tr_ErrorCode_Minus_1008":
            MessageLookupByLibrary.simpleMessage("Timeout"),
        "tr_ErrorCode_Minus_1009":
            MessageLookupByLibrary.simpleMessage("No connection"),
        "tr_ErrorCode_Minus_101":
            MessageLookupByLibrary.simpleMessage("Incorrect password"),
        "tr_ErrorCode_Minus_1010":
            MessageLookupByLibrary.simpleMessage("Socket abnormal"),
        "tr_ErrorCode_Minus_1011":
            MessageLookupByLibrary.simpleMessage("Close socket abnormal"),
        "tr_ErrorCode_Minus_1012":
            MessageLookupByLibrary.simpleMessage("Creating cache failed"),
        "tr_ErrorCode_Minus_1013":
            MessageLookupByLibrary.simpleMessage("Network busy"),
        "tr_ErrorCode_Minus_1014":
            MessageLookupByLibrary.simpleMessage("Listening abnormal"),
        "tr_ErrorCode_Minus_1015":
            MessageLookupByLibrary.simpleMessage("Receiving abnormal"),
        "tr_ErrorCode_Minus_1016":
            MessageLookupByLibrary.simpleMessage("No buffer"),
        "tr_ErrorCode_Minus_1017": MessageLookupByLibrary.simpleMessage(
            "Network error or DNS setting wrong"),
        "tr_ErrorCode_Minus_1018": MessageLookupByLibrary.simpleMessage(
            "Developer account hasn\'t been authorised"),
        "tr_ErrorCode_Minus_102":
            MessageLookupByLibrary.simpleMessage("Account does not exist"),
        "tr_ErrorCode_Minus_103": MessageLookupByLibrary.simpleMessage(
            "Login timeout (network connection failed)"),
        "tr_ErrorCode_Minus_104":
            MessageLookupByLibrary.simpleMessage("Account not logged in"),
        "tr_ErrorCode_Minus_105":
            MessageLookupByLibrary.simpleMessage("Account already logged in"),
        "tr_ErrorCode_Minus_106":
            MessageLookupByLibrary.simpleMessage("Account is blacklisted"),
        "tr_ErrorCode_Minus_107": MessageLookupByLibrary.simpleMessage(
            "Insufficient device resources"),
        "tr_ErrorCode_Minus_109":
            MessageLookupByLibrary.simpleMessage("Network host not found"),
        "tr_ErrorCode_Minus_11000": MessageLookupByLibrary.simpleMessage(
            "The dta is incorrect,the version may not match"),
        "tr_ErrorCode_Minus_11001":
            MessageLookupByLibrary.simpleMessage("The version does\'t support"),
        "tr_ErrorCode_Minus_11200":
            MessageLookupByLibrary.simpleMessage("Failed to open the channel"),
        "tr_ErrorCode_Minus_11201":
            MessageLookupByLibrary.simpleMessage("Failed to close the channel"),
        "tr_ErrorCode_Minus_11202": MessageLookupByLibrary.simpleMessage(
            "Failed to creat media sub-connection"),
        "tr_ErrorCode_Minus_11203": MessageLookupByLibrary.simpleMessage(
            "Media sub-connection communication failed"),
        "tr_ErrorCode_Minus_11204": MessageLookupByLibrary.simpleMessage(
            "Video link reaches the maximum"),
        "tr_ErrorCode_Minus_11300":
            MessageLookupByLibrary.simpleMessage("No permission"),
        "tr_ErrorCode_Minus_11301":
            MessageLookupByLibrary.simpleMessage("Wrong password"),
        "tr_ErrorCode_Minus_11302":
            MessageLookupByLibrary.simpleMessage("User does not exist"),
        "tr_ErrorCode_Minus_11303": MessageLookupByLibrary.simpleMessage(
            "The user is locked,please reboot device"),
        "tr_ErrorCode_Minus_11304": MessageLookupByLibrary.simpleMessage(
            "The user is not allowed to access"),
        "tr_ErrorCode_Minus_11305":
            MessageLookupByLibrary.simpleMessage("The user has logged in"),
        "tr_ErrorCode_Minus_11306":
            MessageLookupByLibrary.simpleMessage("The user hasn\'t logged in"),
        "tr_ErrorCode_Minus_11307": MessageLookupByLibrary.simpleMessage(
            "The device may be not online"),
        "tr_ErrorCode_Minus_11308":
            MessageLookupByLibrary.simpleMessage("User input is illegal"),
        "tr_ErrorCode_Minus_11309":
            MessageLookupByLibrary.simpleMessage("Duplicate index"),
        "tr_ErrorCode_Minus_11310":
            MessageLookupByLibrary.simpleMessage("Object does not exist"),
        "tr_ErrorCode_Minus_11311":
            MessageLookupByLibrary.simpleMessage("Object does not exist"),
        "tr_ErrorCode_Minus_11312":
            MessageLookupByLibrary.simpleMessage("Object is being used"),
        "tr_ErrorCode_Minus_11313":
            MessageLookupByLibrary.simpleMessage("Subset hyperrange"),
        "tr_ErrorCode_Minus_11314":
            MessageLookupByLibrary.simpleMessage("Incorrect password"),
        "tr_ErrorCode_Minus_11315":
            MessageLookupByLibrary.simpleMessage("Password doesn\'t match"),
        "tr_ErrorCode_Minus_11316":
            MessageLookupByLibrary.simpleMessage("Keep this account"),
        "tr_ErrorCode_Minus_11317": MessageLookupByLibrary.simpleMessage(
            "This encrypted login is not supported"),
        "tr_ErrorCode_Minus_11318":
            MessageLookupByLibrary.simpleMessage("Incorrect account password"),
        "tr_ErrorCode_Minus_11400": MessageLookupByLibrary.simpleMessage(
            "You need to restart the application after saving the configuration"),
        "tr_ErrorCode_Minus_11401":
            MessageLookupByLibrary.simpleMessage("Please reboot the device"),
        "tr_ErrorCode_Minus_11402":
            MessageLookupByLibrary.simpleMessage("Error in writing documents"),
        "tr_ErrorCode_Minus_11403": MessageLookupByLibrary.simpleMessage(
            "Configuration features are not supported"),
        "tr_ErrorCode_Minus_11404": MessageLookupByLibrary.simpleMessage(
            "Configuration verification failed"),
        "tr_ErrorCode_Minus_11405": MessageLookupByLibrary.simpleMessage(
            "Configuration doesn\'t exist"),
        "tr_ErrorCode_Minus_11406": MessageLookupByLibrary.simpleMessage(
            "Error in configuration parsing, and the configuration may not be supported"),
        "tr_ErrorCode_Minus_11500":
            MessageLookupByLibrary.simpleMessage("Pause failed"),
        "tr_ErrorCode_Minus_11501":
            MessageLookupByLibrary.simpleMessage("No file found"),
        "tr_ErrorCode_Minus_11502": MessageLookupByLibrary.simpleMessage(
            "Configuration is not enabled"),
        "tr_ErrorCode_Minus_11503": MessageLookupByLibrary.simpleMessage(
            "Video streaming is not opened"),
        "tr_ErrorCode_Minus_11600":
            MessageLookupByLibrary.simpleMessage("Failed to creat connection"),
        "tr_ErrorCode_Minus_11601":
            MessageLookupByLibrary.simpleMessage("Connection failed"),
        "tr_ErrorCode_Minus_11602":
            MessageLookupByLibrary.simpleMessage("Domain name parsing failed"),
        "tr_ErrorCode_Minus_11603":
            MessageLookupByLibrary.simpleMessage("Failed to send data"),
        "tr_ErrorCode_Minus_11605":
            MessageLookupByLibrary.simpleMessage("Busy service"),
        "tr_ErrorCode_Minus_11609": MessageLookupByLibrary.simpleMessage(
            "Connection is limited, or failed to access the server"),
        "tr_ErrorCode_Minus_11612": MessageLookupByLibrary.simpleMessage(
            "The number of server connection is full"),
        "tr_ErrorCode_Minus_11700":
            MessageLookupByLibrary.simpleMessage("Pirated device"),
        "tr_ErrorCode_Minus_120": MessageLookupByLibrary.simpleMessage(
            "Device does not exist (has been deleted)"),
        "tr_ErrorCode_Minus_1239510":
            MessageLookupByLibrary.simpleMessage("Object not exist"),
        "tr_ErrorCode_Minus_1239511":
            MessageLookupByLibrary.simpleMessage("Value not exist"),
        "tr_ErrorCode_Minus_137":
            MessageLookupByLibrary.simpleMessage("Device token is invalid"),
        "tr_ErrorCode_Minus_200000":
            MessageLookupByLibrary.simpleMessage("Invalid parameter"),
        "tr_ErrorCode_Minus_200001":
            MessageLookupByLibrary.simpleMessage("User doesn;t exist"),
        "tr_ErrorCode_Minus_200002":
            MessageLookupByLibrary.simpleMessage("Sql failed"),
        "tr_ErrorCode_Minus_201103":
            MessageLookupByLibrary.simpleMessage("Message format error"),
        "tr_ErrorCode_Minus_201111": MessageLookupByLibrary.simpleMessage(
            "Device is searched successfully"),
        "tr_ErrorCode_Minus_201113":
            MessageLookupByLibrary.simpleMessage("Pirated software"),
        "tr_ErrorCode_Minus_201117": MessageLookupByLibrary.simpleMessage(
            "The number of device connection has reached the uppper limit"),
        "tr_ErrorCode_Minus_201121": MessageLookupByLibrary.simpleMessage(
            "Obtaining  AUTHCODE incorrectly"),
        "tr_ErrorCode_Minus_20221": MessageLookupByLibrary.simpleMessage(
            "If the number of verification exceeds the limitation, you need to reboot the device and try again"),
        "tr_ErrorCode_Minus_210002": MessageLookupByLibrary.simpleMessage(
            "Interface verification failed"),
        "tr_ErrorCode_Minus_210003":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_ErrorCode_Minus_210004": MessageLookupByLibrary.simpleMessage(
            "Phone number has been registered"),
        "tr_ErrorCode_Minus_210005": MessageLookupByLibrary.simpleMessage(
            "Sending text message exceeds the limited number "),
        "tr_ErrorCode_Minus_210008": MessageLookupByLibrary.simpleMessage(
            "HD video is not supported at present, it will switch to SD automatically"),
        "tr_ErrorCode_Minus_210009": MessageLookupByLibrary.simpleMessage(
            "Forwarding mode doesn\'t support HD, please upgrade the firmware that supports DSS"),
        "tr_ErrorCode_Minus_210010": MessageLookupByLibrary.simpleMessage(
            "Sending failed,please try again"),
        "tr_ErrorCode_Minus_210017": MessageLookupByLibrary.simpleMessage(
            "Sending only once in 120 seconds"),
        "tr_ErrorCode_Minus_210106": MessageLookupByLibrary.simpleMessage(
            "Username has been registered"),
        "tr_ErrorCode_Minus_210313": MessageLookupByLibrary.simpleMessage(
            "The origional password is incorrect"),
        "tr_ErrorCode_Minus_210315": MessageLookupByLibrary.simpleMessage(
            "The old and new passwords are same, please change again."),
        "tr_ErrorCode_Minus_210405": MessageLookupByLibrary.simpleMessage(
            "Verification code within 24 hours can not exceed 3 times"),
        "tr_ErrorCode_Minus_210414": MessageLookupByLibrary.simpleMessage(
            "This phone number is not registered"),
        "tr_ErrorCode_Minus_210417": MessageLookupByLibrary.simpleMessage(
            "Sending only once within 120 seconds"),
        "tr_ErrorCode_Minus_210512": MessageLookupByLibrary.simpleMessage(
            "Passwords you sent twice doesn\'t match"),
        "tr_ErrorCode_Minus_210607":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_210700":
            MessageLookupByLibrary.simpleMessage("Server responding failed"),
        "tr_ErrorCode_Minus_211703":
            MessageLookupByLibrary.simpleMessage("Missing upload file"),
        "tr_ErrorCode_Minus_212104":
            MessageLookupByLibrary.simpleMessage("Server query failed"),
        "tr_ErrorCode_Minus_212402": MessageLookupByLibrary.simpleMessage(
            "The uloaded file hasn\'t been received"),
        "tr_ErrorCode_Minus_213000":
            MessageLookupByLibrary.simpleMessage("No this username"),
        "tr_ErrorCode_Minus_213100": MessageLookupByLibrary.simpleMessage(
            "Sending email failed,please check email input is correct or not"),
        "tr_ErrorCode_Minus_213108": MessageLookupByLibrary.simpleMessage(
            "This email has been registered"),
        "tr_ErrorCode_Minus_213206": MessageLookupByLibrary.simpleMessage(
            "Username has been registered"),
        "tr_ErrorCode_Minus_213207":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_213208":
            MessageLookupByLibrary.simpleMessage("Email has been registered"),
        "tr_ErrorCode_Minus_213303":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_ErrorCode_Minus_213314":
            MessageLookupByLibrary.simpleMessage("The email doesn\'t exist"),
        "tr_ErrorCode_Minus_213316": MessageLookupByLibrary.simpleMessage(
            "Email and username don\'t match"),
        "tr_ErrorCode_Minus_213407":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_213414":
            MessageLookupByLibrary.simpleMessage("Eamil doesn\'t exist"),
        "tr_ErrorCode_Minus_213514": MessageLookupByLibrary.simpleMessage(
            "Phone number or email doesn\'t exist"),
        "tr_ErrorCode_Minus_213600": MessageLookupByLibrary.simpleMessage(
            "Device serial number is in the blacklist"),
        "tr_ErrorCode_Minus_213601": MessageLookupByLibrary.simpleMessage(
            "Device serial number already exists"),
        "tr_ErrorCode_Minus_213602": MessageLookupByLibrary.simpleMessage(
            "Device serial number is empty"),
        "tr_ErrorCode_Minus_213603": MessageLookupByLibrary.simpleMessage(
            "Device serial number format is incorrect"),
        "tr_ErrorCode_Minus_213604":
            MessageLookupByLibrary.simpleMessage("There is no whitelist"),
        "tr_ErrorCode_Minus_213605": MessageLookupByLibrary.simpleMessage(
            "The device name cannot be empty"),
        "tr_ErrorCode_Minus_213606": MessageLookupByLibrary.simpleMessage(
            "The device username format is incorrect"),
        "tr_ErrorCode_Minus_213607": MessageLookupByLibrary.simpleMessage(
            "The device password format is incorrect"),
        "tr_ErrorCode_Minus_213608": MessageLookupByLibrary.simpleMessage(
            "The device name format is incorrect and contains keywords"),
        "tr_ErrorCode_Minus_213610":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_ErrorCode_Minus_213611":
            MessageLookupByLibrary.simpleMessage("Username does not exist"),
        "tr_ErrorCode_Minus_213612": MessageLookupByLibrary.simpleMessage(
            "Failed to edit device information"),
        "tr_ErrorCode_Minus_213620":
            MessageLookupByLibrary.simpleMessage("Activation failed"),
        "tr_ErrorCode_Minus_213621": MessageLookupByLibrary.simpleMessage(
            "The cloud service is not activated"),
        "tr_ErrorCode_Minus_213630": MessageLookupByLibrary.simpleMessage(
            "Incorrect username or password"),
        "tr_ErrorCode_Minus_213700":
            MessageLookupByLibrary.simpleMessage("Server responding failed"),
        "tr_ErrorCode_Minus_213702": MessageLookupByLibrary.simpleMessage(
            "Interface verification failed"),
        "tr_ErrorCode_Minus_213703":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_ErrorCode_Minus_213706": MessageLookupByLibrary.simpleMessage(
            "The user has been registered"),
        "tr_ErrorCode_Minus_213800": MessageLookupByLibrary.simpleMessage(
            "Successful, needs to be updated"),
        "tr_ErrorCode_Minus_213801": MessageLookupByLibrary.simpleMessage(
            "Success, it is the latest, no need to update"),
        "tr_ErrorCode_Minus_213802":
            MessageLookupByLibrary.simpleMessage("Failed, invalid request"),
        "tr_ErrorCode_Minus_213803":
            MessageLookupByLibrary.simpleMessage("Failed, resource not found"),
        "tr_ErrorCode_Minus_213804": MessageLookupByLibrary.simpleMessage(
            "Failed, internal server error"),
        "tr_ErrorCode_Minus_213805": MessageLookupByLibrary.simpleMessage(
            "Failed, the server is temporarily unavailable"),
        "tr_ErrorCode_Minus_214206": MessageLookupByLibrary.simpleMessage(
            "Username has been registered"),
        "tr_ErrorCode_Minus_214404":
            MessageLookupByLibrary.simpleMessage("Phone number has been bound"),
        "tr_ErrorCode_Minus_214507":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_214608": MessageLookupByLibrary.simpleMessage(
            "The email address has been bound"),
        "tr_ErrorCode_Minus_214707":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_214708": MessageLookupByLibrary.simpleMessage(
            "The email address has been bound"),
        "tr_ErrorCode_Minus_214908": MessageLookupByLibrary.simpleMessage(
            "The email address has been registered"),
        "tr_ErrorCode_Minus_215100": MessageLookupByLibrary.simpleMessage(
            "Get device DSS information through XMCloud"),
        "tr_ErrorCode_Minus_215101": MessageLookupByLibrary.simpleMessage(
            "DSS failed to connect to Hls server"),
        "tr_ErrorCode_Minus_215102": MessageLookupByLibrary.simpleMessage(
            "DSS information format error"),
        "tr_ErrorCode_Minus_215103": MessageLookupByLibrary.simpleMessage(
            "Failed to obtain device DSS information, please try again later"),
        "tr_ErrorCode_Minus_215104": MessageLookupByLibrary.simpleMessage(
            "DSS code stream format parsing failed"),
        "tr_ErrorCode_Minus_215110": MessageLookupByLibrary.simpleMessage(
            "Failed to parse the video square URL returned by Xiongmai Cloud"),
        "tr_ErrorCode_Minus_215120": MessageLookupByLibrary.simpleMessage(
            "The front end is not connected to the video source"),
        "tr_ErrorCode_Minus_215121": MessageLookupByLibrary.simpleMessage(
            "The front end is not connected to the video source"),
        "tr_ErrorCode_Minus_215122": MessageLookupByLibrary.simpleMessage(
            "The front end does not support this code stream"),
        "tr_ErrorCode_Minus_215124": MessageLookupByLibrary.simpleMessage(
            "DSS cannot be opened using the combined encoding channel, please reopen"),
        "tr_ErrorCode_Minus_215130":
            MessageLookupByLibrary.simpleMessage("Invalid request"),
        "tr_ErrorCode_Minus_215131": MessageLookupByLibrary.simpleMessage(
            "The media video link has reached the maximum limit and access is limited"),
        "tr_ErrorCode_Minus_215140":
            MessageLookupByLibrary.simpleMessage("Invalid token format"),
        "tr_ErrorCode_Minus_215141": MessageLookupByLibrary.simpleMessage(
            "Does not match token serial number"),
        "tr_ErrorCode_Minus_215142": MessageLookupByLibrary.simpleMessage(
            "The remote ip does not match the token ip"),
        "tr_ErrorCode_Minus_215143":
            MessageLookupByLibrary.simpleMessage("Token expired"),
        "tr_ErrorCode_Minus_215144":
            MessageLookupByLibrary.simpleMessage("Failed to obtain the key"),
        "tr_ErrorCode_Minus_215145":
            MessageLookupByLibrary.simpleMessage("Token does not match"),
        "tr_ErrorCode_Minus_215146":
            MessageLookupByLibrary.simpleMessage("Invalid token data format"),
        "tr_ErrorCode_Minus_215147":
            MessageLookupByLibrary.simpleMessage("Failed to decrypt key data"),
        "tr_ErrorCode_Minus_215148":
            MessageLookupByLibrary.simpleMessage("Authcode does not match"),
        "tr_ErrorCode_Minus_215149":
            MessageLookupByLibrary.simpleMessage("Authcode is changed"),
        "tr_ErrorCode_Minus_221201": MessageLookupByLibrary.simpleMessage(
            "Alarm authorization code error"),
        "tr_ErrorCode_Minus_221202": MessageLookupByLibrary.simpleMessage(
            "This function is not supported"),
        "tr_ErrorCode_Minus_222400": MessageLookupByLibrary.simpleMessage(
            "No recording files of the day were found"),
        "tr_ErrorCode_Minus_223000":
            MessageLookupByLibrary.simpleMessage("url is empty"),
        "tr_ErrorCode_Minus_223001":
            MessageLookupByLibrary.simpleMessage("Open failed"),
        "tr_ErrorCode_Minus_223002": MessageLookupByLibrary.simpleMessage(
            "Failed to obtain stream information"),
        "tr_ErrorCode_Minus_223003": MessageLookupByLibrary.simpleMessage(
            "Failed to obtain video stream information"),
        "tr_ErrorCode_Minus_223010": MessageLookupByLibrary.simpleMessage(
            "Unable to obtain video stream"),
        "tr_ErrorCode_Minus_223100":
            MessageLookupByLibrary.simpleMessage("Failed to open telnet"),
        "tr_ErrorCode_Minus_225402":
            MessageLookupByLibrary.simpleMessage("Server error"),
        "tr_ErrorCode_Minus_225501": MessageLookupByLibrary.simpleMessage(
            "Important parameter verification failed (field missing, type mismatch, empty string)"),
        "tr_ErrorCode_Minus_225502": MessageLookupByLibrary.simpleMessage(
            "Getting redis ip, port failed"),
        "tr_ErrorCode_Minus_225503": MessageLookupByLibrary.simpleMessage(
            "redis failed to establish connection"),
        "tr_ErrorCode_Minus_225504":
            MessageLookupByLibrary.simpleMessage("redis operation failed"),
        "tr_ErrorCode_Minus_225505": MessageLookupByLibrary.simpleMessage(
            "Failed to obtain mysql address"),
        "tr_ErrorCode_Minus_225506": MessageLookupByLibrary.simpleMessage(
            "SQL statement input parameter verification failed (SQL injection may exist)"),
        "tr_ErrorCode_Minus_225507":
            MessageLookupByLibrary.simpleMessage("SQL operation failed"),
        "tr_ErrorCode_Minus_225508": MessageLookupByLibrary.simpleMessage(
            "Failed to obtain thumbnail URL and URL expiration time"),
        "tr_ErrorCode_Minus_225509": MessageLookupByLibrary.simpleMessage(
            "Time format verification failed, timestamp conversion failed"),
        "tr_ErrorCode_Minus_225510": MessageLookupByLibrary.simpleMessage(
            "Cloud storage package information is abnormal"),
        "tr_ErrorCode_Minus_225511": MessageLookupByLibrary.simpleMessage(
            "Unknown illegal query type, not MSG or VIDEO"),
        "tr_ErrorCode_Minus_225512": MessageLookupByLibrary.simpleMessage(
            "The start time and end time of the query are not on the same day"),
        "tr_ErrorCode_Minus_225513":
            MessageLookupByLibrary.simpleMessage("The sn format is illegal"),
        "tr_ErrorCode_Minus_225514": MessageLookupByLibrary.simpleMessage(
            "Unknown illegal clearing type, not (ALL, ALARM, VIDEO)"),
        "tr_ErrorCode_Minus_225515": MessageLookupByLibrary.simpleMessage(
            "Unknown subscription query protocol format"),
        "tr_ErrorCode_Minus_225516": MessageLookupByLibrary.simpleMessage(
            "IP request not in the whitelist (only for cloud information deletion interface)"),
        "tr_ErrorCode_Minus_225517": MessageLookupByLibrary.simpleMessage(
            "The time area that this user can query has not been obtained"),
        "tr_ErrorCode_Minus_225518": MessageLookupByLibrary.simpleMessage(
            "JSON data format verification failed"),
        "tr_ErrorCode_Minus_225519": MessageLookupByLibrary.simpleMessage(
            "Error in getting message do-not-disturb time period and parsing configuration data format "),
        "tr_ErrorCode_Minus_226003": MessageLookupByLibrary.simpleMessage(
            "Cannot set read-only configuration"),
        "tr_ErrorCode_Minus_300000":
            MessageLookupByLibrary.simpleMessage("Get Auth Error"),
        "tr_ErrorCode_Minus_400000":
            MessageLookupByLibrary.simpleMessage("Heartbeat timeout"),
        "tr_ErrorCode_Minus_400001":
            MessageLookupByLibrary.simpleMessage("File does not exist"),
        "tr_ErrorCode_Minus_400002": MessageLookupByLibrary.simpleMessage(
            "The device is being upgraded"),
        "tr_ErrorCode_Minus_400003": MessageLookupByLibrary.simpleMessage(
            "Server initialization failed"),
        "tr_ErrorCode_Minus_400004": MessageLookupByLibrary.simpleMessage(
            "Failed to obtain connection type"),
        "tr_ErrorCode_Minus_400005":
            MessageLookupByLibrary.simpleMessage("Failed to query the server"),
        "tr_ErrorCode_Minus_400006": MessageLookupByLibrary.simpleMessage(
            "The device is already connected"),
        "tr_ErrorCode_Minus_400007":
            MessageLookupByLibrary.simpleMessage("logging in"),
        "tr_ErrorCode_Minus_400008": MessageLookupByLibrary.simpleMessage(
            "The device may not be online, please try again later"),
        "tr_ErrorCode_Minus_400009":
            MessageLookupByLibrary.simpleMessage("Device doesn\'t support"),
        "tr_ErrorCode_Minus_400010": MessageLookupByLibrary.simpleMessage(
            "There is no picture of the day, please switch the date"),
        "tr_ErrorCode_Minus_400011":
            MessageLookupByLibrary.simpleMessage("Disconnecting failed"),
        "tr_ErrorCode_Minus_400012": MessageLookupByLibrary.simpleMessage(
            "Other users are using the talk function, please try again later!"),
        "tr_ErrorCode_Minus_400013": MessageLookupByLibrary.simpleMessage(
            "Other users are using the talk function, please try again later!"),
        "tr_ErrorCode_Minus_400014":
            MessageLookupByLibrary.simpleMessage("Backup to USB disk failed"),
        "tr_ErrorCode_Minus_400015": MessageLookupByLibrary.simpleMessage(
            "No storage device (USB disk) or the device is not recording"),
        "tr_ErrorCode_Minus_400017":
            MessageLookupByLibrary.simpleMessage("Capture failed"),
        "tr_ErrorCode_Minus_400018":
            MessageLookupByLibrary.simpleMessage("File size limit exceeded"),
        "tr_ErrorCode_Minus_400019": MessageLookupByLibrary.simpleMessage(
            "File size verification failed"),
        "tr_ErrorCode_Minus_400100":
            MessageLookupByLibrary.simpleMessage("talk is not enabled"),
        "tr_ErrorCode_Minus_400101":
            MessageLookupByLibrary.simpleMessage("Device storage is full"),
        "tr_ErrorCode_Minus_400102": MessageLookupByLibrary.simpleMessage(
            "No clear result was obtained for obtaining login encryption information (supported/unsupported)"),
        "tr_ErrorCode_Minus_400201":
            MessageLookupByLibrary.simpleMessage("Insufficient memory"),
        "tr_ErrorCode_Minus_400202": MessageLookupByLibrary.simpleMessage(
            "The upgrade file format is incorrect"),
        "tr_ErrorCode_Minus_400203": MessageLookupByLibrary.simpleMessage(
            "A certain partition failed to upgrade"),
        "tr_ErrorCode_Minus_400204": MessageLookupByLibrary.simpleMessage(
            "Hardware model does not match"),
        "tr_ErrorCode_Minus_400205": MessageLookupByLibrary.simpleMessage(
            "Customer information does not match"),
        "tr_ErrorCode_Minus_400206": MessageLookupByLibrary.simpleMessage(
            "The compatible version number of the upgrade firmware is smaller than the current version of the device, and the device is not allowed to be upgraded back to the old firmware"),
        "tr_ErrorCode_Minus_400207":
            MessageLookupByLibrary.simpleMessage("Invalid version"),
        "tr_ErrorCode_Minus_400208": MessageLookupByLibrary.simpleMessage(
            "The Wi-Fi driver in the upgrade firmware does not match the Wi-Fi network card currently used by the device"),
        "tr_ErrorCode_Minus_400209":
            MessageLookupByLibrary.simpleMessage("Network error"),
        "tr_ErrorCode_Minus_400210": MessageLookupByLibrary.simpleMessage(
            "The upgrade firmware does not support the flash used by the device"),
        "tr_ErrorCode_Minus_400211": MessageLookupByLibrary.simpleMessage(
            "The upgrade file has been modified and cannot be upgraded through the external network"),
        "tr_ErrorCode_Minus_400212": MessageLookupByLibrary.simpleMessage(
            "Upgrading this firmware requires special capability support"),
        "tr_ErrorCode_Minus_4101": MessageLookupByLibrary.simpleMessage(
            "The device is temporarily unable to play"),
        "tr_ErrorCode_Minus_500000": MessageLookupByLibrary.simpleMessage(
            "Parameter encoding format error (e.g., UTF8 required but GBK provided)"),
        "tr_ErrorCode_Minus_500001": MessageLookupByLibrary.simpleMessage(
            "Parameter is not in JSON format"),
        "tr_ErrorCode_Minus_500003": MessageLookupByLibrary.simpleMessage(
            "Network error, please try again later"),
        "tr_ErrorCode_Minus_515000":
            MessageLookupByLibrary.simpleMessage("Device offline"),
        "tr_ErrorCode_Minus_515001":
            MessageLookupByLibrary.simpleMessage("Device has not reported"),
        "tr_ErrorCode_Minus_515002":
            MessageLookupByLibrary.simpleMessage("Channel mismatch"),
        "tr_ErrorCode_Minus_515003":
            MessageLookupByLibrary.simpleMessage("Channel offline"),
        "tr_ErrorCode_Minus_515004":
            MessageLookupByLibrary.simpleMessage("Account error"),
        "tr_ErrorCode_Minus_515100":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_ErrorCode_Minus_515101":
            MessageLookupByLibrary.simpleMessage("Handle error"),
        "tr_ErrorCode_Minus_515102":
            MessageLookupByLibrary.simpleMessage("API request failed"),
        "tr_ErrorCode_Minus_515103":
            MessageLookupByLibrary.simpleMessage("Play type error"),
        "tr_ErrorCode_Minus_515104": MessageLookupByLibrary.simpleMessage(
            "Failed to request device information from DSM service"),
        "tr_ErrorCode_Minus_515105": MessageLookupByLibrary.simpleMessage(
            "ONVIF SSID not registered yet"),
        "tr_ErrorCode_Minus_515201": MessageLookupByLibrary.simpleMessage(
            "GB preview/playback returned Not Found, corresponding to SIP error code 404"),
        "tr_ErrorCode_Minus_515202":
            MessageLookupByLibrary.simpleMessage("GB preview/playback failed"),
        "tr_ErrorCode_Minus_515203": MessageLookupByLibrary.simpleMessage(
            "GB preview/playback request timeout, device did not respond"),
        "tr_ErrorCode_Minus_516100":
            MessageLookupByLibrary.simpleMessage("RTSP protocol error"),
        "tr_ErrorCode_Minus_516101":
            MessageLookupByLibrary.simpleMessage("URL format error"),
        "tr_ErrorCode_Minus_516102":
            MessageLookupByLibrary.simpleMessage("No recording"),
        "tr_ErrorCode_Minus_516103":
            MessageLookupByLibrary.simpleMessage("URL expired"),
        "tr_ErrorCode_Minus_516104":
            MessageLookupByLibrary.simpleMessage("URL authentication failed"),
        "tr_ErrorCode_Minus_516105":
            MessageLookupByLibrary.simpleMessage("No traffic"),
        "tr_ErrorCode_Minus_516106": MessageLookupByLibrary.simpleMessage(
            "Failed to verify URL with GWM, communication failed"),
        "tr_ErrorCode_Minus_516107": MessageLookupByLibrary.simpleMessage(
            "Playback failed, XMTS communication failed"),
        "tr_ErrorCode_Minus_516108":
            MessageLookupByLibrary.simpleMessage("Query recording failed"),
        "tr_ErrorCode_Minus_516109":
            MessageLookupByLibrary.simpleMessage("Invalid SeekTime"),
        "tr_ErrorCode_Minus_516110":
            MessageLookupByLibrary.simpleMessage("URL information not found"),
        "tr_ErrorCode_Minus_516111":
            MessageLookupByLibrary.simpleMessage("Token parsing failed"),
        "tr_ErrorCode_Minus_516112":
            MessageLookupByLibrary.simpleMessage("Payload failed"),
        "tr_ErrorCode_Minus_516113":
            MessageLookupByLibrary.simpleMessage("Failed to update to Redis"),
        "tr_ErrorCode_Minus_516114":
            MessageLookupByLibrary.simpleMessage("URL not allowed to play"),
        "tr_ErrorCode_Minus_516115": MessageLookupByLibrary.simpleMessage(
            "URL exceeds allowed concurrency"),
        "tr_ErrorCode_Minus_603000": MessageLookupByLibrary.simpleMessage(
            "FunSDK certificate validity verification failed *Illegal UUID or AppKey is not allowed to be used"),
        "tr_ErrorCode_Minus_603001": MessageLookupByLibrary.simpleMessage(
            "JSON data format verification failed"),
        "tr_ErrorCode_Minus_603002": MessageLookupByLibrary.simpleMessage(
            "The login username or password is empty"),
        "tr_ErrorCode_Minus_603003":
            MessageLookupByLibrary.simpleMessage("Login Token is empty"),
        "tr_ErrorCode_Minus_603004": MessageLookupByLibrary.simpleMessage(
            "The third-party login type parameter is empty (WeChat--wx means Google means gg, Faceboo means fb, line means line)"),
        "tr_ErrorCode_Minus_604000": MessageLookupByLibrary.simpleMessage(
            "Incorrect username or password"),
        "tr_ErrorCode_Minus_604010":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_604011":
            MessageLookupByLibrary.simpleMessage("Two passwords don\'t match"),
        "tr_ErrorCode_Minus_604012": MessageLookupByLibrary.simpleMessage(
            "Username has been registered"),
        "tr_ErrorCode_Minus_604013":
            MessageLookupByLibrary.simpleMessage("Username is empty"),
        "tr_ErrorCode_Minus_604014":
            MessageLookupByLibrary.simpleMessage("Password is empty"),
        "tr_ErrorCode_Minus_604015": MessageLookupByLibrary.simpleMessage(
            "Confirming password is empty"),
        "tr_ErrorCode_Minus_604016":
            MessageLookupByLibrary.simpleMessage("Mobile number is empty"),
        "tr_ErrorCode_Minus_604017":
            MessageLookupByLibrary.simpleMessage("Incorrect username format"),
        "tr_ErrorCode_Minus_604018": MessageLookupByLibrary.simpleMessage(
            "New password does not meet requirements. Passwords with 8-64 bits must contain numbers and letters"),
        "tr_ErrorCode_Minus_604019": MessageLookupByLibrary.simpleMessage(
            "Confirm password format incorrect"),
        "tr_ErrorCode_Minus_604020": MessageLookupByLibrary.simpleMessage(
            "Phone number format incorrect"),
        "tr_ErrorCode_Minus_604021": MessageLookupByLibrary.simpleMessage(
            "The phone number has been registered"),
        "tr_ErrorCode_Minus_604022": MessageLookupByLibrary.simpleMessage(
            "The phone number is not registered"),
        "tr_ErrorCode_Minus_604023": MessageLookupByLibrary.simpleMessage(
            "The email has been registered"),
        "tr_ErrorCode_Minus_604024":
            MessageLookupByLibrary.simpleMessage("Email does not exist"),
        "tr_ErrorCode_Minus_604026":
            MessageLookupByLibrary.simpleMessage("Original password error"),
        "tr_ErrorCode_Minus_604027":
            MessageLookupByLibrary.simpleMessage("Modify password failed"),
        "tr_ErrorCode_Minus_604029":
            MessageLookupByLibrary.simpleMessage("UserID is empty"),
        "tr_ErrorCode_Minus_604030":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_604031":
            MessageLookupByLibrary.simpleMessage("Email is empty"),
        "tr_ErrorCode_Minus_604032":
            MessageLookupByLibrary.simpleMessage("Email format is incorrect"),
        "tr_ErrorCode_Minus_604033":
            MessageLookupByLibrary.simpleMessage("User not have authority"),
        "tr_ErrorCode_Minus_604034":
            MessageLookupByLibrary.simpleMessage("User unbond"),
        "tr_ErrorCode_Minus_604035":
            MessageLookupByLibrary.simpleMessage("User binding failed"),
        "tr_ErrorCode_Minus_604036":
            MessageLookupByLibrary.simpleMessage("Mobile binding failed"),
        "tr_ErrorCode_Minus_604037":
            MessageLookupByLibrary.simpleMessage("Email binding failed"),
        "tr_ErrorCode_Minus_604038": MessageLookupByLibrary.simpleMessage(
            "Sending verification code exceeds the maximum numbers of times"),
        "tr_ErrorCode_Minus_604039":
            MessageLookupByLibrary.simpleMessage("register failed"),
        "tr_ErrorCode_Minus_604040":
            MessageLookupByLibrary.simpleMessage("WeChat has bound users"),
        "tr_ErrorCode_Minus_604041": MessageLookupByLibrary.simpleMessage(
            "No permission to modify username (only for generated anonymous users)"),
        "tr_ErrorCode_Minus_604042":
            MessageLookupByLibrary.simpleMessage("User not bindfacebook"),
        "tr_ErrorCode_Minus_604043": MessageLookupByLibrary.simpleMessage(
            "User binding facebook failed"),
        "tr_ErrorCode_Minus_604044":
            MessageLookupByLibrary.simpleMessage("User not bind google"),
        "tr_ErrorCode_Minus_604045":
            MessageLookupByLibrary.simpleMessage("User binding google failed"),
        "tr_ErrorCode_Minus_604046":
            MessageLookupByLibrary.simpleMessage("Line account unbound"),
        "tr_ErrorCode_Minus_604047":
            MessageLookupByLibrary.simpleMessage("Line account binding failed"),
        "tr_ErrorCode_Minus_604048": MessageLookupByLibrary.simpleMessage(
            "Too many user verification code errors, the verification code is invalid. Please try again in 24 hours"),
        "tr_ErrorCode_Minus_604049": MessageLookupByLibrary.simpleMessage(
            "Too many login errors, please try again in ten minutes"),
        "tr_ErrorCode_Minus_604050": MessageLookupByLibrary.simpleMessage(
            "Request too frequent, please try again later"),
        "tr_ErrorCode_Minus_604100": MessageLookupByLibrary.simpleMessage(
            "Illegal device not allowed to be added"),
        "tr_ErrorCode_Minus_604101":
            MessageLookupByLibrary.simpleMessage("Device already exist"),
        "tr_ErrorCode_Minus_604102":
            MessageLookupByLibrary.simpleMessage("Delete device failed"),
        "tr_ErrorCode_Minus_604103":
            MessageLookupByLibrary.simpleMessage("Device info modify failed"),
        "tr_ErrorCode_Minus_604104": MessageLookupByLibrary.simpleMessage(
            "Device uuid parameter abnormal"),
        "tr_ErrorCode_Minus_604105": MessageLookupByLibrary.simpleMessage(
            "Device user parameter abnormal"),
        "tr_ErrorCode_Minus_604106": MessageLookupByLibrary.simpleMessage(
            "Device password parameter abnormal"),
        "tr_ErrorCode_Minus_604107": MessageLookupByLibrary.simpleMessage(
            "Device port parameter abnormal"),
        "tr_ErrorCode_Minus_604108": MessageLookupByLibrary.simpleMessage(
            "Device extension field parameter exception"),
        "tr_ErrorCode_Minus_604109":
            MessageLookupByLibrary.simpleMessage("Wrong position"),
        "tr_ErrorCode_Minus_604110": MessageLookupByLibrary.simpleMessage(
            "New password verification failed"),
        "tr_ErrorCode_Minus_604111": MessageLookupByLibrary.simpleMessage(
            "Confirmed password verification failed"),
        "tr_ErrorCode_Minus_604112": MessageLookupByLibrary.simpleMessage(
            "Device another name verification failed"),
        "tr_ErrorCode_Minus_604113":
            MessageLookupByLibrary.simpleMessage("Device ip address error"),
        "tr_ErrorCode_Minus_604114":
            MessageLookupByLibrary.simpleMessage("Support cloud storage"),
        "tr_ErrorCode_Minus_604115":
            MessageLookupByLibrary.simpleMessage("Not support cloud storage"),
        "tr_ErrorCode_Minus_604116": MessageLookupByLibrary.simpleMessage(
            "Transferring the device master account to another user failed. Check if the user owns the device and if they have the device master account permission"),
        "tr_ErrorCode_Minus_604117": MessageLookupByLibrary.simpleMessage(
            "Current account is not the main account of the current device"),
        "tr_ErrorCode_Minus_604118": MessageLookupByLibrary.simpleMessage(
            "The device not  exists and has been removed"),
        "tr_ErrorCode_Minus_604119": MessageLookupByLibrary.simpleMessage(
            "The device has been added to another account"),
        "tr_ErrorCode_Minus_604120": MessageLookupByLibrary.simpleMessage(
            "Device number under your account has been reached most, no more devices can be added!"),
        "tr_ErrorCode_Minus_604124": MessageLookupByLibrary.simpleMessage(
            "Add device failed, the shared device has been cancelled or deleted by the sharer"),
        "tr_ErrorCode_Minus_604126": MessageLookupByLibrary.simpleMessage(
            "The device has been bound and needs to be unbound before it can be added"),
        "tr_ErrorCode_Minus_604127": MessageLookupByLibrary.simpleMessage(
            "Add device failed, please try other methods"),
        "tr_ErrorCode_Minus_604128": MessageLookupByLibrary.simpleMessage(
            "Add device failed, device verification code illegal"),
        "tr_ErrorCode_Minus_604200":
            MessageLookupByLibrary.simpleMessage("Add authorization failed"),
        "tr_ErrorCode_Minus_604201":
            MessageLookupByLibrary.simpleMessage("Modify authorization failed"),
        "tr_ErrorCode_Minus_604202":
            MessageLookupByLibrary.simpleMessage("Delete authorization failed"),
        "tr_ErrorCode_Minus_604203": MessageLookupByLibrary.simpleMessage(
            "Single authorization synchronization failed (possibly due to incorrect type parameters or cloud product line not returning)"),
        "tr_ErrorCode_Minus_604300": MessageLookupByLibrary.simpleMessage(
            "Verification code send failed.pls check whether input correct"),
        "tr_ErrorCode_Minus_604301":
            MessageLookupByLibrary.simpleMessage("Email signature failed"),
        "tr_ErrorCode_Minus_604302": MessageLookupByLibrary.simpleMessage(
            "Logout account requires a verification code"),
        "tr_ErrorCode_Minus_604303": MessageLookupByLibrary.simpleMessage(
            "The number of times to obtain email links has exceeded the limit today. Please try again in 24 hours"),
        "tr_ErrorCode_Minus_604304": MessageLookupByLibrary.simpleMessage(
            "The number of times to obtain email links has exceeded the limit today. Please try again in 24 hours"),
        "tr_ErrorCode_Minus_604400": MessageLookupByLibrary.simpleMessage(
            "SMS interface verified failed, please contact us"),
        "tr_ErrorCode_Minus_604401": MessageLookupByLibrary.simpleMessage(
            "SMS interface parameter error, please contact us"),
        "tr_ErrorCode_Minus_604402": MessageLookupByLibrary.simpleMessage(
            "The number of times to obtain the verification code within 24 hours cannot exceed 3 times"),
        "tr_ErrorCode_Minus_604403": MessageLookupByLibrary.simpleMessage(
            "Verification code send failed, please check whether input correct"),
        "tr_ErrorCode_Minus_604404": MessageLookupByLibrary.simpleMessage(
            "Can only send once within 120 seconds"),
        "tr_ErrorCode_Minus_604405":
            MessageLookupByLibrary.simpleMessage("Send failed"),
        "tr_ErrorCode_Minus_604500": MessageLookupByLibrary.simpleMessage(
            "Not find user list or user list is empty"),
        "tr_ErrorCode_Minus_604502": MessageLookupByLibrary.simpleMessage(
            "Not find device list or device list is empty"),
        "tr_ErrorCode_Minus_604503":
            MessageLookupByLibrary.simpleMessage("Reset app secret failed"),
        "tr_ErrorCode_Minus_604600":
            MessageLookupByLibrary.simpleMessage("WeChat alarm failed to open"),
        "tr_ErrorCode_Minus_604601": MessageLookupByLibrary.simpleMessage(
            "WeChat alarm failed to close"),
        "tr_ErrorCode_Minus_605000":
            MessageLookupByLibrary.simpleMessage("Server failure"),
        "tr_ErrorCode_Minus_605001":
            MessageLookupByLibrary.simpleMessage("Certification not exist"),
        "tr_ErrorCode_Minus_605002": MessageLookupByLibrary.simpleMessage(
            "Request header information error"),
        "tr_ErrorCode_Minus_605003":
            MessageLookupByLibrary.simpleMessage("Certificate Invalidation"),
        "tr_ErrorCode_Minus_605004": MessageLookupByLibrary.simpleMessage(
            "Generate key verification error"),
        "tr_ErrorCode_Minus_605005":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_ErrorCode_Minus_605006":
            MessageLookupByLibrary.simpleMessage("Connect failed"),
        "tr_ErrorCode_Minus_605007":
            MessageLookupByLibrary.simpleMessage("Unknow error"),
        "tr_ErrorCode_Minus_605008": MessageLookupByLibrary.simpleMessage(
            "ip address not allowed for access"),
        "tr_ErrorCode_Minus_605009": MessageLookupByLibrary.simpleMessage(
            "Decryption error (third-party login code error or AES encryption and decryption error)"),
        "tr_ErrorCode_Minus_605010":
            MessageLookupByLibrary.simpleMessage("token expired"),
        "tr_ErrorCode_Minus_605011":
            MessageLookupByLibrary.simpleMessage("Token error"),
        "tr_ErrorCode_Minus_605012":
            MessageLookupByLibrary.simpleMessage("tokennot have authority"),
        "tr_ErrorCode_Minus_605013":
            MessageLookupByLibrary.simpleMessage("Not support"),
        "tr_ErrorCode_Minus_605017":
            MessageLookupByLibrary.simpleMessage("Message code invalidation!"),
        "tr_ErrorCode_Minus_66000":
            MessageLookupByLibrary.simpleMessage("Invalid login method"),
        "tr_ErrorCode_Minus_661412":
            MessageLookupByLibrary.simpleMessage("Account name not exist"),
        "tr_ErrorCode_Minus_661427": MessageLookupByLibrary.simpleMessage(
            "New password format not correct"),
        "tr_ErrorCode_Minus_70000": MessageLookupByLibrary.simpleMessage(
            "Error value of translitring DVR"),
        "tr_ErrorCode_Minus_70101":
            MessageLookupByLibrary.simpleMessage("Unknown error"),
        "tr_ErrorCode_Minus_70102":
            MessageLookupByLibrary.simpleMessage("Version doesn\'t support"),
        "tr_ErrorCode_Minus_70103":
            MessageLookupByLibrary.simpleMessage("Illegal request"),
        "tr_ErrorCode_Minus_70104":
            MessageLookupByLibrary.simpleMessage("The user has logged in"),
        "tr_ErrorCode_Minus_70105":
            MessageLookupByLibrary.simpleMessage("The user hasn\'t logged in"),
        "tr_ErrorCode_Minus_70106": MessageLookupByLibrary.simpleMessage(
            " Incorrect username or password"),
        "tr_ErrorCode_Minus_70107": MessageLookupByLibrary.simpleMessage(
            "No device function permission"),
        "tr_ErrorCode_Minus_70108":
            MessageLookupByLibrary.simpleMessage("Timeout"),
        "tr_ErrorCode_Minus_70109": MessageLookupByLibrary.simpleMessage(
            "Searching failed,the corresponding file was not found"),
        "tr_ErrorCode_Minus_70110": MessageLookupByLibrary.simpleMessage(
            "Searching successfully,return all files"),
        "tr_ErrorCode_Minus_70111": MessageLookupByLibrary.simpleMessage(
            "Searching successfully,return some files"),
        "tr_ErrorCode_Minus_70112":
            MessageLookupByLibrary.simpleMessage("User already exists"),
        "tr_ErrorCode_Minus_70113":
            MessageLookupByLibrary.simpleMessage("The user does not exist"),
        "tr_ErrorCode_Minus_70114": MessageLookupByLibrary.simpleMessage(
            "This user group already exists"),
        "tr_ErrorCode_Minus_70115":
            MessageLookupByLibrary.simpleMessage("This group doesn\'t exist"),
        "tr_ErrorCode_Minus_70116":
            MessageLookupByLibrary.simpleMessage("Pirated software"),
        "tr_ErrorCode_Minus_70117":
            MessageLookupByLibrary.simpleMessage("Incorrect message format"),
        "tr_ErrorCode_Minus_70118": MessageLookupByLibrary.simpleMessage(
            "The cloud platform protocol is not set"),
        "tr_ErrorCode_Minus_70119":
            MessageLookupByLibrary.simpleMessage("No record file"),
        "tr_ErrorCode_Minus_70120": MessageLookupByLibrary.simpleMessage(
            "Configuration is not enabled"),
        "tr_ErrorCode_Minus_70121": MessageLookupByLibrary.simpleMessage(
            "Video resource not connected to the frond end "),
        "tr_ErrorCode_Minus_70122": MessageLookupByLibrary.simpleMessage(
            "NAT links has been exhausted,and the new NAT connections are not allowed"),
        "tr_ErrorCode_Minus_70123": MessageLookupByLibrary.simpleMessage(
            "Tcp video links are up to the limit, new TCP video link is not allowed"),
        "tr_ErrorCode_Minus_70124": MessageLookupByLibrary.simpleMessage(
            "Incorrect encryption algorithm for username and password"),
        "tr_ErrorCode_Minus_70125": MessageLookupByLibrary.simpleMessage(
            "Other users have been created, and you can no longer log in with admin"),
        "tr_ErrorCode_Minus_70126": MessageLookupByLibrary.simpleMessage(
            "Log in too frequently,try again later"),
        "tr_ErrorCode_Minus_70128": MessageLookupByLibrary.simpleMessage(
            "The device is limited. If you have purchased a traffic package,please reboot the device."),
        "tr_ErrorCode_Minus_70129":
            MessageLookupByLibrary.simpleMessage("Remote login is prohibited"),
        "tr_ErrorCode_Minus_70130":
            MessageLookupByLibrary.simpleMessage("NAS address already exists"),
        "tr_ErrorCode_Minus_70131": MessageLookupByLibrary.simpleMessage(
            "The path is being used, can not be operated"),
        "tr_ErrorCode_Minus_70132": MessageLookupByLibrary.simpleMessage(
            "NAS has reached the maximum supported value, and it is not allowed to continue to be added"),
        "tr_ErrorCode_Minus_70140": MessageLookupByLibrary.simpleMessage(
            "The key pressed in the remote control binding of cunsumer products is wrong"),
        "tr_ErrorCode_Minus_70150": MessageLookupByLibrary.simpleMessage(
            "Succeed,device needs to be restarted"),
        "tr_ErrorCode_Minus_70153":
            MessageLookupByLibrary.simpleMessage("No SD card"),
        "tr_ErrorCode_Minus_70160":
            MessageLookupByLibrary.simpleMessage("Video backup failed"),
        "tr_ErrorCode_Minus_70161": MessageLookupByLibrary.simpleMessage(
            "Not record device or device doesnt record"),
        "tr_ErrorCode_Minus_70162":
            MessageLookupByLibrary.simpleMessage("The device is being added"),
        "tr_ErrorCode_Minus_70163": MessageLookupByLibrary.simpleMessage(
            "Error value of APS customer special password return"),
        "tr_ErrorCode_Minus_70164":
            MessageLookupByLibrary.simpleMessage("Insufficient device space"),
        "tr_ErrorCode_Minus_70165": MessageLookupByLibrary.simpleMessage(
            "The device is busy, and currently unused."),
        "tr_ErrorCode_Minus_70184": MessageLookupByLibrary.simpleMessage(
            "The device current power is lower than minimum required for ”real-time recording mode“,please try again after the device is charged"),
        "tr_ErrorCode_Minus_70202":
            MessageLookupByLibrary.simpleMessage("Not logged in"),
        "tr_ErrorCode_Minus_70203":
            MessageLookupByLibrary.simpleMessage("Incorrect login password"),
        "tr_ErrorCode_Minus_70205":
            MessageLookupByLibrary.simpleMessage("Illegal user"),
        "tr_ErrorCode_Minus_70206": MessageLookupByLibrary.simpleMessage(
            "Account is locked, login error"),
        "tr_ErrorCode_Minus_70207": MessageLookupByLibrary.simpleMessage(
            "The account has been blackedlisted"),
        "tr_ErrorCode_Minus_70208":
            MessageLookupByLibrary.simpleMessage("User is used"),
        "tr_ErrorCode_Minus_70209":
            MessageLookupByLibrary.simpleMessage("Invalid input"),
        "tr_ErrorCode_Minus_70210": MessageLookupByLibrary.simpleMessage(
            "If the user to be added already exists,the index is duplicated"),
        "tr_ErrorCode_Minus_70211": MessageLookupByLibrary.simpleMessage(
            "The object doesn\'t exist when querying"),
        "tr_ErrorCode_Minus_70212":
            MessageLookupByLibrary.simpleMessage("Object does not exist"),
        "tr_ErrorCode_Minus_70213": MessageLookupByLibrary.simpleMessage(
            "The object is in use, if the group has been used,it can not be deleted"),
        "tr_ErrorCode_Minus_70214":
            MessageLookupByLibrary.simpleMessage("The subset is out of scope"),
        "tr_ErrorCode_Minus_70215":
            MessageLookupByLibrary.simpleMessage("Incorrect password"),
        "tr_ErrorCode_Minus_70216":
            MessageLookupByLibrary.simpleMessage("Password doesn\'t match"),
        "tr_ErrorCode_Minus_70217":
            MessageLookupByLibrary.simpleMessage("Reserve account"),
        "tr_ErrorCode_Minus_70218": MessageLookupByLibrary.simpleMessage(
            "Unable to log in during system maintenance"),
        "tr_ErrorCode_Minus_70219": MessageLookupByLibrary.simpleMessage(
            "The number of verifications exsceeds the limit,you need to reboot the device and try again"),
        "tr_ErrorCode_Minus_70220":
            MessageLookupByLibrary.simpleMessage("Incorrect answer"),
        "tr_ErrorCode_Minus_70222":
            MessageLookupByLibrary.simpleMessage("Verification code error"),
        "tr_ErrorCode_Minus_70502":
            MessageLookupByLibrary.simpleMessage("502 command is illegal"),
        "tr_ErrorCode_Minus_70503":
            MessageLookupByLibrary.simpleMessage("Talk is enabled"),
        "tr_ErrorCode_Minus_70504":
            MessageLookupByLibrary.simpleMessage("Talk is not enabled"),
        "tr_ErrorCode_Minus_70602": MessageLookupByLibrary.simpleMessage(
            "The application needs to be restarted"),
        "tr_ErrorCode_Minus_70603": MessageLookupByLibrary.simpleMessage(
            "The device needs to be restarted"),
        "tr_ErrorCode_Minus_70604":
            MessageLookupByLibrary.simpleMessage("Fail to write the file"),
        "tr_ErrorCode_Minus_70605":
            MessageLookupByLibrary.simpleMessage("Function is not supported"),
        "tr_ErrorCode_Minus_70606":
            MessageLookupByLibrary.simpleMessage("Verification failed"),
        "tr_ErrorCode_Minus_70607":
            MessageLookupByLibrary.simpleMessage("Configuration parsing error"),
        "tr_ErrorCode_Minus_70609": MessageLookupByLibrary.simpleMessage(
            "Configuration doesn\'t exist"),
        "tr_ErrorCode_Minus_79001":
            MessageLookupByLibrary.simpleMessage("Unknown error"),
        "tr_ErrorCode_Minus_79002":
            MessageLookupByLibrary.simpleMessage("Query server failed"),
        "tr_ErrorCode_Minus_79004":
            MessageLookupByLibrary.simpleMessage("Offline"),
        "tr_ErrorCode_Minus_79005": MessageLookupByLibrary.simpleMessage(
            "Unable to connect to the server"),
        "tr_ErrorCode_Minus_79007": MessageLookupByLibrary.simpleMessage(
            "The number of connections is full"),
        "tr_ErrorCode_Minus_79008":
            MessageLookupByLibrary.simpleMessage("Unconnected"),
        "tr_ErrorCode_Minus_79020":
            MessageLookupByLibrary.simpleMessage("Connection timeout"),
        "tr_ErrorCode_Minus_79021": MessageLookupByLibrary.simpleMessage(
            "Connecting server request is refused"),
        "tr_ErrorCode_Minus_79022":
            MessageLookupByLibrary.simpleMessage("Query status timeout"),
        "tr_ErrorCode_Minus_79023": MessageLookupByLibrary.simpleMessage(
            "Query WAN information tieout"),
        "tr_ErrorCode_Minus_79024":
            MessageLookupByLibrary.simpleMessage("Network handshake timeout"),
        "tr_ErrorCode_Minus_79025":
            MessageLookupByLibrary.simpleMessage("Query server failed"),
        "tr_ErrorCode_Minus_79026":
            MessageLookupByLibrary.simpleMessage("Heartbeat timeout"),
        "tr_ErrorCode_Minus_79027":
            MessageLookupByLibrary.simpleMessage("Connection is disconnected"),
        "tr_ErrorCode_Minus_79998": MessageLookupByLibrary.simpleMessage(
            "Failed to open audio (device does not support audio playback)"),
        "tr_ErrorCode_Minus_79999":
            MessageLookupByLibrary.simpleMessage("YUV data error"),
        "tr_ErrorCode_Minus_800401":
            MessageLookupByLibrary.simpleMessage("Unauthorized"),
        "tr_ErrorCode_Minus_800403":
            MessageLookupByLibrary.simpleMessage("Prohibit access"),
        "tr_ErrorCode_Minus_800404":
            MessageLookupByLibrary.simpleMessage("Not exist"),
        "tr_ErrorCode_Minus_800500":
            MessageLookupByLibrary.simpleMessage("Server internal error"),
        "tr_ErrorCode_Minus_803004":
            MessageLookupByLibrary.simpleMessage("Account or password error"),
        "tr_ErrorCode_Minus_806002": MessageLookupByLibrary.simpleMessage(
            "The role does not exist, it needs to be configured"),
        "tr_ErrorCode_Minus_90000":
            MessageLookupByLibrary.simpleMessage("User cancellation"),
        "tr_ErrorCode_Minus_90001":
            MessageLookupByLibrary.simpleMessage("File is illegal"),
        "tr_ErrorCode_Minus_90003":
            MessageLookupByLibrary.simpleMessage("Function expired"),
        "tr_ErrorCode_Minus_90004": MessageLookupByLibrary.simpleMessage(
            "Reach the maximum connection number"),
        "tr_ErrorCode_Minus_99975":
            MessageLookupByLibrary.simpleMessage("Offline status"),
        "tr_ErrorCode_Minus_99976":
            MessageLookupByLibrary.simpleMessage("The user is blacklisted"),
        "tr_ErrorCode_Minus_99977":
            MessageLookupByLibrary.simpleMessage("The user is locked"),
        "tr_ErrorCode_Minus_99978": MessageLookupByLibrary.simpleMessage(
            "The user has logged in elsewhere"),
        "tr_ErrorCode_Minus_99979": MessageLookupByLibrary.simpleMessage(
            "Incorrect username or password"),
        "tr_ErrorCode_Minus_99980":
            MessageLookupByLibrary.simpleMessage("Protocol parsing error"),
        "tr_ErrorCode_Minus_99981": MessageLookupByLibrary.simpleMessage(
            "The buffer size is not enough or buffer is full"),
        "tr_ErrorCode_Minus_99982":
            MessageLookupByLibrary.simpleMessage("The sending buffer is full"),
        "tr_ErrorCode_Minus_99983": MessageLookupByLibrary.simpleMessage(
            "Listening server startup failed"),
        "tr_ErrorCode_Minus_99984":
            MessageLookupByLibrary.simpleMessage("Connecting WAN is forbidden"),
        "tr_ErrorCode_Minus_99985":
            MessageLookupByLibrary.simpleMessage("Server internal error"),
        "tr_ErrorCode_Minus_99986":
            MessageLookupByLibrary.simpleMessage("Object busy"),
        "tr_ErrorCode_Minus_99987": MessageLookupByLibrary.simpleMessage(
            "Network error（sending failed）"),
        "tr_ErrorCode_Minus_99988":
            MessageLookupByLibrary.simpleMessage("Network reception error"),
        "tr_ErrorCode_Minus_99989":
            MessageLookupByLibrary.simpleMessage("Fail to creat cache"),
        "tr_ErrorCode_Minus_99990":
            MessageLookupByLibrary.simpleMessage("Not found"),
        "tr_ErrorCode_Minus_99991": MessageLookupByLibrary.simpleMessage(
            "Your network connection timed out,please try again"),
        "tr_ErrorCode_Minus_99992":
            MessageLookupByLibrary.simpleMessage("The device already exists"),
        "tr_ErrorCode_Minus_99993":
            MessageLookupByLibrary.simpleMessage("Network error"),
        "tr_ErrorCode_Minus_99994":
            MessageLookupByLibrary.simpleMessage("Not supported"),
        "tr_ErrorCode_Minus_99995":
            MessageLookupByLibrary.simpleMessage("Failed to read the file"),
        "tr_ErrorCode_Minus_99996":
            MessageLookupByLibrary.simpleMessage("Failed to write the file"),
        "tr_ErrorCode_Minus_99997":
            MessageLookupByLibrary.simpleMessage("Failed to open the file"),
        "tr_ErrorCode_Minus_99998":
            MessageLookupByLibrary.simpleMessage("Failed to creat the file"),
        "tr_ErrorCode_Minus_99999":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_ErrorFormatPassword":
            MessageLookupByLibrary.simpleMessage("Password format incorrect"),
        "tr_ErrorFormatPhoneNum": MessageLookupByLibrary.simpleMessage(
            "Mobile number formagt is incorrect"),
        "tr_EventCenter": MessageLookupByLibrary.simpleMessage("Event center"),
        "tr_FailedRecord":
            MessageLookupByLibrary.simpleMessage("Record failed"),
        "tr_FailedSetDevicePassword": MessageLookupByLibrary.simpleMessage(
            "If set password failed, please long press the reset button on the back of the device to restore the factory default. After restoring the factory settings, it is necessary to add the device again and reset the password."),
        "tr_FileFormatterNotSupport":
            MessageLookupByLibrary.simpleMessage("Unsupported file format"),
        "tr_ForgetPassword":
            MessageLookupByLibrary.simpleMessage("Forgot password"),
        "tr_Format": MessageLookupByLibrary.simpleMessage("Format"),
        "tr_FormatStorageCard":
            MessageLookupByLibrary.simpleMessage("Format storage card"),
        "tr_Formatting": MessageLookupByLibrary.simpleMessage("Formatting..."),
        "tr_Fri": MessageLookupByLibrary.simpleMessage("Fri"),
        "tr_Friday": MessageLookupByLibrary.simpleMessage("Friday"),
        "tr_Function": MessageLookupByLibrary.simpleMessage("Function"),
        "tr_GBCascade": MessageLookupByLibrary.simpleMessage("GB Cascade"),
        "tr_GBCascadeAdd":
            MessageLookupByLibrary.simpleMessage("New GB Cascade"),
        "tr_GBCascadeAuthorization":
            MessageLookupByLibrary.simpleMessage("GB cascade authorization"),
        "tr_GBCascadeAuthorizationRoutes": MessageLookupByLibrary.simpleMessage(
            "Insufficient authorized routes, please expand"),
        "tr_GBCascadeChannelSIPID": m1,
        "tr_GBCascadeChannelTips": m2,
        "tr_GBCascadeDetail":
            MessageLookupByLibrary.simpleMessage("GB Cascade detail"),
        "tr_GBCascadeEdit":
            MessageLookupByLibrary.simpleMessage("GB Cascade detail edit"),
        "tr_GBCascadePush": MessageLookupByLibrary.simpleMessage("Push"),
        "tr_GBCascadeViewSIPID":
            MessageLookupByLibrary.simpleMessage("View SIP ID"),
        "tr_GenIdNumTips": MessageLookupByLibrary.simpleMessage(
            "Generated number must be greater than 0"),
        "tr_GenIdNumTips1": MessageLookupByLibrary.simpleMessage(
            "Number of channels must not exceed 128"),
        "tr_GenerateChannels":
            MessageLookupByLibrary.simpleMessage("Generate channel number"),
        "tr_GenerateID":
            MessageLookupByLibrary.simpleMessage("Generate access ID"),
        "tr_GenerateIDNum":
            MessageLookupByLibrary.simpleMessage("Generate ID number"),
        "tr_GoToSetting": MessageLookupByLibrary.simpleMessage("Go to setting"),
        "tr_GotIt": MessageLookupByLibrary.simpleMessage("Got it"),
        "tr_HDAddress": MessageLookupByLibrary.simpleMessage("HD Address"),
        "tr_HDStream": MessageLookupByLibrary.simpleMessage("HD"),
        "tr_HeartbeatCycle":
            MessageLookupByLibrary.simpleMessage("Heartbeat Cycle"),
        "tr_HelpAndFeedback":
            MessageLookupByLibrary.simpleMessage("Help and feedback"),
        "tr_HoldOnForSearhingDevices": MessageLookupByLibrary.simpleMessage(
            "Searching for nearby devices, please wait..."),
        "tr_IFrameInterval":
            MessageLookupByLibrary.simpleMessage("I-frame interval"),
        "tr_IPC": MessageLookupByLibrary.simpleMessage("IPC"),
        "tr_IPCType": MessageLookupByLibrary.simpleMessage("IPC Type"),
        "tr_IdCheck": MessageLookupByLibrary.simpleMessage("ID Authentication"),
        "tr_Images": MessageLookupByLibrary.simpleMessage("Images"),
        "tr_ImportantClause":
            MessageLookupByLibrary.simpleMessage("Important clause"),
        "tr_ImportantClauseContent": MessageLookupByLibrary.simpleMessage(
            "Hangzhou JFTECH Technology Co., Ltd. hereby declares that the commercial activities you participate in through this software are not related to Apple Inc"),
        "tr_IndustryCode":
            MessageLookupByLibrary.simpleMessage("Industry code"),
        "tr_InitiatePersonRectificationTasks": m3,
        "tr_Input": MessageLookupByLibrary.simpleMessage("Input"),
        "tr_InputAccessPassword":
            MessageLookupByLibrary.simpleMessage("Please set access password"),
        "tr_InputCompanyName": MessageLookupByLibrary.simpleMessage(
            "Please enter the company name"),
        "tr_InputConfirmPasswordRule": MessageLookupByLibrary.simpleMessage(
            "Please enter confirm password"),
        "tr_InputDeviceNickName":
            MessageLookupByLibrary.simpleMessage("Please input device name"),
        "tr_InputDeviceNickNameForSearch": MessageLookupByLibrary.simpleMessage(
            "Please input device name to search"),
        "tr_InputFrequency": MessageLookupByLibrary.simpleMessage(
            "Please enter the number of concurrent viewers"),
        "tr_InputNodeNickName":
            MessageLookupByLibrary.simpleMessage("Please input name"),
        "tr_InputPassword":
            MessageLookupByLibrary.simpleMessage("Please enter password"),
        "tr_InputPasswordRule":
            MessageLookupByLibrary.simpleMessage("Please enter password"),
        "tr_InputPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Please input mobile number"),
        "tr_InputPhoneNumberEmail": MessageLookupByLibrary.simpleMessage(
            "Please input mobile number/email"),
        "tr_InputPwd": MessageLookupByLibrary.simpleMessage("Enter Password"),
        "tr_InputTrueName":
            MessageLookupByLibrary.simpleMessage("Please enter real name"),
        "tr_InputVerifyCode":
            MessageLookupByLibrary.simpleMessage("Enter verification code"),
        "tr_InspectAccordingToPlan":
            MessageLookupByLibrary.simpleMessage("Task check"),
        "tr_Inspection": MessageLookupByLibrary.simpleMessage("Inspection"),
        "tr_InspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("Inspection analysis"),
        "tr_InspectionPlan":
            MessageLookupByLibrary.simpleMessage("Inspection plan"),
        "tr_InspectionRecord":
            MessageLookupByLibrary.simpleMessage("Inspection record"),
        "tr_InvalidData": MessageLookupByLibrary.simpleMessage("Invalid data"),
        "tr_InvalidDevice": MessageLookupByLibrary.simpleMessage(
            "The device serial number is invalid or the device has already been added"),
        "tr_JFDevice": MessageLookupByLibrary.simpleMessage("JFTECH device"),
        "tr_JFDeviceNotes":
            MessageLookupByLibrary.simpleMessage("Add JFTECH protocol device"),
        "tr_JFProtocol":
            MessageLookupByLibrary.simpleMessage("JFTECH Protocol"),
        "tr_LoadedFail": MessageLookupByLibrary.simpleMessage("Failed to load"),
        "tr_Loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "tr_LocalNetworkDisable": MessageLookupByLibrary.simpleMessage(
            "Local network permissions not enabled"),
        "tr_LocalNetworkDisableNotes": MessageLookupByLibrary.simpleMessage(
            "Not enable local network permissions, you will not be able to add devices"),
        "tr_Login": MessageLookupByLibrary.simpleMessage("Login"),
        "tr_LoginTips1":
            MessageLookupByLibrary.simpleMessage("Already read and agree"),
        "tr_Logout": MessageLookupByLibrary.simpleMessage("Log out"),
        "tr_Mail": MessageLookupByLibrary.simpleMessage("Email"),
        "tr_MakeSureDeleteDepartment": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the department?"),
        "tr_MakeSureDeleteDevice": MessageLookupByLibrary.simpleMessage(
            "Confirm to delete the device?"),
        "tr_MakeSureDeleteNode":
            MessageLookupByLibrary.simpleMessage("Confirm to delete node?"),
        "tr_MessageCenter": MessageLookupByLibrary.simpleMessage("Message"),
        "tr_ModifyDeviceNickName":
            MessageLookupByLibrary.simpleMessage("Modify device name"),
        "tr_ModifyDevicePassword":
            MessageLookupByLibrary.simpleMessage("Modify device password"),
        "tr_ModifyDevicePasswordNotes": MessageLookupByLibrary.simpleMessage(
            "For the security of your device, please change the default password"),
        "tr_ModifyPassword":
            MessageLookupByLibrary.simpleMessage("Modify password"),
        "tr_ModifySuccess":
            MessageLookupByLibrary.simpleMessage("Modify success"),
        "tr_Mon": MessageLookupByLibrary.simpleMessage("Mon"),
        "tr_Monday": MessageLookupByLibrary.simpleMessage("Monday"),
        "tr_MoveDevicesToCenterSeverNode": MessageLookupByLibrary.simpleMessage(
            "Deleting the central server will simultaneously delete its channels and nodes. Do you confirm the deletion?"),
        "tr_MoveDevicesToRootNVRNode": MessageLookupByLibrary.simpleMessage(
            "Deleting the NVR will simultaneously delete all subordinate devices. Do you confirm the deletion?"),
        "tr_MoveDevicesToRootNode": MessageLookupByLibrary.simpleMessage(
            "If there is a device, after deleting the node, the device will automatically belong to the root node"),
        "tr_NOAlarmMessage":
            MessageLookupByLibrary.simpleMessage("No alarms found"),
        "tr_NOPersonTips": MessageLookupByLibrary.simpleMessage(
            "No users available \n Please click the add button in the top right corner to add"),
        "tr_NORoleTips": MessageLookupByLibrary.simpleMessage(
            "No roles available\nPlease click the add button in the top right corner to add"),
        "tr_NVR": MessageLookupByLibrary.simpleMessage("NVR"),
        "tr_Name": MessageLookupByLibrary.simpleMessage("Name"),
        "tr_NameContentUnchange":
            MessageLookupByLibrary.simpleMessage("Enter consistent names"),
        "tr_NationalStandardConfiguration":
            MessageLookupByLibrary.simpleMessage(
                "National standard encode configuration"),
        "tr_NationalStandardDevice":
            MessageLookupByLibrary.simpleMessage("National standard device"),
        "tr_NationalStandardDeviceNotes": MessageLookupByLibrary.simpleMessage(
            "Add national standard device"),
        "tr_NetworkConfiguration":
            MessageLookupByLibrary.simpleMessage("Network configuration"),
        "tr_NetworkConfigurationDeclare": MessageLookupByLibrary.simpleMessage(
            "Device not support 5G WiFi, only support 2.4G WiFi"),
        "tr_NetworkIdentification":
            MessageLookupByLibrary.simpleMessage("_Network Identificatio"),
        "tr_Next": MessageLookupByLibrary.simpleMessage("Next"),
        "tr_NoData": MessageLookupByLibrary.simpleMessage("No Data Available"),
        "tr_NoMedia": MessageLookupByLibrary.simpleMessage(
            "No video resources available"),
        "tr_NoRole": MessageLookupByLibrary.simpleMessage("No roles available"),
        "tr_NoSDCardInserted": MessageLookupByLibrary.simpleMessage(
            "No SD card inserted, cannot record"),
        "tr_Node": MessageLookupByLibrary.simpleMessage("Node"),
        "tr_NodeEdit": MessageLookupByLibrary.simpleMessage("Edit node"),
        "tr_NodeName": MessageLookupByLibrary.simpleMessage("Node name"),
        "tr_NormalMessage": MessageLookupByLibrary.simpleMessage("Message"),
        "tr_NormalSetting": MessageLookupByLibrary.simpleMessage("Setting"),
        "tr_NowLatestVersion":
            MessageLookupByLibrary.simpleMessage("Latest version"),
        "tr_OfficialWebsite":
            MessageLookupByLibrary.simpleMessage("Official website"),
        "tr_OnSiteInspection":
            MessageLookupByLibrary.simpleMessage("On-Site Inspection"),
        "tr_OnSiteInspectionDetail":
            MessageLookupByLibrary.simpleMessage("Scene inspection detail"),
        "tr_OperationByInstructions": MessageLookupByLibrary.simpleMessage(
            "According to the instructions, power on the camera to ensure it starts up normally"),
        "tr_OprationDeny": MessageLookupByLibrary.simpleMessage(
            "There are child nodes under this node, please delete the child nodes first"),
        "tr_OprationsOfNetworkConfiguration": MessageLookupByLibrary.simpleMessage(
            "· Please put the QR code towards the camera lens;\n· Maintain a distance of 25-35 centimeters and wait for scanning;\n· Remove the phone after hearing the WiFi configuration prompt sound;\n·Hearing a successful configuration prompt indicates that the device\'s WiFi configuration has been completed"),
        "tr_OrganizationStructure":
            MessageLookupByLibrary.simpleMessage("Device tree"),
        "tr_PTZ": MessageLookupByLibrary.simpleMessage("PTZ"),
        "tr_PTZResetFailed":
            MessageLookupByLibrary.simpleMessage("PTZ reset failed"),
        "tr_PTZResetSuccessful":
            MessageLookupByLibrary.simpleMessage("PTZ reset successful"),
        "tr_PasswordNotOneMatch":
            MessageLookupByLibrary.simpleMessage("Password inconsist"),
        "tr_Permission_Album": MessageLookupByLibrary.simpleMessage(
            "Please Allow Access to Photos!"),
        "tr_Permission_Audio": MessageLookupByLibrary.simpleMessage(
            "Please allow access to the microphone!"),
        "tr_Permission_Bluetooth":
            MessageLookupByLibrary.simpleMessage("Bluetooth"),
        "tr_Permission_Camera":
            MessageLookupByLibrary.simpleMessage("Please allow Camera Access!"),
        "tr_Permission_LocalNetwork":
            MessageLookupByLibrary.simpleMessage("Local network"),
        "tr_Permission_Location":
            MessageLookupByLibrary.simpleMessage("Position"),
        "tr_PersonDetail": MessageLookupByLibrary.simpleMessage("User Details"),
        "tr_PersonDetailEdit":
            MessageLookupByLibrary.simpleMessage("Edit the user details"),
        "tr_PersonInfo": MessageLookupByLibrary.simpleMessage("Personal info"),
        "tr_PersonManage":
            MessageLookupByLibrary.simpleMessage("User management"),
        "tr_Phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "tr_PhoneNum": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "tr_PlatformDevice":
            MessageLookupByLibrary.simpleMessage("Platform device"),
        "tr_PlatformDeviceNotes": MessageLookupByLibrary.simpleMessage(
            "Add a platform protocol device"),
        "tr_PlayUrlIsInvalid":
            MessageLookupByLibrary.simpleMessage("The play address is empty"),
        "tr_Playback": MessageLookupByLibrary.simpleMessage("Playback"),
        "tr_PleaseAgreeAgreementAndPolicy":
            MessageLookupByLibrary.simpleMessage(
                "Please agree to the user agreement and privacy policy first"),
        "tr_PleaseAgreeProtocol": MessageLookupByLibrary.simpleMessage(
            "Please agree protocol firstly"),
        "tr_PleaseAllowVisitAllPhoto": MessageLookupByLibrary.simpleMessage(
            "Please allow access to all photos in album first."),
        "tr_PleaseConfirmPwd":
            MessageLookupByLibrary.simpleMessage("Please confirm password"),
        "tr_PleaseFillInfo": MessageLookupByLibrary.simpleMessage(
            "Please complete the information firstly"),
        "tr_PleaseInputContent":
            MessageLookupByLibrary.simpleMessage("Please input content"),
        "tr_PleaseInputDes":
            MessageLookupByLibrary.simpleMessage("Please enter description"),
        "tr_PleaseInputMail":
            MessageLookupByLibrary.simpleMessage("Please enter email"),
        "tr_PleaseInputName":
            MessageLookupByLibrary.simpleMessage("Please enter name"),
        "tr_PleaseInputNewMail":
            MessageLookupByLibrary.simpleMessage("Please input new email"),
        "tr_PleaseInputNewPhone": MessageLookupByLibrary.simpleMessage(
            "Please input new phone number"),
        "tr_PleaseInputNewPwd": MessageLookupByLibrary.simpleMessage(
            "Please input the new password"),
        "tr_PleaseInputNewPwdCon":
            MessageLookupByLibrary.simpleMessage("Please confirm new password"),
        "tr_PleaseInputOldPwd": MessageLookupByLibrary.simpleMessage(
            "Please input the old password"),
        "tr_PleaseInputPwdCon": MessageLookupByLibrary.simpleMessage(
            "Please enter confirm password"),
        "tr_PleaseSelect":
            MessageLookupByLibrary.simpleMessage("Please Select"),
        "tr_Preset": MessageLookupByLibrary.simpleMessage("Preset"),
        "tr_PresetNameExisted": MessageLookupByLibrary.simpleMessage(
            "Preset name already exist, do not use same name"),
        "tr_PrivacyPolicy":
            MessageLookupByLibrary.simpleMessage("Privacy policy"),
        "tr_Prompt": MessageLookupByLibrary.simpleMessage("Prompt"),
        "tr_PwdCon": MessageLookupByLibrary.simpleMessage("Confirm Password"),
        "tr_QRcodeInvalid": MessageLookupByLibrary.simpleMessage(
            "Unable to recognize QR code information in the image. Please choose another image or retry."),
        "tr_Record": MessageLookupByLibrary.simpleMessage("Record"),
        "tr_RecordCard": MessageLookupByLibrary.simpleMessage("Local Playback"),
        "tr_RecordCloud":
            MessageLookupByLibrary.simpleMessage("Cloud Playback"),
        "tr_Recording":
            MessageLookupByLibrary.simpleMessage("Already enabled record"),
        "tr_RecordingManagement":
            MessageLookupByLibrary.simpleMessage("Recording management"),
        "tr_RecordingSegment":
            MessageLookupByLibrary.simpleMessage("Recording segment"),
        "tr_RecordingSegmentTip": MessageLookupByLibrary.simpleMessage(
            "The maximum duration of a single recording file"),
        "tr_RecordingSwitch":
            MessageLookupByLibrary.simpleMessage("Recording switch"),
        "tr_RegisterAccount":
            MessageLookupByLibrary.simpleMessage("Register account"),
        "tr_Reject": MessageLookupByLibrary.simpleMessage("Reject"),
        "tr_Remaining": MessageLookupByLibrary.simpleMessage("Remaining"),
        "tr_RequestPermission": m4,
        "tr_ResetDevice": MessageLookupByLibrary.simpleMessage(
            "If forgot password, please restore to default and add again"),
        "tr_ResetPassword":
            MessageLookupByLibrary.simpleMessage("Reset password"),
        "tr_RestoreFactorySettings":
            MessageLookupByLibrary.simpleMessage("Initialize device"),
        "tr_RestoreFactorySettingsNotes": MessageLookupByLibrary.simpleMessage(
            "If not，please long press device SET/RESET button for 6 seconds untill hearing\"Restoring to factory,please do not power off\"；After device restoring finished, connect again"),
        "tr_RestoreFactorySettingsNotes2": MessageLookupByLibrary.simpleMessage(
            "Please check the device body or refering to manual for specific button positions"),
        "tr_RestoreFactorySettingsTip": MessageLookupByLibrary.simpleMessage(
            "If you hear the device prompt\"Start Quick Configuration\"or\"Waiting for Connection\",Click\"next\""),
        "tr_ReviewAccess":
            MessageLookupByLibrary.simpleMessage("Review access"),
        "tr_ReviewTips1": MessageLookupByLibrary.simpleMessage(
            "You already submitted your registration application, please be patient and wait"),
        "tr_ReviewTips2": MessageLookupByLibrary.simpleMessage(
            "On weekdays, we will complete the audit within 1 hour. \nOn non weekdays, we will complete the audit within 24 hours. \nThe audit results will be notified to you via SMS! \nPlease pay attention to SMS notifications in a timely manner"),
        "tr_Role": MessageLookupByLibrary.simpleMessage("Role"),
        "tr_RoleDes": MessageLookupByLibrary.simpleMessage("Role Description"),
        "tr_RoleDetail": MessageLookupByLibrary.simpleMessage("Role Details"),
        "tr_RoleManage":
            MessageLookupByLibrary.simpleMessage("Role management"),
        "tr_RoleName": MessageLookupByLibrary.simpleMessage("Role Name"),
        "tr_RulesOfDevicePassword": MessageLookupByLibrary.simpleMessage(
            "Password length 8-64 bits, consisting of letters and numbers"),
        "tr_SCanQRCode": MessageLookupByLibrary.simpleMessage("Scan QR code"),
        "tr_SDAddress": MessageLookupByLibrary.simpleMessage("SD Address"),
        "tr_SDStream": MessageLookupByLibrary.simpleMessage("SD"),
        "tr_SIPServerAddress":
            MessageLookupByLibrary.simpleMessage("SIP Service address"),
        "tr_SIPServerDomain":
            MessageLookupByLibrary.simpleMessage("SIP Server domain"),
        "tr_SIPServerDomain2":
            MessageLookupByLibrary.simpleMessage("SIP Domain"),
        "tr_SIPServerIP": MessageLookupByLibrary.simpleMessage("SIP Server IP"),
        "tr_SIPServerId": MessageLookupByLibrary.simpleMessage("SIP Server ID"),
        "tr_SIPServerNumber":
            MessageLookupByLibrary.simpleMessage("SIP server number"),
        "tr_SIPServerPort":
            MessageLookupByLibrary.simpleMessage("SIP Server port"),
        "tr_Sat": MessageLookupByLibrary.simpleMessage("Sat"),
        "tr_Saturday": MessageLookupByLibrary.simpleMessage("Saturday"),
        "tr_SaveRecordFileFailed":
            MessageLookupByLibrary.simpleMessage("Save record faield"),
        "tr_SaveRecordFileSuccess":
            MessageLookupByLibrary.simpleMessage("Save record success"),
        "tr_ScanJFDevice":
            MessageLookupByLibrary.simpleMessage("Scan available devices"),
        "tr_SelectAll": MessageLookupByLibrary.simpleMessage("Select All"),
        "tr_SelectDeviceResource":
            MessageLookupByLibrary.simpleMessage("Select Device Resource"),
        "tr_SelectItemTips": MessageLookupByLibrary.simpleMessage(
            "Please select the file you want to operate on first."),
        "tr_SelectNode": MessageLookupByLibrary.simpleMessage("Node Selection"),
        "tr_SelectRole": MessageLookupByLibrary.simpleMessage("Select Role"),
        "tr_SelectTime": MessageLookupByLibrary.simpleMessage("Select time"),
        "tr_SendVerifyCode":
            MessageLookupByLibrary.simpleMessage("Send verification code"),
        "tr_ServerParams":
            MessageLookupByLibrary.simpleMessage("Server parameter"),
        "tr_ServiceParams":
            MessageLookupByLibrary.simpleMessage("Service parameter"),
        "tr_SetPresetName":
            MessageLookupByLibrary.simpleMessage("Please input preset name"),
        "tr_ShakeToInspect":
            MessageLookupByLibrary.simpleMessage("Shake to Inspect"),
        "tr_Share": MessageLookupByLibrary.simpleMessage("Share"),
        "tr_ShareFailure": MessageLookupByLibrary.simpleMessage("Share Failed"),
        "tr_ShareSuccess":
            MessageLookupByLibrary.simpleMessage("Share Successful"),
        "tr_ShareTips1": MessageLookupByLibrary.simpleMessage(
            "1.Video and image cannot be combined for sharing.\n2.Only one video can be shared at a time."),
        "tr_ShareTips2": MessageLookupByLibrary.simpleMessage(
            "Only one file can be shared."),
        "tr_ShareTips3": MessageLookupByLibrary.simpleMessage(
            "Video needs to be downloaded. Please share after the download is complete."),
        "tr_ShareTipsImageMaxNum": MessageLookupByLibrary.simpleMessage(
            "Maximum 5 images can be shared at once."),
        "tr_SnapFailed":
            MessageLookupByLibrary.simpleMessage("Snapshot failed"),
        "tr_SnapSuccess":
            MessageLookupByLibrary.simpleMessage("Snapshot success"),
        "tr_SpotCheckInspection":
            MessageLookupByLibrary.simpleMessage("Video check"),
        "tr_StandardAccessTips": MessageLookupByLibrary.simpleMessage(
            "To add an international device, it is necessary to configure the access ID, SIP server IP, SIP server port, and other information to the designated location in the background of the device to be connected. You can view the corresponding information in the generated access ID details."),
        "tr_Status": MessageLookupByLibrary.simpleMessage("Status"),
        "tr_StatusPre": MessageLookupByLibrary.simpleMessage("Status:"),
        "tr_StorageCardInfo":
            MessageLookupByLibrary.simpleMessage("Storage card information"),
        "tr_StorageCardSetting":
            MessageLookupByLibrary.simpleMessage("Storage card setting"),
        "tr_Submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "tr_SuccessfullyDeleted":
            MessageLookupByLibrary.simpleMessage("Deletion Successful"),
        "tr_Sun": MessageLookupByLibrary.simpleMessage("Sun"),
        "tr_Sunday": MessageLookupByLibrary.simpleMessage("Sunday"),
        "tr_SuperAdmin": MessageLookupByLibrary.simpleMessage("Super Admin"),
        "tr_SureDelete":
            MessageLookupByLibrary.simpleMessage("Confirm Deletion"),
        "tr_SureItemDelete": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected item?"),
        "tr_SureSaveToAlbum": MessageLookupByLibrary.simpleMessage(
            "Confirm saving selected files to album?"),
        "tr_TakePhoto": MessageLookupByLibrary.simpleMessage("Take photo"),
        "tr_Template": MessageLookupByLibrary.simpleMessage("Template"),
        "tr_TheRestrictionsOnWiFi": MessageLookupByLibrary.simpleMessage(
            "What are the WiFi requirements for devices？"),
        "tr_Thur": MessageLookupByLibrary.simpleMessage("Thur"),
        "tr_Thursday": MessageLookupByLibrary.simpleMessage("Thursday"),
        "tr_ToDoList": MessageLookupByLibrary.simpleMessage("To Do items"),
        "tr_Today": MessageLookupByLibrary.simpleMessage("Today"),
        "tr_Tool": MessageLookupByLibrary.simpleMessage("Tool"),
        "tr_Tues": MessageLookupByLibrary.simpleMessage("Tues"),
        "tr_Tuesday": MessageLookupByLibrary.simpleMessage("Tuesday"),
        "tr_TurnOnWiFi": MessageLookupByLibrary.simpleMessage(
            "In order to add device normally，need enable WiFi"),
        "tr_TypeCode": MessageLookupByLibrary.simpleMessage("Type code"),
        "tr_TypePre": MessageLookupByLibrary.simpleMessage("Type:"),
        "tr_UnUsed": MessageLookupByLibrary.simpleMessage("Unused"),
        "tr_UnknownError":
            MessageLookupByLibrary.simpleMessage("Unknown error"),
        "tr_Used": MessageLookupByLibrary.simpleMessage("Used"),
        "tr_UserAgreement":
            MessageLookupByLibrary.simpleMessage("User protocol"),
        "tr_UserServiceAgreement":
            MessageLookupByLibrary.simpleMessage("User Service Agreement"),
        "tr_VersionUpdate":
            MessageLookupByLibrary.simpleMessage("Version update"),
        "tr_VideoSpotCheckInspection":
            MessageLookupByLibrary.simpleMessage("Video Spot Check Inspection"),
        "tr_Videos": MessageLookupByLibrary.simpleMessage("Videos"),
        "tr_View": MessageLookupByLibrary.simpleMessage("View"),
        "tr_ViewDetail": MessageLookupByLibrary.simpleMessage("View Details"),
        "tr_Wed": MessageLookupByLibrary.simpleMessage("Wed"),
        "tr_Wednesday": MessageLookupByLibrary.simpleMessage("Wednesday"),
        "tr_WiFiConfigurationRecommendation": MessageLookupByLibrary.simpleMessage(
            "1. If the home is a dual band router, please check if the WiFi connected to the camera is in the 5GHz frequency band, and switch to a 2.4GHz WiFi connection.\n\n2. It is recommended not to be too far from the router when configuring the camera. \n\n3. It is recommended to connect to non bridging WiFi, as bridging may cause your network to be very unstable."),
        "tr_aPatrolTaskHasBeenAssignedToYou":
            MessageLookupByLibrary.simpleMessage(
                "Assigned an inspection task to you"),
        "tr_aRectificationTaskHasBeenAssignedToYou":
            MessageLookupByLibrary.simpleMessage(
                "Assigned a rectification task to you"),
        "tr_abnormalDevice":
            MessageLookupByLibrary.simpleMessage("Abnormal device"),
        "tr_abnormalDeviceTip": MessageLookupByLibrary.simpleMessage(
            "The following are abnormal devices, offline or unable to open video, and do not participate in inspection operations"),
        "tr_aboutDevice": MessageLookupByLibrary.simpleMessage("About device"),
        "tr_accept": MessageLookupByLibrary.simpleMessage("Accept"),
        "tr_acceptance": MessageLookupByLibrary.simpleMessage("Acceptance"),
        "tr_acceptanceAndSubmissionTip": MessageLookupByLibrary.simpleMessage(
            "Before submitting for acceptance, please determine whether the rectification item has passed."),
        "tr_acceptanceCountRanking":
            MessageLookupByLibrary.simpleMessage("Acceptance count ranking"),
        "tr_acceptanceEvent":
            MessageLookupByLibrary.simpleMessage("Acceptance event"),
        "tr_acceptanceInstructions":
            MessageLookupByLibrary.simpleMessage("Acceptance instructions"),
        "tr_acceptancePassed":
            MessageLookupByLibrary.simpleMessage("Acceptance passed"),
        "tr_acceptancePerson": MessageLookupByLibrary.simpleMessage("Acceptor"),
        "tr_accessIdHint": MessageLookupByLibrary.simpleMessage("20 numbers"),
        "tr_accessInfo":
            MessageLookupByLibrary.simpleMessage("Access information"),
        "tr_accessPsdHint": MessageLookupByLibrary.simpleMessage(
            "English, numbers, special symbols"),
        "tr_accessStatus":
            MessageLookupByLibrary.simpleMessage("Access status"),
        "tr_accountActivation":
            MessageLookupByLibrary.simpleMessage("Account activation"),
        "tr_accountAddInfoTips": MessageLookupByLibrary.simpleMessage(
            "The current device has been added by the current account or other accounts, please do not add it repeatedly."),
        "tr_accountAuthorization":
            MessageLookupByLibrary.simpleMessage("Sub Account Authorization"),
        "tr_accountAuthorizationStatistics": m5,
        "tr_accountBlacklisted":
            MessageLookupByLibrary.simpleMessage("Account blacklisted"),
        "tr_accountError":
            MessageLookupByLibrary.simpleMessage("Account error"),
        "tr_accountLocked":
            MessageLookupByLibrary.simpleMessage("Account locked"),
        "tr_accountLoggedIn":
            MessageLookupByLibrary.simpleMessage("Account logged in"),
        "tr_accountNotExist":
            MessageLookupByLibrary.simpleMessage("Account does not exist"),
        "tr_accountNotLoggedIn":
            MessageLookupByLibrary.simpleMessage("Account not logged in"),
        "tr_accountOverdueAccessFailed": MessageLookupByLibrary.simpleMessage(
            "Account overdue, access to streaming service failed"),
        "tr_accountPassword":
            MessageLookupByLibrary.simpleMessage("Account and password"),
        "tr_activate": MessageLookupByLibrary.simpleMessage("Activate"),
        "tr_active": MessageLookupByLibrary.simpleMessage("Active"),
        "tr_activityName":
            MessageLookupByLibrary.simpleMessage("Activity name"),
        "tr_actualVideoFrameRateTooLow": MessageLookupByLibrary.simpleMessage(
            "Actual video frame rate too low"),
        "tr_addApplications":
            MessageLookupByLibrary.simpleMessage("Add as common"),
        "tr_addBatches": MessageLookupByLibrary.simpleMessage("Add in batches"),
        "tr_addBatchesDevices":
            MessageLookupByLibrary.simpleMessage("Add devices in batches"),
        "tr_addChannel": MessageLookupByLibrary.simpleMessage("Add Channel"),
        "tr_addChannelTips": m6,
        "tr_addChooseBatch": MessageLookupByLibrary.simpleMessage("Batch Add"),
        "tr_addChooseContacts":
            MessageLookupByLibrary.simpleMessage("Contacts import"),
        "tr_addChooseFaceDatabase":
            MessageLookupByLibrary.simpleMessage("Face database import"),
        "tr_addChooseManual":
            MessageLookupByLibrary.simpleMessage("Manually add"),
        "tr_addDeviceDialogTip1": MessageLookupByLibrary.simpleMessage(
            "If you forget your password, please restore the factory settings and add it again!"),
        "tr_addDeviceDialogTip2": MessageLookupByLibrary.simpleMessage(
            "The device has been powered on for more than 1 hour and cannot log in to the device. Please restore the factory settings and add it again!"),
        "tr_addDevicesChannelTips": m7,
        "tr_addDiscoveredBatchesDevices": MessageLookupByLibrary.simpleMessage(
            "Add devices discovered by LAN in batches"),
        "tr_addException":
            MessageLookupByLibrary.simpleMessage("Add exception"),
        "tr_addFacialUser":
            MessageLookupByLibrary.simpleMessage("Add a new user"),
        "tr_addFailed": MessageLookupByLibrary.simpleMessage("Add failed"),
        "tr_addHeatArea": MessageLookupByLibrary.simpleMessage("Add heat area"),
        "tr_addHeatAreaTip": MessageLookupByLibrary.simpleMessage(
            "(1)Click + to draw an area on the floor plan;\n(2)Associate the monitoring device of this area;\n(3)Configure and associate the heat area on the device."),
        "tr_addHeatZone": MessageLookupByLibrary.simpleMessage("Add heat zone"),
        "tr_addImageInspectionPlan":
            MessageLookupByLibrary.simpleMessage("Add Image Inspection Plan"),
        "tr_addTime": m8,
        "tr_address": MessageLookupByLibrary.simpleMessage("Address"),
        "tr_advancedSettings":
            MessageLookupByLibrary.simpleMessage("Advanced settings"),
        "tr_afterAlarmContinueDetectionFrequencyReturnsToDefault":
            MessageLookupByLibrary.simpleMessage(
                "After an alarm is triggered, continue detection (with the detection frequency returning to the default)."),
        "tr_afterAlarmCurrentRunningTimeNoDetection":
            MessageLookupByLibrary.simpleMessage(
                "After an alarm is triggered, do not perform detection during the current runtime."),
        "tr_afterAlarmNoDetectionForRestOfDay":
            MessageLookupByLibrary.simpleMessage(
                "After an alarm is triggered, do not perform detection for the rest of the day."),
        "tr_afterRectification":
            MessageLookupByLibrary.simpleMessage("After Rectification"),
        "tr_afternoon": MessageLookupByLibrary.simpleMessage("Afternoon"),
        "tr_ageGroupDistribution":
            MessageLookupByLibrary.simpleMessage("Age group proportion"),
        "tr_agree": MessageLookupByLibrary.simpleMessage("Agree"),
        "tr_aiAlarm": MessageLookupByLibrary.simpleMessage("AI Alarm"),
        "tr_aiBox": MessageLookupByLibrary.simpleMessage("AI Box"),
        "tr_aiBoxAdd": MessageLookupByLibrary.simpleMessage("AI box added"),
        "tr_aiBoxAddNameTip":
            MessageLookupByLibrary.simpleMessage("Please enter device name"),
        "tr_aiBoxAddSNTip":
            MessageLookupByLibrary.simpleMessage("Please enter device SN"),
        "tr_aiBoxChannelConfigLimitTip": MessageLookupByLibrary.simpleMessage(
            "All channels of the AI box have been configured with 8 algorithms, and no more algorithms can be configured."),
        "tr_aiBoxTip":
            MessageLookupByLibrary.simpleMessage("Add AI Box device"),
        "tr_aiDeviceAlarm":
            MessageLookupByLibrary.simpleMessage("AI device alarm"),
        "tr_aiInspection":
            MessageLookupByLibrary.simpleMessage("AI Inspection"),
        "tr_aiInspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("AI Inspection analysis"),
        "tr_aiInspectionAnalysisAlarmCount":
            MessageLookupByLibrary.simpleMessage(
                "AI inspection analysis - alarm count data"),
        "tr_aiInspectionAnalysisAlarmCountByInspectionItem":
            MessageLookupByLibrary.simpleMessage(
                "AI inspection analysis - alarm count percentage by inspection item"),
        "tr_aiInspectionAnalysisAlarmCountRanking":
            MessageLookupByLibrary.simpleMessage(
                "The following is AI inspection analysis-alarm number ranking data"),
        "tr_aiInspectionAnalysisAlarmRate":
            MessageLookupByLibrary.simpleMessage(
                "AI inspection analysis - alarm rate data"),
        "tr_aiInspectionAnalysisAlarmRateRanking":
            MessageLookupByLibrary.simpleMessage("Alarm rate ranking"),
        "tr_aiInspectionAnalysisAlarmRateRankingByInspectionItem":
            MessageLookupByLibrary.simpleMessage(
                "AI inspection analysis - alarm rate ranking by inspection item"),
        "tr_aiInspectionAnalysisInspectionCount":
            MessageLookupByLibrary.simpleMessage(
                "AI inspection analysis - inspection count data"),
        "tr_aiInspectionAnalysisInspectionItemDistributionByTime":
            MessageLookupByLibrary.simpleMessage(
                "AI inspection analysis - inspection item distribution by time"),
        "tr_aiInspectionPlan":
            MessageLookupByLibrary.simpleMessage("AI inspection plan"),
        "tr_aiInspectionRecordDetail":
            MessageLookupByLibrary.simpleMessage("AI inspection record detail"),
        "tr_aiInspectionRecords":
            MessageLookupByLibrary.simpleMessage("AI inspection records"),
        "tr_aiInspectionSource":
            MessageLookupByLibrary.simpleMessage("AI check"),
        "tr_aiPlatformAlarm":
            MessageLookupByLibrary.simpleMessage("AI platform alarm"),
        "tr_alarmAIEdit": MessageLookupByLibrary.simpleMessage("AI alarm edit"),
        "tr_alarmCallCount":
            MessageLookupByLibrary.simpleMessage("Alarm call count"),
        "tr_alarmCount": MessageLookupByLibrary.simpleMessage("Alarm count"),
        "tr_alarmCountRanking":
            MessageLookupByLibrary.simpleMessage("Alarm count ranking"),
        "tr_alarmCountRankingData":
            MessageLookupByLibrary.simpleMessage("Alarm count ranking data"),
        "tr_alarmCountRatio":
            MessageLookupByLibrary.simpleMessage("Alarm count ratio"),
        "tr_alarmDescription":
            MessageLookupByLibrary.simpleMessage("Alarm Description"),
        "tr_alarmDetail": MessageLookupByLibrary.simpleMessage("Alarm details"),
        "tr_alarmDeviceEdit":
            MessageLookupByLibrary.simpleMessage("Device alarm edit"),
        "tr_alarmGeneratedCount":
            MessageLookupByLibrary.simpleMessage("Alarm generated count"),
        "tr_alarmParameters":
            MessageLookupByLibrary.simpleMessage("Alarm parameters"),
        "tr_alarmPeriod": MessageLookupByLibrary.simpleMessage("Alarm period"),
        "tr_alarmRate": MessageLookupByLibrary.simpleMessage("Alarm rate"),
        "tr_alarmRateRanking":
            MessageLookupByLibrary.simpleMessage("Alarm rate ranking"),
        "tr_alarmRuleConfiguration":
            MessageLookupByLibrary.simpleMessage("Alarm rule configuration"),
        "tr_alarmSetting":
            MessageLookupByLibrary.simpleMessage("Alarm setting"),
        "tr_alarmSwitch":
            MessageLookupByLibrary.simpleMessage("Message reception"),
        "tr_alarmSwitchTip": MessageLookupByLibrary.simpleMessage(
            "After closing, the mobile phone will not be able to receive alarm push messages."),
        "tr_alarmTime": MessageLookupByLibrary.simpleMessage("Alarm Time"),
        "tr_alarmTimeInterval":
            MessageLookupByLibrary.simpleMessage("Alarm time interval"),
        "tr_alarmType": MessageLookupByLibrary.simpleMessage("Alarm type"),
        "tr_alarmVoice": MessageLookupByLibrary.simpleMessage("Alarm voice"),
        "tr_alertLine": MessageLookupByLibrary.simpleMessage("Alert line"),
        "tr_alertSound": MessageLookupByLibrary.simpleMessage("Alert sound"),
        "tr_alertZone": MessageLookupByLibrary.simpleMessage("Alert zone"),
        "tr_algorithParamsDoNotPairedYet": MessageLookupByLibrary.simpleMessage(
            "Algorithm parameters are not configured"),
        "tr_algorithParamsHavePaired": MessageLookupByLibrary.simpleMessage(
            "Algorithm parameters are configured"),
        "tr_algorithmAndDevice":
            MessageLookupByLibrary.simpleMessage("Algorithm and device"),
        "tr_algorithmAnomaly":
            MessageLookupByLibrary.simpleMessage("Algorithm anomaly"),
        "tr_algorithmAuthorization":
            MessageLookupByLibrary.simpleMessage("Algorithm Authorization"),
        "tr_algorithmConfigDescription": MessageLookupByLibrary.simpleMessage(
            "Related descriptions about algorithm configuration"),
        "tr_algorithmElectricBikeDetection":
            MessageLookupByLibrary.simpleMessage("Electric vehicle inspection"),
        "tr_algorithmFaceMaskDetection":
            MessageLookupByLibrary.simpleMessage("Mask detection"),
        "tr_algorithmInvocationStatistics":
            MessageLookupByLibrary.simpleMessage(
                "Algorithm invocation statistics"),
        "tr_algorithmList":
            MessageLookupByLibrary.simpleMessage("Algorithm list"),
        "tr_algorithmName":
            MessageLookupByLibrary.simpleMessage("Algorithm name"),
        "tr_algorithmStatistics":
            MessageLookupByLibrary.simpleMessage("Algorithm statistics"),
        "tr_allAlarms": MessageLookupByLibrary.simpleMessage("All alarms"),
        "tr_allAlgorithms":
            MessageLookupByLibrary.simpleMessage("All algorithms"),
        "tr_allAssessmentItems":
            MessageLookupByLibrary.simpleMessage("All assessment items"),
        "tr_allConditions":
            MessageLookupByLibrary.simpleMessage("All conditions"),
        "tr_allDay": MessageLookupByLibrary.simpleMessage("All Day"),
        "tr_allDayAlarm": MessageLookupByLibrary.simpleMessage("All-day alarm"),
        "tr_allDayRecording":
            MessageLookupByLibrary.simpleMessage("All-day recording"),
        "tr_allDayRunning":
            MessageLookupByLibrary.simpleMessage("All day running"),
        "tr_allDevices": MessageLookupByLibrary.simpleMessage("All devices"),
        "tr_allDevicesAbnormal":
            MessageLookupByLibrary.simpleMessage("All devices abnormal"),
        "tr_allDevicesOfflineUnderStore": MessageLookupByLibrary.simpleMessage(
            "All devices offline under store"),
        "tr_allDevicesTotal": m9,
        "tr_allFacesUnderDefaultAccount": MessageLookupByLibrary.simpleMessage(
            "All faces under default account"),
        "tr_allInspectors":
            MessageLookupByLibrary.simpleMessage("All inspectors"),
        "tr_allNode": MessageLookupByLibrary.simpleMessage("All nodes"),
        "tr_allPendingInspectionImagesProcessed":
            MessageLookupByLibrary.simpleMessage(
                "All pending inspection images have been processed"),
        "tr_allPerson": MessageLookupByLibrary.simpleMessage("All personnel"),
        "tr_allSources": MessageLookupByLibrary.simpleMessage("All sources"),
        "tr_allStatus": MessageLookupByLibrary.simpleMessage("All status"),
        "tr_allStores": MessageLookupByLibrary.simpleMessage("All stores"),
        "tr_alreadyTheFirstOne":
            MessageLookupByLibrary.simpleMessage("Already the first one"),
        "tr_alreadyTheLastOne":
            MessageLookupByLibrary.simpleMessage("Already the last one"),
        "tr_amapTitle": MessageLookupByLibrary.simpleMessage("Emap"),
        "tr_analysisError":
            MessageLookupByLibrary.simpleMessage("Parse exception"),
        "tr_analysisRight":
            MessageLookupByLibrary.simpleMessage("Normal analysis"),
        "tr_analysisStop":
            MessageLookupByLibrary.simpleMessage("Analysis stopped"),
        "tr_anyCondition":
            MessageLookupByLibrary.simpleMessage("Any condition"),
        "tr_apiRequestFailed":
            MessageLookupByLibrary.simpleMessage("API request failed"),
        "tr_appAccessAuthority":
            MessageLookupByLibrary.simpleMessage("Permission"),
        "tr_appAlgorithmCenter":
            MessageLookupByLibrary.simpleMessage("Algorithm"),
        "tr_appAlgorithmConfig":
            MessageLookupByLibrary.simpleMessage("Set algorithm"),
        "tr_appAlgorithmList":
            MessageLookupByLibrary.simpleMessage("Cloud algorithm"),
        "tr_appDealCenter": MessageLookupByLibrary.simpleMessage("To Do items"),
        "tr_appDeviceAttribute":
            MessageLookupByLibrary.simpleMessage("Device attribute"),
        "tr_appDeviceSideAlgorithm":
            MessageLookupByLibrary.simpleMessage("Device-side algorithm"),
        "tr_appDevicesTree":
            MessageLookupByLibrary.simpleMessage("Device management"),
        "tr_appEnterpriseManagement":
            MessageLookupByLibrary.simpleMessage("Enterprise"),
        "tr_appFacialManagement":
            MessageLookupByLibrary.simpleMessage("Face database"),
        "tr_appHumanoidLibrary":
            MessageLookupByLibrary.simpleMessage("Humanoid library"),
        "tr_appPatrolIndex": MessageLookupByLibrary.simpleMessage("Inspection"),
        "tr_appPatrolIndexFlow":
            MessageLookupByLibrary.simpleMessage("Inspection process"),
        "tr_appSmartCloudStore":
            MessageLookupByLibrary.simpleMessage("Cloud store"),
        "tr_appSmartStore": MessageLookupByLibrary.simpleMessage("Store list"),
        "tr_appVideoCloudBase":
            MessageLookupByLibrary.simpleMessage("Video base"),
        "tr_appVideoRecord":
            MessageLookupByLibrary.simpleMessage("Video recording"),
        "tr_appVideoSurveillance":
            MessageLookupByLibrary.simpleMessage("Video"),
        "tr_applicationClassification":
            MessageLookupByLibrary.simpleMessage("Application classification"),
        "tr_areYouSureToDeleteThisPlan": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete this plan?"),
        "tr_areYouSureToDeleteThisTemplate":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want to delete this template?"),
        "tr_area": MessageLookupByLibrary.simpleMessage("Area"),
        "tr_areaName": MessageLookupByLibrary.simpleMessage("Area name"),
        "tr_areaTrafficStatistics":
            MessageLookupByLibrary.simpleMessage("Area traffic statistics"),
        "tr_arithmeticChannelNotEnoughTip": m10,
        "tr_arithmeticOverTip": MessageLookupByLibrary.simpleMessage(
            "The aforementioned equipment and algorithms overlap with those in other plans. You may remove them from the current plan or directly delete them from the original plan."),
        "tr_arrivalDepartureTime":
            MessageLookupByLibrary.simpleMessage("Arrival departure time"),
        "tr_assessmentCategory":
            MessageLookupByLibrary.simpleMessage("Assessment Category"),
        "tr_assessmentCategoryDetail":
            MessageLookupByLibrary.simpleMessage("Evaluation Category Detail"),
        "tr_assessmentItem":
            MessageLookupByLibrary.simpleMessage("Assessment Item"),
        "tr_assessmentReferenceImage":
            MessageLookupByLibrary.simpleMessage("Assessment reference image"),
        "tr_assignedPersonnel":
            MessageLookupByLibrary.simpleMessage("Assigned Personnel"),
        "tr_assignedTo": MessageLookupByLibrary.simpleMessage("Assigned to"),
        "tr_assignedToYou":
            MessageLookupByLibrary.simpleMessage(" assigned to you "),
        "tr_associateHumanShape":
            MessageLookupByLibrary.simpleMessage("Associate human shape"),
        "tr_associatedAlgorithm":
            MessageLookupByLibrary.simpleMessage("Correlation algorithm"),
        "tr_associatedFace":
            MessageLookupByLibrary.simpleMessage("Associate Face"),
        "tr_associatedTemplate":
            MessageLookupByLibrary.simpleMessage("Association template"),
        "tr_asyncStoreLatLng": MessageLookupByLibrary.simpleMessage(
            "Store latitude and longitude are set to the device"),
        "tr_atLeastOneEvaluationClassIsRequired":
            MessageLookupByLibrary.simpleMessage(
                "At least one evaluation class is required"),
        "tr_audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "tr_audioVideoEncodingConfig":
            MessageLookupByLibrary.simpleMessage("Audio-video encoding config"),
        "tr_authorization_statistics":
            MessageLookupByLibrary.simpleMessage("Authorization Statistics"),
        "tr_authorizeIntercept":
            MessageLookupByLibrary.simpleMessage("Authorized interception"),
        "tr_authorizedNumChannels":
            MessageLookupByLibrary.simpleMessage("Authorized Num Channels"),
        "tr_autoCertification":
            MessageLookupByLibrary.simpleMessage("Automatic record retention"),
        "tr_autoEvidenceTime": MessageLookupByLibrary.simpleMessage(
            "Automatic retention of recording time"),
        "tr_autoRestart": MessageLookupByLibrary.simpleMessage("auto restart"),
        "tr_autoSubmitEvent": MessageLookupByLibrary.simpleMessage(
            "Event automatically submitted"),
        "tr_automaticallyInitiateRectificationEvent":
            MessageLookupByLibrary.simpleMessage(
                "Automatically initiate rectification event"),
        "tr_averageStayDuration":
            MessageLookupByLibrary.simpleMessage("Average stay duration"),
        "tr_bandwidthCap":
            MessageLookupByLibrary.simpleMessage("Bandwidth cap"),
        "tr_bandwidthStatistics":
            MessageLookupByLibrary.simpleMessage("Bandwidth statistics"),
        "tr_batchAddChannelTips": MessageLookupByLibrary.simpleMessage(
            "The passwords of the devices selected for batch adding must be consistent, otherwise the addition will fail. Devices with inconsistent passwords should be added individually."),
        "tr_batchEdit": MessageLookupByLibrary.simpleMessage("Batch edit"),
        "tr_batchModify":
            MessageLookupByLibrary.simpleMessage("Batch modification"),
        "tr_batchSetting":
            MessageLookupByLibrary.simpleMessage("Batch setting"),
        "tr_batchSettingChannel":
            MessageLookupByLibrary.simpleMessage("Batch setting channel"),
        "tr_bcloud_saas_activity":
            MessageLookupByLibrary.simpleMessage("BcloudSaaS package discount"),
        "tr_bcloud_saas_shopping_tip": MessageLookupByLibrary.simpleMessage(
            "BcloudSaaS package discount, <NAME_EMAIL> for buy! "),
        "tr_beforeAfterBusinessHours":
            MessageLookupByLibrary.simpleMessage("Before after business hours"),
        "tr_beforeAfterBusinessHoursConsideredStaff": m11,
        "tr_beforeRectification":
            MessageLookupByLibrary.simpleMessage("Before Rectification"),
        "tr_beginSceneInspection":
            MessageLookupByLibrary.simpleMessage("Begin scene inspection"),
        "tr_belongDevice":
            MessageLookupByLibrary.simpleMessage("Device to which it belongs"),
        "tr_belongNode":
            MessageLookupByLibrary.simpleMessage("Node to which it belongs"),
        "tr_belongStore":
            MessageLookupByLibrary.simpleMessage("Store to which it belongs"),
        "tr_belowIsAiInspectionAnalysis": MessageLookupByLibrary.simpleMessage(
            "The following is the AI inspection analysis"),
        "tr_bindTag": MessageLookupByLibrary.simpleMessage("Bind tag"),
        "tr_bitRateControlFPS":
            MessageLookupByLibrary.simpleMessage("Bit rate control (FPS)"),
        "tr_bitRateValue":
            MessageLookupByLibrary.simpleMessage("Bit rate value"),
        "tr_bitStream": MessageLookupByLibrary.simpleMessage("Bit Stream"),
        "tr_blur": MessageLookupByLibrary.simpleMessage("Blur"),
        "tr_broadcast": MessageLookupByLibrary.simpleMessage("Broadcast"),
        "tr_broadcastException":
            MessageLookupByLibrary.simpleMessage("Broadcast exception"),
        "tr_broadcasting": MessageLookupByLibrary.simpleMessage("Broadcasting"),
        "tr_btConnectNetwork": MessageLookupByLibrary.simpleMessage(
            "Device connected to wireless network"),
        "tr_btConnectNetworkConfiguration": MessageLookupByLibrary.simpleMessage(
            "The connection process may take 1-2 minutes, please wait for a moment."),
        "tr_btConnectNetworkConfigurationTip":
            MessageLookupByLibrary.simpleMessage(
                "Keep routers, mobile phones and devices as close as possible..."),
        "tr_btEquipment":
            MessageLookupByLibrary.simpleMessage("Bluetooth devices"),
        "tr_btnCreateStore":
            MessageLookupByLibrary.simpleMessage("Set up a store"),
        "tr_businessHours":
            MessageLookupByLibrary.simpleMessage("Business hours"),
        "tr_buy_connect_email_tip": MessageLookupByLibrary.simpleMessage(
            "<NAME_EMAIL> for renewal!"),
        "tr_buy_saas": MessageLookupByLibrary.simpleMessage("Buy Now"),
        "tr_callCount": MessageLookupByLibrary.simpleMessage("Call count"),
        "tr_callStatistics":
            MessageLookupByLibrary.simpleMessage("Call statistics"),
        "tr_canNotEditAreaTip": MessageLookupByLibrary.simpleMessage(
            "The equipment is malfunctioning, unable to save the region."),
        "tr_canOnlyDrawOneArea":
            MessageLookupByLibrary.simpleMessage("Can only draw one area"),
        "tr_cancelAllSelections":
            MessageLookupByLibrary.simpleMessage("Deselect All"),
        "tr_cancelLogin": MessageLookupByLibrary.simpleMessage("Cancel login"),
        "tr_cancelTaskTip": MessageLookupByLibrary.simpleMessage(
            "This action will cancel the ongoing download (requiring it to start from the beginning). Do you wish to continue?"),
        "tr_cancellationAccountAgreement": MessageLookupByLibrary.simpleMessage(
            "Account cancellation agreement"),
        "tr_cannotLaterStopTime": MessageLookupByLibrary.simpleMessage(
            "The start time cannot be later than the end time"),
        "tr_captureDate": MessageLookupByLibrary.simpleMessage("Capture Date"),
        "tr_capturePlan": MessageLookupByLibrary.simpleMessage("Capture Plan"),
        "tr_captureRecord":
            MessageLookupByLibrary.simpleMessage("Capture record"),
        "tr_ccTo": MessageLookupByLibrary.simpleMessage("Copy to "),
        "tr_centralServer":
            MessageLookupByLibrary.simpleMessage("Central Server"),
        "tr_centralServerPlatform":
            MessageLookupByLibrary.simpleMessage("Central Server (Platform)"),
        "tr_chainRatio": MessageLookupByLibrary.simpleMessage("MoM"),
        "tr_changeLanguage":
            MessageLookupByLibrary.simpleMessage("Language switching"),
        "tr_changeNetAddDeviceTip": MessageLookupByLibrary.simpleMessage(
            "Can\'t find the QR code? Please use the local area network to add the device"),
        "tr_channelAdd": MessageLookupByLibrary.simpleMessage("Channel add"),
        "tr_channelCount": m12,
        "tr_channelDetails":
            MessageLookupByLibrary.simpleMessage("Channel Details"),
        "tr_channelFour": MessageLookupByLibrary.simpleMessage("Channel 4"),
        "tr_channelInfo":
            MessageLookupByLibrary.simpleMessage("Channel information"),
        "tr_channelLabel":
            MessageLookupByLibrary.simpleMessage("Channel Label"),
        "tr_channelManagement":
            MessageLookupByLibrary.simpleMessage("Channel Management"),
        "tr_channelMismatch":
            MessageLookupByLibrary.simpleMessage("Channel mismatch"),
        "tr_channelName": MessageLookupByLibrary.simpleMessage("Channel Name"),
        "tr_channelNum":
            MessageLookupByLibrary.simpleMessage("Number of channels"),
        "tr_channelNumInfo": m13,
        "tr_channelNumbers":
            MessageLookupByLibrary.simpleMessage("Number of channels"),
        "tr_channelOccupied":
            MessageLookupByLibrary.simpleMessage("Channel occupied"),
        "tr_channelOffline":
            MessageLookupByLibrary.simpleMessage("Channel offline"),
        "tr_channelOne": MessageLookupByLibrary.simpleMessage("Channel 1"),
        "tr_channelParams":
            MessageLookupByLibrary.simpleMessage("Channel parameter"),
        "tr_channelResources":
            MessageLookupByLibrary.simpleMessage("Channel resources"),
        "tr_channelSIPID":
            MessageLookupByLibrary.simpleMessage("SIP ID of the platform"),
        "tr_channelSN": MessageLookupByLibrary.simpleMessage("Channel SN"),
        "tr_channelSNExistSameCodeTips": m14,
        "tr_channelStartStop":
            MessageLookupByLibrary.simpleMessage("Start/Stop"),
        "tr_channelStartStopTip": MessageLookupByLibrary.simpleMessage(
            "You can choose to disable the channel. After deactivation, the device will not be displayed in the device list."),
        "tr_channelStartStopTitle":
            MessageLookupByLibrary.simpleMessage("Channel start and stop"),
        "tr_channelStatus":
            MessageLookupByLibrary.simpleMessage("Channel status"),
        "tr_channelStatusStart": MessageLookupByLibrary.simpleMessage("Used"),
        "tr_channelStatusStop":
            MessageLookupByLibrary.simpleMessage("Terminated"),
        "tr_channelThree": MessageLookupByLibrary.simpleMessage("Channel 3"),
        "tr_channelTitle":
            MessageLookupByLibrary.simpleMessage("Channel title"),
        "tr_channelTwo": MessageLookupByLibrary.simpleMessage("Channel 2"),
        "tr_channelUnreasonable": MessageLookupByLibrary.simpleMessage(
            "The number of input channels is unreasonable, please re-enter"),
        "tr_channelsInsufficient": MessageLookupByLibrary.simpleMessage(
            "Your device channel authorization number is insufficient"),
        "tr_channelsInsufficientRet": MessageLookupByLibrary.simpleMessage(
            "The remaining number of channels in the package is insufficient, please contact the after-sales staff to expand."),
        "tr_checkAccountLogin":
            MessageLookupByLibrary.simpleMessage("Second verification login"),
        "tr_checkEmailNotValid":
            MessageLookupByLibrary.simpleMessage("Incorrect email format"),
        "tr_checkEnable": MessageLookupByLibrary.simpleMessage("Is it enabled"),
        "tr_checkFrequency": MessageLookupByLibrary.simpleMessage(
            "The number of concurrent viewers is unreasonable"),
        "tr_checkPasswordErrorHint": MessageLookupByLibrary.simpleMessage(
            "The device password is incorrect, please verify the password"),
        "tr_children": MessageLookupByLibrary.simpleMessage("Children"),
        "tr_china": MessageLookupByLibrary.simpleMessage("China"),
        "tr_choice": MessageLookupByLibrary.simpleMessage("Go to select"),
        "tr_chooseAtLeastOneAlgorithm": MessageLookupByLibrary.simpleMessage(
            "Choose at least one algorithm"),
        "tr_chooseAtMostNameAlgorithm": m15,
        "tr_chooseAtMostNameDepartment": m16,
        "tr_chooseAtMostNamePerson": m17,
        "tr_chooseAvailableWiFi": MessageLookupByLibrary.simpleMessage(
            "The WiFi connection is not smooth. Please reselect an available WiFi."),
        "tr_chooseChannels":
            MessageLookupByLibrary.simpleMessage("Please select channels"),
        "tr_chooseCreator":
            MessageLookupByLibrary.simpleMessage("Choose creator"),
        "tr_chooseDeptMaxCount": m18,
        "tr_chooseDeviceMaxCount": m19,
        "tr_chooseDeviceType":
            MessageLookupByLibrary.simpleMessage("Select device type"),
        "tr_chooseExpandGridMode": MessageLookupByLibrary.simpleMessage(
            "Device has switched to grid display"),
        "tr_chooseExpandListMode": MessageLookupByLibrary.simpleMessage(
            "Device has switched list display"),
        "tr_chooseFirmwareFile": MessageLookupByLibrary.simpleMessage(
            "Please select the firmware upgrade file"),
        "tr_chooseInitiatePerson":
            MessageLookupByLibrary.simpleMessage("Choose a sponsor"),
        "tr_chooseInspectionDate": MessageLookupByLibrary.simpleMessage(
            "Please select the inspection date"),
        "tr_chooseInspectionPeriodDate": m20,
        "tr_chooseInspector":
            MessageLookupByLibrary.simpleMessage("Select Inspector"),
        "tr_chooseMaxCount": m21,
        "tr_chooseMember":
            MessageLookupByLibrary.simpleMessage("Select a member"),
        "tr_chooseStoreMaxCount": m22,
        "tr_chooseSyncChannels": MessageLookupByLibrary.simpleMessage(
            "Please select the channel that needs synchronization authorization"),
        "tr_chooseTagsCategory":
            MessageLookupByLibrary.simpleMessage("Filter tag categories"),
        "tr_chooseVisibleRangeDept":
            MessageLookupByLibrary.simpleMessage("Select Visible Departments"),
        "tr_choseChannelCount":
            MessageLookupByLibrary.simpleMessage("Selected Channel"),
        "tr_choseDepartmentResource":
            MessageLookupByLibrary.simpleMessage("Selected department"),
        "tr_choseDeviceCount":
            MessageLookupByLibrary.simpleMessage("Selected resources"),
        "tr_choseDeviceResource":
            MessageLookupByLibrary.simpleMessage("Selected resources: "),
        "tr_clear": MessageLookupByLibrary.simpleMessage("Clear"),
        "tr_clearAllMessage":
            MessageLookupByLibrary.simpleMessage("Clear all messages"),
        "tr_clearAllMessageTip": MessageLookupByLibrary.simpleMessage(
            "Please enter the login password of this account to verify and delete"),
        "tr_clearMessageContent": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete all alarm messages?"),
        "tr_clickEditText":
            MessageLookupByLibrary.simpleMessage("Click to edit text content"),
        "tr_clickToEditStoreFloorPlan": MessageLookupByLibrary.simpleMessage(
            "Click to edit store floor plan"),
        "tr_clientNotSupportedUseChrome": MessageLookupByLibrary.simpleMessage(
            "Client not supported, please use Chrome"),
        "tr_cloudStorageAuthorization":
            MessageLookupByLibrary.simpleMessage("Cloud Storage Authorization"),
        "tr_cloudTraffic":
            MessageLookupByLibrary.simpleMessage("Cloud traffic"),
        "tr_collapse": MessageLookupByLibrary.simpleMessage("Collapse"),
        "tr_collectDevices":
            MessageLookupByLibrary.simpleMessage("Collection devices"),
        "tr_colorCast": MessageLookupByLibrary.simpleMessage("Color cast"),
        "tr_commitSceneInspectionResult": MessageLookupByLibrary.simpleMessage(
            "Commit scene inspection result"),
        "tr_commonApplications":
            MessageLookupByLibrary.simpleMessage("Common Functions"),
        "tr_commonBingSuccess":
            MessageLookupByLibrary.simpleMessage("Binding successful"),
        "tr_commonEmail": MessageLookupByLibrary.simpleMessage("Email"),
        "tr_commonModify": MessageLookupByLibrary.simpleMessage("Modify"),
        "tr_commonPhone": MessageLookupByLibrary.simpleMessage("Phone number"),
        "tr_common_GoSetting":
            MessageLookupByLibrary.simpleMessage("Go to enable"),
        "tr_common_close": MessageLookupByLibrary.simpleMessage("Close"),
        "tr_companyList": MessageLookupByLibrary.simpleMessage("Company List"),
        "tr_completeAcceptance":
            MessageLookupByLibrary.simpleMessage("Complete Acceptance"),
        "tr_completeAcceptanceSort":
            MessageLookupByLibrary.simpleMessage("Complete Acceptance Sort"),
        "tr_completeAcceptanceTimes":
            MessageLookupByLibrary.simpleMessage("Complete Acceptance Times"),
        "tr_completeRectification":
            MessageLookupByLibrary.simpleMessage("Complete Rectification"),
        "tr_completeRectificationSort":
            MessageLookupByLibrary.simpleMessage("Complete Rectification Sort"),
        "tr_completeRectificationTimes": MessageLookupByLibrary.simpleMessage(
            "Complete Rectification Times"),
        "tr_completed": MessageLookupByLibrary.simpleMessage("Completed"),
        "tr_completionRate":
            MessageLookupByLibrary.simpleMessage("Completion Rate"),
        "tr_completionRateExplain": MessageLookupByLibrary.simpleMessage(
            "The task completion rate is only valid for task inspections."),
        "tr_completionTime":
            MessageLookupByLibrary.simpleMessage("Completion time"),
        "tr_conditionsCanBeCombined":
            MessageLookupByLibrary.simpleMessage("Conditions can be combined"),
        "tr_confidenceLevel":
            MessageLookupByLibrary.simpleMessage("Confidence"),
        "tr_confidence_tip": MessageLookupByLibrary.simpleMessage(
            "The lower the confidence level, the lower the accuracy of recognition and the more results being recognized.\nThe higher the confidence level, the fewer results being recognized, but the accuracy of recognition is higher."),
        "tr_confidence_tip_1": MessageLookupByLibrary.simpleMessage(
            "\t1.\tThe smaller the value, the stricter the distinction between individuals; the current recommended threshold is 6.8%.\n2.\tFrequent threshold adjustments within a day can affect deduplication effectiveness under different thresholds.\n3.\tIt is not recommended to adjust the threshold frequently. If adjustment is necessary, try to do it only once a day."),
        "tr_configuration":
            MessageLookupByLibrary.simpleMessage("Configuration"),
        "tr_configurationNotSavedDoYouWantToReturn":
            MessageLookupByLibrary.simpleMessage(
                "Configuration not saved do you want to return?"),
        "tr_configureAlgorithm":
            MessageLookupByLibrary.simpleMessage("Algorithm"),
        "tr_confirmChangeLanguage":
            MessageLookupByLibrary.simpleMessage("Are you sure to switch to:"),
        "tr_confirmClosingTheRecordingPlan":
            MessageLookupByLibrary.simpleMessage(
                "Confirm disabling Recording Plan."),
        "tr_confirmDeletion":
            MessageLookupByLibrary.simpleMessage("Are you sure to delete?"),
        "tr_confirmRemove":
            MessageLookupByLibrary.simpleMessage("Confirm remove"),
        "tr_connecting": MessageLookupByLibrary.simpleMessage("Connecting"),
        "tr_consideredAsStaff":
            MessageLookupByLibrary.simpleMessage("Considered as staff"),
        "tr_continue": MessageLookupByLibrary.simpleMessage("Continue"),
        "tr_continueInspection":
            MessageLookupByLibrary.simpleMessage("Continue inspection"),
        "tr_continueInspectionOutsideStoreRange":
            MessageLookupByLibrary.simpleMessage(
                "Continue inspection outside storeRange"),
        "tr_continuousDetection":
            MessageLookupByLibrary.simpleMessage("Continuous detection"),
        "tr_copySuccess":
            MessageLookupByLibrary.simpleMessage("Copy successful"),
        "tr_copyTo": MessageLookupByLibrary.simpleMessage("Copy to "),
        "tr_cover_record_plan_tip": MessageLookupByLibrary.simpleMessage(
            "Detected that the selected device already has a recording plan. Do you want to overwrite it?"),
        "tr_coverage": MessageLookupByLibrary.simpleMessage("Coverage"),
        "tr_coverageRate":
            MessageLookupByLibrary.simpleMessage("Coverage rate"),
        "tr_createAIInspectionPlan": MessageLookupByLibrary.simpleMessage(
            "Create an AI inspection plan"),
        "tr_createActivityTime":
            MessageLookupByLibrary.simpleMessage("Create activity time"),
        "tr_createCloudStorageRecordingPlan":
            MessageLookupByLibrary.simpleMessage(
                "Create Cloud Storage Recording Plan"),
        "tr_createEvaluationClass":
            MessageLookupByLibrary.simpleMessage("Create Evaluation Class"),
        "tr_createEvaluationItem":
            MessageLookupByLibrary.simpleMessage("Create Evaluation Item"),
        "tr_createEvent": MessageLookupByLibrary.simpleMessage("Create event"),
        "tr_createImageInspectionPlan": MessageLookupByLibrary.simpleMessage(
            "Create image inspection plan"),
        "tr_createInspectionPlan":
            MessageLookupByLibrary.simpleMessage("Create inspection plan"),
        "tr_createOnsiteInspection":
            MessageLookupByLibrary.simpleMessage("Create onsite inspection"),
        "tr_createStore":
            MessageLookupByLibrary.simpleMessage("Create a store"),
        "tr_createStoreSuccessTip": MessageLookupByLibrary.simpleMessage(
            "Successfully created a store"),
        "tr_createTemplate":
            MessageLookupByLibrary.simpleMessage("Create Template"),
        "tr_creationTime":
            MessageLookupByLibrary.simpleMessage("Creation time"),
        "tr_creator": MessageLookupByLibrary.simpleMessage("Creator"),
        "tr_crop": MessageLookupByLibrary.simpleMessage("Crop"),
        "tr_currentAccount":
            MessageLookupByLibrary.simpleMessage("Current account:"),
        "tr_currentFlowPackage":
            MessageLookupByLibrary.simpleMessage("Current Data Package"),
        "tr_currentLocation":
            MessageLookupByLibrary.simpleMessage("Current location"),
        "tr_currentTagHasNoDevice": MessageLookupByLibrary.simpleMessage(
            "There are no devices under the current label"),
        "tr_customContent":
            MessageLookupByLibrary.simpleMessage("Custom Content"),
        "tr_customTemplate":
            MessageLookupByLibrary.simpleMessage("Custom template"),
        "tr_customTime": MessageLookupByLibrary.simpleMessage("Custom time"),
        "tr_customTimePeriod":
            MessageLookupByLibrary.simpleMessage("Custom time period"),
        "tr_customTimeSetting":
            MessageLookupByLibrary.simpleMessage("Custom time setting"),
        "tr_customerGroupEntryInterval": MessageLookupByLibrary.simpleMessage(
            "Customer group entry interval"),
        "tr_customerSegmentationAnalysis":
            MessageLookupByLibrary.simpleMessage("Customer group analysis"),
        "tr_customerSegmentationAnalysisTip": MessageLookupByLibrary.simpleMessage(
            "Customer segmentation analysis: Based on the data statistics after deduplication of customers entering the store\n1. Age of customers entering the store: Data of each age group/Total data of customers entering the store after deduplication\n2. Gender of customers entering the store: Number of men/Number of women in the statistical period after deduplication/Total number of customers entering the store after deduplication"),
        "tr_dailyTotalStayDuration":
            MessageLookupByLibrary.simpleMessage("Daily total stay duration"),
        "tr_dasProtocol": MessageLookupByLibrary.simpleMessage("DAS protocol"),
        "tr_dataDashboard":
            MessageLookupByLibrary.simpleMessage("Data dashboard"),
        "tr_dataDetails": MessageLookupByLibrary.simpleMessage("Data details"),
        "tr_dataNotSavedContinueReturnWillClearData":
            MessageLookupByLibrary.simpleMessage(
                "The data has not been saved yet. Continuing to return will clear the data. Are you sure you want to return?"),
        "tr_dataOverview":
            MessageLookupByLibrary.simpleMessage("Data Overview"),
        "tr_dataSourceFetchFailed":
            MessageLookupByLibrary.simpleMessage("Failed to fetch data source"),
        "tr_dateChoose": MessageLookupByLibrary.simpleMessage("Date choose"),
        "tr_datePicker": MessageLookupByLibrary.simpleMessage("Select date"),
        "tr_day": MessageLookupByLibrary.simpleMessage("Date"),
        "tr_dayMark": MessageLookupByLibrary.simpleMessage(""),
        "tr_dayNo": MessageLookupByLibrary.simpleMessage("No."),
        "tr_days": MessageLookupByLibrary.simpleMessage("Days"),
        "tr_deactivate": MessageLookupByLibrary.simpleMessage("Deactivate"),
        "tr_deadlineTime": MessageLookupByLibrary.simpleMessage("Deadline"),
        "tr_deduplication":
            MessageLookupByLibrary.simpleMessage("Deduplication"),
        "tr_deduplicationAfter":
            MessageLookupByLibrary.simpleMessage("Deduplication after"),
        "tr_deduplicationTip": MessageLookupByLibrary.simpleMessage(
            "Multiple passenger flow devices under the store will automatically merge and deduplicate, and if staff deduplication is enabled, it will be based on the store deduplication!"),
        "tr_deepSeekStatistics":
            MessageLookupByLibrary.simpleMessage("Deepseek calling statistics"),
        "tr_deepSeekStatisticsTip": MessageLookupByLibrary.simpleMessage(
            "Please go to the inspection analysis or event analysis page to view specific functions"),
        "tr_deepseekSuffiex": MessageLookupByLibrary.simpleMessage(
            "What optimization suggestions do you have for the above inspection analysis data? Please respond in report format and use English."),
        "tr_deepseekSuffiex2": MessageLookupByLibrary.simpleMessage(
            "What optimization suggestions do you have for the above event analysis data? Please respond in report format and use English."),
        "tr_default": MessageLookupByLibrary.simpleMessage("Default"),
        "tr_defaultAllDayRunning": MessageLookupByLibrary.simpleMessage(
            "Set to run continuously by default."),
        "tr_delChooseUserInfoTip": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected user?"),
        "tr_delDeviceTags": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected tag information?"),
        "tr_delDeviceTagsCategory": MessageLookupByLibrary.simpleMessage(
            "After a tag category is deleted, the category and its tags will be deleted simultaneously."),
        "tr_delMoreAiAlarmMsg": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected AI alarm messages？"),
        "tr_delMoreDeviceAlarmMsg": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected device alarm messages？"),
        "tr_delMoreStore": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected store?"),
        "tr_delMoreUserPerson": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected users?"),
        "tr_delOneAiAlarmMsg": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete one selected AI alarm message？"),
        "tr_delOneDeviceAlarmMsg": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete one selected device alarm message？"),
        "tr_delOneDeviceGBCascade": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete this GB cascade?"),
        "tr_delOneStore": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected store?？"),
        "tr_delSelectedAiInspection": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected inspection plan?"),
        "tr_delTagDevices": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to remove the selected device under the tag?"),
        "tr_delTheUserPerson": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete this user？"),
        "tr_deleteFailed":
            MessageLookupByLibrary.simpleMessage("Delete failed"),
        "tr_deleteFailedDownloadingTask": MessageLookupByLibrary.simpleMessage(
            "Delete failed downloading task"),
        "tr_deleteFromCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Delete from current plan"),
        "tr_deleteFromOriginalPlan":
            MessageLookupByLibrary.simpleMessage("Delete from original plan"),
        "tr_deny": MessageLookupByLibrary.simpleMessage("Deny"),
        "tr_departmentStructure":
            MessageLookupByLibrary.simpleMessage("Organization"),
        "tr_description": MessageLookupByLibrary.simpleMessage("Describe"),
        "tr_detail": MessageLookupByLibrary.simpleMessage("Detail"),
        "tr_detailInspection":
            MessageLookupByLibrary.simpleMessage("Inspection details"),
        "tr_detailUuid": MessageLookupByLibrary.simpleMessage("Serial number"),
        "tr_detectExistingInspectionPlan": MessageLookupByLibrary.simpleMessage(
            "It is detected that the inspection plan already has a corresponding planned task, please choose whether to overwrite the original AI inspection plan task?"),
        "tr_detectionArea":
            MessageLookupByLibrary.simpleMessage("Detection area"),
        "tr_detectionBox":
            MessageLookupByLibrary.simpleMessage("Detection box"),
        "tr_detectionCount":
            MessageLookupByLibrary.simpleMessage("Detection count"),
        "tr_detectionFrequency":
            MessageLookupByLibrary.simpleMessage("Detection frequency"),
        "tr_detectionFrequencyRangeIs": MessageLookupByLibrary.simpleMessage(
            "Detection frequency range is："),
        "tr_detectionFrequencySmartScheduling":
            MessageLookupByLibrary.simpleMessage(
                "The detection frequency is intelligently scheduled."),
        "tr_detectionFrequencyUpperLimit": MessageLookupByLibrary.simpleMessage(
            "The maximum detection frequency is"),
        "tr_detectionFrequencyUpperLimitRangeIs":
            MessageLookupByLibrary.simpleMessage(
                "Detection frequency upper limit range is："),
        "tr_detectionTrend":
            MessageLookupByLibrary.simpleMessage("Detection trend"),
        "tr_determineBeforeAcceptanceAndSubmission":
            MessageLookupByLibrary.simpleMessage(
                "Before submitting for acceptance, please assess whether the rectification item has passed"),
        "tr_device": MessageLookupByLibrary.simpleMessage("Device"),
        "tr_deviceAdded": MessageLookupByLibrary.simpleMessage("Device added"),
        "tr_deviceAlarm": MessageLookupByLibrary.simpleMessage("Device alarm"),
        "tr_deviceAlertSound":
            MessageLookupByLibrary.simpleMessage("Device alert sound"),
        "tr_deviceAndAlgorithm":
            MessageLookupByLibrary.simpleMessage("Device and algorithms"),
        "tr_deviceAndAlgorithmConfiguration":
            MessageLookupByLibrary.simpleMessage(
                "Device and algorithm configuration"),
        "tr_deviceAndEvidence":
            MessageLookupByLibrary.simpleMessage("Device and records"),
        "tr_deviceAnomaly":
            MessageLookupByLibrary.simpleMessage("Device anomaly"),
        "tr_deviceAuthorization":
            MessageLookupByLibrary.simpleMessage("Device Authorization"),
        "tr_deviceBusy": MessageLookupByLibrary.simpleMessage("DeviceBusy"),
        "tr_deviceCamera": MessageLookupByLibrary.simpleMessage("IPC"),
        "tr_deviceCapacity":
            MessageLookupByLibrary.simpleMessage("Device capacity"),
        "tr_deviceConfigNoLatLng": MessageLookupByLibrary.simpleMessage(
            "The device has not set coordinates"),
        "tr_deviceConfiguration":
            MessageLookupByLibrary.simpleMessage("Device configuration"),
        "tr_deviceCreateCategory":
            MessageLookupByLibrary.simpleMessage("Create a category"),
        "tr_deviceCreateTag":
            MessageLookupByLibrary.simpleMessage("Create tags"),
        "tr_deviceDataOverview":
            MessageLookupByLibrary.simpleMessage("Device data overview"),
        "tr_deviceDetails":
            MessageLookupByLibrary.simpleMessage("Device details"),
        "tr_deviceDimension":
            MessageLookupByLibrary.simpleMessage("Device Dimension"),
        "tr_deviceEventAnalysis":
            MessageLookupByLibrary.simpleMessage("Device event analysis"),
        "tr_deviceExceptionDuringDetectionNotParticipatingInInspection":
            MessageLookupByLibrary.simpleMessage(
                "Device exception during detection not participating in inspection"),
        "tr_deviceExecuteUpgrade":
            MessageLookupByLibrary.simpleMessage("Please click Update Device"),
        "tr_deviceHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("Device deleted"),
        "tr_deviceIdIsEmpty":
            MessageLookupByLibrary.simpleMessage("Device ID is empty"),
        "tr_deviceInUseByAnotherUser": MessageLookupByLibrary.simpleMessage(
            "This device is currently being used for preview or download by another user. Please try again later."),
        "tr_deviceInspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("Device inspection analysis"),
        "tr_deviceInspectionCompletionRate":
            MessageLookupByLibrary.simpleMessage(
                "Inspection task completion rate"),
        "tr_deviceIsNotInPlaybackStateCannotChangeSpeed":
            MessageLookupByLibrary.simpleMessage(
                "Device is not in playback state, cannot change playback speed"),
        "tr_deviceIsNotInPlaybackStateCannotTakeScreenshots":
            MessageLookupByLibrary.simpleMessage(
                "Device is not in playback state, cannot take screenshots"),
        "tr_deviceIsOffline":
            MessageLookupByLibrary.simpleMessage("The device is offline"),
        "tr_deviceList": MessageLookupByLibrary.simpleMessage("View list"),
        "tr_deviceMaintenance":
            MessageLookupByLibrary.simpleMessage("Device maintenance"),
        "tr_deviceNeverReported":
            MessageLookupByLibrary.simpleMessage("Device never reported"),
        "tr_deviceNewCategory":
            MessageLookupByLibrary.simpleMessage("Add new category"),
        "tr_deviceNewSuccessfully":
            MessageLookupByLibrary.simpleMessage("Added successfully"),
        "tr_deviceNewTag": MessageLookupByLibrary.simpleMessage("Add tags"),
        "tr_deviceNoStreamOver20s": MessageLookupByLibrary.simpleMessage(
            "Device has no stream for over 20s"),
        "tr_deviceNoStreamOver2s": MessageLookupByLibrary.simpleMessage(
            "Device has no stream for over 2s"),
        "tr_deviceNotAddedRuntimePoint": MessageLookupByLibrary.simpleMessage(
            "Some device not added runtime point"),
        "tr_deviceNotExistDeleted": MessageLookupByLibrary.simpleMessage(
            "Device does not exist (deleted)"),
        "tr_deviceNotFound": MessageLookupByLibrary.simpleMessage(
            "The device cannot be found, please reset the device and add it again."),
        "tr_deviceNotPlay":
            MessageLookupByLibrary.simpleMessage("The device is not playing"),
        "tr_deviceOffline":
            MessageLookupByLibrary.simpleMessage("Device offline"),
        "tr_deviceOfflineEdit": MessageLookupByLibrary.simpleMessage(
            "The device is offline and cannot be edited"),
        "tr_deviceOfflineOrAbnormalNoSnapshotGenerated":
            MessageLookupByLibrary.simpleMessage(
                "Device offline or abnormal, no snapshot generated"),
        "tr_deviceOnLine":
            MessageLookupByLibrary.simpleMessage("Online Devices"),
        "tr_devicePassword":
            MessageLookupByLibrary.simpleMessage("Device Password"),
        "tr_deviceReporting":
            MessageLookupByLibrary.simpleMessage("Device Reporting"),
        "tr_deviceRunTimeGreaterThanStoreHours":
            MessageLookupByLibrary.simpleMessage(
                "Device run time greater than store hours"),
        "tr_deviceSN": MessageLookupByLibrary.simpleMessage("Device SN"),
        "tr_deviceSelection":
            MessageLookupByLibrary.simpleMessage("Device Selection"),
        "tr_deviceSideFlowTip": MessageLookupByLibrary.simpleMessage(
            "Real-time statistics of the number of people entering and exiting the store based on the target area"),
        "tr_deviceSource":
            MessageLookupByLibrary.simpleMessage("Device source"),
        "tr_deviceStateRegister":
            MessageLookupByLibrary.simpleMessage("Registered"),
        "tr_deviceSum": MessageLookupByLibrary.simpleMessage("Total Devices"),
        "tr_deviceTag": MessageLookupByLibrary.simpleMessage("Device tag"),
        "tr_deviceTags": MessageLookupByLibrary.simpleMessage("Device tags"),
        "tr_deviceTagsCategory":
            MessageLookupByLibrary.simpleMessage("Device tags category"),
        "tr_deviceTime": MessageLookupByLibrary.simpleMessage("Device time"),
        "tr_deviceTimezone":
            MessageLookupByLibrary.simpleMessage("Device timezone"),
        "tr_deviceTokenInvalid":
            MessageLookupByLibrary.simpleMessage("Device token invalid"),
        "tr_deviceTraffic":
            MessageLookupByLibrary.simpleMessage("Device traffic"),
        "tr_deviceTree": MessageLookupByLibrary.simpleMessage("Device Tree"),
        "tr_deviceUpgrade":
            MessageLookupByLibrary.simpleMessage("Device Upgrade"),
        "tr_deviceUpgradeVersion":
            MessageLookupByLibrary.simpleMessage("Already the latest version"),
        "tr_deviceUsername":
            MessageLookupByLibrary.simpleMessage("Device Username"),
        "tr_deviceVersion":
            MessageLookupByLibrary.simpleMessage("Device version"),
        "tr_devicesInTotal": m23,
        "tr_dioException_badCertificate": MessageLookupByLibrary.simpleMessage(
            "Wrong certificate, please recheck the certificate"),
        "tr_dioException_badResponse400":
            MessageLookupByLibrary.simpleMessage("Bad Request"),
        "tr_dioException_badResponse401":
            MessageLookupByLibrary.simpleMessage("Unauthorized"),
        "tr_dioException_badResponse403":
            MessageLookupByLibrary.simpleMessage("Forbidden"),
        "tr_dioException_badResponse404":
            MessageLookupByLibrary.simpleMessage("Not Found"),
        "tr_dioException_badResponse405":
            MessageLookupByLibrary.simpleMessage("Request Method Not Allowed"),
        "tr_dioException_badResponse500":
            MessageLookupByLibrary.simpleMessage("Internal Server Error"),
        "tr_dioException_badResponse502":
            MessageLookupByLibrary.simpleMessage("Bad Gateway"),
        "tr_dioException_badResponse503":
            MessageLookupByLibrary.simpleMessage("Service Unavailable"),
        "tr_dioException_badResponse504":
            MessageLookupByLibrary.simpleMessage("Gateway Timeout"),
        "tr_dioException_badResponse505":
            MessageLookupByLibrary.simpleMessage("HTTP Version Not Supported"),
        "tr_dioException_cancel": MessageLookupByLibrary.simpleMessage(
            "Request has been canceled, please request again"),
        "tr_dioException_connectionError": MessageLookupByLibrary.simpleMessage(
            "Network is not connected, please check the network"),
        "tr_dioException_timeout":
            MessageLookupByLibrary.simpleMessage("Connection timed out"),
        "tr_dioException_unknown": MessageLookupByLibrary.simpleMessage(
            "Network unknown, please check the network request"),
        "tr_disable": MessageLookupByLibrary.simpleMessage("Disable"),
        "tr_disableSuccessful":
            MessageLookupByLibrary.simpleMessage("Disable successfully"),
        "tr_disabled": MessageLookupByLibrary.simpleMessage("Disabled"),
        "tr_displayAlarmRules":
            MessageLookupByLibrary.simpleMessage("Display alarm rules"),
        "tr_displayArrangement":
            MessageLookupByLibrary.simpleMessage("Display Arrangement"),
        "tr_doNotOverwrite":
            MessageLookupByLibrary.simpleMessage("Do Not Overwrite"),
        "tr_doublePersonGroup": MessageLookupByLibrary.simpleMessage(
            "Double-person entry (customer group)"),
        "tr_downloadCanceled":
            MessageLookupByLibrary.simpleMessage("Download canceled."),
        "tr_downloadChannelOccupied":
            MessageLookupByLibrary.simpleMessage("Download channel occupied"),
        "tr_downloadFailed":
            MessageLookupByLibrary.simpleMessage("Download failed"),
        "tr_downloadList":
            MessageLookupByLibrary.simpleMessage("Download list"),
        "tr_downloadManagement":
            MessageLookupByLibrary.simpleMessage("Download management"),
        "tr_downloading": MessageLookupByLibrary.simpleMessage("Downloading"),
        "tr_dragToSort": MessageLookupByLibrary.simpleMessage("Drag to sort"),
        "tr_drawCircle": MessageLookupByLibrary.simpleMessage("Circle"),
        "tr_duration": MessageLookupByLibrary.simpleMessage("Duration"),
        "tr_durationCannotBeLessThanDetectionFrequency":
            MessageLookupByLibrary.simpleMessage(
                "Duration cannot be less than detection frequency"),
        "tr_durationEqualsContinuousDetectionCountTimesDetectionFrequency":
            MessageLookupByLibrary.simpleMessage(
                "The duration is equal to the number of consecutive detections multiplied by the detection frequency."),
        "tr_duration_tip": MessageLookupByLibrary.simpleMessage(
            "An alarm is generated only when the detection behavior time exceeds the duration"),
        "tr_east": MessageLookupByLibrary.simpleMessage("East"),
        "tr_edgeComputing":
            MessageLookupByLibrary.simpleMessage("Edge computing"),
        "tr_editDeviceTags": MessageLookupByLibrary.simpleMessage("Edit Name"),
        "tr_editImage": MessageLookupByLibrary.simpleMessage("Edit Image"),
        "tr_editImageInspectionPlan":
            MessageLookupByLibrary.simpleMessage("Edit Image Inspection Plan"),
        "tr_elderly": MessageLookupByLibrary.simpleMessage("Elderly"),
        "tr_emailActivation":
            MessageLookupByLibrary.simpleMessage("Email activation"),
        "tr_employeeName":
            MessageLookupByLibrary.simpleMessage("Employee name"),
        "tr_employeeRanking":
            MessageLookupByLibrary.simpleMessage("Employee Ranking"),
        "tr_emptyImage": MessageLookupByLibrary.simpleMessage("Empty image"),
        "tr_enable": MessageLookupByLibrary.simpleMessage("Enable"),
        "tr_enableHighQualityImageUnderHighContrast":
            MessageLookupByLibrary.simpleMessage(
                "Enable high-quality image under high contrast lighting"),
        "tr_enableLocationOrGrantLocationPermissions":
            MessageLookupByLibrary.simpleMessage(
                "Please allow location or grant location permissions."),
        "tr_enabledSuccessful":
            MessageLookupByLibrary.simpleMessage("Enabled successfully"),
        "tr_encoderStaticConfig":
            MessageLookupByLibrary.simpleMessage("Encoder static config"),
        "tr_encodingHardSolution":
            MessageLookupByLibrary.simpleMessage("Hard solution"),
        "tr_encodingSettings":
            MessageLookupByLibrary.simpleMessage("Encoding settings"),
        "tr_encodingSoftSolution":
            MessageLookupByLibrary.simpleMessage("Soft solution"),
        "tr_encodingSwitching": MessageLookupByLibrary.simpleMessage(
            "Automatic switching between soft and hard solutions"),
        "tr_endDate": MessageLookupByLibrary.simpleMessage("End date"),
        "tr_endSidePassengerBox": MessageLookupByLibrary.simpleMessage(
            "End-side passenger flow detection frame"),
        "tr_endTime": MessageLookupByLibrary.simpleMessage("End time"),
        "tr_endTimeCannotBeGreaterThanName": m24,
        "tr_ensure": MessageLookupByLibrary.simpleMessage("Confirm"),
        "tr_ensureDeviceOnline": MessageLookupByLibrary.simpleMessage(
            "Please confirm whether the device is online"),
        "tr_enterInspectionItemNameToSearch":
            MessageLookupByLibrary.simpleMessage(
                "Enter inspection item name to search"),
        "tr_enterStore": MessageLookupByLibrary.simpleMessage("Enter store"),
        "tr_enterStoreBatch":
            MessageLookupByLibrary.simpleMessage("Enter store batch"),
        "tr_enterStoreBatchInterval":
            MessageLookupByLibrary.simpleMessage("Enter store batch interval"),
        "tr_enterStoreCount":
            MessageLookupByLibrary.simpleMessage("Enter store count"),
        "tr_enterTitleToSearch":
            MessageLookupByLibrary.simpleMessage("Enter title name to search"),
        "tr_entryPeak": MessageLookupByLibrary.simpleMessage("Peak in store"),
        "tr_entryTrough":
            MessageLookupByLibrary.simpleMessage("Valley in store"),
        "tr_envPreRelease": MessageLookupByLibrary.simpleMessage("Advance"),
        "tr_envRelease": MessageLookupByLibrary.simpleMessage("formal"),
        "tr_envSwitching":
            MessageLookupByLibrary.simpleMessage("Environment switching"),
        "tr_envTest": MessageLookupByLibrary.simpleMessage("Test"),
        "tr_envTestB": MessageLookupByLibrary.simpleMessage("Test B"),
        "tr_errorConvertingImage":
            MessageLookupByLibrary.simpleMessage("Error converting image"),
        "tr_errorMixFormatPassword": MessageLookupByLibrary.simpleMessage(
            "Please enter 8-16 digits including numbers, letters and special characters"),
        "tr_event": MessageLookupByLibrary.simpleMessage("Event"),
        "tr_eventCountRanking":
            MessageLookupByLibrary.simpleMessage("Event count ranking"),
        "tr_eventDetails":
            MessageLookupByLibrary.simpleMessage("Event Details"),
        "tr_eventInitiateType":
            MessageLookupByLibrary.simpleMessage("Event initiation type"),
        "tr_eventLevel": MessageLookupByLibrary.simpleMessage("Event level"),
        "tr_eventLevelProportion":
            MessageLookupByLibrary.simpleMessage("Event Level Proportion"),
        "tr_eventRecording":
            MessageLookupByLibrary.simpleMessage("Event recording"),
        "tr_eventSource":
            MessageLookupByLibrary.simpleMessage("Source of the event"),
        "tr_eventStatus": MessageLookupByLibrary.simpleMessage("Event Status"),
        "tr_eventTrends": MessageLookupByLibrary.simpleMessage("Event Trends"),
        "tr_everyDay": MessageLookupByLibrary.simpleMessage("Everyday"),
        "tr_everyMonth": MessageLookupByLibrary.simpleMessage("Every month"),
        "tr_everyMonthNameDay": m25,
        "tr_everyWeek": MessageLookupByLibrary.simpleMessage("Every week"),
        "tr_everyWeekNameDay": m26,
        "tr_excludeDeliveryCourier":
            MessageLookupByLibrary.simpleMessage("Exclude delivery/courier"),
        "tr_exeSync": MessageLookupByLibrary.simpleMessage("Sync"),
        "tr_executeOpen": MessageLookupByLibrary.simpleMessage("Go to open"),
        "tr_executeVerification":
            MessageLookupByLibrary.simpleMessage("verify"),
        "tr_executionCycle": MessageLookupByLibrary.simpleMessage("Period"),
        "tr_executionDate":
            MessageLookupByLibrary.simpleMessage("Execution date"),
        "tr_executionTime": MessageLookupByLibrary.simpleMessage("Time"),
        "tr_exemptFromWatermarkWhitelist":
            MessageLookupByLibrary.simpleMessage("Exempt From Watermark list"),
        "tr_existed": MessageLookupByLibrary.simpleMessage("Already exists"),
        "tr_exitBTConnectTimeout":
            MessageLookupByLibrary.simpleMessage("Connection timed out"),
        "tr_exitBTConnectTimeoutSub": MessageLookupByLibrary.simpleMessage(
            "Connection timed out, please reset the device, restore factory settings and add it again"),
        "tr_exitBTDistributeFail":
            MessageLookupByLibrary.simpleMessage("Wrong password"),
        "tr_exitBTDistributeFailSub": MessageLookupByLibrary.simpleMessage(
            "The password is wrong. Please reset the device and restore it to factory settings before adding it again."),
        "tr_exitDraw": MessageLookupByLibrary.simpleMessage("Exit"),
        "tr_exitKnow": MessageLookupByLibrary.simpleMessage("Knew"),
        "tr_exitLoginTip": MessageLookupByLibrary.simpleMessage(
            "Your account has been logged in on other devices. If it was not your operation, please log in again and change your password."),
        "tr_expand": MessageLookupByLibrary.simpleMessage("Expand"),
        "tr_expansionChannel":
            MessageLookupByLibrary.simpleMessage("Expansion"),
        "tr_expired": MessageLookupByLibrary.simpleMessage("Expired"),
        "tr_extendRole": MessageLookupByLibrary.simpleMessage("extend role"),
        "tr_faceDatabase": MessageLookupByLibrary.simpleMessage("Face library"),
        "tr_faceSimilarity":
            MessageLookupByLibrary.simpleMessage("Facial similarity"),
        "tr_facialImage": MessageLookupByLibrary.simpleMessage("Face image"),
        "tr_factoryReset":
            MessageLookupByLibrary.simpleMessage("Factory reset"),
        "tr_failConnectNetwork":
            MessageLookupByLibrary.simpleMessage("Networking issues"),
        "tr_failPassed": MessageLookupByLibrary.simpleMessage("Fail"),
        "tr_failedCount": MessageLookupByLibrary.simpleMessage("Failed count"),
        "tr_failedToCreateOnsiteInspection":
            MessageLookupByLibrary.simpleMessage(
                "Failed to create onsite inspection"),
        "tr_failedToGetUrlPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Failed to obtain url, please try again"),
        "tr_failure": MessageLookupByLibrary.simpleMessage("Failure"),
        "tr_female": MessageLookupByLibrary.simpleMessage("Female"),
        "tr_femalePercentageName": m27,
        "tr_fetchingImageDimensionsPleaseRetry":
            MessageLookupByLibrary.simpleMessage(
                "Fetching image dimensions, please retry later"),
        "tr_fileDeletionFailed":
            MessageLookupByLibrary.simpleMessage("File deletion failed"),
        "tr_filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "tr_findingsCount":
            MessageLookupByLibrary.simpleMessage("Findings count"),
        "tr_firmwareManage":
            MessageLookupByLibrary.simpleMessage("Firmware Management"),
        "tr_firmwareUpgradeFailure":
            MessageLookupByLibrary.simpleMessage("Upgrade Failed"),
        "tr_firmwareUpgradeFailureTip": MessageLookupByLibrary.simpleMessage(
            "Upgrade failed, please try again"),
        "tr_firmwareUpgradeSuccess":
            MessageLookupByLibrary.simpleMessage("Upgrade Successfully"),
        "tr_firmwareUpgradeSuccessTip": MessageLookupByLibrary.simpleMessage(
            "Upgrade successful, waiting for the device to restart"),
        "tr_firmwareUpgrading":
            MessageLookupByLibrary.simpleMessage("Upgrading"),
        "tr_firmwareUpgradingTip": MessageLookupByLibrary.simpleMessage(
            "No other operations can be performed during the upgrade process, Please do not exit or press the home button during the upgrade"),
        "tr_firmwareVersion":
            MessageLookupByLibrary.simpleMessage("Firmware version"),
        "tr_floorPlan": MessageLookupByLibrary.simpleMessage("Floor plan"),
        "tr_flowDeviceTip": MessageLookupByLibrary.simpleMessage(
            "Only perform data analysis on devices that support customer flow statistics under the store. Devices that do not support passenger flow statistics will not be counted."),
        "tr_flowExpirationTime":
            MessageLookupByLibrary.simpleMessage("Expiration Time："),
        "tr_flowExpired": MessageLookupByLibrary.simpleMessage("(Expired)"),
        "tr_flowRemaining": MessageLookupByLibrary.simpleMessage("Remaining"),
        "tr_flowRemainingTip": MessageLookupByLibrary.simpleMessage(
            "To renew or expand? <NAME_EMAIL> for renewal!"),
        "tr_flowStatisticsConfigTip": MessageLookupByLibrary.simpleMessage(
            "1. The camera is recommended to be slightly larger than a 45 ° overhead viewing angle, facing outside the store.\n2. The selected area is the outside area of the store, and the inside area on the arrow side is the inside area of the store.\n3. The selection area can be dragged to adjust its size and position."),
        "tr_flowTotal": MessageLookupByLibrary.simpleMessage("Total"),
        "tr_flowTypeEnterStore":
            MessageLookupByLibrary.simpleMessage("Enter the store"),
        "tr_flowTypePassingStore":
            MessageLookupByLibrary.simpleMessage("Passing the store"),
        "tr_flowTypeVisitedStore":
            MessageLookupByLibrary.simpleMessage("Visited the store"),
        "tr_flowUse": MessageLookupByLibrary.simpleMessage("Used"),
        "tr_flow_statistics":
            MessageLookupByLibrary.simpleMessage("Data statistics"),
        "tr_forever": MessageLookupByLibrary.simpleMessage("Permanent"),
        "tr_formatStorageCardTip": MessageLookupByLibrary.simpleMessage(
            "Formatting the storage card will make the device offline and reload the storage card, which will take several minutes, please confirm whether to continue?"),
        "tr_formatSuccess":
            MessageLookupByLibrary.simpleMessage("Format successful"),
        "tr_fraction": MessageLookupByLibrary.simpleMessage("score"),
        "tr_frameRateFPS":
            MessageLookupByLibrary.simpleMessage("Frame rate (FPS)"),
        "tr_frequency": MessageLookupByLibrary.simpleMessage("Number of times"),
        "tr_friday": MessageLookupByLibrary.simpleMessage("Fri"),
        "tr_fullFlip": MessageLookupByLibrary.simpleMessage("Full flip"),
        "tr_fullScreen": MessageLookupByLibrary.simpleMessage("Full screen"),
        "tr_gatewaySN":
            MessageLookupByLibrary.simpleMessage("Gateway serial number"),
        "tr_gatewayStatus":
            MessageLookupByLibrary.simpleMessage("Gateway Status"),
        "tr_gatewayType": MessageLookupByLibrary.simpleMessage("Gateway type"),
        "tr_genderDistribution":
            MessageLookupByLibrary.simpleMessage("Gender ratio"),
        "tr_general": MessageLookupByLibrary.simpleMessage("General"),
        "tr_generatePlayAddress":
            MessageLookupByLibrary.simpleMessage("Generate play address"),
        "tr_gentleReminder":
            MessageLookupByLibrary.simpleMessage("Gentle Reminder"),
        "tr_getDownloadLinkFailed": MessageLookupByLibrary.simpleMessage(
            "Failed to retrieve the download link."),
        "tr_getNoLatLng":
            MessageLookupByLibrary.simpleMessage("No coordinates obtained"),
        "tr_getScore": MessageLookupByLibrary.simpleMessage("Get Score"),
        "tr_gettingVideoStream":
            MessageLookupByLibrary.simpleMessage("Getting video stream"),
        "tr_globalConfiguration":
            MessageLookupByLibrary.simpleMessage("Global configuration"),
        "tr_globalPlaybackPeriod":
            MessageLookupByLibrary.simpleMessage("Global playback period"),
        "tr_globalSetting":
            MessageLookupByLibrary.simpleMessage("Global setting"),
        "tr_goAcceptance":
            MessageLookupByLibrary.simpleMessage("Go to Acceptance"),
        "tr_goConfigure": MessageLookupByLibrary.simpleMessage("Go configure"),
        "tr_goPassedItems":
            MessageLookupByLibrary.simpleMessage("Acceptance items"),
        "tr_goRectification":
            MessageLookupByLibrary.simpleMessage("Go to Rectification"),
        "tr_goToSelect": MessageLookupByLibrary.simpleMessage("Go to select"),
        "tr_goToSelectMultipleChoice": MessageLookupByLibrary.simpleMessage(
            "Go to Select (Multiple possible)"),
        "tr_googlePermissionTips": MessageLookupByLibrary.simpleMessage(
            "BcloudSaaS App collects location data, collects photos, videos, and camera-recorded media content or file data，to enable identification of real-time positioning coordinate and address, identify photos, video media content, or file information data, even when the app is closed or not in use."),
        "tr_handleError": MessageLookupByLibrary.simpleMessage("Handle error"),
        "tr_hasBeenDeleted": MessageLookupByLibrary.simpleMessage("Deleted"),
        "tr_hasChooseNode": MessageLookupByLibrary.simpleMessage(
            "You cannot add a node under the current node, Please select a node"),
        "tr_hasNoAuthorization":
            MessageLookupByLibrary.simpleMessage("No permission to operate"),
        "tr_hasSelectDeviceNode":
            MessageLookupByLibrary.simpleMessage("Please select a node"),
        "tr_hasStopped": MessageLookupByLibrary.simpleMessage("Stopped"),
        "tr_heartbeatPeriod":
            MessageLookupByLibrary.simpleMessage("Heartbeat period (seconds)"),
        "tr_heartbeatPeriodHint":
            MessageLookupByLibrary.simpleMessage("Range 30-3600 seconds"),
        "tr_heatArea": MessageLookupByLibrary.simpleMessage("Heat area"),
        "tr_heatAreaConfiguration":
            MessageLookupByLibrary.simpleMessage("Heat area configuration"),
        "tr_heatDataDetails":
            MessageLookupByLibrary.simpleMessage("Heat data details"),
        "tr_heatMap": MessageLookupByLibrary.simpleMessage("Heat map"),
        "tr_heptagon": MessageLookupByLibrary.simpleMessage("Heptagon"),
        "tr_hexagon": MessageLookupByLibrary.simpleMessage("Hexagon"),
        "tr_high": MessageLookupByLibrary.simpleMessage("High"),
        "tr_highFrequencyQuestion":
            MessageLookupByLibrary.simpleMessage("High frequency problem"),
        "tr_history": MessageLookupByLibrary.simpleMessage("History"),
        "tr_holiday": MessageLookupByLibrary.simpleMessage("Holiday"),
        "tr_holidaySelection":
            MessageLookupByLibrary.simpleMessage("Holiday selection"),
        "tr_horizontalFlip":
            MessageLookupByLibrary.simpleMessage("Horizontal flip"),
        "tr_hour": MessageLookupByLibrary.simpleMessage("Hour"),
        "tr_hours": MessageLookupByLibrary.simpleMessage("Hour"),
        "tr_httpParsingError":
            MessageLookupByLibrary.simpleMessage("HTTP parsing error"),
        "tr_humanDetection":
            MessageLookupByLibrary.simpleMessage("Human detection"),
        "tr_humanDetectionMarking": MessageLookupByLibrary.simpleMessage(
            "When a human figure appears in the video, it will be marked with a box or line"),
        "tr_humanDetectionTip": MessageLookupByLibrary.simpleMessage(
            "Default enable human detection，human detection or motion detection, choose one"),
        "tr_iFrameIntervalRange": MessageLookupByLibrary.simpleMessage(
            "I-frame interval, value range 1~12"),
        "tr_iFrameNoSps":
            MessageLookupByLibrary.simpleMessage("I-frame has no SPS"),
        "tr_iFrameNotFound":
            MessageLookupByLibrary.simpleMessage("I-frame not found"),
        "tr_image": MessageLookupByLibrary.simpleMessage("Image"),
        "tr_imageConfig": MessageLookupByLibrary.simpleMessage("Image config"),
        "tr_imageFlip": MessageLookupByLibrary.simpleMessage("Image flip"),
        "tr_imageGenerationFailed":
            MessageLookupByLibrary.simpleMessage("Image generation failed"),
        "tr_imageInspection":
            MessageLookupByLibrary.simpleMessage("Photo check"),
        "tr_imageInspectionPlan":
            MessageLookupByLibrary.simpleMessage("Capture plan"),
        "tr_imageQuality":
            MessageLookupByLibrary.simpleMessage("Image quality"),
        "tr_imageRequiredForNonCompliance":
            MessageLookupByLibrary.simpleMessage(
                "Image required for non compliance"),
        "tr_imageStyleTip": MessageLookupByLibrary.simpleMessage(
            "Support extensions: jpeg, jpg, bmp formats;\nImage limit: 10MB;\nSuggested resolution: 626 * 413 (2-inch standard ID photo)"),
        "tr_inInspection":
            MessageLookupByLibrary.simpleMessage("In Inspection"),
        "tr_inProgress": MessageLookupByLibrary.simpleMessage("Running"),
        "tr_inStoreBatchRatio":
            MessageLookupByLibrary.simpleMessage("In-store batch ratio"),
        "tr_inStoreCustomerCount":
            MessageLookupByLibrary.simpleMessage("In-store customer count"),
        "tr_inStoreCustomerGroup":
            MessageLookupByLibrary.simpleMessage("In-store customer group"),
        "tr_inStoreCustomerGroupTable": MessageLookupByLibrary.simpleMessage(
            "In-store customer group statistics table"),
        "tr_inStoreCustomerGroupTip": MessageLookupByLibrary.simpleMessage(
            "In-store customer flow: after deduplication, customers who pass through the detection area first and then through the in-store customer line are counted as in-store customer flow\nSingle-person entry (customer group): after deduplication, single-person entry and placement time interval of two seconds after another person\'s entry are counted as single-person entry\nDouble-person entry (customer group): after deduplication, two people\'s entry time interval is set as the same batch\nThree-person entry (customer group): after deduplication, three people\'s entry time interval is set as the same batch 20%\nMultiple-person entry (customer group): after deduplication, three or more people\'s entry time interval is set as the same batch\nIn-store customer flow: after deduplication, any non-in-store customer line is counted as out-store customer flow\nTotal customer flow: in-store customer flow + out-store customer flow\nIn-store rate: in-store customer flow / total customer flow * 100% = in-store customer flow / (in-store customer flow + out-store customer flow) * 100%"),
        "tr_incompleteConfiguration":
            MessageLookupByLibrary.simpleMessage("Incomplete configuration"),
        "tr_incompleteDeviceInfo": MessageLookupByLibrary.simpleMessage(
            "Imperfect device information"),
        "tr_incorrectPassword":
            MessageLookupByLibrary.simpleMessage("Incorrect password"),
        "tr_increaseDetectionFrequency": MessageLookupByLibrary.simpleMessage(
            "Increase detection frequency."),
        "tr_initiatePerson": MessageLookupByLibrary.simpleMessage("Sponsor"),
        "tr_initiateTime":
            MessageLookupByLibrary.simpleMessage("Initiation time"),
        "tr_inputAccessId":
            MessageLookupByLibrary.simpleMessage("Please enter the access ID"),
        "tr_inputAccessIdNumHint":
            MessageLookupByLibrary.simpleMessage("The access ID is 20 digits"),
        "tr_inputAccessPsd": MessageLookupByLibrary.simpleMessage(
            "Please enter the access password"),
        "tr_inputAccessPsdHint": MessageLookupByLibrary.simpleMessage(
            "The access password is 8 to 16 characters in English, numbers, and special symbols"),
        "tr_inputAccount":
            MessageLookupByLibrary.simpleMessage("Please enter account"),
        "tr_inputAccountPassword":
            MessageLookupByLibrary.simpleMessage("Enter account and password"),
        "tr_inputApplicationName": MessageLookupByLibrary.simpleMessage(
            "Please enter the application name"),
        "tr_inputChannels": MessageLookupByLibrary.simpleMessage(
            "Please enter the number of channels"),
        "tr_inputDevicePsd": MessageLookupByLibrary.simpleMessage(
            "Please enter device password"),
        "tr_inputHeartbeatPeriod": MessageLookupByLibrary.simpleMessage(
            "Please enter the heartbeat period (seconds)"),
        "tr_inputHeartbeatPeriodHint": MessageLookupByLibrary.simpleMessage(
            "The heartbeat cycle ranges from 60 to 3600 seconds"),
        "tr_inputIPV4": MessageLookupByLibrary.simpleMessage(
            "Please enter the correct IPV4"),
        "tr_inputNumChannels": MessageLookupByLibrary.simpleMessage(
            "How many channels does your NVR need?"),
        "tr_inputParentPlatName": MessageLookupByLibrary.simpleMessage(
            "Please enter the name of the parent platform"),
        "tr_inputPlatformName": MessageLookupByLibrary.simpleMessage(
            "Please enter the platform name"),
        "tr_inputRegistrationCycle": MessageLookupByLibrary.simpleMessage(
            "Please enter the registration period (seconds)"),
        "tr_inputRegistrationCycleHint": MessageLookupByLibrary.simpleMessage(
            "The registration period ranges from 3600 to 86400 seconds"),
        "tr_inputSIPServerIP": MessageLookupByLibrary.simpleMessage(
            "Please enter the SIP server IP"),
        "tr_inputSIPServerNumber": MessageLookupByLibrary.simpleMessage(
            "Please enter the SIP server number"),
        "tr_inputSIPServerNumberHint": MessageLookupByLibrary.simpleMessage(
            "The SIP server number is 20 digits"),
        "tr_inputSIPServerPort": MessageLookupByLibrary.simpleMessage(
            "Please enter the SIP server port"),
        "tr_inputSIPServerPortHint": MessageLookupByLibrary.simpleMessage(
            "The SIP server port is a 1-5 digit number"),
        "tr_inputSixSNCode": MessageLookupByLibrary.simpleMessage(
            "Please enter the 6-digit serial number"),
        "tr_inputStoreName":
            MessageLookupByLibrary.simpleMessage("Please enter the store name"),
        "tr_inputValidityPeriod":
            MessageLookupByLibrary.simpleMessage("Input time limit"),
        "tr_inspected": MessageLookupByLibrary.simpleMessage("Checked"),
        "tr_inspectionAddress":
            MessageLookupByLibrary.simpleMessage("Inspection address"),
        "tr_inspectionAlarm":
            MessageLookupByLibrary.simpleMessage("Inspection alarm"),
        "tr_inspectionAlarmCount":
            MessageLookupByLibrary.simpleMessage("Inspection alarm count"),
        "tr_inspectionCall":
            MessageLookupByLibrary.simpleMessage("Inspection call"),
        "tr_inspectionCallCount":
            MessageLookupByLibrary.simpleMessage("Inspection call count"),
        "tr_inspectionCategory": MessageLookupByLibrary.simpleMessage(
            "Belongs to the inspection type"),
        "tr_inspectionCompletion":
            MessageLookupByLibrary.simpleMessage("Inspection Completion"),
        "tr_inspectionCompletionRate":
            MessageLookupByLibrary.simpleMessage("Inspection completion rate"),
        "tr_inspectionCompletionTime":
            MessageLookupByLibrary.simpleMessage("Completion Time"),
        "tr_inspectionConfirm":
            MessageLookupByLibrary.simpleMessage("Inspection confirm"),
        "tr_inspectionDevice": MessageLookupByLibrary.simpleMessage("Device"),
        "tr_inspectionDeviceOfflineOrDeleted":
            MessageLookupByLibrary.simpleMessage(
                "Inspection device offline or deleted"),
        "tr_inspectionDimension":
            MessageLookupByLibrary.simpleMessage("Inspection dimension"),
        "tr_inspectionItem":
            MessageLookupByLibrary.simpleMessage("Inspection item"),
        "tr_inspectionItemName":
            MessageLookupByLibrary.simpleMessage("Inspection item name"),
        "tr_inspectionMethod":
            MessageLookupByLibrary.simpleMessage("Inspection method"),
        "tr_inspectionMethods": MessageLookupByLibrary.simpleMessage(
            "Inspection methods include: \'Inspection tasks, Spot checks, Image inspections, Video inspections, Shake inspections, On-site inspections\'"),
        "tr_inspectionNode":
            MessageLookupByLibrary.simpleMessage("Inspection node"),
        "tr_inspectionPerson":
            MessageLookupByLibrary.simpleMessage("Inspector"),
        "tr_inspectionPlanDetail":
            MessageLookupByLibrary.simpleMessage("Inspection plan details"),
        "tr_inspectionPlanSettings":
            MessageLookupByLibrary.simpleMessage("Inspection plan setting"),
        "tr_inspectionProblemDescription": MessageLookupByLibrary.simpleMessage(
            "Inspection problem description"),
        "tr_inspectionProgress":
            MessageLookupByLibrary.simpleMessage("Inspection progress"),
        "tr_inspectionRecordWatermark":
            MessageLookupByLibrary.simpleMessage("Inspection record watermark"),
        "tr_inspectionRecordWatermarkTip": MessageLookupByLibrary.simpleMessage(
            "After enabling, the inspection record page will carry a watermark"),
        "tr_inspectionResult":
            MessageLookupByLibrary.simpleMessage("Inspection Result"),
        "tr_inspectionRetPreview":
            MessageLookupByLibrary.simpleMessage("Inspection result preview"),
        "tr_inspectionScope":
            MessageLookupByLibrary.simpleMessage("Scope of inspection"),
        "tr_inspectionStatus":
            MessageLookupByLibrary.simpleMessage("Inspection status"),
        "tr_inspectionStores":
            MessageLookupByLibrary.simpleMessage("Inspection stores"),
        "tr_inspectionTasks":
            MessageLookupByLibrary.simpleMessage("Inspection tasks"),
        "tr_inspectionTemplate":
            MessageLookupByLibrary.simpleMessage("Template"),
        "tr_inspectionTemplateSelection": MessageLookupByLibrary.simpleMessage(
            "Inspection Template Selection"),
        "tr_inspectionTime": MessageLookupByLibrary.simpleMessage("Time"),
        "tr_inspectionType":
            MessageLookupByLibrary.simpleMessage("Inspection type"),
        "tr_inspectionWatermark":
            MessageLookupByLibrary.simpleMessage("Inspection watermark"),
        "tr_inspector": MessageLookupByLibrary.simpleMessage("Inspector"),
        "tr_instructionsForAccessing": MessageLookupByLibrary.simpleMessage(
            "National Standard Access Instructions"),
        "tr_insufficientApplicationChannels":
            MessageLookupByLibrary.simpleMessage("Insufficient application"),
        "tr_insufficientAuthorization":
            MessageLookupByLibrary.simpleMessage("Insufficient authorization"),
        "tr_insufficientAuthorizationRoutes":
            MessageLookupByLibrary.simpleMessage(
                "Insufficient number of authorized channels"),
        "tr_insufficientChannels":
            MessageLookupByLibrary.simpleMessage("Insufficient application"),
        "tr_insufficientDeviceResources": MessageLookupByLibrary.simpleMessage(
            "Insufficient device resources"),
        "tr_insufficientTip": MessageLookupByLibrary.simpleMessage(
            "contact <EMAIL> to extend"),
        "tr_intervalRangeTip": MessageLookupByLibrary.simpleMessage(
            "Interval range: 0-600 seconds, recommended 30 seconds"),
        "tr_invalidSVData": MessageLookupByLibrary.simpleMessage(
            "Invalid sn, please scan the correct QR code"),
        "tr_invalidSeekTime":
            MessageLookupByLibrary.simpleMessage("Invalid seek time"),
        "tr_irLensReverse":
            MessageLookupByLibrary.simpleMessage("IR lens reverse"),
        "tr_isItOverdue":
            MessageLookupByLibrary.simpleMessage("Whether to overdue"),
        "tr_isNotPlayingTip": MessageLookupByLibrary.simpleMessage(
            "Not in play state, please try again later."),
        "tr_isQualified":
            MessageLookupByLibrary.simpleMessage("Whether it is qualified"),
        "tr_isStaff": MessageLookupByLibrary.simpleMessage("Is staff"),
        "tr_isTheRecordingPlanTurnedOn":
            MessageLookupByLibrary.simpleMessage("Enable Recording Plan?"),
        "tr_issuanceTime": MessageLookupByLibrary.simpleMessage("Issue time"),
        "tr_issueDetails":
            MessageLookupByLibrary.simpleMessage("Issue details"),
        "tr_issueDistributionPeriod":
            MessageLookupByLibrary.simpleMessage("Issue distribution period"),
        "tr_issueLocations":
            MessageLookupByLibrary.simpleMessage("Issue locations"),
        "tr_item": MessageLookupByLibrary.simpleMessage("Item"),
        "tr_itemOther": MessageLookupByLibrary.simpleMessage("Other"),
        "tr_juvenile": MessageLookupByLibrary.simpleMessage("Juvenile"),
        "tr_keyIssueDetails":
            MessageLookupByLibrary.simpleMessage("Key issue details"),
        "tr_keyIssues": MessageLookupByLibrary.simpleMessage("Key issues"),
        "tr_lANEquipment":
            MessageLookupByLibrary.simpleMessage("LAN equipment"),
        "tr_lackNecessaryDeviceInfo": MessageLookupByLibrary.simpleMessage(
            "Lack of necessary device information"),
        "tr_languageSimpleChinese":
            MessageLookupByLibrary.simpleMessage("Simple Chinese"),
        "tr_languageUSEnglish": MessageLookupByLibrary.simpleMessage("English"),
        "tr_last30Days": MessageLookupByLibrary.simpleMessage("Last 30 Days"),
        "tr_last3Days": MessageLookupByLibrary.simpleMessage("Last 3 Days"),
        "tr_last7Days": MessageLookupByLibrary.simpleMessage("Last 7 Days"),
        "tr_lastLogin": MessageLookupByLibrary.simpleMessage("Last Selection"),
        "tr_lastMonth": MessageLookupByLibrary.simpleMessage("Last month"),
        "tr_lastWeek": MessageLookupByLibrary.simpleMessage("Last week"),
        "tr_latestUpgradeVersion":
            MessageLookupByLibrary.simpleMessage("The latest version"),
        "tr_latitude": MessageLookupByLibrary.simpleMessage("latitude"),
        "tr_layers": MessageLookupByLibrary.simpleMessage("Layers"),
        "tr_leastPopulatedArea":
            MessageLookupByLibrary.simpleMessage("Least populated area"),
        "tr_leastPreferredStayArea":
            MessageLookupByLibrary.simpleMessage("Least preferred stay area"),
        "tr_levelEventTitle":
            MessageLookupByLibrary.simpleMessage("Event data"),
        "tr_levelInspectionDataTitle":
            MessageLookupByLibrary.simpleMessage("Inspection data"),
        "tr_levelInspectionTitle":
            MessageLookupByLibrary.simpleMessage("Inspection"),
        "tr_levelSetEvaluationTitle": MessageLookupByLibrary.simpleMessage(
            "Set up an evaluation template"),
        "tr_limitedBitRate":
            MessageLookupByLibrary.simpleMessage("Limited bit rate"),
        "tr_list": MessageLookupByLibrary.simpleMessage("List"),
        "tr_liveStreaming": MessageLookupByLibrary.simpleMessage("Live show"),
        "tr_loadingDataPleaseWait":
            MessageLookupByLibrary.simpleMessage("Loading data, please wait"),
        "tr_localIP": MessageLookupByLibrary.simpleMessage("Local IP"),
        "tr_localNetworkExceptionDuringServiceValidation":
            MessageLookupByLibrary.simpleMessage(
                "Local network exception during service validation"),
        "tr_localPlaybackDownloadConflict": MessageLookupByLibrary.simpleMessage(
            "Local playback preview and download cannot be performed simultaneously on the same device. Continuing playback will pause the download."),
        "tr_localRecording":
            MessageLookupByLibrary.simpleMessage("Local recording"),
        "tr_locate": MessageLookupByLibrary.simpleMessage("Locate"),
        "tr_locating": MessageLookupByLibrary.simpleMessage("Locating"),
        "tr_location": MessageLookupByLibrary.simpleMessage("Location"),
        "tr_locationInfo":
            MessageLookupByLibrary.simpleMessage("Location information"),
        "tr_loginFailed": MessageLookupByLibrary.simpleMessage("Login failed"),
        "tr_loginTimeoutNetworkConnectionFailed":
            MessageLookupByLibrary.simpleMessage(
                "Login timed out (network connection failed)"),
        "tr_longPressToEditStoreFloorPlan":
            MessageLookupByLibrary.simpleMessage(
                "Long press to edit store floor plan"),
        "tr_longPressToShowDetails": MessageLookupByLibrary.simpleMessage(
            "Long press to display details"),
        "tr_longitude": MessageLookupByLibrary.simpleMessage("longitude"),
        "tr_loopAlarmSound":
            MessageLookupByLibrary.simpleMessage("Loop alarm sound"),
        "tr_loopRecording":
            MessageLookupByLibrary.simpleMessage("Loop recording"),
        "tr_low": MessageLookupByLibrary.simpleMessage("Low"),
        "tr_mRectificationTasks":
            MessageLookupByLibrary.simpleMessage(" Rectification Tasks"),
        "tr_mainStream": MessageLookupByLibrary.simpleMessage("Main stream"),
        "tr_makeCopy": MessageLookupByLibrary.simpleMessage("CC"),
        "tr_male": MessageLookupByLibrary.simpleMessage("Male"),
        "tr_malePercentageName": m28,
        "tr_manageStore":
            MessageLookupByLibrary.simpleMessage("Store management"),
        "tr_manualInspectionAnalysisCoverageByTaskCompletionRate":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - task completion rate and coverage data"),
        "tr_manualInspectionAnalysisEmployeeRankingByFindings":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - employee ranking by findings"),
        "tr_manualInspectionAnalysisEmployeeRankingByInspectionCount":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - employee ranking by inspection count"),
        "tr_manualInspectionAnalysisEmployeeRankingByOverdue":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - employee ranking by overdue count"),
        "tr_manualInspectionAnalysisFailedInspectionRatio":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - failed inspection ratio"),
        "tr_manualInspectionAnalysisFailedPointRanking":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - failed point ranking"),
        "tr_manualInspectionAnalysisInspectionCount":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - inspection count data"),
        "tr_manualInspectionAnalysisPassRate":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - pass rate data"),
        "tr_manualInspectionAnalysisStoreRankingByFindings":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - store ranking by findings"),
        "tr_manualInspectionAnalysisTaskCompletionRate":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - store inspection task completion rate"),
        "tr_manualInspectionAnalysisTaskCompletionRateData":
            MessageLookupByLibrary.simpleMessage(
                "Manual inspection analysis - task completion rate data"),
        "tr_manualInspectionCoverageData": MessageLookupByLibrary.simpleMessage(
            "The following is the manual inspection analysis coverage data"),
        "tr_manuallySetStaffNumber":
            MessageLookupByLibrary.simpleMessage("Manually set staff number"),
        "tr_manuallySetStaffNumberRange": MessageLookupByLibrary.simpleMessage(
            "Manually set staff number range"),
        "tr_manyPersonGroup": MessageLookupByLibrary.simpleMessage(
            "Multiple-person entry (customer group)"),
        "tr_maturity": MessageLookupByLibrary.simpleMessage("Expired"),
        "tr_maxDurationCannotBeGreaterThan": m29,
        "tr_maximumNameTasksDownloadLimit": m30,
        "tr_maximumTimeSupport": m31,
        "tr_medium": MessageLookupByLibrary.simpleMessage("Medium"),
        "tr_meetAbove": MessageLookupByLibrary.simpleMessage("Meet above"),
        "tr_meetAboveConditionsConsideredStaff": m32,
        "tr_memberManagement":
            MessageLookupByLibrary.simpleMessage("Member management"),
        "tr_memberManagerDesc": MessageLookupByLibrary.simpleMessage(
            "View platform and non platform members info,relevant operations"),
        "tr_messageNotificationInterval": MessageLookupByLibrary.simpleMessage(
            "Message notification interval"),
        "tr_middleAged": MessageLookupByLibrary.simpleMessage("MiddleAged"),
        "tr_mineAcceptance":
            MessageLookupByLibrary.simpleMessage("My Acceptance"),
        "tr_mineDuplicate": MessageLookupByLibrary.simpleMessage("Copy to me"),
        "tr_mineInitiate": MessageLookupByLibrary.simpleMessage("My Initiated"),
        "tr_mineRectify": MessageLookupByLibrary.simpleMessage("My Rectify"),
        "tr_minor": MessageLookupByLibrary.simpleMessage("Minor"),
        "tr_minute": MessageLookupByLibrary.simpleMessage("Minute"),
        "tr_modifyAccessIdPwd":
            MessageLookupByLibrary.simpleMessage("Modify access ID password"),
        "tr_modifyStore":
            MessageLookupByLibrary.simpleMessage("Store Information"),
        "tr_modifyStoreAddress":
            MessageLookupByLibrary.simpleMessage("Modify store address"),
        "tr_modifyStoreName":
            MessageLookupByLibrary.simpleMessage("Modify store name"),
        "tr_monday": MessageLookupByLibrary.simpleMessage("Mon"),
        "tr_month": MessageLookupByLibrary.simpleMessage("Month"),
        "tr_moreApplications":
            MessageLookupByLibrary.simpleMessage("More features"),
        "tr_moreInstructionsMaxHundred": MessageLookupByLibrary.simpleMessage(
            "Please enter a description, up to 100 words"),
        "tr_morning": MessageLookupByLibrary.simpleMessage("Morning"),
        "tr_mostPopulatedArea":
            MessageLookupByLibrary.simpleMessage("Most populated area"),
        "tr_mostPreferredStayArea":
            MessageLookupByLibrary.simpleMessage("Most preferred stay area"),
        "tr_motionDetection":
            MessageLookupByLibrary.simpleMessage("Motion detection"),
        "tr_msgAlarm": MessageLookupByLibrary.simpleMessage("Msg alarm"),
        "tr_msgCall": MessageLookupByLibrary.simpleMessage("Msg call"),
        "tr_multi": MessageLookupByLibrary.simpleMessage("Multi-port"),
        "tr_multiIPC": MessageLookupByLibrary.simpleMessage("Multi-port IPC"),
        "tr_multiplePeople":
            MessageLookupByLibrary.simpleMessage("Multiple people"),
        "tr_multipleVisitsPerDayConsideredStaff":
            MessageLookupByLibrary.simpleMessage(
                "Multiple visits per day considered staff"),
        "tr_myInitiated": MessageLookupByLibrary.simpleMessage("My initiated"),
        "tr_nameAbnormalRoutes": m33,
        "tr_nameDevices": m34,
        "tr_nameHoursOrMore": m35,
        "tr_nameMinutesOrMore": m36,
        "tr_namePleaseRetry": m37,
        "tr_namePlusAccount":
            MessageLookupByLibrary.simpleMessage("Name Plus Account"),
        "tr_nationalStandardAccessManagement":
            MessageLookupByLibrary.simpleMessage("Access ID"),
        "tr_nationalStandardDeviceAccess":
            MessageLookupByLibrary.simpleMessage("Add GB28181"),
        "tr_nearbyLocation":
            MessageLookupByLibrary.simpleMessage("Nearby location"),
        "tr_nearbyLocationNoData": MessageLookupByLibrary.simpleMessage(
            "No relevant nearby location information was found"),
        "tr_networkAdd": MessageLookupByLibrary.simpleMessage("LAN Add"),
        "tr_networkException":
            MessageLookupByLibrary.simpleMessage("Network exception"),
        "tr_networkHostNotFound":
            MessageLookupByLibrary.simpleMessage("Network host not found"),
        "tr_networkMode": MessageLookupByLibrary.simpleMessage("Network mode"),
        "tr_never": MessageLookupByLibrary.simpleMessage("Never"),
        "tr_newPassword": MessageLookupByLibrary.simpleMessage("New Password"),
        "tr_newRole": MessageLookupByLibrary.simpleMessage("new role"),
        "tr_no": MessageLookupByLibrary.simpleMessage("No"),
        "tr_noAlgorithmData":
            MessageLookupByLibrary.simpleMessage("No algorithm data"),
        "tr_noAvailableDevices":
            MessageLookupByLibrary.simpleMessage("No available devices"),
        "tr_noChannel": MessageLookupByLibrary.simpleMessage("No Channel"),
        "tr_noConfigInfoFoundInRedis": MessageLookupByLibrary.simpleMessage(
            "No configuration information found in Redis"),
        "tr_noCorrespondingRecords":
            MessageLookupByLibrary.simpleMessage("No corresponding records"),
        "tr_noDevice": MessageLookupByLibrary.simpleMessage("No device"),
        "tr_noDeviceNode":
            MessageLookupByLibrary.simpleMessage("No device node"),
        "tr_noDeviceTrafficDevice":
            MessageLookupByLibrary.simpleMessage("No device traffic devices"),
        "tr_noDevicesUnderStore":
            MessageLookupByLibrary.simpleMessage("No available devices"),
        "tr_noEvaluationItemsTemporarily": MessageLookupByLibrary.simpleMessage(
            "No evaluation items available"),
        "tr_noFloorPlanConfigured":
            MessageLookupByLibrary.simpleMessage("No floor plan configured"),
        "tr_noFrameDataReceived2sBeforeDisconnection":
            MessageLookupByLibrary.simpleMessage(
                "No frame data received 2 seconds before disconnection"),
        "tr_noFrameDataReceivedDuringConnection":
            MessageLookupByLibrary.simpleMessage(
                "No frame data received during connection"),
        "tr_noInputParameter":
            MessageLookupByLibrary.simpleMessage("No input parameters"),
        "tr_noMicrophonePermissionTip": MessageLookupByLibrary.simpleMessage(
            "No microphone permission, please open microphone permission in system settings."),
        "tr_noObject": MessageLookupByLibrary.simpleMessage("No object"),
        "tr_noPreciseTrafficFlowDevice": MessageLookupByLibrary.simpleMessage(
            "No precise traffic flow devices"),
        "tr_noPresetPoint":
            MessageLookupByLibrary.simpleMessage("No preset point"),
        "tr_noProcessingPermissionForTheTimeBeing":
            MessageLookupByLibrary.simpleMessage("No processing permission"),
        "tr_noRecording": MessageLookupByLibrary.simpleMessage("No recording"),
        "tr_noRecordingDeviceOrNotRecording":
            MessageLookupByLibrary.simpleMessage(
                "No recording device or device not recording"),
        "tr_noResendBeforeReply": MessageLookupByLibrary.simpleMessage(
            "No resending before reply is completed"),
        "tr_noSdCardOrHardDrive":
            MessageLookupByLibrary.simpleMessage("No SD card or hard drive"),
        "tr_noStoragePermissionPleaseRetryAfterOpening":
            MessageLookupByLibrary.simpleMessage(
                "No storage permission, please enable and try again"),
        "tr_noStoresAvailable":
            MessageLookupByLibrary.simpleMessage("No stores available"),
        "tr_noStoresAvailableForInspection":
            MessageLookupByLibrary.simpleMessage(
                "No stores available for inspection"),
        "tr_noTraffic": MessageLookupByLibrary.simpleMessage("No traffic"),
        "tr_noTrafficHeatZoneCamerasInStoreContactB2bEmail":
            MessageLookupByLibrary.simpleMessage(
                "No traffic heat zone cameras in the current store. You <NAME_EMAIL> for procurement."),
        "tr_noVideoFrameDataReceived2sBeforeDisconnection":
            MessageLookupByLibrary.simpleMessage(
                "No video frame data received 2 seconds before disconnection"),
        "tr_noVideosToDownloadAdjustRegion": MessageLookupByLibrary.simpleMessage(
            "No downloadable videos available. Please adjust the download area."),
        "tr_nodeAddRefuseMessage": MessageLookupByLibrary.simpleMessage(
            "NVR, platform, nodes cannot be set under ONVIF, and devices cannot be added"),
        "tr_nodeAddress": MessageLookupByLibrary.simpleMessage("Node location"),
        "tr_nodeCount": m38,
        "tr_nodeInfo": MessageLookupByLibrary.simpleMessage("Node Information"),
        "tr_nodeSyncAddress":
            MessageLookupByLibrary.simpleMessage("Sync to device"),
        "tr_normal": MessageLookupByLibrary.simpleMessage("Normal"),
        "tr_normalOnline":
            MessageLookupByLibrary.simpleMessage("Normal online"),
        "tr_normalProportion":
            MessageLookupByLibrary.simpleMessage("Normal proportion"),
        "tr_notActivated":
            MessageLookupByLibrary.simpleMessage("Not activated"),
        "tr_notAddChannel":
            MessageLookupByLibrary.simpleMessage("Do not add channels yet"),
        "tr_notConfiguredHeatArea":
            MessageLookupByLibrary.simpleMessage("Not configured heat area"),
        "tr_notEffective":
            MessageLookupByLibrary.simpleMessage("Not effective"),
        "tr_notModifiable":
            MessageLookupByLibrary.simpleMessage("Not modifiable"),
        "tr_notOverdueYet": MessageLookupByLibrary.simpleMessage("Not overdue"),
        "tr_notPlatformUser":
            MessageLookupByLibrary.simpleMessage("Non platform users"),
        "tr_notStart": MessageLookupByLibrary.simpleMessage("Not started"),
        "tr_notSubmitted": MessageLookupByLibrary.simpleMessage("Unsubmitted"),
        "tr_notSupportAuxiliaryStream": MessageLookupByLibrary.simpleMessage(
            "Does not support auxiliary stream play"),
        "tr_notSupportClientFlowDeviceOnlyTip":
            MessageLookupByLibrary.simpleMessage(
                "Edge side passenger flow device、edge cloud combined passenger flow device not support configure cloud precise passenger flow,already disabled display."),
        "tr_notSupportPassengerFlow": MessageLookupByLibrary.simpleMessage(
            "There is no device that supports passenger flow counting"),
        "tr_notSupported":
            MessageLookupByLibrary.simpleMessage("Not supported"),
        "tr_notYet": MessageLookupByLibrary.simpleMessage("None"),
        "tr_notYetSetCustomWatermarkContentAreYouSureToReturn":
            MessageLookupByLibrary.simpleMessage(
                "The custom watermark content has not been set yet, are you sure you want to go back?"),
        "tr_note": MessageLookupByLibrary.simpleMessage("Note:"),
        "tr_numberChannel": MessageLookupByLibrary.simpleMessage("Channel No："),
        "tr_numberCompletedTasks":
            MessageLookupByLibrary.simpleMessage("Number of completed tasks"),
        "tr_numberFailures":
            MessageLookupByLibrary.simpleMessage("Number of nonconformities"),
        "tr_numberHighFrequencyQuestion": MessageLookupByLibrary.simpleMessage(
            "High frequency issues (freq.）"),
        "tr_numberInspectionTasks":
            MessageLookupByLibrary.simpleMessage("Number of inspection tasks"),
        "tr_numberInspectionTasksCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Number of inspection tasks completed"),
        "tr_numberInspections":
            MessageLookupByLibrary.simpleMessage("Number of Inspections"),
        "tr_numberIssuedTasks":
            MessageLookupByLibrary.simpleMessage("Number of tasks issued"),
        "tr_numberOfEntries":
            MessageLookupByLibrary.simpleMessage("Number of entries"),
        "tr_numberOfPeopleEntering":
            MessageLookupByLibrary.simpleMessage("Number of people entering"),
        "tr_numberOverdueEvents":
            MessageLookupByLibrary.simpleMessage("Number of overdue events"),
        "tr_numberOverdueTasks":
            MessageLookupByLibrary.simpleMessage("Number of overdue tasks"),
        "tr_numberQuestions":
            MessageLookupByLibrary.simpleMessage("Number of questions"),
        "tr_numberStoresInspected":
            MessageLookupByLibrary.simpleMessage("Number of stores inspected"),
        "tr_occlusionAlarm":
            MessageLookupByLibrary.simpleMessage("Occlusion alarm"),
        "tr_octagon": MessageLookupByLibrary.simpleMessage("Octagon"),
        "tr_okayIKnow": MessageLookupByLibrary.simpleMessage("Okay, I Know"),
        "tr_onlineOfflineAlarm":
            MessageLookupByLibrary.simpleMessage("Online offline alarm"),
        "tr_onlineStatus":
            MessageLookupByLibrary.simpleMessage("Online status"),
        "tr_onlyCloudStatisticsTip": MessageLookupByLibrary.simpleMessage(
            "Only perform data statistics on devices that support cloud-based traffic counting for the selected stores."),
        "tr_onlyDeviceStatisticsTip": MessageLookupByLibrary.simpleMessage(
            "Only perform data statistics on devices that support device-end traffic counting for the selected stores."),
        "tr_onlySelectStoresWithOnlineDevices":
            MessageLookupByLibrary.simpleMessage(
                "Only select stores with online devices"),
        "tr_onlySupportBroadDeviceTip": MessageLookupByLibrary.simpleMessage(
            "Devices not supporting broadcast and devices offline are grayed out"),
        "tr_onlySupportClientFlowDeviceTip": MessageLookupByLibrary.simpleMessage(
            "Not edge cloud combined passenger flow device not support configuring end cloud combination precise guest flow"),
        "tr_onlySupportOnlineDeviceTip":
            MessageLookupByLibrary.simpleMessage("Only select online devices"),
        "tr_onvifProtocol":
            MessageLookupByLibrary.simpleMessage("ONVIF Protocol"),
        "tr_onvifSSIDNotRegistered":
            MessageLookupByLibrary.simpleMessage("ONVIF SSID not registered"),
        "tr_openBTDevice": MessageLookupByLibrary.simpleMessage(
            "Please turn on Bluetooth to search for the device"),
        "tr_openBTPermission": MessageLookupByLibrary.simpleMessage(
            " want to access your Bluetooth permissions"),
        "tr_openBTPermissionContent": MessageLookupByLibrary.simpleMessage(
            "Please turn on your phone\'s Bluetooth to scan nearby Bluetooth devices"),
        "tr_openBlueTooth": MessageLookupByLibrary.simpleMessage(
            "Please turn on Bluetooth and related permissions"),
        "tr_openLocalNetwork": MessageLookupByLibrary.simpleMessage(
            "Please enable local network permission"),
        "tr_openPackage":
            MessageLookupByLibrary.simpleMessage("Activate Package"),
        "tr_openWiFi":
            MessageLookupByLibrary.simpleMessage("Please turn on WiFi"),
        "tr_operationSuccessfulProceedingToNext":
            MessageLookupByLibrary.simpleMessage(
                "Operation successful, proceeding to the next"),
        "tr_optionalFields":
            MessageLookupByLibrary.simpleMessage("Not required"),
        "tr_orgAddDeviceNodeTips": MessageLookupByLibrary.simpleMessage(
            "No devices and sub-nodes\nPlease click the \'+\' button in the upper right corner to add devices and sub-nodes"),
        "tr_organization": MessageLookupByLibrary.simpleMessage("Organization"),
        "tr_osdSettings": MessageLookupByLibrary.simpleMessage("OSD settings"),
        "tr_otherCompanyAddExceptions": MessageLookupByLibrary.simpleMessage(
            "The device has been added by another company. If it is your device, please contact customer service."),
        "tr_outOfRange": MessageLookupByLibrary.simpleMessage("Out of range"),
        "tr_outStoreTraffic":
            MessageLookupByLibrary.simpleMessage("Outbound customer flow"),
        "tr_outStoreTrafficNoDeduplication":
            MessageLookupByLibrary.simpleMessage(
                "Out-store customer flow (not deduplicated)"),
        "tr_overallScore":
            MessageLookupByLibrary.simpleMessage("Overall score"),
        "tr_overdue": MessageLookupByLibrary.simpleMessage("Overdue"),
        "tr_overdueCountRanking":
            MessageLookupByLibrary.simpleMessage("Overdue count ranking"),
        "tr_overdueRate": MessageLookupByLibrary.simpleMessage("Overdue rate"),
        "tr_overdueTimes":
            MessageLookupByLibrary.simpleMessage("Overdue times"),
        "tr_overdueYet": MessageLookupByLibrary.simpleMessage("Overdue"),
        "tr_overlapWithExistingTimePeriods":
            MessageLookupByLibrary.simpleMessage(
                "Overlap with wxisting time periods"),
        "tr_overwrite": MessageLookupByLibrary.simpleMessage("Overwrite"),
        "tr_pageStay": MessageLookupByLibrary.simpleMessage("Page stay"),
        "tr_pairTheParams":
            MessageLookupByLibrary.simpleMessage("Configuration parameters"),
        "tr_parameterConfiguration":
            MessageLookupByLibrary.simpleMessage("Parameter configuration"),
        "tr_parameterError":
            MessageLookupByLibrary.simpleMessage("Parameter error"),
        "tr_parentCategory":
            MessageLookupByLibrary.simpleMessage("Parent category"),
        "tr_parentPlatformName":
            MessageLookupByLibrary.simpleMessage("Parent platform name"),
        "tr_parentPlatformNameHint": MessageLookupByLibrary.simpleMessage(
            "1-20 characters, both Chinese and English numbers are acceptable"),
        "tr_partialDeleteFailedTasks":
            MessageLookupByLibrary.simpleMessage("Partial delete failed tasks"),
        "tr_passByStore": MessageLookupByLibrary.simpleMessage("Pass by store"),
        "tr_passByTraffic": MessageLookupByLibrary.simpleMessage(
            "Customer flow through the store"),
        "tr_passByTrafficNoDeduplication": MessageLookupByLibrary.simpleMessage(
            "Pass-by customer flow (not deduplicated)"),
        "tr_passFail": MessageLookupByLibrary.simpleMessage("Failed"),
        "tr_passRate": MessageLookupByLibrary.simpleMessage("Pass Rate"),
        "tr_passScore": MessageLookupByLibrary.simpleMessage("Passing score"),
        "tr_passed": MessageLookupByLibrary.simpleMessage("Passed"),
        "tr_passedCount": MessageLookupByLibrary.simpleMessage("Passed count"),
        "tr_passedNumberTimes":
            MessageLookupByLibrary.simpleMessage("Passed Times"),
        "tr_passengerCloudFlowTip": MessageLookupByLibrary.simpleMessage(
            "In-store customer flow: customer flow entering the store from outside\nOut-store customer flow: customer flow walking out of the store from inside\nPassing-store customer flow: customer flow passing by without entering the store\nTotal customer flow = in-store customer flow + passing-store customer flow\nEntry rate = in-store customer flow / total customer flow * 100% = in-store customer flow / (in-store customer flow + passing-store customer flow) * 100%"),
        "tr_passengerFlowData":
            MessageLookupByLibrary.simpleMessage("Passenger flow data"),
        "tr_passengerFlowDataInformationClearsEveryDayAt24":
            MessageLookupByLibrary.simpleMessage(
                "Passenger flow data information is cleared to zero at 24:00 every day"),
        "tr_passengerFlowDevices":
            MessageLookupByLibrary.simpleMessage("Passenger flow equipment"),
        "tr_passengerFlowDevicesTotal": m39,
        "tr_passengerFlowInfo":
            MessageLookupByLibrary.simpleMessage("Passenger flow"),
        "tr_passengerFlowOsdSwitch":
            MessageLookupByLibrary.simpleMessage("Passenger flow osd switch"),
        "tr_passengerFlowPeerConfiguration":
            MessageLookupByLibrary.simpleMessage("Settings"),
        "tr_passengerFlowStatistics":
            MessageLookupByLibrary.simpleMessage("Passenger flow statistics"),
        "tr_passengerFlowStatisticsData": MessageLookupByLibrary.simpleMessage(
            "Passenger flow statistics data"),
        "tr_passengerFlowStatisticsTip": MessageLookupByLibrary.simpleMessage(
            "Customer Traffic Statistics: Statistical Dimensions Total Customer Volume\n\n1. Total customer traffic\n2. Store entry traffic\n3. Pass-by traffic\n4. Store exit traffic\n5. Store entry customers (deduplicated)\n6. Store exit customers (deduplicated)\n7. Pass-by customers (deduplicated)\n8. Total customers (deduplicated)\n\nStore entry peak: The peak of store entry within the statistical period\n\nStore entry value: The value of store entry within the statistical period"),
        "tr_passingScore":
            MessageLookupByLibrary.simpleMessage("Passing Score"),
        "tr_passwordError":
            MessageLookupByLibrary.simpleMessage("Password error"),
        "tr_passwordManage":
            MessageLookupByLibrary.simpleMessage("Password management"),
        "tr_passwordModify":
            MessageLookupByLibrary.simpleMessage("Password modify"),
        "tr_patrolStoreDetails":
            MessageLookupByLibrary.simpleMessage("Patrol store details"),
        "tr_paused": MessageLookupByLibrary.simpleMessage("Paused"),
        "tr_payloadFailed":
            MessageLookupByLibrary.simpleMessage("Payload failed"),
        "tr_pcGateway": MessageLookupByLibrary.simpleMessage("PC Gateway"),
        "tr_peakCloudDirectConnection":
            MessageLookupByLibrary.simpleMessage("Bcloud connection"),
        "tr_pendingAcceptance":
            MessageLookupByLibrary.simpleMessage("To be accepted"),
        "tr_pendingEvent":
            MessageLookupByLibrary.simpleMessage("Pending event"),
        "tr_pendingForAcceptance":
            MessageLookupByLibrary.simpleMessage("Details pending acceptance"),
        "tr_pendingInspection":
            MessageLookupByLibrary.simpleMessage("To be inspected"),
        "tr_pendingInspectionImages":
            MessageLookupByLibrary.simpleMessage("Pending inspection images"),
        "tr_pendingLevel":
            MessageLookupByLibrary.simpleMessage("Pending Priority Level"),
        "tr_pendingPersonAcceptance": m40,
        "tr_pendingPersonRectification": m41,
        "tr_pendingRectification":
            MessageLookupByLibrary.simpleMessage("To be rectified"),
        "tr_pendingSubmission":
            MessageLookupByLibrary.simpleMessage("Pending submission"),
        "tr_pentagon": MessageLookupByLibrary.simpleMessage("Pentagonal"),
        "tr_permissionAudioSubtitle": MessageLookupByLibrary.simpleMessage(
            "Used for device voice intercom and other functions"),
        "tr_permissionAudioTitle":
            MessageLookupByLibrary.simpleMessage("Access microphone"),
        "tr_permissionCameraSubtitle": MessageLookupByLibrary.simpleMessage(
            "Used for taking photos,scanning QR codes and other functions"),
        "tr_permissionCameraTitle":
            MessageLookupByLibrary.simpleMessage("Access Camera"),
        "tr_permissionLocationSubtitle": MessageLookupByLibrary.simpleMessage(
            "Used for real-time positioning and obtaining WiFi information during device networking"),
        "tr_permissionLocationTitle": MessageLookupByLibrary.simpleMessage(
            "Accessing location information"),
        "tr_permissionNotificationSubtitle":
            MessageLookupByLibrary.simpleMessage(
                "Used for APP to send message push notifications"),
        "tr_permissionNotificationTitle": MessageLookupByLibrary.simpleMessage(
            "Notification management permission"),
        "tr_permissionStatement":
            MessageLookupByLibrary.simpleMessage("Permission statement"),
        "tr_permissionStorageSubtitle": MessageLookupByLibrary.simpleMessage(
            "Used for saving device side images, videos, and other functions"),
        "tr_permissionStorageTitle":
            MessageLookupByLibrary.simpleMessage("Accessing storage"),
        "tr_person": MessageLookupByLibrary.simpleMessage("person"),
        "tr_personAcceptancePassed": m42,
        "tr_personInitiate":
            MessageLookupByLibrary.simpleMessage("Manually initiated"),
        "tr_personInspectionSource":
            MessageLookupByLibrary.simpleMessage("Manual inspection"),
        "tr_phoneLocation":
            MessageLookupByLibrary.simpleMessage("Follow mobile positioning"),
        "tr_pictureColorShift":
            MessageLookupByLibrary.simpleMessage("Picture color shift"),
        "tr_planName": MessageLookupByLibrary.simpleMessage("Plan Name"),
        "tr_planType": MessageLookupByLibrary.simpleMessage("Plan type"),
        "tr_planValidityPeriod":
            MessageLookupByLibrary.simpleMessage("Plan validity period"),
        "tr_plannedInspection":
            MessageLookupByLibrary.simpleMessage("Planned inspection"),
        "tr_platform": MessageLookupByLibrary.simpleMessage("Platform"),
        "tr_platformName":
            MessageLookupByLibrary.simpleMessage("level platform"),
        "tr_platformParsing":
            MessageLookupByLibrary.simpleMessage("Platform Parsing"),
        "tr_platformUser":
            MessageLookupByLibrary.simpleMessage("Platform users"),
        "tr_play": MessageLookupByLibrary.simpleMessage("Play"),
        "tr_playAddress": MessageLookupByLibrary.simpleMessage("Play address"),
        "tr_playAddressExpiration":
            MessageLookupByLibrary.simpleMessage("Expiration"),
        "tr_playAddressFlv":
            MessageLookupByLibrary.simpleMessage("FLV play address"),
        "tr_playAddressHls":
            MessageLookupByLibrary.simpleMessage("HLS play address"),
        "tr_playAddressRtmp":
            MessageLookupByLibrary.simpleMessage("RTMP play address"),
        "tr_playAddressRtsp":
            MessageLookupByLibrary.simpleMessage("RTSP play address"),
        "tr_playbackAddress":
            MessageLookupByLibrary.simpleMessage("Playback address"),
        "tr_playbackCompleted":
            MessageLookupByLibrary.simpleMessage("Playback completed"),
        "tr_playbackFailedPleaseRetry": MessageLookupByLibrary.simpleMessage(
            "Playback failed please retry"),
        "tr_playbackFailedXmtsCommunicationFailed":
            MessageLookupByLibrary.simpleMessage(
                "Playback failed, XMTS communication failed"),
        "tr_playbackTypeError":
            MessageLookupByLibrary.simpleMessage("Playback type error"),
        "tr_playerError": MessageLookupByLibrary.simpleMessage("Player error"),
        "tr_pleaseCategoryName": MessageLookupByLibrary.simpleMessage(
            "Please enter a category name"),
        "tr_pleaseChooseDate":
            MessageLookupByLibrary.simpleMessage("Please choose date"),
        "tr_pleaseChooseDepartment":
            MessageLookupByLibrary.simpleMessage("Please select department"),
        "tr_pleaseChooseDevice":
            MessageLookupByLibrary.simpleMessage("Please select a device"),
        "tr_pleaseChooseDeviceTags":
            MessageLookupByLibrary.simpleMessage("Please select device tags"),
        "tr_pleaseChooseExecutionTime": MessageLookupByLibrary.simpleMessage(
            "Please select Execution Time"),
        "tr_pleaseChooseInspectionPerson": MessageLookupByLibrary.simpleMessage(
            "Please select inspection personnel"),
        "tr_pleaseChoosePersons": MessageLookupByLibrary.simpleMessage(
            "Please select user personnel"),
        "tr_pleaseChooseRole":
            MessageLookupByLibrary.simpleMessage("Please select a role"),
        "tr_pleaseChooseRoles":
            MessageLookupByLibrary.simpleMessage("Please select roles"),
        "tr_pleaseChooseTagDevices": MessageLookupByLibrary.simpleMessage(
            "Please select the device under the tag"),
        "tr_pleaseChooseUserType":
            MessageLookupByLibrary.simpleMessage("Please select user type"),
        "tr_pleaseChooseValidityPeriod": MessageLookupByLibrary.simpleMessage(
            "Please select the validity period"),
        "tr_pleaseClick": MessageLookupByLibrary.simpleMessage("Please click"),
        "tr_pleaseEnter": MessageLookupByLibrary.simpleMessage("Please enter"),
        "tr_pleaseEnterActivityName":
            MessageLookupByLibrary.simpleMessage("Please enter activity name"),
        "tr_pleaseEnterAreaName":
            MessageLookupByLibrary.simpleMessage("Please enter area name"),
        "tr_pleaseEnterBitrate":
            MessageLookupByLibrary.simpleMessage("Please enter bitrate"),
        "tr_pleaseEnterContinuousDetectionCount":
            MessageLookupByLibrary.simpleMessage(
                "Please enter continuous detection count"),
        "tr_pleaseEnterCorrectRange":
            MessageLookupByLibrary.simpleMessage("Please enter correct range"),
        "tr_pleaseEnterIFrameInterval": MessageLookupByLibrary.simpleMessage(
            "Please enter I-frame interval"),
        "tr_pleaseEnterStaffNumber":
            MessageLookupByLibrary.simpleMessage("Please enter staff number"),
        "tr_pleaseEnterTemplateName":
            MessageLookupByLibrary.simpleMessage("Please enter template name"),
        "tr_pleaseEnterText":
            MessageLookupByLibrary.simpleMessage("Please Enter Text"),
        "tr_pleaseEnterTheContentOfTheEvaluationItem":
            MessageLookupByLibrary.simpleMessage(
                "Please enter the content of the evaluation item"),
        "tr_pleaseEnterTheEvaluationClassName":
            MessageLookupByLibrary.simpleMessage(
                "Please enter evaluation class name"),
        "tr_pleaseEnterTheInspectionNameToSearch":
            MessageLookupByLibrary.simpleMessage(
                "Please enter the inspection name to search"),
        "tr_pleaseEnterTheScoreOfTheEvaluationItem":
            MessageLookupByLibrary.simpleMessage(
                "Please enter the score of the evaluation item"),
        "tr_pleaseEnterVisitCount":
            MessageLookupByLibrary.simpleMessage("Please enter visit count"),
        "tr_pleaseEntereLigibilityScore": MessageLookupByLibrary.simpleMessage(
            "Please enter eligibility score"),
        "tr_pleaseEntryPlanName":
            MessageLookupByLibrary.simpleMessage("Please Enter Plan Name"),
        "tr_pleaseEntryPlanNameForSearch": MessageLookupByLibrary.simpleMessage(
            "Please enter the plan name to search"),
        "tr_pleaseEntryProblemDescribe": MessageLookupByLibrary.simpleMessage(
            "Please entry problem describe"),
        "tr_pleaseExitReplayPageAndRetry": MessageLookupByLibrary.simpleMessage(
            "Please exit the playback page and try again."),
        "tr_pleaseInputChannelNamePushSIPHint":
            MessageLookupByLibrary.simpleMessage(
                "Please enter the channel name/push SIP ID"),
        "tr_pleaseInputDeviceAccount": MessageLookupByLibrary.simpleMessage(
            "Please enter the device account"),
        "tr_pleaseInputDeviceName": MessageLookupByLibrary.simpleMessage(
            "Please enter the device name to search"),
        "tr_pleaseInputDevicePassword": MessageLookupByLibrary.simpleMessage(
            "Please enter the device password"),
        "tr_pleaseInputEmail":
            MessageLookupByLibrary.simpleMessage("Please enter email"),
        "tr_pleaseInputNameForSearch": MessageLookupByLibrary.simpleMessage(
            "Please input name for search"),
        "tr_pleaseInputNamePhoneEmail": MessageLookupByLibrary.simpleMessage(
            "Please enter username/phone number/email to search"),
        "tr_pleaseInputPhone":
            MessageLookupByLibrary.simpleMessage("Please enter phone number"),
        "tr_pleaseInputPlatformNameIDHit": MessageLookupByLibrary.simpleMessage(
            "Please enter the parent platform name/access ID"),
        "tr_pleaseInputRegionName": MessageLookupByLibrary.simpleMessage(
            "Please enter the region name to search"),
        "tr_pleaseInputSN": MessageLookupByLibrary.simpleMessage(
            "Please enter the serial number"),
        "tr_pleaseInputTwicePsd": MessageLookupByLibrary.simpleMessage(
            "Please enter the password again"),
        "tr_pleaseRetryLater":
            MessageLookupByLibrary.simpleMessage("Please retry later"),
        "tr_pleaseSaveCustomAreaFirst": MessageLookupByLibrary.simpleMessage(
            "Please save custom area first"),
        "tr_pleaseSelectAcceptancePerson":
            MessageLookupByLibrary.simpleMessage("Select the acceptor"),
        "tr_pleaseSelectActivityDate":
            MessageLookupByLibrary.simpleMessage("Please select activity date"),
        "tr_pleaseSelectAlgorithm":
            MessageLookupByLibrary.simpleMessage("Please select algorithm"),
        "tr_pleaseSelectAlgorithmAndDeviceFirst":
            MessageLookupByLibrary.simpleMessage(
                "Please select algorithm and device first"),
        "tr_pleaseSelectArea":
            MessageLookupByLibrary.simpleMessage("Please select area"),
        "tr_pleaseSelectAssignee":
            MessageLookupByLibrary.simpleMessage("Please select assignee."),
        "tr_pleaseSelectAtLeastOneArea": MessageLookupByLibrary.simpleMessage(
            "Please select at least one area"),
        "tr_pleaseSelectCityRegionName": MessageLookupByLibrary.simpleMessage(
            "Please select the city or region first"),
        "tr_pleaseSelectDepartmentToAssociateFaces":
            MessageLookupByLibrary.simpleMessage(
                "Please select the department to which the face needs to be associated"),
        "tr_pleaseSelectDept": MessageLookupByLibrary.simpleMessage(
            "Please select departments first"),
        "tr_pleaseSelectEndTime":
            MessageLookupByLibrary.simpleMessage("Please select the end time"),
        "tr_pleaseSelectInspectionDevice": MessageLookupByLibrary.simpleMessage(
            "Please select the inspection device"),
        "tr_pleaseSelectInspectionTemplate":
            MessageLookupByLibrary.simpleMessage(
                "Please select the inspection template"),
        "tr_pleaseSelectPassOrFail":
            MessageLookupByLibrary.simpleMessage("Please select pass or fail"),
        "tr_pleaseSelectPlan":
            MessageLookupByLibrary.simpleMessage("Please select plan"),
        "tr_pleaseSelectPlaybackTimeError": MessageLookupByLibrary.simpleMessage(
            "The current playback period cannot cross days. Please reselect the time period"),
        "tr_pleaseSelectRectificationItem":
            MessageLookupByLibrary.simpleMessage(
                "Please Select Rectification Item"),
        "tr_pleaseSelectRectificationPerson":
            MessageLookupByLibrary.simpleMessage(
                "Choose a rectification person"),
        "tr_pleaseSelectStartTime": MessageLookupByLibrary.simpleMessage(
            "Please select the start time"),
        "tr_pleaseSelectStore":
            MessageLookupByLibrary.simpleMessage("Please select store"),
        "tr_pleaseSelectTheDeviceFirst":
            MessageLookupByLibrary.simpleMessage("Please select device first"),
        "tr_pleaseSelectTheRecordingPlan": MessageLookupByLibrary.simpleMessage(
            "Please select the Recording plan"),
        "tr_pleaseSelectTime":
            MessageLookupByLibrary.simpleMessage("Please select a time"),
        "tr_pleaseSetEvaluationItems":
            MessageLookupByLibrary.simpleMessage("Please set evaluation items"),
        "tr_pleaseSetExecutionTime": MessageLookupByLibrary.simpleMessage(
            "Please set an execution time"),
        "tr_pleaseSetStaffNumberRange": MessageLookupByLibrary.simpleMessage(
            "Please set staff number range"),
        "tr_pleaseSign": MessageLookupByLibrary.simpleMessage("Please sign"),
        "tr_pleaseTagsCategoryName": MessageLookupByLibrary.simpleMessage(
            "Please enter a label category name"),
        "tr_pleaseTagsName":
            MessageLookupByLibrary.simpleMessage("Please enter a label name"),
        "tr_pleaseUnbindHasChildren": MessageLookupByLibrary.simpleMessage(
            "Please delete the node and its subordinate nodes\' devices first"),
        "tr_pleaseUnbindStoreId": MessageLookupByLibrary.simpleMessage(
            "Please unbind the following cloud stores before deleting them"),
        "tr_pleaseUploadFaceImage": MessageLookupByLibrary.simpleMessage(
            "Please take or upload a facial photo"),
        "tr_preciseTrafficFlow":
            MessageLookupByLibrary.simpleMessage("Precise traffic flow"),
        "tr_precisionDevicesTotal": m43,
        "tr_presetEvaluationTemplate":
            MessageLookupByLibrary.simpleMessage("Preset Evaluation Template"),
        "tr_pressAgainToExitTheApplication":
            MessageLookupByLibrary.simpleMessage(
                "Press again to exit the application"),
        "tr_previewAddress":
            MessageLookupByLibrary.simpleMessage("Preview address"),
        "tr_previewPlaybackDeviceReturnedNotFound":
            MessageLookupByLibrary.simpleMessage(
                "Preview/playback device returned Not Found"),
        "tr_previewPlaybackFailed":
            MessageLookupByLibrary.simpleMessage("Preview/playback failed"),
        "tr_previewPlaybackRequestTimeoutNoResponse":
            MessageLookupByLibrary.simpleMessage(
                "Preview/playback request timeout, no response"),
        "tr_previewPlaybackServerGatewayCannotProcess":
            MessageLookupByLibrary.simpleMessage(
                "Preview/playback server or gateway cannot process due to device overload or maintenance"),
        "tr_privacyPolicyFrontTip":
            MessageLookupByLibrary.simpleMessage("View full version"),
        "tr_privacyPolicyUpdate":
            MessageLookupByLibrary.simpleMessage("Privacy Policy Update"),
        "tr_privacyPolicyUpdateTip": MessageLookupByLibrary.simpleMessage(
            "Thank you for trusting and using the products and services of BcloudSaaS. In order to better protect the interests of users, we have updated the Privacy Policy and hereby send you this reminder. Please read carefully and fully understand the relevant terms. If you click \"Agree\", it means that you have read and agreed to the updated Privacy Policy, and BcloudSaaS will do its best to protect your legal rights and interests."),
        "tr_problemChain":
            MessageLookupByLibrary.simpleMessage("Problem chain"),
        "tr_problemDescription":
            MessageLookupByLibrary.simpleMessage("Problem description"),
        "tr_problemDescriptionCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage(
                "Problem description cannot be empty"),
        "tr_problemFound":
            MessageLookupByLibrary.simpleMessage("Problem Found"),
        "tr_processingDeadline":
            MessageLookupByLibrary.simpleMessage("Processing Deadline"),
        "tr_processingTime":
            MessageLookupByLibrary.simpleMessage("Processing time"),
        "tr_processor": MessageLookupByLibrary.simpleMessage("Handler"),
        "tr_proportion": MessageLookupByLibrary.simpleMessage("Proportion"),
        "tr_proportionUnqualifiedInspections":
            MessageLookupByLibrary.simpleMessage(
                "Proportion of Unqualified Inspections"),
        "tr_proportionValue":
            MessageLookupByLibrary.simpleMessage("proportion"),
        "tr_protocolChooseTip": MessageLookupByLibrary.simpleMessage(
            "When the network is poor, prioritize TCP"),
        "tr_protocolMessageParsingError": MessageLookupByLibrary.simpleMessage(
            "Protocol message parsing error"),
        "tr_psdCheckFailureTip": MessageLookupByLibrary.simpleMessage(
            "If the following password verification fails, you can manually enter the correct password"),
        "tr_psdCheckNoticeTip": MessageLookupByLibrary.simpleMessage(
            "Note: Password modification is not allowed when the device is offline!"),
        "tr_psdCheckSuccessTip": MessageLookupByLibrary.simpleMessage(
            "Verification successful! The following is the channel account and password"),
        "tr_psdKeyVerify":
            MessageLookupByLibrary.simpleMessage("Key verification"),
        "tr_psdVerification":
            MessageLookupByLibrary.simpleMessage("Password verification"),
        "tr_psdVerificationFail": MessageLookupByLibrary.simpleMessage(
            "Password verification failed"),
        "tr_psdVerificationTip": MessageLookupByLibrary.simpleMessage(
            "Verify whether the current default password is correct. If incorrect, enter the password to verify."),
        "tr_pureBlackWhite":
            MessageLookupByLibrary.simpleMessage("Pure black/white"),
        "tr_pushChannel": MessageLookupByLibrary.simpleMessage("Push channel"),
        "tr_pushSIPID": MessageLookupByLibrary.simpleMessage("Push SIP ID"),
        "tr_pwdConfig":
            MessageLookupByLibrary.simpleMessage("Password setting"),
        "tr_qrScan": MessageLookupByLibrary.simpleMessage("Scan"),
        "tr_quadrilateral":
            MessageLookupByLibrary.simpleMessage("Quadrilateral"),
        "tr_qualified": MessageLookupByLibrary.simpleMessage("Qualified"),
        "tr_qualifiedItem":
            MessageLookupByLibrary.simpleMessage("Qualified item"),
        "tr_queryNoDevicesTip": MessageLookupByLibrary.simpleMessage(
            "No device was found in the area, please redraw the area"),
        "tr_queryRecordingFailed":
            MessageLookupByLibrary.simpleMessage("Query recording failed"),
        "tr_randomShake": MessageLookupByLibrary.simpleMessage("Random shake"),
        "tr_ranking": MessageLookupByLibrary.simpleMessage("ranking"),
        "tr_rankingOfCameraIncidents":
            MessageLookupByLibrary.simpleMessage("Ranking of Camera Incidents"),
        "tr_realTimeBandwidth":
            MessageLookupByLibrary.simpleMessage("Real time bandwidth"),
        "tr_reason": MessageLookupByLibrary.simpleMessage("Reason"),
        "tr_recordCardAddress":
            MessageLookupByLibrary.simpleMessage("Local Playback Address"),
        "tr_recordCloudAddress":
            MessageLookupByLibrary.simpleMessage("Cloud Playback Address"),
        "tr_record_plan_not_enough_tip": MessageLookupByLibrary.simpleMessage(
            "Your cloud storage recording package authorization channels are insufficient. <NAME_EMAIL> to authorize more channels."),
        "tr_record_plan_upgrade_tip": MessageLookupByLibrary.simpleMessage(
            "<NAME_EMAIL> to upgrade your package."),
        "tr_recordingEndTime":
            MessageLookupByLibrary.simpleMessage("Recording End Time"),
        "tr_recordingFull":
            MessageLookupByLibrary.simpleMessage("Recording full"),
        "tr_recordingHasStopped":
            MessageLookupByLibrary.simpleMessage("Stop recording"),
        "tr_recordingPlanEndTime":
            MessageLookupByLibrary.simpleMessage("Recording Plan End Time"),
        "tr_recordingPlanStartTime":
            MessageLookupByLibrary.simpleMessage("Recording Plan Start Time"),
        "tr_recordingSchedule":
            MessageLookupByLibrary.simpleMessage("Recording Plan"),
        "tr_recordingSetting":
            MessageLookupByLibrary.simpleMessage("Recording setting"),
        "tr_recordingStartTime":
            MessageLookupByLibrary.simpleMessage("Recording Start Time"),
        "tr_recordingTime":
            MessageLookupByLibrary.simpleMessage("Recording Time"),
        "tr_recordingType":
            MessageLookupByLibrary.simpleMessage("Recording type"),
        "tr_rectificationComplete":
            MessageLookupByLibrary.simpleMessage("Passed"),
        "tr_rectificationCompletedLookingForwardToYourAcceptance":
            MessageLookupByLibrary.simpleMessage(
                "The rectification is complete, awaiting acceptance"),
        "tr_rectificationDeadline":
            MessageLookupByLibrary.simpleMessage("Deadline"),
        "tr_rectificationEvent":
            MessageLookupByLibrary.simpleMessage("Rectification event"),
        "tr_rectificationInstructions":
            MessageLookupByLibrary.simpleMessage("Remarks"),
        "tr_rectificationItem":
            MessageLookupByLibrary.simpleMessage("Rectification Item"),
        "tr_rectificationOverdue":
            MessageLookupByLibrary.simpleMessage("Rectification Overdue"),
        "tr_rectificationOverdueSort":
            MessageLookupByLibrary.simpleMessage("Rectification Overdue Sort"),
        "tr_rectificationOverdueTimes":
            MessageLookupByLibrary.simpleMessage("Rectification Overdue Times"),
        "tr_rectificationPerson":
            MessageLookupByLibrary.simpleMessage("Rectifier"),
        "tr_rectificationScope":
            MessageLookupByLibrary.simpleMessage("Rectification scope"),
        "tr_rectificationStatus":
            MessageLookupByLibrary.simpleMessage("Rectification Status"),
        "tr_rectificationSubmissionTime": MessageLookupByLibrary.simpleMessage(
            "Rectification Submission Time"),
        "tr_rectificationTime":
            MessageLookupByLibrary.simpleMessage("Rectification time"),
        "tr_rectificationTimeMustBeGreaterThanOneMinute":
            MessageLookupByLibrary.simpleMessage(
                "Rectification time must be greater than 1 minute"),
        "tr_rectify": MessageLookupByLibrary.simpleMessage("Rectification"),
        "tr_rectifyCount":
            MessageLookupByLibrary.simpleMessage("Rectify count"),
        "tr_referencePicture":
            MessageLookupByLibrary.simpleMessage("Reference Picture"),
        "tr_refreshFooterCanLoadingText": MessageLookupByLibrary.simpleMessage(
            "Release to start loading data"),
        "tr_refreshFooterFailedText":
            MessageLookupByLibrary.simpleMessage("Loading failed"),
        "tr_refreshFooterIdleText":
            MessageLookupByLibrary.simpleMessage("Pull-up loading"),
        "tr_refreshFooterLoadingText":
            MessageLookupByLibrary.simpleMessage("Loading..."),
        "tr_refreshFooterNoDataText":
            MessageLookupByLibrary.simpleMessage("No more"),
        "tr_refreshHeaderCompleteText":
            MessageLookupByLibrary.simpleMessage("Refresh is complete"),
        "tr_refreshHeaderFailedText":
            MessageLookupByLibrary.simpleMessage("Refresh failed"),
        "tr_refreshHeaderIdleText":
            MessageLookupByLibrary.simpleMessage("Drop down to refresh"),
        "tr_refreshHeaderRefreshingText":
            MessageLookupByLibrary.simpleMessage("Refreshing"),
        "tr_refreshHeaderReleaseText": MessageLookupByLibrary.simpleMessage(
            "Release your hand to refresh"),
        "tr_refreshText": MessageLookupByLibrary.simpleMessage("Refresh"),
        "tr_refreshed": MessageLookupByLibrary.simpleMessage("Refreshed"),
        "tr_registrationCycle": MessageLookupByLibrary.simpleMessage(
            "Registration cycle (seconds)"),
        "tr_registrationCycleHint":
            MessageLookupByLibrary.simpleMessage("Range 3600-86400 seconds"),
        "tr_registrationSwitch":
            MessageLookupByLibrary.simpleMessage("Registration switch"),
        "tr_registrationSwitchExplanation": MessageLookupByLibrary.simpleMessage(
            "When it is turned on, it means that it is communicating with the upper-level platform and cannot be edited; when the registration switch is turned off, the national standard cascade information can be edited"),
        "tr_relatedPerson":
            MessageLookupByLibrary.simpleMessage("Related Person"),
        "tr_releaseDate": MessageLookupByLibrary.simpleMessage("Release date"),
        "tr_remainingPath":
            MessageLookupByLibrary.simpleMessage("Remaining channels"),
        "tr_remainingRoad": m44,
        "tr_remaining_flow":
            MessageLookupByLibrary.simpleMessage("Remaining Data"),
        "tr_remark": MessageLookupByLibrary.simpleMessage("Remark"),
        "tr_remove": MessageLookupByLibrary.simpleMessage("Remove"),
        "tr_removeSuccessfully":
            MessageLookupByLibrary.simpleMessage("Removed successfully"),
        "tr_renewal": MessageLookupByLibrary.simpleMessage("Renewal"),
        "tr_replayChannelOccupied":
            MessageLookupByLibrary.simpleMessage("Replay channel occupied"),
        "tr_requestDeviceInfoFromDSMFailed":
            MessageLookupByLibrary.simpleMessage(
                "Request device info from DSM failed"),
        "tr_requestNotificationCamera": MessageLookupByLibrary.simpleMessage(
            "Explanation for Camera Permission: Used for taking photos, recording videos, etc."),
        "tr_requestNotificationLocation": MessageLookupByLibrary.simpleMessage(
            "Explanation for Location Permission: Used for accessing precise location information, searching for cameras and other device scenes"),
        "tr_requestNotificationPhoto": MessageLookupByLibrary.simpleMessage(
            "Explanation for Storage Card Permission: Used for reading photos, media content, and files on the storage card"),
        "tr_requestNotificationStory": MessageLookupByLibrary.simpleMessage(
            "Explanation for Storage Access Permission: Used for modifying or deleting photos, media content, and files on the storage card"),
        "tr_resetDevice": MessageLookupByLibrary.simpleMessage("Reset device"),
        "tr_resetDeviceFailure":
            MessageLookupByLibrary.simpleMessage("Reset device failed"),
        "tr_resetDeviceNode":
            MessageLookupByLibrary.simpleMessage("Reset default"),
        "tr_resetDeviceSuccess":
            MessageLookupByLibrary.simpleMessage("Reset device successfully"),
        "tr_resetDraw": MessageLookupByLibrary.simpleMessage("Redraw"),
        "tr_resetFlowInfo": MessageLookupByLibrary.simpleMessage(
            "Passenger flow data information is cleared at 24:00 every day"),
        "tr_resetNode": MessageLookupByLibrary.simpleMessage("Reset node"),
        "tr_resign": MessageLookupByLibrary.simpleMessage("Resign"),
        "tr_resolution": MessageLookupByLibrary.simpleMessage("Resolution"),
        "tr_restartDevice":
            MessageLookupByLibrary.simpleMessage("Restart device"),
        "tr_restartDeviceFailure":
            MessageLookupByLibrary.simpleMessage("Restart device failed"),
        "tr_restartDeviceSuccess":
            MessageLookupByLibrary.simpleMessage("Restart device successfully"),
        "tr_retAddDeviceFailed": MessageLookupByLibrary.simpleMessage(
            "1. Please plug in the power and data cables to ensure that the device is powered on and running normally;\n\n2. Please keep the device, mobile phone and router as close as possible (within 1 meter) to ensure successful signal reception;\n\n3. Please make sure that the router account and password you enter are correct;\n\n4. You can press the SET/RESET button on the device to restore the device to factory settings and then reconnect to the network."),
        "tr_retry": MessageLookupByLibrary.simpleMessage("Retry"),
        "tr_retryBTConnectFailed": MessageLookupByLibrary.simpleMessage(
            "Unable to connect to Bluetooth device, please try again!"),
        "tr_retrying": MessageLookupByLibrary.simpleMessage("Retrying..."),
        "tr_retryingNameCount": m45,
        "tr_returnHomePage":
            MessageLookupByLibrary.simpleMessage("Return to home page"),
        "tr_returnTime": MessageLookupByLibrary.simpleMessage("Return time"),
        "tr_rootNode": MessageLookupByLibrary.simpleMessage("Root node"),
        "tr_rtspProtocolError":
            MessageLookupByLibrary.simpleMessage("RTSP protocol error"),
        "tr_ruleConfiguration":
            MessageLookupByLibrary.simpleMessage("Rule configuration"),
        "tr_running": MessageLookupByLibrary.simpleMessage("Running"),
        "tr_runtime": MessageLookupByLibrary.simpleMessage("Runtime"),
        "tr_runtimePoint":
            MessageLookupByLibrary.simpleMessage("Runtime point"),
        "tr_safetyManagement":
            MessageLookupByLibrary.simpleMessage("Safety management"),
        "tr_saturday": MessageLookupByLibrary.simpleMessage("Sat"),
        "tr_scanDeviceQRCodeTip": MessageLookupByLibrary.simpleMessage(
            "Scan the QR code on your device"),
        "tr_scanLogin":
            MessageLookupByLibrary.simpleMessage("Login confirmation"),
        "tr_scanLoginSuccess": MessageLookupByLibrary.simpleMessage(
            "Scan the QR code to login successfully"),
        "tr_scanOnlyQRCodeTip": MessageLookupByLibrary.simpleMessage(
            "Place the QR code into the frame and scan"),
        "tr_scanQRNetworkCodeTips": MessageLookupByLibrary.simpleMessage(
            "Scan the QR code on the device, but can\'t find it, please use LAN to add devices"),
        "tr_scanReLoginTip": MessageLookupByLibrary.simpleMessage(
            "Please scan the QR code again to login"),
        "tr_scanWebLogin": MessageLookupByLibrary.simpleMessage(
            "Desktop version login confirmation"),
        "tr_sceneInspection":
            MessageLookupByLibrary.simpleMessage("Scene inspection"),
        "tr_score": MessageLookupByLibrary.simpleMessage("Score"),
        "tr_scoreCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("Score cannot be empty"),
        "tr_scoreValue": MessageLookupByLibrary.simpleMessage("Score"),
        "tr_scoresCanBeOnlyEnteredInNumbers":
            MessageLookupByLibrary.simpleMessage(
                "Scores can only be entered in numbers"),
        "tr_scoringItem": MessageLookupByLibrary.simpleMessage("Scoring Item"),
        "tr_scoringRate": MessageLookupByLibrary.simpleMessage("Scoring Rate"),
        "tr_screenGlitch":
            MessageLookupByLibrary.simpleMessage("Screen glitch"),
        "tr_screenshotFailedPleaseRetry": MessageLookupByLibrary.simpleMessage(
            "Screenshot failed, please retry"),
        "tr_searchAiBoxDeviceTip": MessageLookupByLibrary.simpleMessage(
            "The device and the box must be in the same LAN"),
        "tr_searchAlgorithmByName": MessageLookupByLibrary.simpleMessage(
            "Enter the algorithm name to search"),
        "tr_searchChannelTips":
            MessageLookupByLibrary.simpleMessage("Searched devices"),
        "tr_searchDevice":
            MessageLookupByLibrary.simpleMessage("Search device"),
        "tr_searchDeviceNameSN": MessageLookupByLibrary.simpleMessage(
            "Enter device name/serial number"),
        "tr_searchDeviceNoData": MessageLookupByLibrary.simpleMessage(
            "No corresponding device information was searched"),
        "tr_searchDeviceTags":
            MessageLookupByLibrary.simpleMessage("Search device tags"),
        "tr_searchFaceHitText": MessageLookupByLibrary.simpleMessage(
            "Please enter username/phone to search"),
        "tr_searchHistory":
            MessageLookupByLibrary.simpleMessage("Search history"),
        "tr_searchHitText": MessageLookupByLibrary.simpleMessage(
            "Please enter the store name to search"),
        "tr_searchJFDevice":
            MessageLookupByLibrary.simpleMessage("Scan and discover devices"),
        "tr_searchJFDeviceTip": MessageLookupByLibrary.simpleMessage(
            "NVR and cameras must be in the same LAN"),
        "tr_searchJFStatusFailureBehind":
            MessageLookupByLibrary.simpleMessage("rescan"),
        "tr_searchJFStatusFailureFront": MessageLookupByLibrary.simpleMessage(
            "No device was scanned, please "),
        "tr_searchJFStatusRefresh": MessageLookupByLibrary.simpleMessage(
            "Searching for devices, please wait..."),
        "tr_searchJFStatusResultBehind":
            MessageLookupByLibrary.simpleMessage("check the details"),
        "tr_searchJFStatusResultFront": m46,
        "tr_searchNameHitText": MessageLookupByLibrary.simpleMessage(
            "Please enter the name to search"),
        "tr_searchNoAddressData": MessageLookupByLibrary.simpleMessage(
            "Please search or locate to obtain location information"),
        "tr_searchNoDeviceTip": MessageLookupByLibrary.simpleMessage(
            "The device was not detected or has already been added"),
        "tr_searchNodeDevice": MessageLookupByLibrary.simpleMessage(
            "Enter the node or device name"),
        "tr_searchRegion": MessageLookupByLibrary.simpleMessage("Search area"),
        "tr_searchRegionNoAddressData": MessageLookupByLibrary.simpleMessage(
            "No nearby address information was searched"),
        "tr_searchRegionNoData": MessageLookupByLibrary.simpleMessage(
            "Place name information not searched"),
        "tr_searchStoreAddress":
            MessageLookupByLibrary.simpleMessage("Go search for store address"),
        "tr_searchStoreAddressAddressInfo":
            MessageLookupByLibrary.simpleMessage(
                "Please search for store address and location information"),
        "tr_searchStoreAddressNoAddressData": MessageLookupByLibrary.simpleMessage(
            "Please search or locate the store address for location information"),
        "tr_searchingDeviceTip": MessageLookupByLibrary.simpleMessage(
            "Scanning devices, please wait..."),
        "tr_searchingMonitoringDevicePleaseWait":
            MessageLookupByLibrary.simpleMessage(
                "Searching for camera, please wait a moment..."),
        "tr_second": MessageLookupByLibrary.simpleMessage("Second"),
        "tr_secondaryVerification":
            MessageLookupByLibrary.simpleMessage("Secondary verification"),
        "tr_secondsPerLoop":
            MessageLookupByLibrary.simpleMessage("Loop every second"),
        "tr_select": MessageLookupByLibrary.simpleMessage("Select"),
        "tr_selectAcceptancePerson":
            MessageLookupByLibrary.simpleMessage("Select the acceptor"),
        "tr_selectAlgorithm":
            MessageLookupByLibrary.simpleMessage("select algorithm"),
        "tr_selectAll": MessageLookupByLibrary.simpleMessage("Select all"),
        "tr_selectAtLeastOneParameter": MessageLookupByLibrary.simpleMessage(
            "Please select at least one parameter"),
        "tr_selectCategory":
            MessageLookupByLibrary.simpleMessage("Selected category"),
        "tr_selectDevice":
            MessageLookupByLibrary.simpleMessage("Select Device"),
        "tr_selectDeviceNode":
            MessageLookupByLibrary.simpleMessage("Please select node"),
        "tr_selectDeviceNodeResources": MessageLookupByLibrary.simpleMessage(
            "Please select the device node"),
        "tr_selectEvaluationCategory":
            MessageLookupByLibrary.simpleMessage("Select Evaluation Category"),
        "tr_selectEvaluationTemplate":
            MessageLookupByLibrary.simpleMessage("Select evaluation template"),
        "tr_selectLocalFileUpgrade":
            MessageLookupByLibrary.simpleMessage("Select Local File Upgrade"),
        "tr_selectMonth": MessageLookupByLibrary.simpleMessage("Select month"),
        "tr_selectMultiple":
            MessageLookupByLibrary.simpleMessage("Select multiple"),
        "tr_selectMultipleChoices": MessageLookupByLibrary.simpleMessage(
            "Go to select (Multiple choices available)"),
        "tr_selectOrAddConversation": MessageLookupByLibrary.simpleMessage(
            "Select a conversation or add one"),
        "tr_selectPerson": MessageLookupByLibrary.simpleMessage("Select"),
        "tr_selectPersonnel":
            MessageLookupByLibrary.simpleMessage("Select personnel"),
        "tr_selectRectificationPerson": MessageLookupByLibrary.simpleMessage(
            "Choose a rectification person"),
        "tr_selectStoragePackage":
            MessageLookupByLibrary.simpleMessage("Select Storage Package"),
        "tr_selectStores":
            MessageLookupByLibrary.simpleMessage("Please select stores"),
        "tr_selectTagsCategory": MessageLookupByLibrary.simpleMessage(
            "Please select a tag category"),
        "tr_selectTemplate":
            MessageLookupByLibrary.simpleMessage("Select Template"),
        "tr_selectTime": MessageLookupByLibrary.simpleMessage("Select time"),
        "tr_selectTimePeriod":
            MessageLookupByLibrary.simpleMessage("Select time period"),
        "tr_selectUsefulWiFi": MessageLookupByLibrary.simpleMessage(
            "Please select an available WiFi"),
        "tr_selectYear": MessageLookupByLibrary.simpleMessage("Select year"),
        "tr_selectYearAndMonth":
            MessageLookupByLibrary.simpleMessage("Select year and month"),
        "tr_selectableRangeIsInspectorEquipmentPermissions":
            MessageLookupByLibrary.simpleMessage(
                "Selectable range is inspector equipment permissions"),
        "tr_selectedTime":
            MessageLookupByLibrary.simpleMessage("Selected time"),
        "tr_sendMessageToDeepseek":
            MessageLookupByLibrary.simpleMessage("Send message to deepseek"),
        "tr_sensitivity": MessageLookupByLibrary.simpleMessage("Sensitivity"),
        "tr_serialNumberHasNotExistConnect": MessageLookupByLibrary.simpleMessage(
            "The serial number does not exist/the device is not connected to the Internet"),
        "tr_serverActualAccessChannelRate":
            MessageLookupByLibrary.simpleMessage(
                "Actual Access Channels/Requested Channels"),
        "tr_serverGateway":
            MessageLookupByLibrary.simpleMessage("Server Gateway"),
        "tr_serverPortNum":
            MessageLookupByLibrary.simpleMessage("SIP Server Port"),
        "tr_serverValidationTimeout":
            MessageLookupByLibrary.simpleMessage("Server validation timed out"),
        "tr_serviceValidationException": MessageLookupByLibrary.simpleMessage(
            "Service validation exception"),
        "tr_setAMapParam": MessageLookupByLibrary.simpleMessage("Map settings"),
        "tr_setBusinessHours":
            MessageLookupByLibrary.simpleMessage("Set business hours"),
        "tr_setDemandNumChannels":
            MessageLookupByLibrary.simpleMessage("Set Demand Num Channels"),
        "tr_setDetectionFrequency":
            MessageLookupByLibrary.simpleMessage("Set the detection frequency"),
        "tr_setDuration": MessageLookupByLibrary.simpleMessage("Set duration"),
        "tr_setNationalStandardConfiguration":
            MessageLookupByLibrary.simpleMessage(
                "Set national standard encoding configuration"),
        "tr_setNoHit": MessageLookupByLibrary.simpleMessage("Not set yet"),
        "tr_setPwdConfig": MessageLookupByLibrary.simpleMessage("Set password"),
        "tr_setSingleAndTotalStayDuration":
            MessageLookupByLibrary.simpleMessage(
                "Set single and total stay duration"),
        "tr_setStaffArrivalDepartureTime": MessageLookupByLibrary.simpleMessage(
            "Set staff arrival departure time"),
        "tr_setStaffDeduplicationRules": MessageLookupByLibrary.simpleMessage(
            "Set staff deduplication rules, conditions can be combined"),
        "tr_setStaffNumber":
            MessageLookupByLibrary.simpleMessage("Set staff number"),
        "tr_setStoreManagerHit": MessageLookupByLibrary.simpleMessage(
            "Please select Set Store Manager"),
        "tr_setSystemPermission":
            MessageLookupByLibrary.simpleMessage("System permissions"),
        "tr_setTime": MessageLookupByLibrary.simpleMessage("Time settings"),
        "tr_setUp": MessageLookupByLibrary.simpleMessage("Set up"),
        "tr_setUpToNameTimePeriods": m47,
        "tr_severe": MessageLookupByLibrary.simpleMessage("Serious"),
        "tr_shake": MessageLookupByLibrary.simpleMessage("Shake"),
        "tr_shakeDevice": MessageLookupByLibrary.simpleMessage("Shake device"),
        "tr_shakeForInspection":
            MessageLookupByLibrary.simpleMessage("Shake check"),
        "tr_shakeStore": MessageLookupByLibrary.simpleMessage("Shake store"),
        "tr_shakeToSearchMonitoring": MessageLookupByLibrary.simpleMessage(
            "Shake it to search for the camera"),
        "tr_showLanguage":
            MessageLookupByLibrary.simpleMessage("Display language"),
        "tr_sign": MessageLookupByLibrary.simpleMessage("Click to sign"),
        "tr_signInImage": MessageLookupByLibrary.simpleMessage("Sign in image"),
        "tr_signInInfo": MessageLookupByLibrary.simpleMessage("Sign in info"),
        "tr_signOutInfo": MessageLookupByLibrary.simpleMessage("Sign out info"),
        "tr_signTip": MessageLookupByLibrary.simpleMessage("Sign area"),
        "tr_signalingTransmission":
            MessageLookupByLibrary.simpleMessage("Signaling transmission"),
        "tr_similarity": MessageLookupByLibrary.simpleMessage("Similarity"),
        "tr_singlePerson":
            MessageLookupByLibrary.simpleMessage("Single person"),
        "tr_singlePersonGroup": MessageLookupByLibrary.simpleMessage(
            "Single-person entry (customer group)"),
        "tr_singleStayDuration":
            MessageLookupByLibrary.simpleMessage("Single stay duration"),
        "tr_singleStayDurationAboveMinutes": m48,
        "tr_sipServerPortHint": MessageLookupByLibrary.simpleMessage(
            "The port number is a 1-5 digit number"),
        "tr_sixSNCode":
            MessageLookupByLibrary.simpleMessage("6-digit serial number"),
        "tr_sixSNCodeTips": MessageLookupByLibrary.simpleMessage(
            "Length 6 digits, consisting of numbers"),
        "tr_sixSNNationsCodeTips": MessageLookupByLibrary.simpleMessage(
            "The length is 6 digits, consisting of numbers, and the serial numbers cannot be the same."),
        "tr_skyCloudStorage":
            MessageLookupByLibrary.simpleMessage("Sky Cloud Storage"),
        "tr_skyCloudStorageLoopRecording": MessageLookupByLibrary.simpleMessage(
            "Sky Cloud Storage Loop Recording"),
        "tr_smartAlarm": MessageLookupByLibrary.simpleMessage("Smart alarm"),
        "tr_smartAlert": MessageLookupByLibrary.simpleMessage("Smart Alert"),
        "tr_smartAlertTip": MessageLookupByLibrary.simpleMessage(
            "By configuring relevant parameters, the device can trigger human detection and motion detection"),
        "tr_smartEncoding":
            MessageLookupByLibrary.simpleMessage("Smart encoding"),
        "tr_snAdd": MessageLookupByLibrary.simpleMessage("SN Add"),
        "tr_softwareVersion":
            MessageLookupByLibrary.simpleMessage("Software Version"),
        "tr_softwareVersionReleaseDate":
            MessageLookupByLibrary.simpleMessage("Software Build Time"),
        "tr_someDevicesHaveNoConfiguredAlgorithm":
            MessageLookupByLibrary.simpleMessage(
                "Some devices have no configured algorithm"),
        "tr_someOptionsUnSelect":
            MessageLookupByLibrary.simpleMessage("There is not selected item"),
        "tr_speakerVolume":
            MessageLookupByLibrary.simpleMessage("Speaker volume"),
        "tr_spotCheck": MessageLookupByLibrary.simpleMessage("Spot check"),
        "tr_staffAcceptanceRectificationSort":
            MessageLookupByLibrary.simpleMessage(
                "Employee complete acceptance count ranking"),
        "tr_staffCompleteRectificationSort":
            MessageLookupByLibrary.simpleMessage(
                "Employee complete rectification count ranking"),
        "tr_staffDeduplication":
            MessageLookupByLibrary.simpleMessage("Staff deduplication"),
        "tr_staffDeduplicationRules":
            MessageLookupByLibrary.simpleMessage("Staff deduplication rules"),
        "tr_staffFilter": MessageLookupByLibrary.simpleMessage("Staff filter"),
        "tr_staffFilterRules":
            MessageLookupByLibrary.simpleMessage("Staff filter rules"),
        "tr_staffNumber": MessageLookupByLibrary.simpleMessage("Staff number"),
        "tr_staffOverdueRectificationSort":
            MessageLookupByLibrary.simpleMessage(
                "Employee overdue rectification count ranking"),
        "tr_startDate": MessageLookupByLibrary.simpleMessage("Start date"),
        "tr_startDownloadTip": MessageLookupByLibrary.simpleMessage(
            "Download task added successfully, please view in download manager"),
        "tr_startTime": MessageLookupByLibrary.simpleMessage("Start Time"),
        "tr_startTimeMustBeBeforeEndTime": MessageLookupByLibrary.simpleMessage(
            "The start time must be less than the end time"),
        "tr_statisticalDimension":
            MessageLookupByLibrary.simpleMessage("Statistical dimension"),
        "tr_statisticalPeriod":
            MessageLookupByLibrary.simpleMessage("Statistical period"),
        "tr_statistics": MessageLookupByLibrary.simpleMessage("Statistics"),
        "tr_statistics1": MessageLookupByLibrary.simpleMessage("Statistics"),
        "tr_statisticsTime":
            MessageLookupByLibrary.simpleMessage("Statistics time"),
        "tr_stayDuration":
            MessageLookupByLibrary.simpleMessage("Stay duration"),
        "tr_stayRate": MessageLookupByLibrary.simpleMessage("Stay rate"),
        "tr_stopBTNetConfigure": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to terminate Bluetooth pairing?"),
        "tr_stopFailedDownloadingTask": MessageLookupByLibrary.simpleMessage(
            "Stop failed downloading task"),
        "tr_stopNetConfigure": MessageLookupByLibrary.simpleMessage(
            "Currently, the network is being configured quickly and scanned. Are you sure you want to interrupt the network configuration?"),
        "tr_stopRecording":
            MessageLookupByLibrary.simpleMessage("Stop recording"),
        "tr_stopTime": MessageLookupByLibrary.simpleMessage("End time"),
        "tr_storageCardManagement":
            MessageLookupByLibrary.simpleMessage("Storage card management"),
        "tr_storageManagement":
            MessageLookupByLibrary.simpleMessage("Storage management"),
        "tr_store": MessageLookupByLibrary.simpleMessage("Store"),
        "tr_storeAcceptanceCount":
            MessageLookupByLibrary.simpleMessage("Acceptance count"),
        "tr_storeAddress":
            MessageLookupByLibrary.simpleMessage("Store address"),
        "tr_storeAddressHit":
            MessageLookupByLibrary.simpleMessage("Please enter store address"),
        "tr_storeAndAlgorithm":
            MessageLookupByLibrary.simpleMessage("Stores and algorithms"),
        "tr_storeChannelDevices": m49,
        "tr_storeConversionRate":
            MessageLookupByLibrary.simpleMessage("In store conversion rate"),
        "tr_storeConversionRateTip": MessageLookupByLibrary.simpleMessage(
            "Store conversion rate\n1. Store conversion rate=Number of customers entering the store/Total number of customers entering the store\n2. Total store entry rate: Number of customers entering the store/Total number of customers entering the store (Store entry traffic+Pass-by traffic)\n3. Store entry customer group: Number of customers entering the store (single person/batch of two people/batch of three people/batch of multiple people)/Total number of customers entering the store (single person/batch of two people/batch of three people/batch of multiple people)"),
        "tr_storeCoverageRateExplain": MessageLookupByLibrary.simpleMessage(
            "Number of stores inspected during the statistical period / all stores"),
        "tr_storeDataOverview":
            MessageLookupByLibrary.simpleMessage("Store data overview"),
        "tr_storeDeleted":
            MessageLookupByLibrary.simpleMessage("Store deleted"),
        "tr_storeDetail": MessageLookupByLibrary.simpleMessage("Store Details"),
        "tr_storeDeviceInformation":
            MessageLookupByLibrary.simpleMessage("Store device information"),
        "tr_storeDevices":
            MessageLookupByLibrary.simpleMessage("Store devices"),
        "tr_storeDimension":
            MessageLookupByLibrary.simpleMessage("Store Dimension"),
        "tr_storeEntry": MessageLookupByLibrary.simpleMessage("Store entry"),
        "tr_storeEntryExitConsideredStaff": m50,
        "tr_storeEntryRate":
            MessageLookupByLibrary.simpleMessage("Store entry rate"),
        "tr_storeEventAnalysis":
            MessageLookupByLibrary.simpleMessage("Store event analysis"),
        "tr_storeEventCount":
            MessageLookupByLibrary.simpleMessage("Event count"),
        "tr_storeEventRanking":
            MessageLookupByLibrary.simpleMessage("Store event ranking"),
        "tr_storeExceptionInspectionNotPossible":
            MessageLookupByLibrary.simpleMessage(
                "Store exception, inspection not possible"),
        "tr_storeFloorPlan":
            MessageLookupByLibrary.simpleMessage("Store floor plan"),
        "tr_storeFlow": MessageLookupByLibrary.simpleMessage("Store flow"),
        "tr_storeFlowRanking": MessageLookupByLibrary.simpleMessage(
            "Store passenger flow ranking"),
        "tr_storeHasNoPassengerFlowCameraPleaseAddFirst":
            MessageLookupByLibrary.simpleMessage(
                "The store does not have a passenger flow statistics camera. Please add a passenger flow statistics camera first."),
        "tr_storeHeatMapConfiguration": MessageLookupByLibrary.simpleMessage(
            "Store heat map configuration"),
        "tr_storeInspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("Store inspection analysis"),
        "tr_storeInspectionCompletionRate":
            MessageLookupByLibrary.simpleMessage(
                "Store inspection completion rate"),
        "tr_storeInspectionCoverageDetail":
            MessageLookupByLibrary.simpleMessage("Inspection coverage"),
        "tr_storeInspectionCoverageRate": MessageLookupByLibrary.simpleMessage(
            "Store inspection coverage rate"),
        "tr_storeInspectionOverview":
            MessageLookupByLibrary.simpleMessage("Store inspection overview"),
        "tr_storeInspectorSign":
            MessageLookupByLibrary.simpleMessage("Store inspector sign"),
        "tr_storeManager":
            MessageLookupByLibrary.simpleMessage("Store Manager"),
        "tr_storeManagerSign":
            MessageLookupByLibrary.simpleMessage("Store manager sign"),
        "tr_storeName": MessageLookupByLibrary.simpleMessage("Store name"),
        "tr_storeNameHit":
            MessageLookupByLibrary.simpleMessage("Please enter store name"),
        "tr_storeNoPermission":
            MessageLookupByLibrary.simpleMessage("Store no permission"),
        "tr_storeOverdueCount":
            MessageLookupByLibrary.simpleMessage("Overdue count"),
        "tr_storeQuestionRanking": MessageLookupByLibrary.simpleMessage(
            "Ranking of problems found in stores"),
        "tr_storeResources":
            MessageLookupByLibrary.simpleMessage("Store resources"),
        "tr_storeSelfInspection":
            MessageLookupByLibrary.simpleMessage("Store self-inspection"),
        "tr_storeTraffic":
            MessageLookupByLibrary.simpleMessage("Inbound customer flow"),
        "tr_storeTrafficNoDeduplication": MessageLookupByLibrary.simpleMessage(
            "In-store customer flow (not deduplicated)"),
        "tr_storeTrafficSettings":
            MessageLookupByLibrary.simpleMessage("Store traffic settings"),
        "tr_storeTrafficStatistics": MessageLookupByLibrary.simpleMessage(
            "Store passenger flow statistics"),
        "tr_storesPendingInspectionContinue": MessageLookupByLibrary.simpleMessage(
            "There are stores pending inspection in this random check. Do you want to continue?"),
        "tr_subAccountActivation":
            MessageLookupByLibrary.simpleMessage("Subaccount Activation"),
        "tr_subDate": MessageLookupByLibrary.simpleMessage("Date"),
        "tr_subStream": MessageLookupByLibrary.simpleMessage("Sub stream"),
        "tr_subTitlePermission":
            MessageLookupByLibrary.simpleMessage("Permission"),
        "tr_subTitleReset": MessageLookupByLibrary.simpleMessage(
            "After the device is restored to factory settings, the device cannot connect to WIFI and related functions cannot operate normally"),
        "tr_subTitleRestart": MessageLookupByLibrary.simpleMessage(
            "After the device restarts, you need to wait for a while before the function returns to normal"),
        "tr_submissionTime":
            MessageLookupByLibrary.simpleMessage("Submission Time"),
        "tr_submitRectificationTaskToYou":
            MessageLookupByLibrary.simpleMessage("submitted events"),
        "tr_submitted": MessageLookupByLibrary.simpleMessage("Submitted"),
        "tr_submittedEvent":
            MessageLookupByLibrary.simpleMessage("Submitted Event"),
        "tr_submittedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Submission successful."),
        "tr_success": MessageLookupByLibrary.simpleMessage("Success"),
        "tr_successPassed": MessageLookupByLibrary.simpleMessage("Pass"),
        "tr_sunday": MessageLookupByLibrary.simpleMessage("Sun"),
        "tr_supervisionInspection":
            MessageLookupByLibrary.simpleMessage("Supervision inspection"),
        "tr_support": MessageLookupByLibrary.simpleMessage("Support"),
        "tr_supportAlgorithms":
            MessageLookupByLibrary.simpleMessage("Support algorithms"),
        "tr_supportedCentralAlgorithms":
            MessageLookupByLibrary.simpleMessage("Supported core algorithms"),
        "tr_supportedEdgeAlgorithms":
            MessageLookupByLibrary.simpleMessage("Supported edge algorithms"),
        "tr_surplusNameWay": m51,
        "tr_surrounding": MessageLookupByLibrary.simpleMessage("Surrounding"),
        "tr_switchEmail": MessageLookupByLibrary.simpleMessage("Switch email"),
        "tr_switchPhone":
            MessageLookupByLibrary.simpleMessage("Switch phone number"),
        "tr_switchPlatform":
            MessageLookupByLibrary.simpleMessage("Switch the platform"),
        "tr_switchPlatformSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Successfully transferred to platform"),
        "tr_switchPlatformTip": MessageLookupByLibrary.simpleMessage(
            "The following are non-platform users, you can switch it to platform users"),
        "tr_switchingDevicesPleaseWait": MessageLookupByLibrary.simpleMessage(
            "Switching devices please wait"),
        "tr_switchingStreamPleaseWait": MessageLookupByLibrary.simpleMessage(
            "Switching the main and auxiliary streams, please wait"),
        "tr_syncInterceptTitle": MessageLookupByLibrary.simpleMessage(
            "Authorization Intercept Details"),
        "tr_syncSubordinateChannels": MessageLookupByLibrary.simpleMessage(
            "Synchronize modification of subordinate channels"),
        "tr_tagHasNoDeviceTags": MessageLookupByLibrary.simpleMessage(
            "No device tags have been added yet"),
        "tr_takePhotoForSignin":
            MessageLookupByLibrary.simpleMessage("Take photo for sign in"),
        "tr_takePhotoForSigninTip": MessageLookupByLibrary.simpleMessage(
            "Please take scene photo for sign in"),
        "tr_talk": MessageLookupByLibrary.simpleMessage("Talk"),
        "tr_talkChannelOccupied":
            MessageLookupByLibrary.simpleMessage("Talk channel occupied"),
        "tr_tapToAdd": MessageLookupByLibrary.simpleMessage("Tap to add"),
        "tr_targetBox": MessageLookupByLibrary.simpleMessage("Target box"),
        "tr_targetInformation":
            MessageLookupByLibrary.simpleMessage("Target Information"),
        "tr_task": MessageLookupByLibrary.simpleMessage("Task"),
        "tr_taskCreationTime":
            MessageLookupByLibrary.simpleMessage("Task creation time"),
        "tr_taskExecutionDate":
            MessageLookupByLibrary.simpleMessage("Task execution date"),
        "tr_taskExecutionMaximum": m52,
        "tr_taskExecutionTime":
            MessageLookupByLibrary.simpleMessage("Task execution time"),
        "tr_taskExists": MessageLookupByLibrary.simpleMessage(
            "The task has already been assigned, there\'s no need for duplication."),
        "tr_taskIssuanceTime":
            MessageLookupByLibrary.simpleMessage("Task issue time"),
        "tr_taskIssued": MessageLookupByLibrary.simpleMessage("Task issued"),
        "tr_taskName": MessageLookupByLibrary.simpleMessage("Task name"),
        "tr_taskSubmissionTime":
            MessageLookupByLibrary.simpleMessage("Task Submission Time"),
        "tr_templateDetails":
            MessageLookupByLibrary.simpleMessage("Template Details"),
        "tr_templateName":
            MessageLookupByLibrary.simpleMessage("Template Name"),
        "tr_text": MessageLookupByLibrary.simpleMessage("Text"),
        "tr_theDayBeforeYesterday":
            MessageLookupByLibrary.simpleMessage("Day before yesterday"),
        "tr_thenExecuteAction":
            MessageLookupByLibrary.simpleMessage("Just (perform the action)"),
        "tr_thereAreStillAssessmentItemsWithoutResultsSet":
            MessageLookupByLibrary.simpleMessage(
                "There are still evaluation items without results set"),
        "tr_thereMustBeAtLeastOneEvaluationItem":
            MessageLookupByLibrary.simpleMessage(
                "There must be at least one evaluation item"),
        "tr_thinking": MessageLookupByLibrary.simpleMessage("Thinking"),
        "tr_thinkingFailed":
            MessageLookupByLibrary.simpleMessage("Thinking failed"),
        "tr_thinkingFailedTip": MessageLookupByLibrary.simpleMessage(
            "Service busy, please try again later"),
        "tr_thinkingPleaseWait":
            MessageLookupByLibrary.simpleMessage("Thinking, please wait"),
        "tr_thisCompanyAddExceptions": MessageLookupByLibrary.simpleMessage(
            "The device has been added by this company, please contact the administrator to confirm. If necessary, you can contact the administrator to assign the device to you."),
        "tr_thisDeviceHasNoConfiguredAlgorithm":
            MessageLookupByLibrary.simpleMessage(
                "No algorithms are currently configured for this device."),
        "tr_thisMonth": MessageLookupByLibrary.simpleMessage("This month"),
        "tr_thisPlatformName":
            MessageLookupByLibrary.simpleMessage("This platform name"),
        "tr_thisRecordHasNoImage": MessageLookupByLibrary.simpleMessage(
            "This record does not have any images."),
        "tr_thisWeek": MessageLookupByLibrary.simpleMessage("This week"),
        "tr_thisYear": MessageLookupByLibrary.simpleMessage("This year"),
        "tr_this_month": MessageLookupByLibrary.simpleMessage("Month"),
        "tr_this_week": MessageLookupByLibrary.simpleMessage("Week"),
        "tr_this_year": MessageLookupByLibrary.simpleMessage("Year"),
        "tr_threePeople": MessageLookupByLibrary.simpleMessage("Three people"),
        "tr_threePersonGroup": MessageLookupByLibrary.simpleMessage(
            "Three-person entry (customer group)"),
        "tr_thursday": MessageLookupByLibrary.simpleMessage("Thur"),
        "tr_time": MessageLookupByLibrary.simpleMessage("Time"),
        "tr_timeFilter": MessageLookupByLibrary.simpleMessage("Time filter"),
        "tr_timeOverdueTimes":
            MessageLookupByLibrary.simpleMessage("Overdue Times"),
        "tr_timePeriod": MessageLookupByLibrary.simpleMessage("Time period"),
        "tr_timePoint": MessageLookupByLibrary.simpleMessage("time point"),
        "tr_time_c": MessageLookupByLibrary.simpleMessage("Times"),
        "tr_timesNoWarning":
            MessageLookupByLibrary.simpleMessage("No warnings"),
        "tr_titleAIAlgorithm":
            MessageLookupByLibrary.simpleMessage("AI algorithm"),
        "tr_titleCollect": MessageLookupByLibrary.simpleMessage("Collect"),
        "tr_titleContent":
            MessageLookupByLibrary.simpleMessage("Title content"),
        "tr_titleDeviceList":
            MessageLookupByLibrary.simpleMessage("Device list"),
        "tr_titleDeviceNodeResources":
            MessageLookupByLibrary.simpleMessage("Device node resources"),
        "tr_titleEventAnalysis":
            MessageLookupByLibrary.simpleMessage("Event analysis"),
        "tr_titleImprovements":
            MessageLookupByLibrary.simpleMessage("Rectification Progress"),
        "tr_titleReset":
            MessageLookupByLibrary.simpleMessage("Confirm factory reset"),
        "tr_titleRestart": MessageLookupByLibrary.simpleMessage(
            "Confirm to restart the device"),
        "tr_titleSelectStore":
            MessageLookupByLibrary.simpleMessage("Select store"),
        "tr_titleTime": MessageLookupByLibrary.simpleMessage("Title time"),
        "tr_titleViewPassedItems":
            MessageLookupByLibrary.simpleMessage("View Passed Items"),
        "tr_titleWorkbenchApplication":
            MessageLookupByLibrary.simpleMessage("Function"),
        "tr_to": MessageLookupByLibrary.simpleMessage("to"),
        "tr_today": MessageLookupByLibrary.simpleMessage("Today"),
        "tr_todayTraffic":
            MessageLookupByLibrary.simpleMessage("Today traffic"),
        "tr_tokenParsingFailed":
            MessageLookupByLibrary.simpleMessage("Token parsing failed"),
        "tr_totalAlarmCount":
            MessageLookupByLibrary.simpleMessage("Total alarm count"),
        "tr_totalCallCount":
            MessageLookupByLibrary.simpleMessage("Total call count"),
        "tr_totalCount": MessageLookupByLibrary.simpleMessage("Total count"),
        "tr_totalNameWay": m53,
        "tr_totalNumberFailures": MessageLookupByLibrary.simpleMessage(
            "Total number of nonconformities"),
        "tr_totalPeople": MessageLookupByLibrary.simpleMessage("Total people"),
        "tr_totalScore": MessageLookupByLibrary.simpleMessage("Total Score"),
        "tr_totalStayDuration":
            MessageLookupByLibrary.simpleMessage("Total stay duration"),
        "tr_totalStayDurationAboveHours": m54,
        "tr_totalStoreEntryRateProportion":
            MessageLookupByLibrary.simpleMessage(
                "The proportion of total store entry rate"),
        "tr_totalTraffic":
            MessageLookupByLibrary.simpleMessage("Total passenger flow"),
        "tr_totalTrafficNoDeduplication": MessageLookupByLibrary.simpleMessage(
            "Total customer flow (not deduplicated)"),
        "tr_tr_btConnectNetworkConfiguration": MessageLookupByLibrary.simpleMessage(
            "The connection process may take 1-2 minutes, please wait for a moment."),
        "tr_tr_btConnectNetworkConfigurationTip":
            MessageLookupByLibrary.simpleMessage(
                "Keep routers, mobile phones and devices as close as possible..."),
        "tr_trackingTrajectory":
            MessageLookupByLibrary.simpleMessage("Tracking trajectory"),
        "tr_trafficExcess":
            MessageLookupByLibrary.simpleMessage("Traffic excess"),
        "tr_trafficHeatZone":
            MessageLookupByLibrary.simpleMessage("Traffic heat zone"),
        "tr_trafficHeatZoneAlgorithmNotEnabledContactB2b":
            MessageLookupByLibrary.simpleMessage(
                "The traffic heat zone algorithm is not enabled and cannot be configured. <NAME_EMAIL> to enable it."),
        "tr_trafficTrend":
            MessageLookupByLibrary.simpleMessage("Traffic trend"),
        "tr_transcodingInProgress":
            MessageLookupByLibrary.simpleMessage("Transcoding in progress."),
        "tr_transcodingTemplate":
            MessageLookupByLibrary.simpleMessage("Transcoding template"),
        "tr_transparentAudioTemplate":
            MessageLookupByLibrary.simpleMessage("Transparent audio template"),
        "tr_transparentTransmission":
            MessageLookupByLibrary.simpleMessage("Transparent transmission"),
        "tr_triangle": MessageLookupByLibrary.simpleMessage("Triangle"),
        "tr_tryCaptureAction":
            MessageLookupByLibrary.simpleMessage("Capture again"),
        "tr_tuesday": MessageLookupByLibrary.simpleMessage("Tue"),
        "tr_turnOff": MessageLookupByLibrary.simpleMessage("Turn off"),
        "tr_turnOn": MessageLookupByLibrary.simpleMessage("Turn on"),
        "tr_twiceAddDevice":
            MessageLookupByLibrary.simpleMessage("Keep adding"),
        "tr_twoPeople": MessageLookupByLibrary.simpleMessage("Two people"),
        "tr_type": MessageLookupByLibrary.simpleMessage("Type"),
        "tr_typeNormal": MessageLookupByLibrary.simpleMessage("Standard"),
        "tr_typeSatellite": MessageLookupByLibrary.simpleMessage("Satellite"),
        "tr_unableToGetImage":
            MessageLookupByLibrary.simpleMessage("Unable to getImage"),
        "tr_uncompleted": MessageLookupByLibrary.simpleMessage("Uncompleted"),
        "tr_underAcceptance":
            MessageLookupByLibrary.simpleMessage("In Acceptance"),
        "tr_underRectification":
            MessageLookupByLibrary.simpleMessage("In rectification"),
        "tr_unitTimes": MessageLookupByLibrary.simpleMessage("Unit: times"),
        "tr_unnormal": MessageLookupByLibrary.simpleMessage("Abnormal"),
        "tr_unqualified": MessageLookupByLibrary.simpleMessage("Unqualified"),
        "tr_unqualifiedCameraRanking":
            MessageLookupByLibrary.simpleMessage("Unqualified points"),
        "tr_unqualifiedItem":
            MessageLookupByLibrary.simpleMessage("Unqualified item"),
        "tr_upcomingTasks": MessageLookupByLibrary.simpleMessage("To Do items"),
        "tr_updateRedisFailed":
            MessageLookupByLibrary.simpleMessage("Update Redis failed"),
        "tr_updateTime": MessageLookupByLibrary.simpleMessage("Update time"),
        "tr_upgradeFirmwareTip": MessageLookupByLibrary.simpleMessage(
            "This device has new device firmware that can be upgraded. Do you want to upgrade?"),
        "tr_upgradePackage":
            MessageLookupByLibrary.simpleMessage("Upgrade Package"),
        "tr_uploadFaceImage":
            MessageLookupByLibrary.simpleMessage("Upload Face"),
        "tr_uploadFaceImageBtn":
            MessageLookupByLibrary.simpleMessage("Click to take photos/upload"),
        "tr_uploadStoreFloorPlan": MessageLookupByLibrary.simpleMessage(
            "Please upload store floor plan"),
        "tr_uploadUpgradeFail": MessageLookupByLibrary.simpleMessage(
            "Firmware upload and upgrade failed"),
        "tr_uploadingImagePleaseWait":
            MessageLookupByLibrary.simpleMessage("Uploading image please wait"),
        "tr_urlAuthenticationFailed":
            MessageLookupByLibrary.simpleMessage("URL authentication failed"),
        "tr_urlConcurrencyLimited":
            MessageLookupByLibrary.simpleMessage("URL concurrency limited"),
        "tr_urlExceedsAllowedConcurrency": MessageLookupByLibrary.simpleMessage(
            "URL exceeds allowed concurrency"),
        "tr_urlExpired": MessageLookupByLibrary.simpleMessage("URL expired"),
        "tr_urlFormatError":
            MessageLookupByLibrary.simpleMessage("URL format error"),
        "tr_urlNotAllowedToPlay":
            MessageLookupByLibrary.simpleMessage("URL not allowed to play"),
        "tr_urlVerificationFailedGwmCommunicationFailed":
            MessageLookupByLibrary.simpleMessage(
                "URL verification failed, GWM communication failed"),
        "tr_usageRatio": MessageLookupByLibrary.simpleMessage("Usage Ratio"),
        "tr_useCase": MessageLookupByLibrary.simpleMessage("Usage"),
        "tr_useTemplate": MessageLookupByLibrary.simpleMessage("Use Template"),
        "tr_usedAlgorithms":
            MessageLookupByLibrary.simpleMessage("Used algorithms"),
        "tr_usedNameWay": m55,
        "tr_usedNumChannels":
            MessageLookupByLibrary.simpleMessage("Used Num Channels"),
        "tr_userForeverPeriod":
            MessageLookupByLibrary.simpleMessage("Permanent validity"),
        "tr_userHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("User has been deleted"),
        "tr_userHasPeriod":
            MessageLookupByLibrary.simpleMessage("User validity period"),
        "tr_userNoQueryPermission": MessageLookupByLibrary.simpleMessage(
            "User has no query permission"),
        "tr_userNotBoundToFacebook":
            MessageLookupByLibrary.simpleMessage("User not bound to Facebook"),
        "tr_userNotBoundToGoogle":
            MessageLookupByLibrary.simpleMessage("User not bound to Google"),
        "tr_userPermissionConfig": MessageLookupByLibrary.simpleMessage(
            "User permission configuration"),
        "tr_userType": MessageLookupByLibrary.simpleMessage("User type"),
        "tr_userTypeNotPlatformTip": MessageLookupByLibrary.simpleMessage(
            "Non platform users (for facial database management only)"),
        "tr_userTypePlatformTip": MessageLookupByLibrary.simpleMessage(
            "Platform users (for facial database management only)"),
        "tr_validityPeriod": MessageLookupByLibrary.simpleMessage("Validity"),
        "tr_variableBitRate":
            MessageLookupByLibrary.simpleMessage("Variable bit rate"),
        "tr_verification": MessageLookupByLibrary.simpleMessage("Go to verify"),
        "tr_verificationActivation": MessageLookupByLibrary.simpleMessage(
            "Verification code activation"),
        "tr_verificationFail":
            MessageLookupByLibrary.simpleMessage("Verification failed"),
        "tr_verificationSuccess":
            MessageLookupByLibrary.simpleMessage("Verification successful"),
        "tr_verifyCodeLogin":
            MessageLookupByLibrary.simpleMessage("Verification code login"),
        "tr_versionUpdateCancel":
            MessageLookupByLibrary.simpleMessage("Do not upgrade"),
        "tr_versionUpdateConfirm":
            MessageLookupByLibrary.simpleMessage("upgrade now"),
        "tr_versionUpdateContent":
            MessageLookupByLibrary.simpleMessage("Update content"),
        "tr_versionUpdateFindLatestVersion":
            MessageLookupByLibrary.simpleMessage("Discovering new versions"),
        "tr_versionUpdateLatestDownload":
            MessageLookupByLibrary.simpleMessage("Updated version"),
        "tr_versionUpdateVersionLatestTip":
            MessageLookupByLibrary.simpleMessage("It\'s the latest version"),
        "tr_verticalFlip":
            MessageLookupByLibrary.simpleMessage("Vertical flip"),
        "tr_video": MessageLookupByLibrary.simpleMessage("Video"),
        "tr_videoBitrateKbps": MessageLookupByLibrary.simpleMessage(
            "Video bitrate (kbps), value range 0~8192"),
        "tr_videoBlocking":
            MessageLookupByLibrary.simpleMessage("Video Blocking"),
        "tr_videoBlockingTip": MessageLookupByLibrary.simpleMessage(
            "When the camera image is blocked by an object, an alarm is triggered"),
        "tr_videoCloudBase":
            MessageLookupByLibrary.simpleMessage("Video Cloud Base"),
        "tr_videoDisplayAlarmRules":
            MessageLookupByLibrary.simpleMessage("Show alarm rules in video"),
        "tr_videoDuration":
            MessageLookupByLibrary.simpleMessage("Video duration"),
        "tr_videoInspection":
            MessageLookupByLibrary.simpleMessage("Video inspection"),
        "tr_videoOcclusionAlarm":
            MessageLookupByLibrary.simpleMessage("Video occlusion alarm"),
        "tr_videoPlayAddress":
            MessageLookupByLibrary.simpleMessage("Video play address"),
        "tr_videoWatermark":
            MessageLookupByLibrary.simpleMessage("Video Watermark"),
        "tr_viewDepartmentInfoOperation": MessageLookupByLibrary.simpleMessage(
            "View department structure and related operations"),
        "tr_viewDownloadList":
            MessageLookupByLibrary.simpleMessage("View download list"),
        "tr_viewEvent": MessageLookupByLibrary.simpleMessage("View Event"),
        "tr_viewIssueDetails":
            MessageLookupByLibrary.simpleMessage("View issue details"),
        "tr_viewRecordingSchedule":
            MessageLookupByLibrary.simpleMessage("Video recording"),
        "tr_viewUserInfoOperation": MessageLookupByLibrary.simpleMessage(
            "View and manage user information and related operations"),
        "tr_viewUserRoleInfoOperation": MessageLookupByLibrary.simpleMessage(
            "View management role authority information and related operations"),
        "tr_visibleDeptTip": MessageLookupByLibrary.simpleMessage(
            "The above are the departments to which they belong. The departments to which they belong are visible departments by default."),
        "tr_visibleRangeDept":
            MessageLookupByLibrary.simpleMessage("Visible Department"),
        "tr_visitCount": MessageLookupByLibrary.simpleMessage("Visit count"),
        "tr_waitDiscoveredDevices": MessageLookupByLibrary.simpleMessage(
            "Scan for available devices to perform the add operation"),
        "tr_waitForGwmValidationTimeout": MessageLookupByLibrary.simpleMessage(
            "Waiting for GWM validation result timed out"),
        "tr_waitingMineAcceptance":
            MessageLookupByLibrary.simpleMessage("Waiting for my acceptance"),
        "tr_waitingMineRectify":
            MessageLookupByLibrary.simpleMessage("To be rectified"),
        "tr_watchConcurrency":
            MessageLookupByLibrary.simpleMessage("Concurrent viewing"),
        "tr_watchCopyTask":
            MessageLookupByLibrary.simpleMessage("View CC tasks"),
        "tr_watchRectifyTask":
            MessageLookupByLibrary.simpleMessage("View rectification tasks"),
        "tr_watermarkContent":
            MessageLookupByLibrary.simpleMessage("Watermark content"),
        "tr_watermarkContentCanNotBeEmpty":
            MessageLookupByLibrary.simpleMessage(
                "Watermark content can not be empty"),
        "tr_watermarkContentTip": MessageLookupByLibrary.simpleMessage(
            "Echo back the custom watermark content"),
        "tr_watermarkSettings":
            MessageLookupByLibrary.simpleMessage("Watermark Settings"),
        "tr_watermarkSettingsTip": MessageLookupByLibrary.simpleMessage(
            "Once activated, video playback, download, and screenshot will all carry a watermark."),
        "tr_wednesday": MessageLookupByLibrary.simpleMessage("Wed"),
        "tr_west": MessageLookupByLibrary.simpleMessage("West"),
        "tr_whenTriggerCondition": MessageLookupByLibrary.simpleMessage(
            "When (trigger condition) is met."),
        "tr_wideDynamicConfig":
            MessageLookupByLibrary.simpleMessage("Wide dynamic config"),
        "tr_workbenchApplicationsConfiguration":
            MessageLookupByLibrary.simpleMessage("Configuration function"),
        "tr_workbenchExpireDaysTip": m56,
        "tr_workbenchHelloTip": MessageLookupByLibrary.simpleMessage("Hello"),
        "tr_workbenchSubTip": MessageLookupByLibrary.simpleMessage(
            "Welcome to use BcloudSaaS App"),
        "tr_year": MessageLookupByLibrary.simpleMessage("Year"),
        "tr_yearOnYear": MessageLookupByLibrary.simpleMessage("YoY"),
        "tr_yes": MessageLookupByLibrary.simpleMessage("Yes"),
        "tr_yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
        "tr_youDoNotHavePermissionToHandleThisTask":
            MessageLookupByLibrary.simpleMessage(
                "You do not have permission to process this task"),
        "tr_youHaveNotYetOpenedTheCloudStorageRecordingPackageAuthorization":
            MessageLookupByLibrary.simpleMessage(
                "You have not yet opened the cloud storage recording package authorization"),
        "tr_youth": MessageLookupByLibrary.simpleMessage("Youth"),
        "tr_youthMiddleAged":
            MessageLookupByLibrary.simpleMessage("Young and middle-aged")
      };
}
