// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  static String m0(phone) =>
      "您的账号【${phone}】注销后，您将无法再以该账号登录并使用【蜂云平台】的相关产品与服务，并自动退出您在【蜂云平台】加入的企业/组织，您的账号在【蜂云平台】的所有数据信息将被清空，且无法恢复。\n如有疑问，请联系蜂云客服邮箱：<EMAIL>";

  static String m1(platformId) => "${platformId}平台SIP ID查看";

  static String m2(maxChannel, remainChannel) =>
      "国标级联授权${maxChannel}路，还剩${remainChannel}路";

  static String m3(name) => "“${name}”的整改任务";

  static String m4(permission) => "为了您能正常添加设备，需要【${permission}】权限";

  static String m5(total, allocatedNum, unallocatedNum) =>
      "子账号授权${total}个，已分配${allocatedNum}个，还剩${unallocatedNum}个。";

  static String m6(maxChannel, remainChannel) =>
      "设备通道接入授权${maxChannel}路，剩余${remainChannel}路，请联系**************拓展授权。";

  static String m7(maxChannel, remainChannel) =>
      "设备通道接入授权${maxChannel}路，剩余${remainChannel}路。";

  static String m8(time) => "添加时间${time}";

  static String m9(quantity) => "全部(${quantity}台)";

  static String m10(name) => "“${name}”算法，路数不足";

  static String m11(name) => "营业时间前后${name}分钟进店/离店，认为是店员";

  static String m12(count) => "${count}路";

  static String m13(serialNum) => "通道${serialNum}信息";

  static String m14(channel) => "与通道${channel}的序列号相同，请修改序列号编码";

  static String m15(name) => "最多选择${name}个算法";

  static String m16(name) => "最多选择${name}个部门";

  static String m17(name) => "最多选择${name}个人员";

  static String m18(maxCount) => "最多可选${maxCount}个部门";

  static String m19(deviceMaxCount) => "最多可选${deviceMaxCount}个资源";

  static String m20(period) => "请选择${period}巡检日期";

  static String m21(maxCount) => "最多可选${maxCount}个用户";

  static String m22(name) => "最多可选${name}个门店";

  static String m23(quantity) => "共${quantity}台设备";

  static String m24(name) => "结束时间不能超过${name}";

  static String m25(name) => "每月:${name}";

  static String m26(name) => "每周:${name}";

  static String m27(name) => "女性占比:${name}%";

  static String m28(name) => "男性占比:${name}%";

  static String m29(name) => "最大时长不能超过${name}";

  static String m30(name) => "最多只能同时下载${name}个任务";

  static String m31(day) => "时间最多支持${day}天";

  static String m32(name) => "满足以上${name}认为是店员";

  static String m33(name) => "${name}路异常";

  static String m34(name) => "${name}个设备";

  static String m35(name) => "${name}小时以上";

  static String m36(name) => "${name}分钟以上";

  static String m37(name) => "${name}，请重试";

  static String m38(nodeCount) => "共${nodeCount}个节点";

  static String m39(quantity) => "端侧客流(${quantity}台)";

  static String m40(name) => "待“${name}”验收";

  static String m41(name) => "待“${name}”整改";

  static String m42(name) => "”${name}”验收通过";

  static String m43(quantity) => "精准客流(${quantity}台)";

  static String m44(name) => "剩余${name}路";

  static String m45(name) => "正在进行第${name}次重试";

  static String m46(quantity) => "发现${quantity}款设备，";

  static String m47(name) => "最多可设置${name}个时间段";

  static String m48(name) => "单次停留时长${name}分钟以上";

  static String m49(channels) => "共${channels}台设备";

  static String m50(name) => "在营业时间前后${name}分钟，进店离店认为是店员";

  static String m51(name) => "还剩${name}路";

  static String m52(max) => "最多可设置${max}个";

  static String m53(name) => "共${name}路";

  static String m54(name) => "总停留时长${name}小时以上";

  static String m55(name) => "已用${name}路";

  static String m56(days) => "平台${days}天后到期，请联系 <EMAIL> 续费/扩容";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "local": MessageLookupByLibrary.simpleMessage("zh"),
        "openCameraPermissionTip":
            MessageLookupByLibrary.simpleMessage("请在系统设置中为当前应用打开相机访问权限"),
        "openLocationPermissionTip": MessageLookupByLibrary.simpleMessage(
            "需要您授予定位权限以读取当前连接的WiFi，请在系统设置中为当前应用打开定位权限。如果不授权，请手动输入"),
        "openLocationPermissionTip1":
            MessageLookupByLibrary.simpleMessage("请在系统设置中为当前应用打开定位权限"),
        "openPhotoPermissionTip":
            MessageLookupByLibrary.simpleMessage("请在系统设置中为当前应用打开相册访问权限"),
        "openStoragePermissionTip":
            MessageLookupByLibrary.simpleMessage("请在系统设置中为当前应用打开存储权限"),
        "tr_AIDetailInspection":
            MessageLookupByLibrary.simpleMessage("AI 巡检详情"),
        "tr_AIInitiate": MessageLookupByLibrary.simpleMessage("AI发起"),
        "tr_AIInspectionStatistics":
            MessageLookupByLibrary.simpleMessage("AI巡检统计"),
        "tr_AIPlanInspectionEdit":
            MessageLookupByLibrary.simpleMessage("AI 巡检计划编辑"),
        "tr_About": MessageLookupByLibrary.simpleMessage("关于"),
        "tr_AccessIdPwdTips":
            MessageLookupByLibrary.simpleMessage("密码长度8-64位，由字母和数字组成"),
        "tr_AccessIds": MessageLookupByLibrary.simpleMessage("接入ID"),
        "tr_AccessNationalStandardDevice":
            MessageLookupByLibrary.simpleMessage("国标设备接入"),
        "tr_AccessNode": MessageLookupByLibrary.simpleMessage("接入节点"),
        "tr_AccessPassword": MessageLookupByLibrary.simpleMessage("接入密码"),
        "tr_AccessProtocol": MessageLookupByLibrary.simpleMessage("接入协议"),
        "tr_AccessPwdConfig": MessageLookupByLibrary.simpleMessage("接入密码设置"),
        "tr_AccessTips": MessageLookupByLibrary.simpleMessage(
            "注意:\n国标协议暂不支持H.265编码，请确保设备码流为H.264设备需使用GB28181-2016协议，才能支持TCP。"),
        "tr_AccessType": MessageLookupByLibrary.simpleMessage("接入类型"),
        "tr_Account": MessageLookupByLibrary.simpleMessage("账号"),
        "tr_AccountCancellation": MessageLookupByLibrary.simpleMessage("注销账号"),
        "tr_AccountCancellation1": MessageLookupByLibrary.simpleMessage("账户注销"),
        "tr_AccountPasswordRule":
            MessageLookupByLibrary.simpleMessage("密码为8-16位字符，需包含字母、数字和特殊字符"),
        "tr_AccountPwdRuleSimple":
            MessageLookupByLibrary.simpleMessage("8-16位数字和字母"),
        "tr_AccountSafe": MessageLookupByLibrary.simpleMessage("账号与安全"),
        "tr_AccountSet": MessageLookupByLibrary.simpleMessage("个人设置"),
        "tr_Add4GDevice": MessageLookupByLibrary.simpleMessage("4G摄像机"),
        "tr_Add4GDeviceNotes":
            MessageLookupByLibrary.simpleMessage("扫描设备二维码直接添加"),
        "tr_AddChildDeviceNodeTips":
            MessageLookupByLibrary.simpleMessage("当前节点下暂无设备与子节点"),
        "tr_AddDepartment": MessageLookupByLibrary.simpleMessage("新增部门"),
        "tr_AddDepartmentTips":
            MessageLookupByLibrary.simpleMessage("当前部门下暂无子部门,请点击右上角添加按钮添加部门"),
        "tr_AddDepartmentTipsSimple":
            MessageLookupByLibrary.simpleMessage("当前部门下暂无子部门"),
        "tr_AddDevice": MessageLookupByLibrary.simpleMessage("添加设备"),
        "tr_AddDeviceByManual": MessageLookupByLibrary.simpleMessage("手动添加"),
        "tr_AddDeviceByWIFI": MessageLookupByLibrary.simpleMessage("WiFi配网"),
        "tr_AddDeviceByWIFINotes":
            MessageLookupByLibrary.simpleMessage("支持二维码配网和快速配网"),
        "tr_AddDeviceFailed": MessageLookupByLibrary.simpleMessage("添加失败"),
        "tr_AddDirectNetConnectionNotes": MessageLookupByLibrary.simpleMessage(
            "注： \n1. 确保设备已上电 \n2. 网线与设备网线连接口连接，DVR/NVR接入有线网络。设备启动成功后尝试连接网络，听到设备联网成功，表示联网完成"),
        "tr_AddJFDevice": MessageLookupByLibrary.simpleMessage("设备添加"),
        "tr_AddNode": MessageLookupByLibrary.simpleMessage("添加节点"),
        "tr_AddNodeTips": MessageLookupByLibrary.simpleMessage(
            "当前节点下暂无设备与子节点请点击右上角添加按钮添加设备与子节点"),
        "tr_AddPerson": MessageLookupByLibrary.simpleMessage("添加新用户"),
        "tr_AddPreset": MessageLookupByLibrary.simpleMessage("新建预置点"),
        "tr_AddRecordDevice": MessageLookupByLibrary.simpleMessage("网线直连添加"),
        "tr_AddRecordDeviceNotes":
            MessageLookupByLibrary.simpleMessage("适用于带网线插口的设备"),
        "tr_AddRecordNotes": MessageLookupByLibrary.simpleMessage(
            "注： \n· 确保设备已上电 \n· 网线与设备网线连接口连接"),
        "tr_AddRole": MessageLookupByLibrary.simpleMessage("添加角色"),
        "tr_AddSuccess": MessageLookupByLibrary.simpleMessage("添加成功"),
        "tr_AgreeAndContinue": MessageLookupByLibrary.simpleMessage("同意并继续"),
        "tr_AlarmAI": MessageLookupByLibrary.simpleMessage("AI告警"),
        "tr_AlarmDevice": MessageLookupByLibrary.simpleMessage("设备告警"),
        "tr_AlarmMessage": MessageLookupByLibrary.simpleMessage("报警消息"),
        "tr_AlarmRecording": MessageLookupByLibrary.simpleMessage("报警录像"),
        "tr_AlarmRecordingTip":
            MessageLookupByLibrary.simpleMessage("仅在检测到报警时录像，录像时间长"),
        "tr_AlarmSetting": MessageLookupByLibrary.simpleMessage("报警设置"),
        "tr_AlarmTotalNum": MessageLookupByLibrary.simpleMessage("告警总数"),
        "tr_Album": MessageLookupByLibrary.simpleMessage("相册"),
        "tr_AlertPreRecordTime": MessageLookupByLibrary.simpleMessage("告警预录时段"),
        "tr_AlertPreRecordTimeTip":
            MessageLookupByLibrary.simpleMessage("范围为0-30秒"),
        "tr_Algorithm": MessageLookupByLibrary.simpleMessage("算法"),
        "tr_AlgorithmAlarm": MessageLookupByLibrary.simpleMessage("算法告警"),
        "tr_AlgorithmConfiguration":
            MessageLookupByLibrary.simpleMessage("算法配置"),
        "tr_AllDate": MessageLookupByLibrary.simpleMessage("全部日期"),
        "tr_AllDayRecording": MessageLookupByLibrary.simpleMessage("全天录像"),
        "tr_AllDayRecordingTip":
            MessageLookupByLibrary.simpleMessage("全天24小时不间断录像"),
        "tr_AllProtocols": MessageLookupByLibrary.simpleMessage("所有协议"),
        "tr_AllRightsReserved":
            MessageLookupByLibrary.simpleMessage("版权所有：杭州杰峰科技"),
        "tr_AllTypes": MessageLookupByLibrary.simpleMessage("所有类型"),
        "tr_And": MessageLookupByLibrary.simpleMessage("和"),
        "tr_AppName": MessageLookupByLibrary.simpleMessage("蜂云SaaS"),
        "tr_AppPolicyTip1": MessageLookupByLibrary.simpleMessage("隐私保护政策概要"),
        "tr_AppPolicyTip2": MessageLookupByLibrary.simpleMessage(
            "        感谢您信任并使用蜂云！蜂云非常重视您的隐私保护和个人信息保护。在您使用蜂云服务之前，请认真阅读"),
        "tr_AppPolicyTip3": MessageLookupByLibrary.simpleMessage(
            "的全部条款，同意并接受全部条款后开始使用我们的服务。\n        我们将按照法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。"),
        "tr_AppWelcome": MessageLookupByLibrary.simpleMessage("欢迎使用蜂云SaaS"),
        "tr_Avatar": MessageLookupByLibrary.simpleMessage("头像"),
        "tr_BasicInfo": MessageLookupByLibrary.simpleMessage("基本信息"),
        "tr_BindEmail": MessageLookupByLibrary.simpleMessage("绑定邮箱"),
        "tr_BindNewPhone": MessageLookupByLibrary.simpleMessage("绑定新手机"),
        "tr_BindPhone": MessageLookupByLibrary.simpleMessage("绑定手机"),
        "tr_Cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "tr_Cancellation": MessageLookupByLibrary.simpleMessage("注销"),
        "tr_CancellationProtocol":
            MessageLookupByLibrary.simpleMessage("《蜂云SaaS账号注销协议》"),
        "tr_CancellationSuccess": MessageLookupByLibrary.simpleMessage("注销成功"),
        "tr_CancellationValidityPeriod":
            MessageLookupByLibrary.simpleMessage("注册有效期"),
        "tr_CannotChangeSameEmail":
            MessageLookupByLibrary.simpleMessage("新邮箱不能和原邮箱一致"),
        "tr_CannotChangeSamePhone":
            MessageLookupByLibrary.simpleMessage("新手机号不能和原手机号一致"),
        "tr_Capacity": MessageLookupByLibrary.simpleMessage("容量"),
        "tr_CheckAddBTDeviceFailed": MessageLookupByLibrary.simpleMessage(
            "设备校验失败，请在设备端确认。如需继续添加，可将新的设备重置再试一试。"),
        "tr_CheckAddDeviceFailed":
            MessageLookupByLibrary.simpleMessage("查询不到设备，请重置设备后重新添加"),
        "tr_CheckCurrentEmail":
            MessageLookupByLibrary.simpleMessage("检测到您当前绑定的邮箱为："),
        "tr_CheckCurrentPhone":
            MessageLookupByLibrary.simpleMessage("检测到您当前绑定的手机号为："),
        "tr_CheckDeviceLoginInfo":
            MessageLookupByLibrary.simpleMessage("设备用户名和密码"),
        "tr_CheckView": MessageLookupByLibrary.simpleMessage("查看"),
        "tr_ChooseDepartment": MessageLookupByLibrary.simpleMessage("选择部门"),
        "tr_ChooseNetwork":
            MessageLookupByLibrary.simpleMessage("请选择设备连接的无线网络"),
        "tr_ChoosePrettyWiFi":
            MessageLookupByLibrary.simpleMessage("请选择信号较强的WiFi，可快速让设备联网"),
        "tr_ClickToUploadTheBusinessLicense":
            MessageLookupByLibrary.simpleMessage("点击上传营业执照"),
        "tr_CloudRecord": MessageLookupByLibrary.simpleMessage("云回放"),
        "tr_CommandSuccess": MessageLookupByLibrary.simpleMessage("操作成功"),
        "tr_CommonSave": MessageLookupByLibrary.simpleMessage("保存"),
        "tr_Common_Add": MessageLookupByLibrary.simpleMessage("添加"),
        "tr_Common_AdministrativeArea":
            MessageLookupByLibrary.simpleMessage("行政区域"),
        "tr_Common_All": MessageLookupByLibrary.simpleMessage("全部"),
        "tr_Common_Channel": MessageLookupByLibrary.simpleMessage("通道"),
        "tr_Common_ChooseAdministrativeArea":
            MessageLookupByLibrary.simpleMessage("行政区域选择"),
        "tr_Common_ContentPairingFailed":
            MessageLookupByLibrary.simpleMessage("暂无搜索结果"),
        "tr_Common_Custom": MessageLookupByLibrary.simpleMessage("自定义"),
        "tr_Common_Delete": MessageLookupByLibrary.simpleMessage("删除"),
        "tr_Common_DeleteSuccess": MessageLookupByLibrary.simpleMessage("删除成功"),
        "tr_Common_Device": MessageLookupByLibrary.simpleMessage("设备"),
        "tr_Common_Disconnect": MessageLookupByLibrary.simpleMessage("断开连接"),
        "tr_Common_Edit": MessageLookupByLibrary.simpleMessage("编辑"),
        "tr_Common_Function": MessageLookupByLibrary.simpleMessage("功能"),
        "tr_Common_GrassrootsUnitNum":
            MessageLookupByLibrary.simpleMessage("基层单位编号"),
        "tr_Common_InputPassword":
            MessageLookupByLibrary.simpleMessage("请输入登录密码"),
        "tr_Common_Loading": MessageLookupByLibrary.simpleMessage("正在加载中..."),
        "tr_Common_Message": MessageLookupByLibrary.simpleMessage("消息"),
        "tr_Common_Mine": MessageLookupByLibrary.simpleMessage("我的"),
        "tr_Common_Monitor": MessageLookupByLibrary.simpleMessage("监控"),
        "tr_Common_More": MessageLookupByLibrary.simpleMessage("更多"),
        "tr_Common_Password": MessageLookupByLibrary.simpleMessage("密码"),
        "tr_Common_PlsFillInfo": MessageLookupByLibrary.simpleMessage("请选择"),
        "tr_Common_Random": MessageLookupByLibrary.simpleMessage("随机"),
        "tr_Common_Record": MessageLookupByLibrary.simpleMessage("录像"),
        "tr_Common_Rename": MessageLookupByLibrary.simpleMessage("重命名"),
        "tr_Common_Reset": MessageLookupByLibrary.simpleMessage("重置"),
        "tr_Common_Search": MessageLookupByLibrary.simpleMessage("搜索"),
        "tr_Common_SelectAll": MessageLookupByLibrary.simpleMessage("全选"),
        "tr_Common_Snap": MessageLookupByLibrary.simpleMessage("截图"),
        "tr_Common_Store": MessageLookupByLibrary.simpleMessage("商城"),
        "tr_Common_Voice": MessageLookupByLibrary.simpleMessage("声音"),
        "tr_Common_Workbench": MessageLookupByLibrary.simpleMessage("工作台"),
        "tr_Common_definition": MessageLookupByLibrary.simpleMessage("清晰度"),
        "tr_Common_export": MessageLookupByLibrary.simpleMessage("导出"),
        "tr_Confirm": MessageLookupByLibrary.simpleMessage("确定"),
        "tr_ConfirmCancellation": MessageLookupByLibrary.simpleMessage("确认注销"),
        "tr_ConfirmTips": MessageLookupByLibrary.simpleMessage("确定退出登录？"),
        "tr_ContactUs": MessageLookupByLibrary.simpleMessage("联系我们"),
        "tr_ContactUsContent": MessageLookupByLibrary.simpleMessage(
            "感谢您使用我司的产品和服务，若您在使用产品或服务过程中有任何问题，意见或建议，请将您的反馈意见发送至**************，我们会在第一时间给您回复，感谢您的配合."),
        "tr_CopySuccess": MessageLookupByLibrary.simpleMessage("已经复制到粘贴板"),
        "tr_DVR": MessageLookupByLibrary.simpleMessage("DVR"),
        "tr_DearUser": MessageLookupByLibrary.simpleMessage("尊敬的用户："),
        "tr_DearUserDetail": m0,
        "tr_Delete": MessageLookupByLibrary.simpleMessage("删除"),
        "tr_DeleteDevice": MessageLookupByLibrary.simpleMessage("删除设备"),
        "tr_DeleteDeviceTips":
            MessageLookupByLibrary.simpleMessage("摄像机删除后，将清除云端动态产生的照片和视频"),
        "tr_Department": MessageLookupByLibrary.simpleMessage("部门"),
        "tr_DepartmentManage": MessageLookupByLibrary.simpleMessage("部门管理"),
        "tr_Detailed": MessageLookupByLibrary.simpleMessage("详细信息"),
        "tr_Device": MessageLookupByLibrary.simpleMessage("设备"),
        "tr_DeviceAccessChannelNum":
            MessageLookupByLibrary.simpleMessage("接入通道数"),
        "tr_DeviceAccessIDPwd": MessageLookupByLibrary.simpleMessage("接入ID密码"),
        "tr_DeviceAdd": MessageLookupByLibrary.simpleMessage("设备添加"),
        "tr_DeviceChannelID": MessageLookupByLibrary.simpleMessage("通道ID"),
        "tr_DeviceChannelNum": MessageLookupByLibrary.simpleMessage("通道号"),
        "tr_DeviceEdit": MessageLookupByLibrary.simpleMessage("编辑设备"),
        "tr_DeviceInfo": MessageLookupByLibrary.simpleMessage("设备信息"),
        "tr_DeviceLoginName": MessageLookupByLibrary.simpleMessage("设备登录名"),
        "tr_DeviceName": MessageLookupByLibrary.simpleMessage("设备名称"),
        "tr_DeviceNode": MessageLookupByLibrary.simpleMessage("所属节点"),
        "tr_DeviceParams": MessageLookupByLibrary.simpleMessage("设备参数"),
        "tr_DevicePassword": MessageLookupByLibrary.simpleMessage("请输入密码"),
        "tr_DeviceResource": MessageLookupByLibrary.simpleMessage("设备资源"),
        "tr_DeviceSN": MessageLookupByLibrary.simpleMessage("序列号"),
        "tr_DeviceScanTips": MessageLookupByLibrary.simpleMessage(
            "快速配网和扫码配网同时开启中，请查看说明书是否支持扫码配网，如果设备不支持扫码配网，无需进行二维码对码"),
        "tr_DeviceState": MessageLookupByLibrary.simpleMessage("设备状态"),
        "tr_DeviceStateChangeOffline":
            MessageLookupByLibrary.simpleMessage("下线"),
        "tr_DeviceStateChangeOnline":
            MessageLookupByLibrary.simpleMessage("上线"),
        "tr_DeviceStateOffline": MessageLookupByLibrary.simpleMessage("离线"),
        "tr_DeviceStateOnline": MessageLookupByLibrary.simpleMessage("在线"),
        "tr_DeviceStateUnRegister": MessageLookupByLibrary.simpleMessage("未注册"),
        "tr_DeviceUuid": MessageLookupByLibrary.simpleMessage("设备序列号"),
        "tr_Done": MessageLookupByLibrary.simpleMessage("完成"),
        "tr_Download": MessageLookupByLibrary.simpleMessage("下载"),
        "tr_DownloadSuccess": MessageLookupByLibrary.simpleMessage("下载成功"),
        "tr_Downloaded": MessageLookupByLibrary.simpleMessage("已下载"),
        "tr_EditDepartment": MessageLookupByLibrary.simpleMessage("编辑部门"),
        "tr_EditPreset": MessageLookupByLibrary.simpleMessage("编辑预置点"),
        "tr_EncodingMethod": MessageLookupByLibrary.simpleMessage("编码方式"),
        "tr_EncodingMethodNationalStandard":
            MessageLookupByLibrary.simpleMessage("国标编码"),
        "tr_EncodingMethodRandom": MessageLookupByLibrary.simpleMessage("随机编码"),
        "tr_EnterNoteDepartment":
            MessageLookupByLibrary.simpleMessage("请输入部门名称"),
        "tr_EnterNoteNickName": MessageLookupByLibrary.simpleMessage("请输入节点名称"),
        "tr_EnterWiFiPassword":
            MessageLookupByLibrary.simpleMessage("请输入WiFi密码"),
        "tr_ErrorCode": MessageLookupByLibrary.simpleMessage("错误码"),
        "tr_ErrorCode_Minus_1": MessageLookupByLibrary.simpleMessage("数据解析失败"),
        "tr_ErrorCode_Minus_1000": MessageLookupByLibrary.simpleMessage("网络错误"),
        "tr_ErrorCode_Minus_10000":
            MessageLookupByLibrary.simpleMessage("您的请求是非法的哦"),
        "tr_ErrorCode_Minus_100000": MessageLookupByLibrary.simpleMessage("错误"),
        "tr_ErrorCode_Minus_10001":
            MessageLookupByLibrary.simpleMessage("系统没有初始化"),
        "tr_ErrorCode_Minus_10002":
            MessageLookupByLibrary.simpleMessage("参数不正确"),
        "tr_ErrorCode_Minus_10003":
            MessageLookupByLibrary.simpleMessage("句柄无效"),
        "tr_ErrorCode_Minus_10004":
            MessageLookupByLibrary.simpleMessage("SDK清理出错"),
        "tr_ErrorCode_Minus_10005":
            MessageLookupByLibrary.simpleMessage("您的网络连接超时，请重试"),
        "tr_ErrorCode_Minus_10006":
            MessageLookupByLibrary.simpleMessage("存储空间不足"),
        "tr_ErrorCode_Minus_10007":
            MessageLookupByLibrary.simpleMessage("网络连接失败"),
        "tr_ErrorCode_Minus_10008":
            MessageLookupByLibrary.simpleMessage("打开文件失败"),
        "tr_ErrorCode_Minus_10009":
            MessageLookupByLibrary.simpleMessage("未知错误"),
        "tr_ErrorCode_Minus_1001":
            MessageLookupByLibrary.simpleMessage("发送缓冲区已满"),
        "tr_ErrorCode_Minus_1002":
            MessageLookupByLibrary.simpleMessage("网络发送失败"),
        "tr_ErrorCode_Minus_1003":
            MessageLookupByLibrary.simpleMessage("网络接收失败"),
        "tr_ErrorCode_Minus_1004": MessageLookupByLibrary.simpleMessage("网络超时"),
        "tr_ErrorCode_Minus_1005": MessageLookupByLibrary.simpleMessage("没有对象"),
        "tr_ErrorCode_Minus_1006": MessageLookupByLibrary.simpleMessage("创建失败"),
        "tr_ErrorCode_Minus_1007": MessageLookupByLibrary.simpleMessage("连接失败"),
        "tr_ErrorCode_Minus_1008": MessageLookupByLibrary.simpleMessage("超时"),
        "tr_ErrorCode_Minus_1009": MessageLookupByLibrary.simpleMessage("无连接"),
        "tr_ErrorCode_Minus_101": MessageLookupByLibrary.simpleMessage("密码不正确"),
        "tr_ErrorCode_Minus_1010":
            MessageLookupByLibrary.simpleMessage("socket异常"),
        "tr_ErrorCode_Minus_1011":
            MessageLookupByLibrary.simpleMessage("socket关闭异常"),
        "tr_ErrorCode_Minus_1012":
            MessageLookupByLibrary.simpleMessage("创建缓存失败"),
        "tr_ErrorCode_Minus_1013": MessageLookupByLibrary.simpleMessage("网络忙"),
        "tr_ErrorCode_Minus_1014": MessageLookupByLibrary.simpleMessage("监听异常"),
        "tr_ErrorCode_Minus_1015": MessageLookupByLibrary.simpleMessage("接收异常"),
        "tr_ErrorCode_Minus_1016": MessageLookupByLibrary.simpleMessage("无缓冲区"),
        "tr_ErrorCode_Minus_1017":
            MessageLookupByLibrary.simpleMessage("网络错误或DNS配置错误"),
        "tr_ErrorCode_Minus_1018":
            MessageLookupByLibrary.simpleMessage("开发者账号未鉴权"),
        "tr_ErrorCode_Minus_102": MessageLookupByLibrary.simpleMessage("账号不存在"),
        "tr_ErrorCode_Minus_103":
            MessageLookupByLibrary.simpleMessage("登录超时(网络连接失败)"),
        "tr_ErrorCode_Minus_104": MessageLookupByLibrary.simpleMessage("账号未登录"),
        "tr_ErrorCode_Minus_105": MessageLookupByLibrary.simpleMessage("账号已登录"),
        "tr_ErrorCode_Minus_106":
            MessageLookupByLibrary.simpleMessage("账号被列为黑名单"),
        "tr_ErrorCode_Minus_107":
            MessageLookupByLibrary.simpleMessage("设备资源不足"),
        "tr_ErrorCode_Minus_109":
            MessageLookupByLibrary.simpleMessage("找不到网络主机"),
        "tr_ErrorCode_Minus_11000":
            MessageLookupByLibrary.simpleMessage("数据不正确，可能版本不匹配"),
        "tr_ErrorCode_Minus_11001":
            MessageLookupByLibrary.simpleMessage("版本不支持"),
        "tr_ErrorCode_Minus_11200":
            MessageLookupByLibrary.simpleMessage("打开通道失败"),
        "tr_ErrorCode_Minus_11201":
            MessageLookupByLibrary.simpleMessage("关闭通道失败"),
        "tr_ErrorCode_Minus_11202":
            MessageLookupByLibrary.simpleMessage("建立媒体子连接失败"),
        "tr_ErrorCode_Minus_11203":
            MessageLookupByLibrary.simpleMessage("媒体子连接通讯失败"),
        "tr_ErrorCode_Minus_11204":
            MessageLookupByLibrary.simpleMessage("视频链接达到最大"),
        "tr_ErrorCode_Minus_11300": MessageLookupByLibrary.simpleMessage("无权限"),
        "tr_ErrorCode_Minus_11301":
            MessageLookupByLibrary.simpleMessage("错误的密码"),
        "tr_ErrorCode_Minus_11302":
            MessageLookupByLibrary.simpleMessage("用户不存在"),
        "tr_ErrorCode_Minus_11303":
            MessageLookupByLibrary.simpleMessage("用户被锁定，请重启设备"),
        "tr_ErrorCode_Minus_11304":
            MessageLookupByLibrary.simpleMessage("该用户不允许访问"),
        "tr_ErrorCode_Minus_11305":
            MessageLookupByLibrary.simpleMessage("该用户已登陆"),
        "tr_ErrorCode_Minus_11306":
            MessageLookupByLibrary.simpleMessage("该用户没有登陆"),
        "tr_ErrorCode_Minus_11307":
            MessageLookupByLibrary.simpleMessage("设备可能不在线"),
        "tr_ErrorCode_Minus_11308":
            MessageLookupByLibrary.simpleMessage("用户输入不合法"),
        "tr_ErrorCode_Minus_11309":
            MessageLookupByLibrary.simpleMessage("索引重复"),
        "tr_ErrorCode_Minus_11310":
            MessageLookupByLibrary.simpleMessage("对象不存在"),
        "tr_ErrorCode_Minus_11311":
            MessageLookupByLibrary.simpleMessage("对象不存在"),
        "tr_ErrorCode_Minus_11312":
            MessageLookupByLibrary.simpleMessage("对象正在使用"),
        "tr_ErrorCode_Minus_11313":
            MessageLookupByLibrary.simpleMessage("子集超范围"),
        "tr_ErrorCode_Minus_11314":
            MessageLookupByLibrary.simpleMessage("密码不正确"),
        "tr_ErrorCode_Minus_11315":
            MessageLookupByLibrary.simpleMessage("密码不匹配"),
        "tr_ErrorCode_Minus_11316":
            MessageLookupByLibrary.simpleMessage("保留帐号"),
        "tr_ErrorCode_Minus_11317":
            MessageLookupByLibrary.simpleMessage("不支持此种加密方式登录"),
        "tr_ErrorCode_Minus_11318":
            MessageLookupByLibrary.simpleMessage("账号密码不对"),
        "tr_ErrorCode_Minus_11400":
            MessageLookupByLibrary.simpleMessage("保存配置后需要重启应用程序"),
        "tr_ErrorCode_Minus_11401":
            MessageLookupByLibrary.simpleMessage("请重启设备"),
        "tr_ErrorCode_Minus_11402":
            MessageLookupByLibrary.simpleMessage("写文件出错"),
        "tr_ErrorCode_Minus_11403":
            MessageLookupByLibrary.simpleMessage("配置特性不支持"),
        "tr_ErrorCode_Minus_11404":
            MessageLookupByLibrary.simpleMessage("配置校验失败"),
        "tr_ErrorCode_Minus_11405":
            MessageLookupByLibrary.simpleMessage("配置不存在"),
        "tr_ErrorCode_Minus_11406":
            MessageLookupByLibrary.simpleMessage("配置解析出错,可能不支持该配置"),
        "tr_ErrorCode_Minus_11500":
            MessageLookupByLibrary.simpleMessage("暂停失败"),
        "tr_ErrorCode_Minus_11501":
            MessageLookupByLibrary.simpleMessage("没有找到文件"),
        "tr_ErrorCode_Minus_11502":
            MessageLookupByLibrary.simpleMessage("配置未启用"),
        "tr_ErrorCode_Minus_11503":
            MessageLookupByLibrary.simpleMessage("视频流未开启"),
        "tr_ErrorCode_Minus_11600":
            MessageLookupByLibrary.simpleMessage("创建连接失败"),
        "tr_ErrorCode_Minus_11601":
            MessageLookupByLibrary.simpleMessage("连接失败"),
        "tr_ErrorCode_Minus_11602":
            MessageLookupByLibrary.simpleMessage("域名解析失败"),
        "tr_ErrorCode_Minus_11603":
            MessageLookupByLibrary.simpleMessage("发送数据失败"),
        "tr_ErrorCode_Minus_11605":
            MessageLookupByLibrary.simpleMessage("服务繁忙"),
        "tr_ErrorCode_Minus_11609":
            MessageLookupByLibrary.simpleMessage("连接受限，或访问服务器失败"),
        "tr_ErrorCode_Minus_11612":
            MessageLookupByLibrary.simpleMessage("服务器连接数已满"),
        "tr_ErrorCode_Minus_11700":
            MessageLookupByLibrary.simpleMessage("盗版设备"),
        "tr_ErrorCode_Minus_120":
            MessageLookupByLibrary.simpleMessage("设备不存在（被删除掉了）"),
        "tr_ErrorCode_Minus_1239510":
            MessageLookupByLibrary.simpleMessage("对象不存在"),
        "tr_ErrorCode_Minus_1239511":
            MessageLookupByLibrary.simpleMessage("值不存在"),
        "tr_ErrorCode_Minus_137":
            MessageLookupByLibrary.simpleMessage("设备token不合法"),
        "tr_ErrorCode_Minus_200000":
            MessageLookupByLibrary.simpleMessage("无效参数"),
        "tr_ErrorCode_Minus_200001":
            MessageLookupByLibrary.simpleMessage("用户不存在"),
        "tr_ErrorCode_Minus_200002":
            MessageLookupByLibrary.simpleMessage("sql失败"),
        "tr_ErrorCode_Minus_201103":
            MessageLookupByLibrary.simpleMessage("消息格式错误"),
        "tr_ErrorCode_Minus_201111":
            MessageLookupByLibrary.simpleMessage("设备查找成功"),
        "tr_ErrorCode_Minus_201113":
            MessageLookupByLibrary.simpleMessage("盗版软件"),
        "tr_ErrorCode_Minus_201117":
            MessageLookupByLibrary.simpleMessage("设备连接数达到上限"),
        "tr_ErrorCode_Minus_201121":
            MessageLookupByLibrary.simpleMessage("获取AUTHCODE有误"),
        "tr_ErrorCode_Minus_20221":
            MessageLookupByLibrary.simpleMessage("验证次数超过限制，需重启设备再尝试"),
        "tr_ErrorCode_Minus_210002":
            MessageLookupByLibrary.simpleMessage("接口验证失败"),
        "tr_ErrorCode_Minus_210003":
            MessageLookupByLibrary.simpleMessage("参数错误"),
        "tr_ErrorCode_Minus_210004":
            MessageLookupByLibrary.simpleMessage("手机号已被注册"),
        "tr_ErrorCode_Minus_210005":
            MessageLookupByLibrary.simpleMessage("超出短信发送次数"),
        "tr_ErrorCode_Minus_210008":
            MessageLookupByLibrary.simpleMessage("当前不支持高清视频，将自动切换为标清"),
        "tr_ErrorCode_Minus_210009":
            MessageLookupByLibrary.simpleMessage("转发模式不支持高清，请升级支持DSS的设备固件"),
        "tr_ErrorCode_Minus_210010":
            MessageLookupByLibrary.simpleMessage("发送失败，请重试"),
        "tr_ErrorCode_Minus_210017":
            MessageLookupByLibrary.simpleMessage("120秒之内只能发送一次"),
        "tr_ErrorCode_Minus_210106":
            MessageLookupByLibrary.simpleMessage("用户名已被注册"),
        "tr_ErrorCode_Minus_210313":
            MessageLookupByLibrary.simpleMessage("原始密码不正确"),
        "tr_ErrorCode_Minus_210315":
            MessageLookupByLibrary.simpleMessage("新旧密码相同，请重新更改"),
        "tr_ErrorCode_Minus_210405":
            MessageLookupByLibrary.simpleMessage("24小时内获取验证码次数不能超过3次"),
        "tr_ErrorCode_Minus_210414":
            MessageLookupByLibrary.simpleMessage("该手机号未注册"),
        "tr_ErrorCode_Minus_210417":
            MessageLookupByLibrary.simpleMessage("120秒之内只能发送一次"),
        "tr_ErrorCode_Minus_210512":
            MessageLookupByLibrary.simpleMessage("您两次输入的新密码不一致"),
        "tr_ErrorCode_Minus_210607":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_210700":
            MessageLookupByLibrary.simpleMessage("服务器响应失败"),
        "tr_ErrorCode_Minus_211703":
            MessageLookupByLibrary.simpleMessage("缺少上传文件"),
        "tr_ErrorCode_Minus_212104":
            MessageLookupByLibrary.simpleMessage("服务器查询失败"),
        "tr_ErrorCode_Minus_212402":
            MessageLookupByLibrary.simpleMessage("没有接收到上传的文件"),
        "tr_ErrorCode_Minus_213000":
            MessageLookupByLibrary.simpleMessage("无此用户名"),
        "tr_ErrorCode_Minus_213100":
            MessageLookupByLibrary.simpleMessage("发送邮件失败，请检查邮箱输入是否正确"),
        "tr_ErrorCode_Minus_213108":
            MessageLookupByLibrary.simpleMessage("该邮箱已被注册"),
        "tr_ErrorCode_Minus_213206":
            MessageLookupByLibrary.simpleMessage("用户名已被注册"),
        "tr_ErrorCode_Minus_213207":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_213208":
            MessageLookupByLibrary.simpleMessage("邮箱已被注册"),
        "tr_ErrorCode_Minus_213303":
            MessageLookupByLibrary.simpleMessage("参数错误"),
        "tr_ErrorCode_Minus_213314":
            MessageLookupByLibrary.simpleMessage("邮箱不存在"),
        "tr_ErrorCode_Minus_213316":
            MessageLookupByLibrary.simpleMessage("邮箱和用户名不匹配"),
        "tr_ErrorCode_Minus_213407":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_213414":
            MessageLookupByLibrary.simpleMessage("邮箱不存在"),
        "tr_ErrorCode_Minus_213514":
            MessageLookupByLibrary.simpleMessage("手机号码或邮箱不存在"),
        "tr_ErrorCode_Minus_213600":
            MessageLookupByLibrary.simpleMessage("设备序列号在黑名单中"),
        "tr_ErrorCode_Minus_213601":
            MessageLookupByLibrary.simpleMessage("设备序列号已存在"),
        "tr_ErrorCode_Minus_213602":
            MessageLookupByLibrary.simpleMessage("设备序列号为空"),
        "tr_ErrorCode_Minus_213603":
            MessageLookupByLibrary.simpleMessage("设备序列号格式不正确"),
        "tr_ErrorCode_Minus_213604":
            MessageLookupByLibrary.simpleMessage("不存在白名单"),
        "tr_ErrorCode_Minus_213605":
            MessageLookupByLibrary.simpleMessage("设备名不能为空"),
        "tr_ErrorCode_Minus_213606":
            MessageLookupByLibrary.simpleMessage("设备用户名格式不正确"),
        "tr_ErrorCode_Minus_213607":
            MessageLookupByLibrary.simpleMessage("设备密码格式不正确"),
        "tr_ErrorCode_Minus_213608":
            MessageLookupByLibrary.simpleMessage("设备名称格式不正确，含关键字"),
        "tr_ErrorCode_Minus_213610":
            MessageLookupByLibrary.simpleMessage("参数异常"),
        "tr_ErrorCode_Minus_213611":
            MessageLookupByLibrary.simpleMessage("用户名不存在"),
        "tr_ErrorCode_Minus_213612":
            MessageLookupByLibrary.simpleMessage("编辑设备信息失败"),
        "tr_ErrorCode_Minus_213620":
            MessageLookupByLibrary.simpleMessage("开通失败"),
        "tr_ErrorCode_Minus_213621":
            MessageLookupByLibrary.simpleMessage("没有开通云服务"),
        "tr_ErrorCode_Minus_213630":
            MessageLookupByLibrary.simpleMessage("用户名或密码错误"),
        "tr_ErrorCode_Minus_213700":
            MessageLookupByLibrary.simpleMessage("服务器响应失败"),
        "tr_ErrorCode_Minus_213702":
            MessageLookupByLibrary.simpleMessage("接口验证失败"),
        "tr_ErrorCode_Minus_213703":
            MessageLookupByLibrary.simpleMessage("参数错误"),
        "tr_ErrorCode_Minus_213706":
            MessageLookupByLibrary.simpleMessage("用户已被注册"),
        "tr_ErrorCode_Minus_213800":
            MessageLookupByLibrary.simpleMessage("成功，需要更新"),
        "tr_ErrorCode_Minus_213801":
            MessageLookupByLibrary.simpleMessage("成功，已是最新，无需更新"),
        "tr_ErrorCode_Minus_213802":
            MessageLookupByLibrary.simpleMessage("失败，无效请求"),
        "tr_ErrorCode_Minus_213803":
            MessageLookupByLibrary.simpleMessage("失败，资源未找到"),
        "tr_ErrorCode_Minus_213804":
            MessageLookupByLibrary.simpleMessage("失败，服务器内部错误"),
        "tr_ErrorCode_Minus_213805":
            MessageLookupByLibrary.simpleMessage("失败，服务器暂时不可用"),
        "tr_ErrorCode_Minus_214206":
            MessageLookupByLibrary.simpleMessage("用户名已被注册"),
        "tr_ErrorCode_Minus_214404":
            MessageLookupByLibrary.simpleMessage("手机号码已被绑定"),
        "tr_ErrorCode_Minus_214507":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_214608":
            MessageLookupByLibrary.simpleMessage("邮箱已被绑定"),
        "tr_ErrorCode_Minus_214707":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_214708":
            MessageLookupByLibrary.simpleMessage("邮箱已被绑定"),
        "tr_ErrorCode_Minus_214908":
            MessageLookupByLibrary.simpleMessage("邮箱已被注册"),
        "tr_ErrorCode_Minus_215100":
            MessageLookupByLibrary.simpleMessage("通过XMCloud获取设备DSS信息"),
        "tr_ErrorCode_Minus_215101":
            MessageLookupByLibrary.simpleMessage("DSS连接Hls服务器失败"),
        "tr_ErrorCode_Minus_215102":
            MessageLookupByLibrary.simpleMessage("DSS信息格式错误"),
        "tr_ErrorCode_Minus_215103":
            MessageLookupByLibrary.simpleMessage("获取设备DSS信息失败，请稍候重试"),
        "tr_ErrorCode_Minus_215104":
            MessageLookupByLibrary.simpleMessage("DSS码流格式解析失败"),
        "tr_ErrorCode_Minus_215110":
            MessageLookupByLibrary.simpleMessage("解析雄迈云返回的视频广场url失败"),
        "tr_ErrorCode_Minus_215120":
            MessageLookupByLibrary.simpleMessage("前端未连接视频源"),
        "tr_ErrorCode_Minus_215121":
            MessageLookupByLibrary.simpleMessage("前端未连接视频源"),
        "tr_ErrorCode_Minus_215122":
            MessageLookupByLibrary.simpleMessage("前端不支持此种码流"),
        "tr_ErrorCode_Minus_215124":
            MessageLookupByLibrary.simpleMessage("DSS 不能使用组合编码通道进行打开，请重新打开"),
        "tr_ErrorCode_Minus_215130":
            MessageLookupByLibrary.simpleMessage("无效请求"),
        "tr_ErrorCode_Minus_215131":
            MessageLookupByLibrary.simpleMessage("媒体视频链接达到最大，访问受限"),
        "tr_ErrorCode_Minus_215140":
            MessageLookupByLibrary.simpleMessage("无效的令牌格式"),
        "tr_ErrorCode_Minus_215141":
            MessageLookupByLibrary.simpleMessage("不匹配令牌序列号"),
        "tr_ErrorCode_Minus_215142":
            MessageLookupByLibrary.simpleMessage("远程ip不匹配令牌ip"),
        "tr_ErrorCode_Minus_215143":
            MessageLookupByLibrary.simpleMessage("令牌到期"),
        "tr_ErrorCode_Minus_215144":
            MessageLookupByLibrary.simpleMessage("获取秘钥key失败"),
        "tr_ErrorCode_Minus_215145":
            MessageLookupByLibrary.simpleMessage("令牌不符"),
        "tr_ErrorCode_Minus_215146":
            MessageLookupByLibrary.simpleMessage("令牌数据无效格式"),
        "tr_ErrorCode_Minus_215147":
            MessageLookupByLibrary.simpleMessage("解密秘钥数据失败"),
        "tr_ErrorCode_Minus_215148":
            MessageLookupByLibrary.simpleMessage("Authcode不匹配"),
        "tr_ErrorCode_Minus_215149":
            MessageLookupByLibrary.simpleMessage("更改了authcode"),
        "tr_ErrorCode_Minus_221201":
            MessageLookupByLibrary.simpleMessage("报警授权码错误"),
        "tr_ErrorCode_Minus_221202":
            MessageLookupByLibrary.simpleMessage("该功能不支持"),
        "tr_ErrorCode_Minus_222400":
            MessageLookupByLibrary.simpleMessage("没有查询到当天的录像文件"),
        "tr_ErrorCode_Minus_223000":
            MessageLookupByLibrary.simpleMessage("url为空"),
        "tr_ErrorCode_Minus_223001":
            MessageLookupByLibrary.simpleMessage("打开失败"),
        "tr_ErrorCode_Minus_223002":
            MessageLookupByLibrary.simpleMessage("获取流信息失败"),
        "tr_ErrorCode_Minus_223003":
            MessageLookupByLibrary.simpleMessage("获取视频流信息失败"),
        "tr_ErrorCode_Minus_223010":
            MessageLookupByLibrary.simpleMessage("无法获取视频流"),
        "tr_ErrorCode_Minus_223100":
            MessageLookupByLibrary.simpleMessage("打开telnet失败"),
        "tr_ErrorCode_Minus_225402":
            MessageLookupByLibrary.simpleMessage("服务器错误"),
        "tr_ErrorCode_Minus_225501":
            MessageLookupByLibrary.simpleMessage("重要参数校验失败（字段缺失，类型不匹配，为空字符串）"),
        "tr_ErrorCode_Minus_225502":
            MessageLookupByLibrary.simpleMessage("获取redis的ip，port失败"),
        "tr_ErrorCode_Minus_225503":
            MessageLookupByLibrary.simpleMessage("redis建立连接失败"),
        "tr_ErrorCode_Minus_225504":
            MessageLookupByLibrary.simpleMessage("redis操作失败"),
        "tr_ErrorCode_Minus_225505":
            MessageLookupByLibrary.simpleMessage("获取mysql地址失败"),
        "tr_ErrorCode_Minus_225506":
            MessageLookupByLibrary.simpleMessage("SQL语句输入参数校验失败，（可能存在SQL注入）"),
        "tr_ErrorCode_Minus_225507":
            MessageLookupByLibrary.simpleMessage("SQL操作失败"),
        "tr_ErrorCode_Minus_225508":
            MessageLookupByLibrary.simpleMessage("缩略图url与url过期时间获取失败"),
        "tr_ErrorCode_Minus_225509":
            MessageLookupByLibrary.simpleMessage("时间格式校验失败，时间戳转化失败"),
        "tr_ErrorCode_Minus_225510":
            MessageLookupByLibrary.simpleMessage("云存储套餐信息异常"),
        "tr_ErrorCode_Minus_225511":
            MessageLookupByLibrary.simpleMessage("未知不合法查询类型，非MSG或VIDEO"),
        "tr_ErrorCode_Minus_225512":
            MessageLookupByLibrary.simpleMessage("查询的开始时间与结束时间不在同一天"),
        "tr_ErrorCode_Minus_225513":
            MessageLookupByLibrary.simpleMessage("sn格式不合法"),
        "tr_ErrorCode_Minus_225514": MessageLookupByLibrary.simpleMessage(
            "未知不合法清除类型，非（ALL，ALARM，VIDEO）"),
        "tr_ErrorCode_Minus_225515":
            MessageLookupByLibrary.simpleMessage("未知的订阅查询协议格式"),
        "tr_ErrorCode_Minus_225516":
            MessageLookupByLibrary.simpleMessage("非白名单内IP请求（仅针对云信息删除接口）"),
        "tr_ErrorCode_Minus_225517":
            MessageLookupByLibrary.simpleMessage("未获取到此用户可查询的时间区域"),
        "tr_ErrorCode_Minus_225518":
            MessageLookupByLibrary.simpleMessage("json数据格式校验失败"),
        "tr_ErrorCode_Minus_225519":
            MessageLookupByLibrary.simpleMessage("获取消息免打扰时间段配置数据格式解析错误"),
        "tr_ErrorCode_Minus_226003":
            MessageLookupByLibrary.simpleMessage("不能设置只读配置"),
        "tr_ErrorCode_Minus_300000":
            MessageLookupByLibrary.simpleMessage("获取Auth Error"),
        "tr_ErrorCode_Minus_400000":
            MessageLookupByLibrary.simpleMessage("心跳超时"),
        "tr_ErrorCode_Minus_400001":
            MessageLookupByLibrary.simpleMessage("文件不存在"),
        "tr_ErrorCode_Minus_400002":
            MessageLookupByLibrary.simpleMessage("设备正在升级中"),
        "tr_ErrorCode_Minus_400003":
            MessageLookupByLibrary.simpleMessage("服务器初始化失败"),
        "tr_ErrorCode_Minus_400004":
            MessageLookupByLibrary.simpleMessage("获取连接类型失败"),
        "tr_ErrorCode_Minus_400005":
            MessageLookupByLibrary.simpleMessage("查询服务器失败"),
        "tr_ErrorCode_Minus_400006":
            MessageLookupByLibrary.simpleMessage("设备已经连接"),
        "tr_ErrorCode_Minus_400007":
            MessageLookupByLibrary.simpleMessage("正在登录"),
        "tr_ErrorCode_Minus_400008":
            MessageLookupByLibrary.simpleMessage("设备可能不在线，请稍候重试"),
        "tr_ErrorCode_Minus_400009":
            MessageLookupByLibrary.simpleMessage("设备不支持"),
        "tr_ErrorCode_Minus_400010":
            MessageLookupByLibrary.simpleMessage("没有当天图片，请切换日期"),
        "tr_ErrorCode_Minus_400011":
            MessageLookupByLibrary.simpleMessage("断开连接失败"),
        "tr_ErrorCode_Minus_400012":
            MessageLookupByLibrary.simpleMessage("有其它用户正在使用对讲功能，请稍候再试!"),
        "tr_ErrorCode_Minus_400013":
            MessageLookupByLibrary.simpleMessage("有其他用户正在使用对讲功能，请稍后再试！"),
        "tr_ErrorCode_Minus_400014":
            MessageLookupByLibrary.simpleMessage("备份到u盘失败"),
        "tr_ErrorCode_Minus_400015":
            MessageLookupByLibrary.simpleMessage("无存储设备(u盘)或设备没在录像"),
        "tr_ErrorCode_Minus_400017":
            MessageLookupByLibrary.simpleMessage("抓图失败"),
        "tr_ErrorCode_Minus_400018":
            MessageLookupByLibrary.simpleMessage("超出文件大小限制"),
        "tr_ErrorCode_Minus_400019":
            MessageLookupByLibrary.simpleMessage("文件大小校验失败"),
        "tr_ErrorCode_Minus_400100":
            MessageLookupByLibrary.simpleMessage("对讲未开启"),
        "tr_ErrorCode_Minus_400101":
            MessageLookupByLibrary.simpleMessage("设备存储已满"),
        "tr_ErrorCode_Minus_400102":
            MessageLookupByLibrary.simpleMessage("获取登录加密信息未得到明确的结果（支持/不支持）"),
        "tr_ErrorCode_Minus_400201":
            MessageLookupByLibrary.simpleMessage("内存不足"),
        "tr_ErrorCode_Minus_400202":
            MessageLookupByLibrary.simpleMessage("升级文件格式不对"),
        "tr_ErrorCode_Minus_400203":
            MessageLookupByLibrary.simpleMessage("某个分区升级失败"),
        "tr_ErrorCode_Minus_400204":
            MessageLookupByLibrary.simpleMessage("硬件型号不匹配"),
        "tr_ErrorCode_Minus_400205":
            MessageLookupByLibrary.simpleMessage("客户信息不匹配"),
        "tr_ErrorCode_Minus_400206": MessageLookupByLibrary.simpleMessage(
            "升级程序的兼容版本号比设备现在的小，不允许设备升级回老程序"),
        "tr_ErrorCode_Minus_400207":
            MessageLookupByLibrary.simpleMessage("无效的版本"),
        "tr_ErrorCode_Minus_400208": MessageLookupByLibrary.simpleMessage(
            "升级程序里Wi-Fi驱动和设备当前在使用的Wi-Fi网卡不匹配"),
        "tr_ErrorCode_Minus_400209":
            MessageLookupByLibrary.simpleMessage("网络出错"),
        "tr_ErrorCode_Minus_400210":
            MessageLookupByLibrary.simpleMessage("升级程序不支持设备使用的flash"),
        "tr_ErrorCode_Minus_400211":
            MessageLookupByLibrary.simpleMessage("升级文件被修改，不能通过外网升级"),
        "tr_ErrorCode_Minus_400212":
            MessageLookupByLibrary.simpleMessage("升级此固件需要特殊能力支持"),
        "tr_ErrorCode_Minus_4101":
            MessageLookupByLibrary.simpleMessage("设备暂时无法播放"),
        "tr_ErrorCode_Minus_500000":
            MessageLookupByLibrary.simpleMessage("参数编码格式错误（如要求格式为UTF8，但传入gbk）"),
        "tr_ErrorCode_Minus_500001":
            MessageLookupByLibrary.simpleMessage("参数不是JSON格式"),
        "tr_ErrorCode_Minus_500003":
            MessageLookupByLibrary.simpleMessage("网络错误，请稍后重试"),
        "tr_ErrorCode_Minus_515000":
            MessageLookupByLibrary.simpleMessage("设备离线"),
        "tr_ErrorCode_Minus_515001":
            MessageLookupByLibrary.simpleMessage("设备没有上报过"),
        "tr_ErrorCode_Minus_515002":
            MessageLookupByLibrary.simpleMessage("通道不符合"),
        "tr_ErrorCode_Minus_515003":
            MessageLookupByLibrary.simpleMessage("通道不在线"),
        "tr_ErrorCode_Minus_515004":
            MessageLookupByLibrary.simpleMessage("账号错误"),
        "tr_ErrorCode_Minus_515100":
            MessageLookupByLibrary.simpleMessage("参数错误"),
        "tr_ErrorCode_Minus_515101":
            MessageLookupByLibrary.simpleMessage("句柄错误"),
        "tr_ErrorCode_Minus_515102":
            MessageLookupByLibrary.simpleMessage("api 请求失败"),
        "tr_ErrorCode_Minus_515103":
            MessageLookupByLibrary.simpleMessage("播放类型错误"),
        "tr_ErrorCode_Minus_515104":
            MessageLookupByLibrary.simpleMessage("向DSM服务请求设备信息失败"),
        "tr_ErrorCode_Minus_515105":
            MessageLookupByLibrary.simpleMessage("onvif ssid还没有注册上来"),
        "tr_ErrorCode_Minus_515201": MessageLookupByLibrary.simpleMessage(
            "国标预览/回放返回Not Found， 对应sip错误码 404"),
        "tr_ErrorCode_Minus_515202":
            MessageLookupByLibrary.simpleMessage("国标预览/回放失败"),
        "tr_ErrorCode_Minus_515203":
            MessageLookupByLibrary.simpleMessage("国标预览/回放请求发送设备超时没有回复"),
        "tr_ErrorCode_Minus_516100":
            MessageLookupByLibrary.simpleMessage("RTSP协议错误"),
        "tr_ErrorCode_Minus_516101":
            MessageLookupByLibrary.simpleMessage("URL格式错误"),
        "tr_ErrorCode_Minus_516102":
            MessageLookupByLibrary.simpleMessage("没有录像"),
        "tr_ErrorCode_Minus_516103":
            MessageLookupByLibrary.simpleMessage("URL过期"),
        "tr_ErrorCode_Minus_516104":
            MessageLookupByLibrary.simpleMessage("URL鉴权失败"),
        "tr_ErrorCode_Minus_516105":
            MessageLookupByLibrary.simpleMessage("没有流量"),
        "tr_ErrorCode_Minus_516106":
            MessageLookupByLibrary.simpleMessage("向GWM校验URL失败，通信失败"),
        "tr_ErrorCode_Minus_516107":
            MessageLookupByLibrary.simpleMessage("播放失败，xmts通信失败"),
        "tr_ErrorCode_Minus_516108":
            MessageLookupByLibrary.simpleMessage("查询录像失败"),
        "tr_ErrorCode_Minus_516109":
            MessageLookupByLibrary.simpleMessage("错误的SeekTime时间"),
        "tr_ErrorCode_Minus_516110":
            MessageLookupByLibrary.simpleMessage("没有这个url信息"),
        "tr_ErrorCode_Minus_516111":
            MessageLookupByLibrary.simpleMessage("token解析失败"),
        "tr_ErrorCode_Minus_516112":
            MessageLookupByLibrary.simpleMessage("payload失败"),
        "tr_ErrorCode_Minus_516113":
            MessageLookupByLibrary.simpleMessage("更新到redis失败"),
        "tr_ErrorCode_Minus_516114":
            MessageLookupByLibrary.simpleMessage("URL不被允许播放"),
        "tr_ErrorCode_Minus_516115":
            MessageLookupByLibrary.simpleMessage("URL超过允许并发数"),
        "tr_ErrorCode_Minus_603000": MessageLookupByLibrary.simpleMessage(
            "FunSDK证书合法性验证校验失败*不合法UUID或者AppKey不允许使用"),
        "tr_ErrorCode_Minus_603001":
            MessageLookupByLibrary.simpleMessage("JSON数据格式校验失败"),
        "tr_ErrorCode_Minus_603002":
            MessageLookupByLibrary.simpleMessage("登录用户名或密码为空"),
        "tr_ErrorCode_Minus_603003":
            MessageLookupByLibrary.simpleMessage("登录Token为空"),
        "tr_ErrorCode_Minus_603004": MessageLookupByLibrary.simpleMessage(
            "第三方登录类型参数为空(微信--wx是Google是gg，Facebook是fb，line是line)"),
        "tr_ErrorCode_Minus_604000":
            MessageLookupByLibrary.simpleMessage("用户名或密码错误"),
        "tr_ErrorCode_Minus_604010":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_604011":
            MessageLookupByLibrary.simpleMessage("两次密码不一致"),
        "tr_ErrorCode_Minus_604012":
            MessageLookupByLibrary.simpleMessage("用户名已被注册"),
        "tr_ErrorCode_Minus_604013":
            MessageLookupByLibrary.simpleMessage("用户名为空"),
        "tr_ErrorCode_Minus_604014":
            MessageLookupByLibrary.simpleMessage("密码为空"),
        "tr_ErrorCode_Minus_604015":
            MessageLookupByLibrary.simpleMessage("确认密码为空"),
        "tr_ErrorCode_Minus_604016":
            MessageLookupByLibrary.simpleMessage("手机号为空"),
        "tr_ErrorCode_Minus_604017":
            MessageLookupByLibrary.simpleMessage("用户名格式不正确"),
        "tr_ErrorCode_Minus_604018":
            MessageLookupByLibrary.simpleMessage("新密码不符合要求，密码8-64位必须包含数字和字母"),
        "tr_ErrorCode_Minus_604019":
            MessageLookupByLibrary.simpleMessage("确认密码格式不正确"),
        "tr_ErrorCode_Minus_604020":
            MessageLookupByLibrary.simpleMessage("手机号格式不正确"),
        "tr_ErrorCode_Minus_604021":
            MessageLookupByLibrary.simpleMessage("手机号已被注册"),
        "tr_ErrorCode_Minus_604022":
            MessageLookupByLibrary.simpleMessage("该手机号未注册"),
        "tr_ErrorCode_Minus_604023":
            MessageLookupByLibrary.simpleMessage("邮箱已被注册"),
        "tr_ErrorCode_Minus_604024":
            MessageLookupByLibrary.simpleMessage("邮箱不存在"),
        "tr_ErrorCode_Minus_604026":
            MessageLookupByLibrary.simpleMessage("原始密码错误"),
        "tr_ErrorCode_Minus_604027":
            MessageLookupByLibrary.simpleMessage("修改密码失败"),
        "tr_ErrorCode_Minus_604029":
            MessageLookupByLibrary.simpleMessage("用户ID为空"),
        "tr_ErrorCode_Minus_604030":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_604031":
            MessageLookupByLibrary.simpleMessage("邮箱为空"),
        "tr_ErrorCode_Minus_604032":
            MessageLookupByLibrary.simpleMessage("邮箱格式不正确"),
        "tr_ErrorCode_Minus_604033":
            MessageLookupByLibrary.simpleMessage("用户无权限"),
        "tr_ErrorCode_Minus_604034":
            MessageLookupByLibrary.simpleMessage("用户未绑定"),
        "tr_ErrorCode_Minus_604035":
            MessageLookupByLibrary.simpleMessage("用户绑定失败"),
        "tr_ErrorCode_Minus_604036":
            MessageLookupByLibrary.simpleMessage("手机绑定失败"),
        "tr_ErrorCode_Minus_604037":
            MessageLookupByLibrary.simpleMessage("邮箱绑定失败"),
        "tr_ErrorCode_Minus_604038":
            MessageLookupByLibrary.simpleMessage("发送验证码超过最大次数"),
        "tr_ErrorCode_Minus_604039":
            MessageLookupByLibrary.simpleMessage("注册失败"),
        "tr_ErrorCode_Minus_604040":
            MessageLookupByLibrary.simpleMessage("微信已绑定用户"),
        "tr_ErrorCode_Minus_604041":
            MessageLookupByLibrary.simpleMessage("没有权限修改用户名（仅针对生成的匿名用户修改）"),
        "tr_ErrorCode_Minus_604042":
            MessageLookupByLibrary.simpleMessage("用户没有绑定facebook"),
        "tr_ErrorCode_Minus_604043":
            MessageLookupByLibrary.simpleMessage("用户绑定facebook失败"),
        "tr_ErrorCode_Minus_604044":
            MessageLookupByLibrary.simpleMessage("用户没有google绑定"),
        "tr_ErrorCode_Minus_604045":
            MessageLookupByLibrary.simpleMessage("用户绑定google失败"),
        "tr_ErrorCode_Minus_604046":
            MessageLookupByLibrary.simpleMessage("Line账户未绑定"),
        "tr_ErrorCode_Minus_604047":
            MessageLookupByLibrary.simpleMessage("Line账户绑定失败"),
        "tr_ErrorCode_Minus_604048":
            MessageLookupByLibrary.simpleMessage("用户验证码错误次数太多,验证码失效,请24小时后重试"),
        "tr_ErrorCode_Minus_604049":
            MessageLookupByLibrary.simpleMessage("登录错误次数太多，请十分钟后重试"),
        "tr_ErrorCode_Minus_604050":
            MessageLookupByLibrary.simpleMessage("请求太频繁，请稍后尝试"),
        "tr_ErrorCode_Minus_604100":
            MessageLookupByLibrary.simpleMessage("设备非法不允许添加"),
        "tr_ErrorCode_Minus_604101":
            MessageLookupByLibrary.simpleMessage("设备已经存在"),
        "tr_ErrorCode_Minus_604102":
            MessageLookupByLibrary.simpleMessage("删除设备失败"),
        "tr_ErrorCode_Minus_604103":
            MessageLookupByLibrary.simpleMessage("设备信息修改失败"),
        "tr_ErrorCode_Minus_604104":
            MessageLookupByLibrary.simpleMessage("设备uuid参数异常"),
        "tr_ErrorCode_Minus_604105":
            MessageLookupByLibrary.simpleMessage("设备用户名参数异常"),
        "tr_ErrorCode_Minus_604106":
            MessageLookupByLibrary.simpleMessage("设备密码参数异常"),
        "tr_ErrorCode_Minus_604107":
            MessageLookupByLibrary.simpleMessage("设备端口参数异常"),
        "tr_ErrorCode_Minus_604108":
            MessageLookupByLibrary.simpleMessage("设备扩展字段参数异常"),
        "tr_ErrorCode_Minus_604109":
            MessageLookupByLibrary.simpleMessage("位置错误"),
        "tr_ErrorCode_Minus_604110":
            MessageLookupByLibrary.simpleMessage("新密码校验失败"),
        "tr_ErrorCode_Minus_604111":
            MessageLookupByLibrary.simpleMessage("确认密码校验失败"),
        "tr_ErrorCode_Minus_604112":
            MessageLookupByLibrary.simpleMessage("设备别名校验失败"),
        "tr_ErrorCode_Minus_604113":
            MessageLookupByLibrary.simpleMessage("设备ip地址错误"),
        "tr_ErrorCode_Minus_604114":
            MessageLookupByLibrary.simpleMessage("支持云存储"),
        "tr_ErrorCode_Minus_604115":
            MessageLookupByLibrary.simpleMessage("不支持云存储"),
        "tr_ErrorCode_Minus_604116": MessageLookupByLibrary.simpleMessage(
            "将设备主账户移交给其他用户失败，检查用户是否拥有设备并且该用户拥有设备主账户权限"),
        "tr_ErrorCode_Minus_604117":
            MessageLookupByLibrary.simpleMessage("当前账户不是当前设备的主账户"),
        "tr_ErrorCode_Minus_604118":
            MessageLookupByLibrary.simpleMessage("设备不存在了已经被移除了"),
        "tr_ErrorCode_Minus_604119":
            MessageLookupByLibrary.simpleMessage("其他账户已添加该设备"),
        "tr_ErrorCode_Minus_604120":
            MessageLookupByLibrary.simpleMessage("您的账号下的设备数已经达到最大，无法再添加设备了！"),
        "tr_ErrorCode_Minus_604124":
            MessageLookupByLibrary.simpleMessage("添加设备失败，分享的设备已经被分享者取消或者删除"),
        "tr_ErrorCode_Minus_604126":
            MessageLookupByLibrary.simpleMessage("该设备已被绑定，需解绑后才能添加"),
        "tr_ErrorCode_Minus_604127":
            MessageLookupByLibrary.simpleMessage("添加设备失败，请尝试其他方式"),
        "tr_ErrorCode_Minus_604128":
            MessageLookupByLibrary.simpleMessage("添加设备失败，设备校验码非法"),
        "tr_ErrorCode_Minus_604200":
            MessageLookupByLibrary.simpleMessage("添加授权失败"),
        "tr_ErrorCode_Minus_604201":
            MessageLookupByLibrary.simpleMessage("修改授权失败"),
        "tr_ErrorCode_Minus_604202":
            MessageLookupByLibrary.simpleMessage("删除授权失败"),
        "tr_ErrorCode_Minus_604203": MessageLookupByLibrary.simpleMessage(
            "单个授权同步失败(原因可能是type参数不对,或者云产品线未返回)"),
        "tr_ErrorCode_Minus_604300":
            MessageLookupByLibrary.simpleMessage("验证码发送失败，请检查输入是否正确"),
        "tr_ErrorCode_Minus_604301":
            MessageLookupByLibrary.simpleMessage("邮箱签名失败"),
        "tr_ErrorCode_Minus_604302":
            MessageLookupByLibrary.simpleMessage("注销账号需要验证码"),
        "tr_ErrorCode_Minus_604303":
            MessageLookupByLibrary.simpleMessage("今日获取邮箱链接次数已超过限制，请24小时之后再试"),
        "tr_ErrorCode_Minus_604304":
            MessageLookupByLibrary.simpleMessage("今日获取邮箱链接次数已超过限制，请24小时之后再试"),
        "tr_ErrorCode_Minus_604400":
            MessageLookupByLibrary.simpleMessage("短信接口验证失败，请联系我们"),
        "tr_ErrorCode_Minus_604401":
            MessageLookupByLibrary.simpleMessage("短信接口参数错误，请联系我们"),
        "tr_ErrorCode_Minus_604402":
            MessageLookupByLibrary.simpleMessage("24小时内获取验证码次数不能超过3次"),
        "tr_ErrorCode_Minus_604403":
            MessageLookupByLibrary.simpleMessage("验证码发送失败，请检查输入是否正确"),
        "tr_ErrorCode_Minus_604404":
            MessageLookupByLibrary.simpleMessage("120秒之内只能发送一次"),
        "tr_ErrorCode_Minus_604405":
            MessageLookupByLibrary.simpleMessage("发送失败"),
        "tr_ErrorCode_Minus_604500":
            MessageLookupByLibrary.simpleMessage("未查到用户列表或用户列表为空"),
        "tr_ErrorCode_Minus_604502":
            MessageLookupByLibrary.simpleMessage("未查到设备列表或设备列表为空"),
        "tr_ErrorCode_Minus_604503":
            MessageLookupByLibrary.simpleMessage("重置 app secret 失败"),
        "tr_ErrorCode_Minus_604600":
            MessageLookupByLibrary.simpleMessage("微信报警打开失败"),
        "tr_ErrorCode_Minus_604601":
            MessageLookupByLibrary.simpleMessage("微信报警关闭失败"),
        "tr_ErrorCode_Minus_605000":
            MessageLookupByLibrary.simpleMessage("服务器故障"),
        "tr_ErrorCode_Minus_605001":
            MessageLookupByLibrary.simpleMessage("证书不存在"),
        "tr_ErrorCode_Minus_605002":
            MessageLookupByLibrary.simpleMessage("请求头信息错误"),
        "tr_ErrorCode_Minus_605003":
            MessageLookupByLibrary.simpleMessage("证书失效"),
        "tr_ErrorCode_Minus_605004":
            MessageLookupByLibrary.simpleMessage("生成密钥校验错误"),
        "tr_ErrorCode_Minus_605005":
            MessageLookupByLibrary.simpleMessage("参数异常"),
        "tr_ErrorCode_Minus_605006":
            MessageLookupByLibrary.simpleMessage("连接失败"),
        "tr_ErrorCode_Minus_605007":
            MessageLookupByLibrary.simpleMessage("未知错误"),
        "tr_ErrorCode_Minus_605008":
            MessageLookupByLibrary.simpleMessage("ip地址不允许接入"),
        "tr_ErrorCode_Minus_605009":
            MessageLookupByLibrary.simpleMessage("解密错误(第三方登录code错误或者AES加解密错误)"),
        "tr_ErrorCode_Minus_605010":
            MessageLookupByLibrary.simpleMessage("token已过期"),
        "tr_ErrorCode_Minus_605011":
            MessageLookupByLibrary.simpleMessage("Token错误"),
        "tr_ErrorCode_Minus_605012":
            MessageLookupByLibrary.simpleMessage("token无权限"),
        "tr_ErrorCode_Minus_605013":
            MessageLookupByLibrary.simpleMessage("不支持"),
        "tr_ErrorCode_Minus_605017":
            MessageLookupByLibrary.simpleMessage("消息码失效了！"),
        "tr_ErrorCode_Minus_66000":
            MessageLookupByLibrary.simpleMessage("无效登录方式"),
        "tr_ErrorCode_Minus_661412":
            MessageLookupByLibrary.simpleMessage("用户名不存在"),
        "tr_ErrorCode_Minus_661427":
            MessageLookupByLibrary.simpleMessage("新密码格式不正确"),
        "tr_ErrorCode_Minus_70000":
            MessageLookupByLibrary.simpleMessage("透转DVR的错误值"),
        "tr_ErrorCode_Minus_70101":
            MessageLookupByLibrary.simpleMessage("未知错误"),
        "tr_ErrorCode_Minus_70102":
            MessageLookupByLibrary.simpleMessage("版本不支持"),
        "tr_ErrorCode_Minus_70103":
            MessageLookupByLibrary.simpleMessage("非法请求"),
        "tr_ErrorCode_Minus_70104":
            MessageLookupByLibrary.simpleMessage("用户已登录"),
        "tr_ErrorCode_Minus_70105":
            MessageLookupByLibrary.simpleMessage("用户未登录"),
        "tr_ErrorCode_Minus_70106":
            MessageLookupByLibrary.simpleMessage("用户名或密码不正确"),
        "tr_ErrorCode_Minus_70107":
            MessageLookupByLibrary.simpleMessage("无设备功能权限"),
        "tr_ErrorCode_Minus_70108": MessageLookupByLibrary.simpleMessage("超时"),
        "tr_ErrorCode_Minus_70109":
            MessageLookupByLibrary.simpleMessage("搜索失败，未找到相应的文件"),
        "tr_ErrorCode_Minus_70110":
            MessageLookupByLibrary.simpleMessage("搜索成功，返回所有文件"),
        "tr_ErrorCode_Minus_70111":
            MessageLookupByLibrary.simpleMessage("搜索成功，返回部分文件"),
        "tr_ErrorCode_Minus_70112":
            MessageLookupByLibrary.simpleMessage("用户已存在"),
        "tr_ErrorCode_Minus_70113":
            MessageLookupByLibrary.simpleMessage("该用户不存在"),
        "tr_ErrorCode_Minus_70114":
            MessageLookupByLibrary.simpleMessage("该用户组已经存在"),
        "tr_ErrorCode_Minus_70115":
            MessageLookupByLibrary.simpleMessage("该用户组不存在"),
        "tr_ErrorCode_Minus_70116":
            MessageLookupByLibrary.simpleMessage("盗版软件"),
        "tr_ErrorCode_Minus_70117":
            MessageLookupByLibrary.simpleMessage("消息格式不正确"),
        "tr_ErrorCode_Minus_70118":
            MessageLookupByLibrary.simpleMessage("未设置云台协议"),
        "tr_ErrorCode_Minus_70119":
            MessageLookupByLibrary.simpleMessage("未找到录像文件"),
        "tr_ErrorCode_Minus_70120":
            MessageLookupByLibrary.simpleMessage("配置未启用"),
        "tr_ErrorCode_Minus_70121":
            MessageLookupByLibrary.simpleMessage("前端未连接视频源"),
        "tr_ErrorCode_Minus_70122":
            MessageLookupByLibrary.simpleMessage("NAT链接已用尽，不允许新的NAT连接"),
        "tr_ErrorCode_Minus_70123":
            MessageLookupByLibrary.simpleMessage("Tcp视频链接达到最大，不允许新的Tcp视频链接"),
        "tr_ErrorCode_Minus_70124":
            MessageLookupByLibrary.simpleMessage("用户名和密码的加密算法错误"),
        "tr_ErrorCode_Minus_70125":
            MessageLookupByLibrary.simpleMessage("创建了其它用户，不能再用admin登陆"),
        "tr_ErrorCode_Minus_70126":
            MessageLookupByLibrary.simpleMessage("登录太频繁，稍后重试"),
        "tr_ErrorCode_Minus_70128":
            MessageLookupByLibrary.simpleMessage("设备受限，如已购流量套餐，请重启设备。"),
        "tr_ErrorCode_Minus_70129":
            MessageLookupByLibrary.simpleMessage("禁止远程登陆"),
        "tr_ErrorCode_Minus_70130":
            MessageLookupByLibrary.simpleMessage("NAS地址已存在"),
        "tr_ErrorCode_Minus_70131":
            MessageLookupByLibrary.simpleMessage("路径正在被使用，无法操作"),
        "tr_ErrorCode_Minus_70132":
            MessageLookupByLibrary.simpleMessage("NAS已达到支持的最大值,不允许继续添加"),
        "tr_ErrorCode_Minus_70140":
            MessageLookupByLibrary.simpleMessage("消费类产品遥控器绑定按的键错了"),
        "tr_ErrorCode_Minus_70150":
            MessageLookupByLibrary.simpleMessage("成功，设备需要重启"),
        "tr_ErrorCode_Minus_70153":
            MessageLookupByLibrary.simpleMessage("无SD卡"),
        "tr_ErrorCode_Minus_70160":
            MessageLookupByLibrary.simpleMessage("视频备份失败"),
        "tr_ErrorCode_Minus_70161":
            MessageLookupByLibrary.simpleMessage("没有录制设备或设备未录制"),
        "tr_ErrorCode_Minus_70162":
            MessageLookupByLibrary.simpleMessage("设备正在添加过程中"),
        "tr_ErrorCode_Minus_70163":
            MessageLookupByLibrary.simpleMessage("APS客户特殊的密码错误返回值"),
        "tr_ErrorCode_Minus_70164":
            MessageLookupByLibrary.simpleMessage("设备空间不足"),
        "tr_ErrorCode_Minus_70165":
            MessageLookupByLibrary.simpleMessage("设备正忙，当前未使用"),
        "tr_ErrorCode_Minus_70184": MessageLookupByLibrary.simpleMessage(
            "当前设备电量低于”实时录像模式“要求的最低值，请在设备充电后重试"),
        "tr_ErrorCode_Minus_70202": MessageLookupByLibrary.simpleMessage("未登录"),
        "tr_ErrorCode_Minus_70203":
            MessageLookupByLibrary.simpleMessage("登录设备密码错误"),
        "tr_ErrorCode_Minus_70205":
            MessageLookupByLibrary.simpleMessage("非法用户"),
        "tr_ErrorCode_Minus_70206":
            MessageLookupByLibrary.simpleMessage("帐户被锁定，登录错误"),
        "tr_ErrorCode_Minus_70207":
            MessageLookupByLibrary.simpleMessage("帐户已列入黑名单"),
        "tr_ErrorCode_Minus_70208":
            MessageLookupByLibrary.simpleMessage("用户已使用"),
        "tr_ErrorCode_Minus_70209":
            MessageLookupByLibrary.simpleMessage("输入无效"),
        "tr_ErrorCode_Minus_70210":
            MessageLookupByLibrary.simpleMessage("如果要添加的用户已经存在，则索引重复"),
        "tr_ErrorCode_Minus_70211":
            MessageLookupByLibrary.simpleMessage("用于查询时对象不存在"),
        "tr_ErrorCode_Minus_70212":
            MessageLookupByLibrary.simpleMessage("对象不存在"),
        "tr_ErrorCode_Minus_70213":
            MessageLookupByLibrary.simpleMessage("目标正在使用中，如果组已使用，则无法删除"),
        "tr_ErrorCode_Minus_70214":
            MessageLookupByLibrary.simpleMessage("子集超出范围"),
        "tr_ErrorCode_Minus_70215":
            MessageLookupByLibrary.simpleMessage("密码不正确"),
        "tr_ErrorCode_Minus_70216":
            MessageLookupByLibrary.simpleMessage("密码不匹配"),
        "tr_ErrorCode_Minus_70217":
            MessageLookupByLibrary.simpleMessage("保留帐户"),
        "tr_ErrorCode_Minus_70218":
            MessageLookupByLibrary.simpleMessage("系统维护期间无法登录"),
        "tr_ErrorCode_Minus_70219":
            MessageLookupByLibrary.simpleMessage("验证次数超过限制，需重启设备再尝试"),
        "tr_ErrorCode_Minus_70220":
            MessageLookupByLibrary.simpleMessage("答案错误"),
        "tr_ErrorCode_Minus_70222":
            MessageLookupByLibrary.simpleMessage("验证码错误"),
        "tr_ErrorCode_Minus_70502":
            MessageLookupByLibrary.simpleMessage("502 命令不合法"),
        "tr_ErrorCode_Minus_70503":
            MessageLookupByLibrary.simpleMessage("对讲已经开启"),
        "tr_ErrorCode_Minus_70504":
            MessageLookupByLibrary.simpleMessage("对讲未开启"),
        "tr_ErrorCode_Minus_70602":
            MessageLookupByLibrary.simpleMessage("需要重新启动应用程序"),
        "tr_ErrorCode_Minus_70603":
            MessageLookupByLibrary.simpleMessage("需要重新启动设备"),
        "tr_ErrorCode_Minus_70604":
            MessageLookupByLibrary.simpleMessage("写入文件失败"),
        "tr_ErrorCode_Minus_70605":
            MessageLookupByLibrary.simpleMessage("功能不支持"),
        "tr_ErrorCode_Minus_70606":
            MessageLookupByLibrary.simpleMessage("验证失败"),
        "tr_ErrorCode_Minus_70607":
            MessageLookupByLibrary.simpleMessage("配置解析错误"),
        "tr_ErrorCode_Minus_70609":
            MessageLookupByLibrary.simpleMessage("配置不存在"),
        "tr_ErrorCode_Minus_79001":
            MessageLookupByLibrary.simpleMessage("未知错误"),
        "tr_ErrorCode_Minus_79002":
            MessageLookupByLibrary.simpleMessage("查询服务器失败"),
        "tr_ErrorCode_Minus_79004": MessageLookupByLibrary.simpleMessage("离线"),
        "tr_ErrorCode_Minus_79005":
            MessageLookupByLibrary.simpleMessage("无法连接到服务器"),
        "tr_ErrorCode_Minus_79007":
            MessageLookupByLibrary.simpleMessage("连接数已满"),
        "tr_ErrorCode_Minus_79008": MessageLookupByLibrary.simpleMessage("未连接"),
        "tr_ErrorCode_Minus_79020":
            MessageLookupByLibrary.simpleMessage("连接超时"),
        "tr_ErrorCode_Minus_79021":
            MessageLookupByLibrary.simpleMessage("连接服务器拒绝连接请求"),
        "tr_ErrorCode_Minus_79022":
            MessageLookupByLibrary.simpleMessage("查询状态超时"),
        "tr_ErrorCode_Minus_79023":
            MessageLookupByLibrary.simpleMessage("查询外网信息超时"),
        "tr_ErrorCode_Minus_79024":
            MessageLookupByLibrary.simpleMessage("网络握手超时"),
        "tr_ErrorCode_Minus_79025":
            MessageLookupByLibrary.simpleMessage("查询服务器失败"),
        "tr_ErrorCode_Minus_79026":
            MessageLookupByLibrary.simpleMessage("心跳超时"),
        "tr_ErrorCode_Minus_79027":
            MessageLookupByLibrary.simpleMessage("连接断开"),
        "tr_ErrorCode_Minus_79998":
            MessageLookupByLibrary.simpleMessage("打开音频失败（设备不支持音频播放）"),
        "tr_ErrorCode_Minus_79999":
            MessageLookupByLibrary.simpleMessage("YUV数据异常"),
        "tr_ErrorCode_Minus_800401":
            MessageLookupByLibrary.simpleMessage("未授权"),
        "tr_ErrorCode_Minus_800403":
            MessageLookupByLibrary.simpleMessage("禁止访问"),
        "tr_ErrorCode_Minus_800404":
            MessageLookupByLibrary.simpleMessage("不存在"),
        "tr_ErrorCode_Minus_800500":
            MessageLookupByLibrary.simpleMessage("服务器内部错误"),
        "tr_ErrorCode_Minus_803004":
            MessageLookupByLibrary.simpleMessage("用户名或密码错误"),
        "tr_ErrorCode_Minus_806002":
            MessageLookupByLibrary.simpleMessage("角色不存在，需要配置角色"),
        "tr_ErrorCode_Minus_90000":
            MessageLookupByLibrary.simpleMessage("用户取消"),
        "tr_ErrorCode_Minus_90001":
            MessageLookupByLibrary.simpleMessage("文件非法"),
        "tr_ErrorCode_Minus_90003":
            MessageLookupByLibrary.simpleMessage("功能超期"),
        "tr_ErrorCode_Minus_90004":
            MessageLookupByLibrary.simpleMessage("达到最大连接数"),
        "tr_ErrorCode_Minus_99975":
            MessageLookupByLibrary.simpleMessage("离线状态"),
        "tr_ErrorCode_Minus_99976":
            MessageLookupByLibrary.simpleMessage("用户列为黑名单"),
        "tr_ErrorCode_Minus_99977":
            MessageLookupByLibrary.simpleMessage("用户被锁定"),
        "tr_ErrorCode_Minus_99978":
            MessageLookupByLibrary.simpleMessage("用户已在其他地方登录"),
        "tr_ErrorCode_Minus_99979":
            MessageLookupByLibrary.simpleMessage("用户名或密码错误"),
        "tr_ErrorCode_Minus_99980":
            MessageLookupByLibrary.simpleMessage("协议解析错误"),
        "tr_ErrorCode_Minus_99981":
            MessageLookupByLibrary.simpleMessage("缓冲区大小不够或缓冲区满"),
        "tr_ErrorCode_Minus_99982":
            MessageLookupByLibrary.simpleMessage("发送缓冲区已满"),
        "tr_ErrorCode_Minus_99983":
            MessageLookupByLibrary.simpleMessage("监听服务器启动失败"),
        "tr_ErrorCode_Minus_99984":
            MessageLookupByLibrary.simpleMessage("禁止连接外网"),
        "tr_ErrorCode_Minus_99985":
            MessageLookupByLibrary.simpleMessage("服务器内部错误"),
        "tr_ErrorCode_Minus_99986":
            MessageLookupByLibrary.simpleMessage("对象繁忙"),
        "tr_ErrorCode_Minus_99987":
            MessageLookupByLibrary.simpleMessage("网络错误（发送失败）"),
        "tr_ErrorCode_Minus_99988":
            MessageLookupByLibrary.simpleMessage("网络接受错误"),
        "tr_ErrorCode_Minus_99989":
            MessageLookupByLibrary.simpleMessage("创建缓存失败"),
        "tr_ErrorCode_Minus_99990":
            MessageLookupByLibrary.simpleMessage("没有找到"),
        "tr_ErrorCode_Minus_99991":
            MessageLookupByLibrary.simpleMessage("您的网络连接超时，请重试"),
        "tr_ErrorCode_Minus_99992":
            MessageLookupByLibrary.simpleMessage("该设备已存在"),
        "tr_ErrorCode_Minus_99993":
            MessageLookupByLibrary.simpleMessage("网络错误"),
        "tr_ErrorCode_Minus_99994": MessageLookupByLibrary.simpleMessage("不支持"),
        "tr_ErrorCode_Minus_99995":
            MessageLookupByLibrary.simpleMessage("读取文件失败"),
        "tr_ErrorCode_Minus_99996":
            MessageLookupByLibrary.simpleMessage("写文件失败"),
        "tr_ErrorCode_Minus_99997":
            MessageLookupByLibrary.simpleMessage("打开文件失败"),
        "tr_ErrorCode_Minus_99998":
            MessageLookupByLibrary.simpleMessage("创建文件失败"),
        "tr_ErrorCode_Minus_99999":
            MessageLookupByLibrary.simpleMessage("参数错误"),
        "tr_ErrorFormatPassword":
            MessageLookupByLibrary.simpleMessage("密码格式不正确"),
        "tr_ErrorFormatPhoneNum":
            MessageLookupByLibrary.simpleMessage("手机号格式不正确"),
        "tr_EventCenter": MessageLookupByLibrary.simpleMessage("事件中心"),
        "tr_FailedRecord": MessageLookupByLibrary.simpleMessage("录像失败"),
        "tr_FailedSetDevicePassword": MessageLookupByLibrary.simpleMessage(
            "若密码设置不成功，请长按设备后方的reset键，进行恢复出厂默认。恢复出厂设置后，需要重新添加设备，重新设置密码。"),
        "tr_FileFormatterNotSupport":
            MessageLookupByLibrary.simpleMessage("不支持的文件格式"),
        "tr_ForgetPassword": MessageLookupByLibrary.simpleMessage("忘记密码"),
        "tr_Format": MessageLookupByLibrary.simpleMessage("格式化"),
        "tr_FormatStorageCard": MessageLookupByLibrary.simpleMessage("存储卡格式化"),
        "tr_Formatting": MessageLookupByLibrary.simpleMessage("格式化中..."),
        "tr_Fri": MessageLookupByLibrary.simpleMessage("五"),
        "tr_Friday": MessageLookupByLibrary.simpleMessage("周五"),
        "tr_Function": MessageLookupByLibrary.simpleMessage("功能"),
        "tr_GBCascade": MessageLookupByLibrary.simpleMessage("国标级联"),
        "tr_GBCascadeAdd": MessageLookupByLibrary.simpleMessage("新建国标级联"),
        "tr_GBCascadeAuthorization":
            MessageLookupByLibrary.simpleMessage("国标级联授权"),
        "tr_GBCascadeAuthorizationRoutes":
            MessageLookupByLibrary.simpleMessage("授权路数不足，请扩容"),
        "tr_GBCascadeChannelSIPID": m1,
        "tr_GBCascadeChannelTips": m2,
        "tr_GBCascadeDetail": MessageLookupByLibrary.simpleMessage("国标级联详情"),
        "tr_GBCascadeEdit": MessageLookupByLibrary.simpleMessage("国标级联详情编辑"),
        "tr_GBCascadePush": MessageLookupByLibrary.simpleMessage("推 送"),
        "tr_GBCascadeViewSIPID":
            MessageLookupByLibrary.simpleMessage("查看SIP ID"),
        "tr_GenIdNumTips": MessageLookupByLibrary.simpleMessage("生成数必须大于0"),
        "tr_GenIdNumTips1":
            MessageLookupByLibrary.simpleMessage("生成通道数必须不能超过128"),
        "tr_GenerateChannels": MessageLookupByLibrary.simpleMessage("生成通道数"),
        "tr_GenerateID": MessageLookupByLibrary.simpleMessage("生成接入ID"),
        "tr_GenerateIDNum": MessageLookupByLibrary.simpleMessage("生成ID数"),
        "tr_GoToSetting": MessageLookupByLibrary.simpleMessage("去设置"),
        "tr_GotIt": MessageLookupByLibrary.simpleMessage("我知道了"),
        "tr_HDAddress": MessageLookupByLibrary.simpleMessage("高清地址"),
        "tr_HDStream": MessageLookupByLibrary.simpleMessage("高清"),
        "tr_HeartbeatCycle": MessageLookupByLibrary.simpleMessage("心跳周期"),
        "tr_HelpAndFeedback": MessageLookupByLibrary.simpleMessage("帮助与反馈"),
        "tr_HoldOnForSearhingDevices":
            MessageLookupByLibrary.simpleMessage("正在搜索附近设备，请等待..."),
        "tr_IFrameInterval": MessageLookupByLibrary.simpleMessage("I帧间隔"),
        "tr_IPC": MessageLookupByLibrary.simpleMessage("IPC"),
        "tr_IPCType": MessageLookupByLibrary.simpleMessage("IPC类型"),
        "tr_IdCheck": MessageLookupByLibrary.simpleMessage("身份验证"),
        "tr_Images": MessageLookupByLibrary.simpleMessage("图片"),
        "tr_ImportantClause": MessageLookupByLibrary.simpleMessage("特别声明"),
        "tr_ImportantClauseContent": MessageLookupByLibrary.simpleMessage(
            "杭州杰峰科技有限公司在此声明，您通过本软件参与的商业活动，与Apple inc.无关"),
        "tr_IndustryCode": MessageLookupByLibrary.simpleMessage("行业编码"),
        "tr_InitiatePersonRectificationTasks": m3,
        "tr_Input": MessageLookupByLibrary.simpleMessage("Input"),
        "tr_InputAccessPassword":
            MessageLookupByLibrary.simpleMessage("请先设置接入密码"),
        "tr_InputCompanyName": MessageLookupByLibrary.simpleMessage("请输入单位名称"),
        "tr_InputConfirmPasswordRule":
            MessageLookupByLibrary.simpleMessage("请输入确认密码，格式为8-16位数字和字母"),
        "tr_InputDeviceNickName":
            MessageLookupByLibrary.simpleMessage("请输入设备名称"),
        "tr_InputDeviceNickNameForSearch":
            MessageLookupByLibrary.simpleMessage("请输入设备名称搜索"),
        "tr_InputFrequency": MessageLookupByLibrary.simpleMessage("请输入观看并发数"),
        "tr_InputNodeNickName": MessageLookupByLibrary.simpleMessage("请输入名称"),
        "tr_InputPassword": MessageLookupByLibrary.simpleMessage("请输入密码"),
        "tr_InputPasswordRule":
            MessageLookupByLibrary.simpleMessage("请输入密码，格式为8-16位数字和字母"),
        "tr_InputPhoneNumber": MessageLookupByLibrary.simpleMessage("请输入手机号"),
        "tr_InputPhoneNumberEmail":
            MessageLookupByLibrary.simpleMessage("请输入手机号/邮箱"),
        "tr_InputPwd": MessageLookupByLibrary.simpleMessage("输入密码"),
        "tr_InputTrueName": MessageLookupByLibrary.simpleMessage("请输入真实姓名"),
        "tr_InputVerifyCode": MessageLookupByLibrary.simpleMessage("输入验证码"),
        "tr_InspectAccordingToPlan":
            MessageLookupByLibrary.simpleMessage("巡检任务"),
        "tr_Inspection": MessageLookupByLibrary.simpleMessage("巡检"),
        "tr_InspectionAnalysis": MessageLookupByLibrary.simpleMessage("巡检分析"),
        "tr_InspectionPlan": MessageLookupByLibrary.simpleMessage("巡检计划"),
        "tr_InspectionRecord": MessageLookupByLibrary.simpleMessage("巡检记录"),
        "tr_InvalidData": MessageLookupByLibrary.simpleMessage("无效数据"),
        "tr_InvalidDevice":
            MessageLookupByLibrary.simpleMessage("设备序列号不合法或者设备已经被添加过"),
        "tr_JFDevice": MessageLookupByLibrary.simpleMessage("杰峰设备"),
        "tr_JFDeviceNotes": MessageLookupByLibrary.simpleMessage("添加杰峰协议的设备"),
        "tr_JFProtocol": MessageLookupByLibrary.simpleMessage("杰峰协议"),
        "tr_LoadedFail": MessageLookupByLibrary.simpleMessage("加载失败"),
        "tr_Loading": MessageLookupByLibrary.simpleMessage("加载中..."),
        "tr_LocalNetworkDisable":
            MessageLookupByLibrary.simpleMessage("本地网络权限未开启"),
        "tr_LocalNetworkDisableNotes":
            MessageLookupByLibrary.simpleMessage("未开启本地网络权限，您将无法对设备进行添加操作"),
        "tr_Login": MessageLookupByLibrary.simpleMessage("登录"),
        "tr_LoginTips1": MessageLookupByLibrary.simpleMessage("我已经阅读并同意"),
        "tr_Logout": MessageLookupByLibrary.simpleMessage("退出登录"),
        "tr_Mail": MessageLookupByLibrary.simpleMessage("邮箱"),
        "tr_MakeSureDeleteDepartment":
            MessageLookupByLibrary.simpleMessage("确定删除部门?"),
        "tr_MakeSureDeleteDevice":
            MessageLookupByLibrary.simpleMessage("确定删除设备?"),
        "tr_MakeSureDeleteNode":
            MessageLookupByLibrary.simpleMessage("确认删除该节点?"),
        "tr_MessageCenter": MessageLookupByLibrary.simpleMessage("消息中心"),
        "tr_ModifyDeviceNickName":
            MessageLookupByLibrary.simpleMessage("修改设备名称"),
        "tr_ModifyDevicePassword": MessageLookupByLibrary.simpleMessage("重设密码"),
        "tr_ModifyDevicePasswordNotes":
            MessageLookupByLibrary.simpleMessage("为了您的设备安全，请修改默认密码"),
        "tr_ModifyPassword": MessageLookupByLibrary.simpleMessage("修改密码"),
        "tr_ModifySuccess": MessageLookupByLibrary.simpleMessage("修改成功"),
        "tr_Mon": MessageLookupByLibrary.simpleMessage("一"),
        "tr_Monday": MessageLookupByLibrary.simpleMessage("周一"),
        "tr_MoveDevicesToCenterSeverNode": MessageLookupByLibrary.simpleMessage(
            "删除中心服务器，将同步删除所属通道和节点，是否确认删除？"),
        "tr_MoveDevicesToRootNVRNode":
            MessageLookupByLibrary.simpleMessage("删除NVR，将同步删除下属所有设备，是否确认删除？"),
        "tr_MoveDevicesToRootNode":
            MessageLookupByLibrary.simpleMessage("若有设备，删除节点后，设备自动归属到根节点下"),
        "tr_NOAlarmMessage": MessageLookupByLibrary.simpleMessage("暂无告警"),
        "tr_NOPersonTips":
            MessageLookupByLibrary.simpleMessage("暂无用户\n请点击右上角添加按钮添加"),
        "tr_NORoleTips":
            MessageLookupByLibrary.simpleMessage("暂无角色\n请点击右上角添加按钮添加"),
        "tr_NVR": MessageLookupByLibrary.simpleMessage("NVR"),
        "tr_Name": MessageLookupByLibrary.simpleMessage("姓名"),
        "tr_NameContentUnchange":
            MessageLookupByLibrary.simpleMessage("输入名称一致"),
        "tr_NationalStandardConfiguration":
            MessageLookupByLibrary.simpleMessage("国标编码配置"),
        "tr_NationalStandardDevice":
            MessageLookupByLibrary.simpleMessage("国标设备"),
        "tr_NationalStandardDeviceNotes":
            MessageLookupByLibrary.simpleMessage("添加国标协议的设备"),
        "tr_NetworkConfiguration": MessageLookupByLibrary.simpleMessage("网络配置"),
        "tr_NetworkConfigurationDeclare":
            MessageLookupByLibrary.simpleMessage("设备暂不支持5G WiFi,仅支持2.4G WiFi"),
        "tr_NetworkIdentification":
            MessageLookupByLibrary.simpleMessage("网络标识"),
        "tr_Next": MessageLookupByLibrary.simpleMessage("下一步"),
        "tr_NoData": MessageLookupByLibrary.simpleMessage("暂无数据"),
        "tr_NoMedia": MessageLookupByLibrary.simpleMessage("暂无视频资源"),
        "tr_NoRole": MessageLookupByLibrary.simpleMessage("暂无角色"),
        "tr_NoSDCardInserted":
            MessageLookupByLibrary.simpleMessage("未插入SD卡，无法录像"),
        "tr_Node": MessageLookupByLibrary.simpleMessage("节点"),
        "tr_NodeEdit": MessageLookupByLibrary.simpleMessage("编辑节点"),
        "tr_NodeName": MessageLookupByLibrary.simpleMessage("节点名称"),
        "tr_NormalMessage": MessageLookupByLibrary.simpleMessage("消息"),
        "tr_NormalSetting": MessageLookupByLibrary.simpleMessage("设置"),
        "tr_NowLatestVersion": MessageLookupByLibrary.simpleMessage("最新版本"),
        "tr_OfficialWebsite": MessageLookupByLibrary.simpleMessage("官网"),
        "tr_OnSiteInspection": MessageLookupByLibrary.simpleMessage("现场巡检"),
        "tr_OnSiteInspectionDetail":
            MessageLookupByLibrary.simpleMessage("现场巡检详情"),
        "tr_OperationByInstructions":
            MessageLookupByLibrary.simpleMessage("按照说明书，给摄像机上电，确保摄像机正常开机"),
        "tr_OprationDeny":
            MessageLookupByLibrary.simpleMessage("该节点下存在子节点，请先删除子节点"),
        "tr_OprationsOfNetworkConfiguration": MessageLookupByLibrary.simpleMessage(
            "· 请将二维码朝向设备镜头;\n· 保持25-35公分的距离，等待扫描;\n· 听到正在配置WiFi提示音后移开手机;\n· 听到配置成功提示音表示设备WiFi配置已完成"),
        "tr_OrganizationStructure":
            MessageLookupByLibrary.simpleMessage("组织结构"),
        "tr_PTZ": MessageLookupByLibrary.simpleMessage("云台"),
        "tr_PTZResetFailed": MessageLookupByLibrary.simpleMessage("云台重置失败"),
        "tr_PTZResetSuccessful": MessageLookupByLibrary.simpleMessage("云台重置成功"),
        "tr_PasswordNotOneMatch": MessageLookupByLibrary.simpleMessage("密码不一致"),
        "tr_Permission_Album": MessageLookupByLibrary.simpleMessage("请允许访问相册！"),
        "tr_Permission_Audio":
            MessageLookupByLibrary.simpleMessage("请允许访问麦克风！"),
        "tr_Permission_Bluetooth": MessageLookupByLibrary.simpleMessage("蓝牙"),
        "tr_Permission_Camera":
            MessageLookupByLibrary.simpleMessage("请打开相机权限！"),
        "tr_Permission_LocalNetwork":
            MessageLookupByLibrary.simpleMessage("本地网络"),
        "tr_Permission_Location": MessageLookupByLibrary.simpleMessage("定位"),
        "tr_PersonDetail": MessageLookupByLibrary.simpleMessage("用户详情"),
        "tr_PersonDetailEdit": MessageLookupByLibrary.simpleMessage("用户详情编辑"),
        "tr_PersonInfo": MessageLookupByLibrary.simpleMessage("个人信息"),
        "tr_PersonManage": MessageLookupByLibrary.simpleMessage("用户管理"),
        "tr_Phone": MessageLookupByLibrary.simpleMessage("手机"),
        "tr_PhoneNum": MessageLookupByLibrary.simpleMessage("手机号"),
        "tr_PlatformDevice": MessageLookupByLibrary.simpleMessage("平台设备"),
        "tr_PlatformDeviceNotes":
            MessageLookupByLibrary.simpleMessage("添加平台协议的设备"),
        "tr_PlayUrlIsInvalid": MessageLookupByLibrary.simpleMessage("播放地址为空"),
        "tr_Playback": MessageLookupByLibrary.simpleMessage("回放"),
        "tr_PleaseAgreeAgreementAndPolicy":
            MessageLookupByLibrary.simpleMessage("请先同意用户协议与隐私政策"),
        "tr_PleaseAgreeProtocol":
            MessageLookupByLibrary.simpleMessage("请先同意协议"),
        "tr_PleaseAllowVisitAllPhoto":
            MessageLookupByLibrary.simpleMessage("请先允许访问所有相册图片"),
        "tr_PleaseConfirmPwd": MessageLookupByLibrary.simpleMessage("请确认密码"),
        "tr_PleaseFillInfo": MessageLookupByLibrary.simpleMessage("请先完善信息"),
        "tr_PleaseInputContent": MessageLookupByLibrary.simpleMessage("请输入内容"),
        "tr_PleaseInputDes": MessageLookupByLibrary.simpleMessage("请输入描述"),
        "tr_PleaseInputMail": MessageLookupByLibrary.simpleMessage("请输入邮箱"),
        "tr_PleaseInputName": MessageLookupByLibrary.simpleMessage("请输入姓名"),
        "tr_PleaseInputNewMail":
            MessageLookupByLibrary.simpleMessage("请输入新的邮箱"),
        "tr_PleaseInputNewPhone":
            MessageLookupByLibrary.simpleMessage("请输入新的手机号"),
        "tr_PleaseInputNewPwd": MessageLookupByLibrary.simpleMessage("请输入新密码"),
        "tr_PleaseInputNewPwdCon":
            MessageLookupByLibrary.simpleMessage("请确认新密码"),
        "tr_PleaseInputOldPwd": MessageLookupByLibrary.simpleMessage("请输入旧密码"),
        "tr_PleaseInputPwdCon": MessageLookupByLibrary.simpleMessage("请输入确认密码"),
        "tr_PleaseSelect": MessageLookupByLibrary.simpleMessage("请选择"),
        "tr_Preset": MessageLookupByLibrary.simpleMessage("预置点"),
        "tr_PresetNameExisted":
            MessageLookupByLibrary.simpleMessage("预置点名称已经存在，请不要重复命名"),
        "tr_PrivacyPolicy": MessageLookupByLibrary.simpleMessage("隐私政策"),
        "tr_Prompt": MessageLookupByLibrary.simpleMessage("提示"),
        "tr_PwdCon": MessageLookupByLibrary.simpleMessage("确认密码"),
        "tr_QRcodeInvalid":
            MessageLookupByLibrary.simpleMessage("未能识别图片中的二维码信息，请重新选择图片或者重试！"),
        "tr_Record": MessageLookupByLibrary.simpleMessage("记录"),
        "tr_RecordCard": MessageLookupByLibrary.simpleMessage("本地回放"),
        "tr_RecordCloud": MessageLookupByLibrary.simpleMessage("云回放"),
        "tr_Recording": MessageLookupByLibrary.simpleMessage("已开启录像"),
        "tr_RecordingManagement": MessageLookupByLibrary.simpleMessage("录像管理"),
        "tr_RecordingSegment": MessageLookupByLibrary.simpleMessage("录像段"),
        "tr_RecordingSegmentTip":
            MessageLookupByLibrary.simpleMessage("单个录像文件最长的时间"),
        "tr_RecordingSwitch": MessageLookupByLibrary.simpleMessage("录像开关"),
        "tr_RegisterAccount": MessageLookupByLibrary.simpleMessage("注册账号"),
        "tr_Reject": MessageLookupByLibrary.simpleMessage("拒绝"),
        "tr_Remaining": MessageLookupByLibrary.simpleMessage("剩余"),
        "tr_RequestPermission": m4,
        "tr_ResetDevice":
            MessageLookupByLibrary.simpleMessage("若忘记密码，请恢复出厂设置重新添加"),
        "tr_ResetPassword": MessageLookupByLibrary.simpleMessage("重置密码"),
        "tr_RestoreFactorySettings":
            MessageLookupByLibrary.simpleMessage("初始化设备"),
        "tr_RestoreFactorySettingsNotes": MessageLookupByLibrary.simpleMessage(
            "如果没有听到，请长按设备SET/RESET按键约6秒，待听到\"恢复出厂设置，请勿断电\"后松开；待设备恢复出厂设置后，重新连接"),
        "tr_RestoreFactorySettingsNotes2":
            MessageLookupByLibrary.simpleMessage("按键位置具体查看机身标识或者说明书"),
        "tr_RestoreFactorySettingsTip": MessageLookupByLibrary.simpleMessage(
            "如果听到设备提示\"开始快速配置\"或\"等待连接\",点击\"下一步\""),
        "tr_ReviewAccess": MessageLookupByLibrary.simpleMessage("查看接入"),
        "tr_ReviewTips1":
            MessageLookupByLibrary.simpleMessage("您已提交注册申请，请耐心等待"),
        "tr_ReviewTips2": MessageLookupByLibrary.simpleMessage(
            "工作日，我们将在 1 小时内完成审核\n非工作日，我们将在 24 小时内完成审核\n审核结果会以短信方式通知您！\n请及时关注短信通知"),
        "tr_Role": MessageLookupByLibrary.simpleMessage("角色"),
        "tr_RoleDes": MessageLookupByLibrary.simpleMessage("角色描述"),
        "tr_RoleDetail": MessageLookupByLibrary.simpleMessage("角色详情"),
        "tr_RoleManage": MessageLookupByLibrary.simpleMessage("角色管理"),
        "tr_RoleName": MessageLookupByLibrary.simpleMessage("角色名称"),
        "tr_RulesOfDevicePassword":
            MessageLookupByLibrary.simpleMessage("密码长度8-64位，由字母和数字组成"),
        "tr_SCanQRCode": MessageLookupByLibrary.simpleMessage("扫码识别"),
        "tr_SDAddress": MessageLookupByLibrary.simpleMessage("流畅地址"),
        "tr_SDStream": MessageLookupByLibrary.simpleMessage("流畅"),
        "tr_SIPServerAddress": MessageLookupByLibrary.simpleMessage("SIP服务器地址"),
        "tr_SIPServerDomain": MessageLookupByLibrary.simpleMessage("SIP服务器域"),
        "tr_SIPServerDomain2": MessageLookupByLibrary.simpleMessage("SIP域"),
        "tr_SIPServerIP": MessageLookupByLibrary.simpleMessage("SIP服务器IP"),
        "tr_SIPServerId": MessageLookupByLibrary.simpleMessage("SIP服务器ID"),
        "tr_SIPServerNumber": MessageLookupByLibrary.simpleMessage("SIP服务器号"),
        "tr_SIPServerPort": MessageLookupByLibrary.simpleMessage("SIP服务器端口"),
        "tr_Sat": MessageLookupByLibrary.simpleMessage("六"),
        "tr_Saturday": MessageLookupByLibrary.simpleMessage("周六"),
        "tr_SaveRecordFileFailed":
            MessageLookupByLibrary.simpleMessage("录像保存失败"),
        "tr_SaveRecordFileSuccess":
            MessageLookupByLibrary.simpleMessage("录像保存成功"),
        "tr_ScanJFDevice": MessageLookupByLibrary.simpleMessage("扫描可用设备"),
        "tr_SelectAll": MessageLookupByLibrary.simpleMessage("全选"),
        "tr_SelectDeviceResource":
            MessageLookupByLibrary.simpleMessage("选择设备资源"),
        "tr_SelectItemTips":
            MessageLookupByLibrary.simpleMessage("请先选择想要操作的文件"),
        "tr_SelectNode": MessageLookupByLibrary.simpleMessage("节点选择"),
        "tr_SelectRole": MessageLookupByLibrary.simpleMessage("选择角色"),
        "tr_SelectTime": MessageLookupByLibrary.simpleMessage("选择时间"),
        "tr_SendVerifyCode": MessageLookupByLibrary.simpleMessage("发送验证码"),
        "tr_ServerParams": MessageLookupByLibrary.simpleMessage("服务器参数"),
        "tr_ServiceParams": MessageLookupByLibrary.simpleMessage("业务参数"),
        "tr_SetPresetName": MessageLookupByLibrary.simpleMessage("请输入预置点名称"),
        "tr_ShakeToInspect": MessageLookupByLibrary.simpleMessage("摇一摇巡检"),
        "tr_Share": MessageLookupByLibrary.simpleMessage("分享"),
        "tr_ShareFailure": MessageLookupByLibrary.simpleMessage("分享失败"),
        "tr_ShareSuccess": MessageLookupByLibrary.simpleMessage("分享成功"),
        "tr_ShareTips1":
            MessageLookupByLibrary.simpleMessage("1.视频图片不能组合分享\n2.视频每次只能分享一个"),
        "tr_ShareTips2": MessageLookupByLibrary.simpleMessage("仅支持分享一个文件"),
        "tr_ShareTips3":
            MessageLookupByLibrary.simpleMessage("视频需要下载,请在下载完成后再分享"),
        "tr_ShareTipsImageMaxNum":
            MessageLookupByLibrary.simpleMessage("图片分享单次不能超过5张"),
        "tr_SnapFailed": MessageLookupByLibrary.simpleMessage("截图失败"),
        "tr_SnapSuccess": MessageLookupByLibrary.simpleMessage("截图成功"),
        "tr_SpotCheckInspection": MessageLookupByLibrary.simpleMessage("抽查巡检"),
        "tr_StandardAccessTips": MessageLookupByLibrary.simpleMessage(
            "添加国际设备，需要将接入ID、SIP服务器IP、SIP服务器端口等信息配置到待接入设备的后台指定位置。在生成接入id详情中可查看对应信息。"),
        "tr_Status": MessageLookupByLibrary.simpleMessage("状态"),
        "tr_StatusPre": MessageLookupByLibrary.simpleMessage("状态:"),
        "tr_StorageCardInfo": MessageLookupByLibrary.simpleMessage("存储卡信息"),
        "tr_StorageCardSetting": MessageLookupByLibrary.simpleMessage("存储卡设置"),
        "tr_Submit": MessageLookupByLibrary.simpleMessage("提交"),
        "tr_SuccessfullyDeleted": MessageLookupByLibrary.simpleMessage("删除成功"),
        "tr_Sun": MessageLookupByLibrary.simpleMessage("日"),
        "tr_Sunday": MessageLookupByLibrary.simpleMessage("周日"),
        "tr_SuperAdmin": MessageLookupByLibrary.simpleMessage("超级管理员"),
        "tr_SureDelete": MessageLookupByLibrary.simpleMessage("确认删除"),
        "tr_SureItemDelete": MessageLookupByLibrary.simpleMessage("确定删除选中项目?"),
        "tr_SureSaveToAlbum":
            MessageLookupByLibrary.simpleMessage("确定保存选中文件到相册?"),
        "tr_TakePhoto": MessageLookupByLibrary.simpleMessage("拍照"),
        "tr_Template": MessageLookupByLibrary.simpleMessage("考评模板"),
        "tr_TheRestrictionsOnWiFi":
            MessageLookupByLibrary.simpleMessage("设备对WiFi有什么要求？"),
        "tr_Thur": MessageLookupByLibrary.simpleMessage("四"),
        "tr_Thursday": MessageLookupByLibrary.simpleMessage("周四"),
        "tr_ToDoList": MessageLookupByLibrary.simpleMessage("待办事项"),
        "tr_Today": MessageLookupByLibrary.simpleMessage("今日"),
        "tr_Tool": MessageLookupByLibrary.simpleMessage("工具"),
        "tr_Tues": MessageLookupByLibrary.simpleMessage("二"),
        "tr_Tuesday": MessageLookupByLibrary.simpleMessage("周二"),
        "tr_TurnOnWiFi":
            MessageLookupByLibrary.simpleMessage("为了您能正常添加设备，需要打开WiFi"),
        "tr_TypeCode": MessageLookupByLibrary.simpleMessage("类型编码"),
        "tr_TypePre": MessageLookupByLibrary.simpleMessage("类型:"),
        "tr_UnUsed": MessageLookupByLibrary.simpleMessage("未使用"),
        "tr_UnknownError": MessageLookupByLibrary.simpleMessage("未知错误"),
        "tr_Used": MessageLookupByLibrary.simpleMessage("已使用"),
        "tr_UserAgreement": MessageLookupByLibrary.simpleMessage("用户协议"),
        "tr_UserServiceAgreement":
            MessageLookupByLibrary.simpleMessage("用户服务协议"),
        "tr_VersionUpdate": MessageLookupByLibrary.simpleMessage("版本更新"),
        "tr_VideoSpotCheckInspection":
            MessageLookupByLibrary.simpleMessage("视频抽查巡检"),
        "tr_Videos": MessageLookupByLibrary.simpleMessage("视频"),
        "tr_View": MessageLookupByLibrary.simpleMessage("查看"),
        "tr_ViewDetail": MessageLookupByLibrary.simpleMessage("查看详情"),
        "tr_Wed": MessageLookupByLibrary.simpleMessage("三"),
        "tr_Wednesday": MessageLookupByLibrary.simpleMessage("周三"),
        "tr_WiFiConfigurationRecommendation": MessageLookupByLibrary.simpleMessage(
            "1. 如果家庭为双频路由器，请检查摄像连接的WiFi是否是5GHz频段的WiFi，请切换连接2.4GHz的WiFi\n\n2. 配置摄像机时建议距离路由器不要太远。\n\n3. 建议连接非桥接的WiFi，因为桥接的原因可能导致您的网络非常不稳定。"),
        "tr_aPatrolTaskHasBeenAssignedToYou":
            MessageLookupByLibrary.simpleMessage("为您分配了巡检任务"),
        "tr_aRectificationTaskHasBeenAssignedToYou":
            MessageLookupByLibrary.simpleMessage("为您分配了整改任务"),
        "tr_abnormalDevice": MessageLookupByLibrary.simpleMessage("异常设备"),
        "tr_abnormalDeviceTip":
            MessageLookupByLibrary.simpleMessage("以下为异常设备，离线或无法打开视频，不参与巡检操作"),
        "tr_aboutDevice": MessageLookupByLibrary.simpleMessage("关于设备"),
        "tr_accept": MessageLookupByLibrary.simpleMessage("接受"),
        "tr_acceptance": MessageLookupByLibrary.simpleMessage("验收"),
        "tr_acceptanceAndSubmissionTip":
            MessageLookupByLibrary.simpleMessage("验收提交前，请判断整改项是否通过"),
        "tr_acceptanceCountRanking":
            MessageLookupByLibrary.simpleMessage("验收数排名"),
        "tr_acceptanceEvent": MessageLookupByLibrary.simpleMessage("验收事件"),
        "tr_acceptanceInstructions":
            MessageLookupByLibrary.simpleMessage("验收说明"),
        "tr_acceptancePassed": MessageLookupByLibrary.simpleMessage("验收通过"),
        "tr_acceptancePerson": MessageLookupByLibrary.simpleMessage("验收人"),
        "tr_accessIdHint": MessageLookupByLibrary.simpleMessage("20个数字"),
        "tr_accessInfo": MessageLookupByLibrary.simpleMessage("接入信息"),
        "tr_accessPsdHint": MessageLookupByLibrary.simpleMessage("英文、数字、特殊符号"),
        "tr_accessStatus": MessageLookupByLibrary.simpleMessage("接入状态"),
        "tr_accountActivation": MessageLookupByLibrary.simpleMessage("账号激活"),
        "tr_accountAddInfoTips":
            MessageLookupByLibrary.simpleMessage("当前设备已经被当前账户或者其他账户添加,请勿重复添加。"),
        "tr_accountAuthorization":
            MessageLookupByLibrary.simpleMessage("子账号授权"),
        "tr_accountAuthorizationStatistics": m5,
        "tr_accountBlacklisted":
            MessageLookupByLibrary.simpleMessage("账号被列为黑名单"),
        "tr_accountError": MessageLookupByLibrary.simpleMessage("账号错误"),
        "tr_accountLocked": MessageLookupByLibrary.simpleMessage("账号已锁定"),
        "tr_accountLoggedIn": MessageLookupByLibrary.simpleMessage("账号已登录"),
        "tr_accountNotExist": MessageLookupByLibrary.simpleMessage("账号不存在"),
        "tr_accountNotLoggedIn": MessageLookupByLibrary.simpleMessage("账号未登录"),
        "tr_accountOverdueAccessFailed":
            MessageLookupByLibrary.simpleMessage("账户欠费，访问流量服务失败"),
        "tr_accountPassword": MessageLookupByLibrary.simpleMessage("账号密码"),
        "tr_activate": MessageLookupByLibrary.simpleMessage("激活"),
        "tr_active": MessageLookupByLibrary.simpleMessage("启用中"),
        "tr_activityName": MessageLookupByLibrary.simpleMessage("活动名称"),
        "tr_actualVideoFrameRateTooLow":
            MessageLookupByLibrary.simpleMessage("实际视频帧帧率太小"),
        "tr_addApplications": MessageLookupByLibrary.simpleMessage("添加常用"),
        "tr_addBatches": MessageLookupByLibrary.simpleMessage("批量添加"),
        "tr_addBatchesDevices": MessageLookupByLibrary.simpleMessage("批量添加设备"),
        "tr_addChannel": MessageLookupByLibrary.simpleMessage("添加通道"),
        "tr_addChannelTips": m6,
        "tr_addChooseBatch": MessageLookupByLibrary.simpleMessage("批量添加"),
        "tr_addChooseContacts": MessageLookupByLibrary.simpleMessage("通讯录导入"),
        "tr_addChooseFaceDatabase":
            MessageLookupByLibrary.simpleMessage("人脸库导入"),
        "tr_addChooseManual": MessageLookupByLibrary.simpleMessage("手动添加"),
        "tr_addDeviceDialogTip1":
            MessageLookupByLibrary.simpleMessage("若忘记密码，请恢复出厂设置重新添加！"),
        "tr_addDeviceDialogTip2": MessageLookupByLibrary.simpleMessage(
            "设备开机超过1小时，无法登录设备，请恢复出厂设置重新添加！"),
        "tr_addDevicesChannelTips": m7,
        "tr_addDiscoveredBatchesDevices":
            MessageLookupByLibrary.simpleMessage("批量添加局域网搜索到的设备"),
        "tr_addException": MessageLookupByLibrary.simpleMessage("添加异常"),
        "tr_addFacialUser": MessageLookupByLibrary.simpleMessage("添加新用户"),
        "tr_addFailed": MessageLookupByLibrary.simpleMessage("添加失败"),
        "tr_addHeatArea": MessageLookupByLibrary.simpleMessage("添加热力区"),
        "tr_addHeatAreaTip": MessageLookupByLibrary.simpleMessage(
            "（1）点击+在平面图上划定区域；\n（2）关联此区域的监控设备；\n（3）配置并关联设备上热力区；"),
        "tr_addHeatZone": MessageLookupByLibrary.simpleMessage("添加热区"),
        "tr_addImageInspectionPlan":
            MessageLookupByLibrary.simpleMessage("添加抓拍计划"),
        "tr_addTime": m8,
        "tr_address": MessageLookupByLibrary.simpleMessage("地址"),
        "tr_advancedSettings": MessageLookupByLibrary.simpleMessage("高级设置"),
        "tr_afterAlarmContinueDetectionFrequencyReturnsToDefault":
            MessageLookupByLibrary.simpleMessage("告警产生后，继续检测（检测频率回到默认）"),
        "tr_afterAlarmCurrentRunningTimeNoDetection":
            MessageLookupByLibrary.simpleMessage("告警产生后，当前运行时间不检测"),
        "tr_afterAlarmNoDetectionForRestOfDay":
            MessageLookupByLibrary.simpleMessage("告警产生后，当天不检测"),
        "tr_afterRectification": MessageLookupByLibrary.simpleMessage("整改后"),
        "tr_afternoon": MessageLookupByLibrary.simpleMessage("下午"),
        "tr_ageGroupDistribution":
            MessageLookupByLibrary.simpleMessage("年龄段占比"),
        "tr_agree": MessageLookupByLibrary.simpleMessage("同意"),
        "tr_aiAlarm": MessageLookupByLibrary.simpleMessage("AI报警"),
        "tr_aiBox": MessageLookupByLibrary.simpleMessage("AI盒子"),
        "tr_aiBoxAdd": MessageLookupByLibrary.simpleMessage("AI盒子添加"),
        "tr_aiBoxAddNameTip":
            MessageLookupByLibrary.simpleMessage("请输入设备名称（默认名称设备序列号）"),
        "tr_aiBoxAddSNTip": MessageLookupByLibrary.simpleMessage("请输入序列号"),
        "tr_aiBoxChannelConfigLimitTip":
            MessageLookupByLibrary.simpleMessage("AI盒子所有通道已配置8路算法，无法配置更多算法。"),
        "tr_aiBoxTip": MessageLookupByLibrary.simpleMessage("添加AI Box设备"),
        "tr_aiDeviceAlarm": MessageLookupByLibrary.simpleMessage("AI设备报警"),
        "tr_aiInspection": MessageLookupByLibrary.simpleMessage("AI 巡检"),
        "tr_aiInspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("AI巡检分析"),
        "tr_aiInspectionAnalysisAlarmCount":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析告警次数数据"),
        "tr_aiInspectionAnalysisAlarmCountByInspectionItem":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析巡检项告警次数占比数据"),
        "tr_aiInspectionAnalysisAlarmCountRanking":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析-告警次数排名数据"),
        "tr_aiInspectionAnalysisAlarmRate":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析告警率数据"),
        "tr_aiInspectionAnalysisAlarmRateRanking":
            MessageLookupByLibrary.simpleMessage("告警率排名数据"),
        "tr_aiInspectionAnalysisAlarmRateRankingByInspectionItem":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析巡检项告警率排名数据"),
        "tr_aiInspectionAnalysisInspectionCount":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析巡检次数数据"),
        "tr_aiInspectionAnalysisInspectionItemDistributionByTime":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析巡检项分布时段数据"),
        "tr_aiInspectionPlan": MessageLookupByLibrary.simpleMessage("AI巡检计划"),
        "tr_aiInspectionRecordDetail":
            MessageLookupByLibrary.simpleMessage("AI巡检记录详情"),
        "tr_aiInspectionRecords":
            MessageLookupByLibrary.simpleMessage("AI巡检记录"),
        "tr_aiInspectionSource": MessageLookupByLibrary.simpleMessage("AI巡检"),
        "tr_aiPlatformAlarm": MessageLookupByLibrary.simpleMessage("AI平台报警"),
        "tr_alarmAIEdit": MessageLookupByLibrary.simpleMessage("AI告警编辑"),
        "tr_alarmCallCount": MessageLookupByLibrary.simpleMessage("告警调用次数"),
        "tr_alarmCount": MessageLookupByLibrary.simpleMessage("告警次数"),
        "tr_alarmCountRanking": MessageLookupByLibrary.simpleMessage("告警次数排名"),
        "tr_alarmCountRankingData":
            MessageLookupByLibrary.simpleMessage("告警次数排名数据"),
        "tr_alarmCountRatio": MessageLookupByLibrary.simpleMessage("告警次数占比"),
        "tr_alarmDescription": MessageLookupByLibrary.simpleMessage("报警描述"),
        "tr_alarmDetail": MessageLookupByLibrary.simpleMessage("告警详情"),
        "tr_alarmDeviceEdit": MessageLookupByLibrary.simpleMessage("设备告警编辑"),
        "tr_alarmGeneratedCount":
            MessageLookupByLibrary.simpleMessage("产生告警次数"),
        "tr_alarmParameters": MessageLookupByLibrary.simpleMessage("告警参数"),
        "tr_alarmPeriod": MessageLookupByLibrary.simpleMessage("报警时段"),
        "tr_alarmRate": MessageLookupByLibrary.simpleMessage("告警率"),
        "tr_alarmRateRanking": MessageLookupByLibrary.simpleMessage("告警率排名"),
        "tr_alarmRuleConfiguration":
            MessageLookupByLibrary.simpleMessage("告警规则配置"),
        "tr_alarmSetting": MessageLookupByLibrary.simpleMessage("告警设置"),
        "tr_alarmSwitch": MessageLookupByLibrary.simpleMessage("消息接收"),
        "tr_alarmSwitchTip":
            MessageLookupByLibrary.simpleMessage("关闭后，手机将无法接收报警推送消息"),
        "tr_alarmTime": MessageLookupByLibrary.simpleMessage("告警时间"),
        "tr_alarmTimeInterval": MessageLookupByLibrary.simpleMessage("报警时间间隔"),
        "tr_alarmType": MessageLookupByLibrary.simpleMessage("报警类型"),
        "tr_alarmVoice": MessageLookupByLibrary.simpleMessage("报警语音"),
        "tr_alertLine": MessageLookupByLibrary.simpleMessage("警戒线"),
        "tr_alertSound": MessageLookupByLibrary.simpleMessage("警戒音"),
        "tr_alertZone": MessageLookupByLibrary.simpleMessage("警戒区域"),
        "tr_algorithParamsDoNotPairedYet":
            MessageLookupByLibrary.simpleMessage("算法参数未配置"),
        "tr_algorithParamsHavePaired":
            MessageLookupByLibrary.simpleMessage("算法参数已配置"),
        "tr_algorithmAndDevice": MessageLookupByLibrary.simpleMessage("算法和设备"),
        "tr_algorithmAnomaly": MessageLookupByLibrary.simpleMessage("算法异常"),
        "tr_algorithmAuthorization":
            MessageLookupByLibrary.simpleMessage("算法授权"),
        "tr_algorithmConfigDescription":
            MessageLookupByLibrary.simpleMessage("关于算法配置的相关描述"),
        "tr_algorithmElectricBikeDetection":
            MessageLookupByLibrary.simpleMessage("电动车检测"),
        "tr_algorithmFaceMaskDetection":
            MessageLookupByLibrary.simpleMessage("口罩检测"),
        "tr_algorithmInvocationStatistics":
            MessageLookupByLibrary.simpleMessage("算法调用统计"),
        "tr_algorithmList": MessageLookupByLibrary.simpleMessage("算法列表"),
        "tr_algorithmName": MessageLookupByLibrary.simpleMessage("算法名称"),
        "tr_algorithmStatistics": MessageLookupByLibrary.simpleMessage("算法统计"),
        "tr_allAlarms": MessageLookupByLibrary.simpleMessage("所有报警"),
        "tr_allAlgorithms": MessageLookupByLibrary.simpleMessage("全部算法"),
        "tr_allAssessmentItems": MessageLookupByLibrary.simpleMessage("所有考评项"),
        "tr_allConditions": MessageLookupByLibrary.simpleMessage("所有条件"),
        "tr_allDay": MessageLookupByLibrary.simpleMessage("全天"),
        "tr_allDayAlarm": MessageLookupByLibrary.simpleMessage("全天报警"),
        "tr_allDayRecording": MessageLookupByLibrary.simpleMessage("全天录像"),
        "tr_allDayRunning": MessageLookupByLibrary.simpleMessage("全天运行"),
        "tr_allDevices": MessageLookupByLibrary.simpleMessage("所有设备"),
        "tr_allDevicesAbnormal": MessageLookupByLibrary.simpleMessage("设备全部异常"),
        "tr_allDevicesOfflineUnderStore":
            MessageLookupByLibrary.simpleMessage("门店下设备全离线"),
        "tr_allDevicesTotal": m9,
        "tr_allFacesUnderDefaultAccount":
            MessageLookupByLibrary.simpleMessage("默认账号下全部人脸"),
        "tr_allInspectors": MessageLookupByLibrary.simpleMessage("全部巡检人"),
        "tr_allNode": MessageLookupByLibrary.simpleMessage("全部节点"),
        "tr_allPendingInspectionImagesProcessed":
            MessageLookupByLibrary.simpleMessage("所有待巡检图片已处理完成"),
        "tr_allPerson": MessageLookupByLibrary.simpleMessage("全部人员"),
        "tr_allSources": MessageLookupByLibrary.simpleMessage("所有来源"),
        "tr_allStatus": MessageLookupByLibrary.simpleMessage("所有状态"),
        "tr_allStores": MessageLookupByLibrary.simpleMessage("所有门店"),
        "tr_alreadyTheFirstOne": MessageLookupByLibrary.simpleMessage("已经是第一个"),
        "tr_alreadyTheLastOne": MessageLookupByLibrary.simpleMessage("已经是最后一个"),
        "tr_amapTitle": MessageLookupByLibrary.simpleMessage("电子地图"),
        "tr_analysisError": MessageLookupByLibrary.simpleMessage("解析异常"),
        "tr_analysisRight": MessageLookupByLibrary.simpleMessage("解析正常"),
        "tr_analysisStop": MessageLookupByLibrary.simpleMessage("解析停止"),
        "tr_anyCondition": MessageLookupByLibrary.simpleMessage("任一条件"),
        "tr_apiRequestFailed": MessageLookupByLibrary.simpleMessage("api 请求失败"),
        "tr_appAccessAuthority": MessageLookupByLibrary.simpleMessage("权限管理"),
        "tr_appAlgorithmCenter": MessageLookupByLibrary.simpleMessage("算法中心"),
        "tr_appAlgorithmConfig": MessageLookupByLibrary.simpleMessage("算法配置"),
        "tr_appAlgorithmList": MessageLookupByLibrary.simpleMessage("云端算法"),
        "tr_appDealCenter": MessageLookupByLibrary.simpleMessage("待办中心"),
        "tr_appDeviceAttribute": MessageLookupByLibrary.simpleMessage("设备属性"),
        "tr_appDeviceSideAlgorithm":
            MessageLookupByLibrary.simpleMessage("端侧算法"),
        "tr_appDevicesTree": MessageLookupByLibrary.simpleMessage("设备管理"),
        "tr_appEnterpriseManagement":
            MessageLookupByLibrary.simpleMessage("企业管理"),
        "tr_appFacialManagement": MessageLookupByLibrary.simpleMessage("人脸管理"),
        "tr_appHumanoidLibrary": MessageLookupByLibrary.simpleMessage("人形库"),
        "tr_appPatrolIndex": MessageLookupByLibrary.simpleMessage("智慧巡检"),
        "tr_appPatrolIndexFlow": MessageLookupByLibrary.simpleMessage("巡检流程"),
        "tr_appSmartCloudStore": MessageLookupByLibrary.simpleMessage("智慧云店"),
        "tr_appSmartStore": MessageLookupByLibrary.simpleMessage("门店列表"),
        "tr_appVideoCloudBase": MessageLookupByLibrary.simpleMessage("视频基座"),
        "tr_appVideoRecord": MessageLookupByLibrary.simpleMessage("录像管理"),
        "tr_appVideoSurveillance": MessageLookupByLibrary.simpleMessage("视频监控"),
        "tr_applicationClassification":
            MessageLookupByLibrary.simpleMessage("应用分类"),
        "tr_areYouSureToDeleteThisPlan":
            MessageLookupByLibrary.simpleMessage("确认删除该计划吗"),
        "tr_areYouSureToDeleteThisTemplate":
            MessageLookupByLibrary.simpleMessage("确认删除该模板吗"),
        "tr_area": MessageLookupByLibrary.simpleMessage("区域"),
        "tr_areaName": MessageLookupByLibrary.simpleMessage("区域名称"),
        "tr_areaTrafficStatistics":
            MessageLookupByLibrary.simpleMessage("区域客流统计"),
        "tr_arithmeticChannelNotEnoughTip": m10,
        "tr_arithmeticOverTip": MessageLookupByLibrary.simpleMessage(
            "以上设备和算法与其他计划中重叠，您可在当前计划中删除或直接删除原计划中的设备和算法。"),
        "tr_arrivalDepartureTime":
            MessageLookupByLibrary.simpleMessage("到店/离店时间点"),
        "tr_assessmentCategory": MessageLookupByLibrary.simpleMessage("考评类"),
        "tr_assessmentCategoryDetail":
            MessageLookupByLibrary.simpleMessage("考评类详情"),
        "tr_assessmentItem": MessageLookupByLibrary.simpleMessage("考评项"),
        "tr_assessmentReferenceImage":
            MessageLookupByLibrary.simpleMessage("考评参考图"),
        "tr_assignedPersonnel": MessageLookupByLibrary.simpleMessage("指派人员"),
        "tr_assignedTo": MessageLookupByLibrary.simpleMessage("指派给"),
        "tr_assignedToYou": MessageLookupByLibrary.simpleMessage("为您分配了"),
        "tr_associateHumanShape": MessageLookupByLibrary.simpleMessage("关联人形"),
        "tr_associatedAlgorithm": MessageLookupByLibrary.simpleMessage("关联算法"),
        "tr_associatedFace": MessageLookupByLibrary.simpleMessage("关联人脸"),
        "tr_associatedTemplate": MessageLookupByLibrary.simpleMessage("关联模板"),
        "tr_asyncStoreLatLng":
            MessageLookupByLibrary.simpleMessage("门店经纬度设置到设备"),
        "tr_atLeastOneEvaluationClassIsRequired":
            MessageLookupByLibrary.simpleMessage("至少要有一个考评类"),
        "tr_audio": MessageLookupByLibrary.simpleMessage("音频"),
        "tr_audioVideoEncodingConfig":
            MessageLookupByLibrary.simpleMessage("音视频编码配置"),
        "tr_authorization_statistics":
            MessageLookupByLibrary.simpleMessage("授权统计"),
        "tr_authorizeIntercept": MessageLookupByLibrary.simpleMessage("授权拦截"),
        "tr_authorizedNumChannels":
            MessageLookupByLibrary.simpleMessage("授权通道数"),
        "tr_autoCertification": MessageLookupByLibrary.simpleMessage("自动留证"),
        "tr_autoEvidenceTime": MessageLookupByLibrary.simpleMessage("自动留证时间"),
        "tr_autoRestart": MessageLookupByLibrary.simpleMessage("自动重启"),
        "tr_autoSubmitEvent": MessageLookupByLibrary.simpleMessage("已自动提交事件"),
        "tr_automaticallyInitiateRectificationEvent":
            MessageLookupByLibrary.simpleMessage("自动发起整改事件"),
        "tr_averageStayDuration":
            MessageLookupByLibrary.simpleMessage("平均停留时长"),
        "tr_bandwidthCap": MessageLookupByLibrary.simpleMessage("带宽上限"),
        "tr_bandwidthStatistics": MessageLookupByLibrary.simpleMessage("带宽统计"),
        "tr_batchAddChannelTips": MessageLookupByLibrary.simpleMessage(
            "批量添加选择的设备密码必须一致，否则会添加失败，密码不一致的设备请单个添加。"),
        "tr_batchEdit": MessageLookupByLibrary.simpleMessage("批量编辑"),
        "tr_batchModify": MessageLookupByLibrary.simpleMessage("批量修改"),
        "tr_batchSetting": MessageLookupByLibrary.simpleMessage("批量设置"),
        "tr_batchSettingChannel":
            MessageLookupByLibrary.simpleMessage("批量设置通道"),
        "tr_bcloud_saas_activity":
            MessageLookupByLibrary.simpleMessage("蜂云 SAAS 套餐优惠"),
        "tr_bcloud_saas_shopping_tip": MessageLookupByLibrary.simpleMessage(
            "蜂云 SAAS 套餐优惠，欢迎联系**************购买。"),
        "tr_beforeAfterBusinessHours":
            MessageLookupByLibrary.simpleMessage("营业时间前后"),
        "tr_beforeAfterBusinessHoursConsideredStaff": m11,
        "tr_beforeRectification": MessageLookupByLibrary.simpleMessage("整改前"),
        "tr_beginSceneInspection":
            MessageLookupByLibrary.simpleMessage("开始现场巡检"),
        "tr_belongDevice": MessageLookupByLibrary.simpleMessage("所属设备"),
        "tr_belongNode": MessageLookupByLibrary.simpleMessage("所属节点"),
        "tr_belongStore": MessageLookupByLibrary.simpleMessage("所属门店"),
        "tr_belowIsAiInspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("以下是AI巡检分析"),
        "tr_bindTag": MessageLookupByLibrary.simpleMessage("打标签"),
        "tr_bitRateControlFPS":
            MessageLookupByLibrary.simpleMessage("码流控制(FPS)"),
        "tr_bitRateValue": MessageLookupByLibrary.simpleMessage("码流值"),
        "tr_bitStream": MessageLookupByLibrary.simpleMessage("码流"),
        "tr_blur": MessageLookupByLibrary.simpleMessage("模糊"),
        "tr_broadcast": MessageLookupByLibrary.simpleMessage("广播"),
        "tr_broadcastException": MessageLookupByLibrary.simpleMessage("广播异常"),
        "tr_broadcasting": MessageLookupByLibrary.simpleMessage("广播中"),
        "tr_btConnectNetwork": MessageLookupByLibrary.simpleMessage("设备连接无线网络"),
        "tr_btConnectNetworkConfiguration":
            MessageLookupByLibrary.simpleMessage("连接过程大概需要1-2分钟，请您稍等片刻。"),
        "tr_btConnectNetworkConfigurationTip":
            MessageLookupByLibrary.simpleMessage("路由器、手机和设备尽量靠近..."),
        "tr_btEquipment": MessageLookupByLibrary.simpleMessage("蓝牙设备"),
        "tr_btnCreateStore": MessageLookupByLibrary.simpleMessage("设定门店"),
        "tr_businessHours": MessageLookupByLibrary.simpleMessage("营业时间"),
        "tr_buy_connect_email_tip":
            MessageLookupByLibrary.simpleMessage("请联系**************续费。"),
        "tr_buy_saas": MessageLookupByLibrary.simpleMessage("立即购买"),
        "tr_callCount": MessageLookupByLibrary.simpleMessage("调用次数"),
        "tr_callStatistics": MessageLookupByLibrary.simpleMessage("调用统计"),
        "tr_canNotEditAreaTip":
            MessageLookupByLibrary.simpleMessage("设备异常，无法保存区域。"),
        "tr_canOnlyDrawOneArea":
            MessageLookupByLibrary.simpleMessage("只能绘制一个区域"),
        "tr_cancelAllSelections": MessageLookupByLibrary.simpleMessage("取消全选"),
        "tr_cancelLogin": MessageLookupByLibrary.simpleMessage("取消登录"),
        "tr_cancelTaskTip": MessageLookupByLibrary.simpleMessage(
            "该操作会取消正在下载的任务（需从头开始下载），是否继续？"),
        "tr_cancellationAccountAgreement":
            MessageLookupByLibrary.simpleMessage("账户注销协议"),
        "tr_cannotLaterStopTime":
            MessageLookupByLibrary.simpleMessage("开始时间不能晚于终止时间"),
        "tr_captureDate": MessageLookupByLibrary.simpleMessage("抓拍日期"),
        "tr_capturePlan": MessageLookupByLibrary.simpleMessage("抓拍计划"),
        "tr_captureRecord": MessageLookupByLibrary.simpleMessage("抓拍记录"),
        "tr_ccTo": MessageLookupByLibrary.simpleMessage("抄送给"),
        "tr_centralServer": MessageLookupByLibrary.simpleMessage("中心服务器"),
        "tr_centralServerPlatform":
            MessageLookupByLibrary.simpleMessage("中心服务器（平台）"),
        "tr_chainRatio": MessageLookupByLibrary.simpleMessage("环比"),
        "tr_changeLanguage": MessageLookupByLibrary.simpleMessage("语言切换"),
        "tr_changeNetAddDeviceTip":
            MessageLookupByLibrary.simpleMessage("找不到二维码？请使用局域网添加设备"),
        "tr_channelAdd": MessageLookupByLibrary.simpleMessage("通道添加"),
        "tr_channelCount": m12,
        "tr_channelDetails": MessageLookupByLibrary.simpleMessage("通道详情"),
        "tr_channelFour": MessageLookupByLibrary.simpleMessage("通道4"),
        "tr_channelInfo": MessageLookupByLibrary.simpleMessage("通道信息"),
        "tr_channelLabel": MessageLookupByLibrary.simpleMessage("通道标签"),
        "tr_channelManagement": MessageLookupByLibrary.simpleMessage("通道管理"),
        "tr_channelMismatch": MessageLookupByLibrary.simpleMessage("通道不符合"),
        "tr_channelName": MessageLookupByLibrary.simpleMessage("通道名称"),
        "tr_channelNum": MessageLookupByLibrary.simpleMessage("通道数"),
        "tr_channelNumInfo": m13,
        "tr_channelNumbers": MessageLookupByLibrary.simpleMessage("通道数量"),
        "tr_channelOccupied": MessageLookupByLibrary.simpleMessage("通道被占用"),
        "tr_channelOffline": MessageLookupByLibrary.simpleMessage("通道不在线"),
        "tr_channelOne": MessageLookupByLibrary.simpleMessage("通道1"),
        "tr_channelParams": MessageLookupByLibrary.simpleMessage("通道参数"),
        "tr_channelResources": MessageLookupByLibrary.simpleMessage("通道资源"),
        "tr_channelSIPID": MessageLookupByLibrary.simpleMessage("本平台SIP ID"),
        "tr_channelSN": MessageLookupByLibrary.simpleMessage("通道序列号"),
        "tr_channelSNExistSameCodeTips": m14,
        "tr_channelStartStop": MessageLookupByLibrary.simpleMessage("通道启停"),
        "tr_channelStartStopTip":
            MessageLookupByLibrary.simpleMessage("可选择通道停用，停用后，设备将不在设备列表中显示。"),
        "tr_channelStartStopTitle":
            MessageLookupByLibrary.simpleMessage("通道启停"),
        "tr_channelStatus": MessageLookupByLibrary.simpleMessage("通道状态"),
        "tr_channelStatusStart": MessageLookupByLibrary.simpleMessage("已使用"),
        "tr_channelStatusStop": MessageLookupByLibrary.simpleMessage("已停用"),
        "tr_channelThree": MessageLookupByLibrary.simpleMessage("通道3"),
        "tr_channelTitle": MessageLookupByLibrary.simpleMessage("通道标题"),
        "tr_channelTwo": MessageLookupByLibrary.simpleMessage("通道2"),
        "tr_channelUnreasonable":
            MessageLookupByLibrary.simpleMessage("输入路数不合理，请重新输入"),
        "tr_channelsInsufficient":
            MessageLookupByLibrary.simpleMessage("您的设备通道授权数不足"),
        "tr_channelsInsufficientRet":
            MessageLookupByLibrary.simpleMessage("套餐剩余路数不足，请联系售后人员进行扩容再操作。"),
        "tr_checkAccountLogin": MessageLookupByLibrary.simpleMessage("二次校验登录"),
        "tr_checkEmailNotValid":
            MessageLookupByLibrary.simpleMessage("邮箱格式不正确"),
        "tr_checkEnable": MessageLookupByLibrary.simpleMessage("是否启用"),
        "tr_checkFrequency": MessageLookupByLibrary.simpleMessage("观看并发数不合理"),
        "tr_checkPasswordErrorHint":
            MessageLookupByLibrary.simpleMessage("设备密码错误，请密码校验"),
        "tr_children": MessageLookupByLibrary.simpleMessage("儿童"),
        "tr_china": MessageLookupByLibrary.simpleMessage("中国"),
        "tr_choice": MessageLookupByLibrary.simpleMessage("去选择"),
        "tr_chooseAtLeastOneAlgorithm":
            MessageLookupByLibrary.simpleMessage("最少选择一个算法"),
        "tr_chooseAtMostNameAlgorithm": m15,
        "tr_chooseAtMostNameDepartment": m16,
        "tr_chooseAtMostNamePerson": m17,
        "tr_chooseAvailableWiFi":
            MessageLookupByLibrary.simpleMessage("WiFi连接不通畅，请重新选择可用的WiFi"),
        "tr_chooseChannels": MessageLookupByLibrary.simpleMessage("请选择通道"),
        "tr_chooseCreator": MessageLookupByLibrary.simpleMessage("选择创建人"),
        "tr_chooseDeptMaxCount": m18,
        "tr_chooseDeviceMaxCount": m19,
        "tr_chooseDeviceType": MessageLookupByLibrary.simpleMessage("选择设备类型"),
        "tr_chooseExpandGridMode":
            MessageLookupByLibrary.simpleMessage("设备已切换宫格展示"),
        "tr_chooseExpandListMode":
            MessageLookupByLibrary.simpleMessage("设备已切换列表展示"),
        "tr_chooseFirmwareFile":
            MessageLookupByLibrary.simpleMessage("请选择固件升级文件"),
        "tr_chooseInitiatePerson":
            MessageLookupByLibrary.simpleMessage("选择发起人"),
        "tr_chooseInspectionDate":
            MessageLookupByLibrary.simpleMessage("请选择巡检日期"),
        "tr_chooseInspectionPeriodDate": m20,
        "tr_chooseInspector": MessageLookupByLibrary.simpleMessage("选择巡检人"),
        "tr_chooseMaxCount": m21,
        "tr_chooseMember": MessageLookupByLibrary.simpleMessage("选择成员"),
        "tr_chooseStoreMaxCount": m22,
        "tr_chooseSyncChannels":
            MessageLookupByLibrary.simpleMessage("请选择需要同步授权的通道"),
        "tr_chooseTagsCategory": MessageLookupByLibrary.simpleMessage("筛选标签分类"),
        "tr_chooseVisibleRangeDept":
            MessageLookupByLibrary.simpleMessage("选择可见部门"),
        "tr_choseChannelCount": MessageLookupByLibrary.simpleMessage("已选通道"),
        "tr_choseDepartmentResource":
            MessageLookupByLibrary.simpleMessage("已选部门"),
        "tr_choseDeviceCount": MessageLookupByLibrary.simpleMessage("已选资源"),
        "tr_choseDeviceResource":
            MessageLookupByLibrary.simpleMessage("已选资源: "),
        "tr_clear": MessageLookupByLibrary.simpleMessage("清除"),
        "tr_clearAllMessage": MessageLookupByLibrary.simpleMessage("一键清除"),
        "tr_clearAllMessageTip":
            MessageLookupByLibrary.simpleMessage("请输入本账号登录密码验证删除"),
        "tr_clearMessageContent":
            MessageLookupByLibrary.simpleMessage("确认一键删除所有报警消息？"),
        "tr_clickEditText": MessageLookupByLibrary.simpleMessage("点击修改文字"),
        "tr_clickToEditStoreFloorPlan":
            MessageLookupByLibrary.simpleMessage("点击修改门店平面图"),
        "tr_clientNotSupportedUseChrome":
            MessageLookupByLibrary.simpleMessage("客户端不支持，请使用chrome"),
        "tr_cloudStorageAuthorization":
            MessageLookupByLibrary.simpleMessage("云录像授权"),
        "tr_cloudTraffic": MessageLookupByLibrary.simpleMessage("云端客流"),
        "tr_collapse": MessageLookupByLibrary.simpleMessage("收起"),
        "tr_collectDevices": MessageLookupByLibrary.simpleMessage("收藏设备"),
        "tr_colorCast": MessageLookupByLibrary.simpleMessage("偏色"),
        "tr_commitSceneInspectionResult":
            MessageLookupByLibrary.simpleMessage("提交巡店结果"),
        "tr_commonApplications": MessageLookupByLibrary.simpleMessage("常用功能"),
        "tr_commonBingSuccess": MessageLookupByLibrary.simpleMessage("绑定成功"),
        "tr_commonEmail": MessageLookupByLibrary.simpleMessage("电子邮箱"),
        "tr_commonModify": MessageLookupByLibrary.simpleMessage("修改"),
        "tr_commonPhone": MessageLookupByLibrary.simpleMessage("手机号码"),
        "tr_common_GoSetting": MessageLookupByLibrary.simpleMessage("前往开启"),
        "tr_common_close": MessageLookupByLibrary.simpleMessage("关闭"),
        "tr_companyList": MessageLookupByLibrary.simpleMessage("企业列表"),
        "tr_completeAcceptance": MessageLookupByLibrary.simpleMessage("完成验收"),
        "tr_completeAcceptanceSort":
            MessageLookupByLibrary.simpleMessage("完成验收次数排名"),
        "tr_completeAcceptanceTimes":
            MessageLookupByLibrary.simpleMessage("完成验收次数"),
        "tr_completeRectification":
            MessageLookupByLibrary.simpleMessage("完成整改"),
        "tr_completeRectificationSort":
            MessageLookupByLibrary.simpleMessage("完成整改次数排名"),
        "tr_completeRectificationTimes":
            MessageLookupByLibrary.simpleMessage("完成整改次数"),
        "tr_completed": MessageLookupByLibrary.simpleMessage("已完成"),
        "tr_completionRate": MessageLookupByLibrary.simpleMessage("完成率"),
        "tr_completionRateExplain":
            MessageLookupByLibrary.simpleMessage("任务完成率仅针对任务巡检有效。"),
        "tr_completionTime": MessageLookupByLibrary.simpleMessage("完成时间"),
        "tr_conditionsCanBeCombined":
            MessageLookupByLibrary.simpleMessage("条件可任意组合"),
        "tr_confidenceLevel": MessageLookupByLibrary.simpleMessage("置信度"),
        "tr_confidence_tip": MessageLookupByLibrary.simpleMessage(
            "置信度越低，识别精准度越低，识别的结果越多\n置信度越高，识别的结果越少，识别精准度越高"),
        "tr_confidence_tip_1": MessageLookupByLibrary.simpleMessage(
            "1.值越小区分人行越严格；当前推荐阈值6.8%\n2.若一天内频繁调整阈值，对不同阈值下的去重效果会有影响；\n3.不建议调整阈值，若要调整阈值，尽量一天调整一次"),
        "tr_configuration": MessageLookupByLibrary.simpleMessage("配置"),
        "tr_configurationNotSavedDoYouWantToReturn":
            MessageLookupByLibrary.simpleMessage("配置还未保存，是否返回？"),
        "tr_configureAlgorithm": MessageLookupByLibrary.simpleMessage("配置算法"),
        "tr_confirmChangeLanguage":
            MessageLookupByLibrary.simpleMessage("是否确定切换成："),
        "tr_confirmClosingTheRecordingPlan":
            MessageLookupByLibrary.simpleMessage("确认关闭录像计划"),
        "tr_confirmDeletion": MessageLookupByLibrary.simpleMessage("确定删除？"),
        "tr_confirmRemove": MessageLookupByLibrary.simpleMessage("确认移除"),
        "tr_connecting": MessageLookupByLibrary.simpleMessage("连接中"),
        "tr_consideredAsStaff": MessageLookupByLibrary.simpleMessage("认为是店员"),
        "tr_continue": MessageLookupByLibrary.simpleMessage("继续"),
        "tr_continueInspection": MessageLookupByLibrary.simpleMessage("继续巡检"),
        "tr_continueInspectionOutsideStoreRange":
            MessageLookupByLibrary.simpleMessage("您未在门店范围内，是否继续巡检？"),
        "tr_continuousDetection": MessageLookupByLibrary.simpleMessage("连续检测"),
        "tr_copySuccess": MessageLookupByLibrary.simpleMessage("复制成功"),
        "tr_copyTo": MessageLookupByLibrary.simpleMessage("抄送人"),
        "tr_cover_record_plan_tip":
            MessageLookupByLibrary.simpleMessage("检测到所选设备已有录像计划，是否覆盖？"),
        "tr_coverage": MessageLookupByLibrary.simpleMessage("覆盖情况"),
        "tr_coverageRate": MessageLookupByLibrary.simpleMessage("覆盖率"),
        "tr_createAIInspectionPlan":
            MessageLookupByLibrary.simpleMessage("创建 AI 巡检计划"),
        "tr_createActivityTime": MessageLookupByLibrary.simpleMessage("创建活动时间"),
        "tr_createCloudStorageRecordingPlan":
            MessageLookupByLibrary.simpleMessage("创建云存储录像计划"),
        "tr_createEvaluationClass":
            MessageLookupByLibrary.simpleMessage("新建考评类"),
        "tr_createEvaluationItem":
            MessageLookupByLibrary.simpleMessage("新建考评项"),
        "tr_createEvent": MessageLookupByLibrary.simpleMessage("创建事件"),
        "tr_createImageInspectionPlan":
            MessageLookupByLibrary.simpleMessage("创建抓拍计划"),
        "tr_createInspectionPlan":
            MessageLookupByLibrary.simpleMessage("创建巡检计划"),
        "tr_createOnsiteInspection":
            MessageLookupByLibrary.simpleMessage("创建现场巡检"),
        "tr_createStore": MessageLookupByLibrary.simpleMessage("创建门店"),
        "tr_createStoreSuccessTip":
            MessageLookupByLibrary.simpleMessage("创建门店成功"),
        "tr_createTemplate": MessageLookupByLibrary.simpleMessage("新建考评模板"),
        "tr_creationTime": MessageLookupByLibrary.simpleMessage("创建时间"),
        "tr_creator": MessageLookupByLibrary.simpleMessage("创建人"),
        "tr_crop": MessageLookupByLibrary.simpleMessage("裁剪"),
        "tr_currentAccount": MessageLookupByLibrary.simpleMessage("当前账号为："),
        "tr_currentFlowPackage": MessageLookupByLibrary.simpleMessage("当前流量包"),
        "tr_currentLocation": MessageLookupByLibrary.simpleMessage("当前位置"),
        "tr_currentTagHasNoDevice":
            MessageLookupByLibrary.simpleMessage("当前标签下没有任何设备"),
        "tr_customContent": MessageLookupByLibrary.simpleMessage("自定义内容"),
        "tr_customTemplate": MessageLookupByLibrary.simpleMessage("自定义模板"),
        "tr_customTime": MessageLookupByLibrary.simpleMessage("自定义时间"),
        "tr_customTimePeriod": MessageLookupByLibrary.simpleMessage("自定义时间段"),
        "tr_customTimeSetting": MessageLookupByLibrary.simpleMessage("自定义时间设置"),
        "tr_customerGroupEntryInterval":
            MessageLookupByLibrary.simpleMessage("进店顾客组间隔"),
        "tr_customerSegmentationAnalysis":
            MessageLookupByLibrary.simpleMessage("客群分析"),
        "tr_customerSegmentationAnalysisTip": MessageLookupByLibrary.simpleMessage(
            "客群分析：基于进店去重后的数据统计分析\n1. 进店人员年龄段：各年龄段数据/全部进店去重后的数据\n2. 进店人员性别：统计时间段男/女去重后的个数/统计时间段去重后的进店数"),
        "tr_dailyTotalStayDuration":
            MessageLookupByLibrary.simpleMessage("一天停留总时长"),
        "tr_dasProtocol": MessageLookupByLibrary.simpleMessage("DAS协议"),
        "tr_dataDashboard": MessageLookupByLibrary.simpleMessage("数据看板"),
        "tr_dataDetails": MessageLookupByLibrary.simpleMessage("数据详情"),
        "tr_dataNotSavedContinueReturnWillClearData":
            MessageLookupByLibrary.simpleMessage("数据还没有保存，继续返回将清空数据，确认返回吗？"),
        "tr_dataOverview": MessageLookupByLibrary.simpleMessage("数据概览"),
        "tr_dataSourceFetchFailed":
            MessageLookupByLibrary.simpleMessage("获取数据源失败"),
        "tr_dateChoose": MessageLookupByLibrary.simpleMessage("日期选择"),
        "tr_datePicker": MessageLookupByLibrary.simpleMessage("日期选择"),
        "tr_day": MessageLookupByLibrary.simpleMessage("日"),
        "tr_dayMark": MessageLookupByLibrary.simpleMessage("号"),
        "tr_dayNo": MessageLookupByLibrary.simpleMessage("号"),
        "tr_days": MessageLookupByLibrary.simpleMessage("天"),
        "tr_deactivate": MessageLookupByLibrary.simpleMessage("停用"),
        "tr_deadlineTime": MessageLookupByLibrary.simpleMessage("截止时间"),
        "tr_deduplication": MessageLookupByLibrary.simpleMessage("去重数"),
        "tr_deduplicationAfter": MessageLookupByLibrary.simpleMessage("去重后"),
        "tr_deduplicationTip": MessageLookupByLibrary.simpleMessage(
            "门店下有多台客流设备，会自动进行合并去重，若开启店员去重，将基于门店去重！"),
        "tr_deepSeekStatistics":
            MessageLookupByLibrary.simpleMessage("Deepseek调用统计"),
        "tr_deepSeekStatisticsTip":
            MessageLookupByLibrary.simpleMessage("请到巡检分析或事件分析页面查看具体功能"),
        "tr_deepseekSuffiex": MessageLookupByLibrary.simpleMessage(
            "针对以上巡检分析数据，有什么优化建议？请使用报表格式回答，请使用中文回答。"),
        "tr_deepseekSuffiex2": MessageLookupByLibrary.simpleMessage(
            "针对以上事件分析数据，有什么优化建议？请使用报表格式回答，请使用中文回答。"),
        "tr_default": MessageLookupByLibrary.simpleMessage("默认"),
        "tr_defaultAllDayRunning":
            MessageLookupByLibrary.simpleMessage("默认全天运行"),
        "tr_delChooseUserInfoTip":
            MessageLookupByLibrary.simpleMessage("您确定要删除选中的用户吗？"),
        "tr_delDeviceTags":
            MessageLookupByLibrary.simpleMessage("您确定要删除选中的标签信息吗？"),
        "tr_delDeviceTagsCategory":
            MessageLookupByLibrary.simpleMessage("标签分类删除后，分类及分类所属的标签将同步删除。"),
        "tr_delMoreAiAlarmMsg":
            MessageLookupByLibrary.simpleMessage("您确定要删除选中的AI告警消息吗？"),
        "tr_delMoreDeviceAlarmMsg":
            MessageLookupByLibrary.simpleMessage("您确定要删除选中的设备告警消息吗？"),
        "tr_delMoreStore":
            MessageLookupByLibrary.simpleMessage("您确定要删除选中的门店吗？"),
        "tr_delMoreUserPerson":
            MessageLookupByLibrary.simpleMessage("您确定要删除选中的用户吗？"),
        "tr_delOneAiAlarmMsg":
            MessageLookupByLibrary.simpleMessage("您确定要删除此AI告警消息吗？"),
        "tr_delOneDeviceAlarmMsg":
            MessageLookupByLibrary.simpleMessage("您确定要删除此设备告警消息吗？"),
        "tr_delOneDeviceGBCascade":
            MessageLookupByLibrary.simpleMessage("您确定要删除此国标级联吗？"),
        "tr_delOneStore": MessageLookupByLibrary.simpleMessage("您确定要删除此门店吗？"),
        "tr_delSelectedAiInspection":
            MessageLookupByLibrary.simpleMessage("您确定要删除选中的巡检计划吗？"),
        "tr_delTagDevices":
            MessageLookupByLibrary.simpleMessage("您确定要从标签下移除选中的设备吗？"),
        "tr_delTheUserPerson":
            MessageLookupByLibrary.simpleMessage("您确定要删除此用户吗？"),
        "tr_deleteFailed": MessageLookupByLibrary.simpleMessage("删除失败"),
        "tr_deleteFailedDownloadingTask":
            MessageLookupByLibrary.simpleMessage("正在下载的任务删除失败"),
        "tr_deleteFromCurrentPlan":
            MessageLookupByLibrary.simpleMessage("从当前计划中删除"),
        "tr_deleteFromOriginalPlan":
            MessageLookupByLibrary.simpleMessage("从原计划中删除"),
        "tr_deny": MessageLookupByLibrary.simpleMessage("拒绝"),
        "tr_departmentStructure": MessageLookupByLibrary.simpleMessage("组织结构"),
        "tr_description": MessageLookupByLibrary.simpleMessage("描述"),
        "tr_detail": MessageLookupByLibrary.simpleMessage("详情"),
        "tr_detailInspection": MessageLookupByLibrary.simpleMessage("巡检详情"),
        "tr_detailUuid": MessageLookupByLibrary.simpleMessage("序列号"),
        "tr_detectExistingInspectionPlan": MessageLookupByLibrary.simpleMessage(
            "检测到巡检计划已有对应计划任务，请选择是否覆盖原有AI巡检计划任务？"),
        "tr_detectionArea": MessageLookupByLibrary.simpleMessage("检测区域"),
        "tr_detectionBox": MessageLookupByLibrary.simpleMessage("检测框"),
        "tr_detectionCount": MessageLookupByLibrary.simpleMessage("检测次数"),
        "tr_detectionFrequency": MessageLookupByLibrary.simpleMessage("检测频率"),
        "tr_detectionFrequencyRangeIs":
            MessageLookupByLibrary.simpleMessage("检测频率设置范围为："),
        "tr_detectionFrequencySmartScheduling":
            MessageLookupByLibrary.simpleMessage("检测频率智能调度"),
        "tr_detectionFrequencyUpperLimit":
            MessageLookupByLibrary.simpleMessage("检测频率上限为"),
        "tr_detectionFrequencyUpperLimitRangeIs":
            MessageLookupByLibrary.simpleMessage("检测频率上限设置范围为："),
        "tr_detectionTrend": MessageLookupByLibrary.simpleMessage("检测趋势"),
        "tr_determineBeforeAcceptanceAndSubmission":
            MessageLookupByLibrary.simpleMessage("检测到有整改未截图，是否继续提交？"),
        "tr_device": MessageLookupByLibrary.simpleMessage("设备"),
        "tr_deviceAdded": MessageLookupByLibrary.simpleMessage("设备已添加"),
        "tr_deviceAlarm": MessageLookupByLibrary.simpleMessage("设备报警"),
        "tr_deviceAlertSound": MessageLookupByLibrary.simpleMessage("设备警戒音"),
        "tr_deviceAndAlgorithm": MessageLookupByLibrary.simpleMessage("设备和算法"),
        "tr_deviceAndAlgorithmConfiguration":
            MessageLookupByLibrary.simpleMessage("设备和算法配置"),
        "tr_deviceAndEvidence": MessageLookupByLibrary.simpleMessage("设备及留证"),
        "tr_deviceAnomaly": MessageLookupByLibrary.simpleMessage("设备异常"),
        "tr_deviceAuthorization":
            MessageLookupByLibrary.simpleMessage("设备接入授权"),
        "tr_deviceBusy": MessageLookupByLibrary.simpleMessage("设备占线"),
        "tr_deviceCamera": MessageLookupByLibrary.simpleMessage("摄像头"),
        "tr_deviceCapacity": MessageLookupByLibrary.simpleMessage("设备能力"),
        "tr_deviceConfigNoLatLng":
            MessageLookupByLibrary.simpleMessage("设备未配置经纬度坐标"),
        "tr_deviceConfiguration": MessageLookupByLibrary.simpleMessage("设备配置"),
        "tr_deviceCreateCategory": MessageLookupByLibrary.simpleMessage("创建分类"),
        "tr_deviceCreateTag": MessageLookupByLibrary.simpleMessage("创建标签"),
        "tr_deviceDataOverview": MessageLookupByLibrary.simpleMessage("设备数据概览"),
        "tr_deviceDetails": MessageLookupByLibrary.simpleMessage("设备详情"),
        "tr_deviceDimension": MessageLookupByLibrary.simpleMessage("设备维度"),
        "tr_deviceEventAnalysis":
            MessageLookupByLibrary.simpleMessage("设备事件分析"),
        "tr_deviceExceptionDuringDetectionNotParticipatingInInspection":
            MessageLookupByLibrary.simpleMessage("检测过程中出现异常的设备，不参与巡检"),
        "tr_deviceExecuteUpgrade":
            MessageLookupByLibrary.simpleMessage("请点击更新设备"),
        "tr_deviceHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("设备已删除"),
        "tr_deviceIdIsEmpty": MessageLookupByLibrary.simpleMessage("设备id为空"),
        "tr_deviceInUseByAnotherUser":
            MessageLookupByLibrary.simpleMessage("此设备已经有其他人在预览或下载，请稍候重试"),
        "tr_deviceInspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("设备巡检分析"),
        "tr_deviceInspectionCompletionRate":
            MessageLookupByLibrary.simpleMessage("巡检任务完成率"),
        "tr_deviceIsNotInPlaybackStateCannotChangeSpeed":
            MessageLookupByLibrary.simpleMessage("设备不在播放状态，无法改变播放速度"),
        "tr_deviceIsNotInPlaybackStateCannotTakeScreenshots":
            MessageLookupByLibrary.simpleMessage("设备不在播放状态，无法截图"),
        "tr_deviceIsOffline": MessageLookupByLibrary.simpleMessage("设备已离线"),
        "tr_deviceList": MessageLookupByLibrary.simpleMessage("查看列表"),
        "tr_deviceMaintenance": MessageLookupByLibrary.simpleMessage("设备维护"),
        "tr_deviceNeverReported":
            MessageLookupByLibrary.simpleMessage("设备没有上报过"),
        "tr_deviceNewCategory": MessageLookupByLibrary.simpleMessage("新增分类"),
        "tr_deviceNewSuccessfully":
            MessageLookupByLibrary.simpleMessage("新增成功"),
        "tr_deviceNewTag": MessageLookupByLibrary.simpleMessage("新增标签"),
        "tr_deviceNoStreamOver20s":
            MessageLookupByLibrary.simpleMessage("设备无流超20s"),
        "tr_deviceNoStreamOver2s":
            MessageLookupByLibrary.simpleMessage("设备无流超2s"),
        "tr_deviceNotAddedRuntimePoint":
            MessageLookupByLibrary.simpleMessage("还有设备未添加运行时间点"),
        "tr_deviceNotExistDeleted":
            MessageLookupByLibrary.simpleMessage("设备不存在（被删除掉了）"),
        "tr_deviceNotFound":
            MessageLookupByLibrary.simpleMessage("查询不到设备，请重置设备后重新添加"),
        "tr_deviceNotPlay": MessageLookupByLibrary.simpleMessage("设备不在播放状态"),
        "tr_deviceOffline": MessageLookupByLibrary.simpleMessage("设备不在线"),
        "tr_deviceOfflineEdit":
            MessageLookupByLibrary.simpleMessage("设备离线不可编辑"),
        "tr_deviceOfflineOrAbnormalNoSnapshotGenerated":
            MessageLookupByLibrary.simpleMessage("设备离线或异常，未生成抓拍图"),
        "tr_deviceOnLine": MessageLookupByLibrary.simpleMessage("在线设备"),
        "tr_devicePassword": MessageLookupByLibrary.simpleMessage("设备密码"),
        "tr_deviceReporting": MessageLookupByLibrary.simpleMessage("设备上报"),
        "tr_deviceRunTimeGreaterThanStoreHours":
            MessageLookupByLibrary.simpleMessage("设备运行时间要大于门店营业时间"),
        "tr_deviceSN": MessageLookupByLibrary.simpleMessage("设备SN"),
        "tr_deviceSelection": MessageLookupByLibrary.simpleMessage("设备选择"),
        "tr_deviceSideFlowTip":
            MessageLookupByLibrary.simpleMessage("按目标区域实时统计画面内进出店的人员数量"),
        "tr_deviceSource": MessageLookupByLibrary.simpleMessage("设备来源"),
        "tr_deviceStateRegister": MessageLookupByLibrary.simpleMessage("已注册"),
        "tr_deviceSum": MessageLookupByLibrary.simpleMessage("总设备数"),
        "tr_deviceTag": MessageLookupByLibrary.simpleMessage("设备标签"),
        "tr_deviceTags": MessageLookupByLibrary.simpleMessage("设备标签"),
        "tr_deviceTagsCategory": MessageLookupByLibrary.simpleMessage("设备标签分类"),
        "tr_deviceTime": MessageLookupByLibrary.simpleMessage("设备时间"),
        "tr_deviceTimezone": MessageLookupByLibrary.simpleMessage("设备时区"),
        "tr_deviceTokenInvalid":
            MessageLookupByLibrary.simpleMessage("设备token不合法"),
        "tr_deviceTraffic": MessageLookupByLibrary.simpleMessage("端侧客流"),
        "tr_deviceTree": MessageLookupByLibrary.simpleMessage("设备树"),
        "tr_deviceUpgrade": MessageLookupByLibrary.simpleMessage("设备升级"),
        "tr_deviceUpgradeVersion":
            MessageLookupByLibrary.simpleMessage("已经最新版本"),
        "tr_deviceUsername": MessageLookupByLibrary.simpleMessage("设备用户名"),
        "tr_deviceVersion": MessageLookupByLibrary.simpleMessage("设备版本"),
        "tr_devicesInTotal": m23,
        "tr_dioException_badCertificate":
            MessageLookupByLibrary.simpleMessage("错误证书，请重新检查证书"),
        "tr_dioException_badResponse400":
            MessageLookupByLibrary.simpleMessage("参数错误"),
        "tr_dioException_badResponse401":
            MessageLookupByLibrary.simpleMessage("没有权限"),
        "tr_dioException_badResponse403":
            MessageLookupByLibrary.simpleMessage("服务器拒绝执行"),
        "tr_dioException_badResponse404":
            MessageLookupByLibrary.simpleMessage("无法连接服务器"),
        "tr_dioException_badResponse405":
            MessageLookupByLibrary.simpleMessage("请求方法被禁止"),
        "tr_dioException_badResponse500":
            MessageLookupByLibrary.simpleMessage("服务器内部错误"),
        "tr_dioException_badResponse502":
            MessageLookupByLibrary.simpleMessage("无效的请求"),
        "tr_dioException_badResponse503":
            MessageLookupByLibrary.simpleMessage("服务器挂了"),
        "tr_dioException_badResponse504":
            MessageLookupByLibrary.simpleMessage("服务器无法在规定的时间内获得想要的响应"),
        "tr_dioException_badResponse505":
            MessageLookupByLibrary.simpleMessage("不支持请求所使用的HTTP版本"),
        "tr_dioException_cancel":
            MessageLookupByLibrary.simpleMessage("请求已被取消，请重新请求"),
        "tr_dioException_connectionError":
            MessageLookupByLibrary.simpleMessage("网络未连接,请检查网络"),
        "tr_dioException_timeout": MessageLookupByLibrary.simpleMessage("连接超时"),
        "tr_dioException_unknown":
            MessageLookupByLibrary.simpleMessage("网络异常，请检查网络请求"),
        "tr_disable": MessageLookupByLibrary.simpleMessage("禁用"),
        "tr_disableSuccessful": MessageLookupByLibrary.simpleMessage("禁用成功"),
        "tr_disabled": MessageLookupByLibrary.simpleMessage("禁用中"),
        "tr_displayAlarmRules": MessageLookupByLibrary.simpleMessage("显示告警规则"),
        "tr_displayArrangement": MessageLookupByLibrary.simpleMessage("陈列摆放"),
        "tr_doNotOverwrite": MessageLookupByLibrary.simpleMessage("不覆盖"),
        "tr_doublePersonGroup":
            MessageLookupByLibrary.simpleMessage("双人进店（顾客组）"),
        "tr_downloadCanceled": MessageLookupByLibrary.simpleMessage("下载取消"),
        "tr_downloadChannelOccupied":
            MessageLookupByLibrary.simpleMessage("下载通道被占用"),
        "tr_downloadFailed": MessageLookupByLibrary.simpleMessage("下载失败"),
        "tr_downloadList": MessageLookupByLibrary.simpleMessage("下载列表"),
        "tr_downloadManagement": MessageLookupByLibrary.simpleMessage("下载管理"),
        "tr_downloading": MessageLookupByLibrary.simpleMessage("下载中"),
        "tr_dragToSort": MessageLookupByLibrary.simpleMessage("按住拖动排序"),
        "tr_drawCircle": MessageLookupByLibrary.simpleMessage("画圈"),
        "tr_duration": MessageLookupByLibrary.simpleMessage("持续时长"),
        "tr_durationCannotBeLessThanDetectionFrequency":
            MessageLookupByLibrary.simpleMessage("持续时长不允许小于检测频率"),
        "tr_durationEqualsContinuousDetectionCountTimesDetectionFrequency":
            MessageLookupByLibrary.simpleMessage("时长为连续检测次数*检测频率"),
        "tr_duration_tip":
            MessageLookupByLibrary.simpleMessage("检测的行为时间大于持续时长时，才产生告警"),
        "tr_east": MessageLookupByLibrary.simpleMessage("东"),
        "tr_edgeComputing": MessageLookupByLibrary.simpleMessage("边缘计算"),
        "tr_editDeviceTags": MessageLookupByLibrary.simpleMessage("编辑名称"),
        "tr_editImage": MessageLookupByLibrary.simpleMessage("编辑图片"),
        "tr_editImageInspectionPlan":
            MessageLookupByLibrary.simpleMessage("编辑抓拍计划"),
        "tr_elderly": MessageLookupByLibrary.simpleMessage("老年"),
        "tr_emailActivation": MessageLookupByLibrary.simpleMessage("邮箱激活"),
        "tr_employeeName": MessageLookupByLibrary.simpleMessage("员工姓名"),
        "tr_employeeRanking": MessageLookupByLibrary.simpleMessage("员工排名"),
        "tr_emptyImage": MessageLookupByLibrary.simpleMessage("图片为空"),
        "tr_enable": MessageLookupByLibrary.simpleMessage("启用"),
        "tr_enableHighQualityImageUnderHighContrast":
            MessageLookupByLibrary.simpleMessage("开启后，在高对比度光线下拍摄高质量的图像"),
        "tr_enableLocationOrGrantLocationPermissions":
            MessageLookupByLibrary.simpleMessage("请开启定位或授予定位权限"),
        "tr_enabledSuccessful": MessageLookupByLibrary.simpleMessage("启用成功"),
        "tr_encoderStaticConfig":
            MessageLookupByLibrary.simpleMessage("编码器静态配置"),
        "tr_encodingHardSolution": MessageLookupByLibrary.simpleMessage("硬解"),
        "tr_encodingSettings": MessageLookupByLibrary.simpleMessage("编码设置"),
        "tr_encodingSoftSolution": MessageLookupByLibrary.simpleMessage("软解"),
        "tr_encodingSwitching": MessageLookupByLibrary.simpleMessage("软硬自动切换"),
        "tr_endDate": MessageLookupByLibrary.simpleMessage("终止日期"),
        "tr_endSidePassengerBox":
            MessageLookupByLibrary.simpleMessage("端侧客流检测框"),
        "tr_endTime": MessageLookupByLibrary.simpleMessage("结束时间"),
        "tr_endTimeCannotBeGreaterThanName": m24,
        "tr_ensure": MessageLookupByLibrary.simpleMessage("确认"),
        "tr_ensureDeviceOnline":
            MessageLookupByLibrary.simpleMessage("请确认设备是否在线"),
        "tr_enterInspectionItemNameToSearch":
            MessageLookupByLibrary.simpleMessage("请输入巡检项名称搜索"),
        "tr_enterStore": MessageLookupByLibrary.simpleMessage("进店"),
        "tr_enterStoreBatch": MessageLookupByLibrary.simpleMessage("进店批次"),
        "tr_enterStoreBatchInterval":
            MessageLookupByLibrary.simpleMessage("进店批次间隔"),
        "tr_enterStoreCount": MessageLookupByLibrary.simpleMessage("进店人数"),
        "tr_enterTitleToSearch":
            MessageLookupByLibrary.simpleMessage("请输入标题名称搜索"),
        "tr_entryPeak": MessageLookupByLibrary.simpleMessage("进店峰值"),
        "tr_entryTrough": MessageLookupByLibrary.simpleMessage("进店谷值"),
        "tr_envPreRelease": MessageLookupByLibrary.simpleMessage("预发"),
        "tr_envRelease": MessageLookupByLibrary.simpleMessage("正式"),
        "tr_envSwitching": MessageLookupByLibrary.simpleMessage("环境切换"),
        "tr_envTest": MessageLookupByLibrary.simpleMessage("测试"),
        "tr_envTestB": MessageLookupByLibrary.simpleMessage("测试B"),
        "tr_errorConvertingImage":
            MessageLookupByLibrary.simpleMessage("转换图片出错"),
        "tr_errorMixFormatPassword":
            MessageLookupByLibrary.simpleMessage("请输入8-16位含数字、字母和特殊字符"),
        "tr_event": MessageLookupByLibrary.simpleMessage("事件"),
        "tr_eventCountRanking": MessageLookupByLibrary.simpleMessage("事件数排名"),
        "tr_eventDetails": MessageLookupByLibrary.simpleMessage("事件详情"),
        "tr_eventInitiateType": MessageLookupByLibrary.simpleMessage("事件发起类型"),
        "tr_eventLevel": MessageLookupByLibrary.simpleMessage("事件等级"),
        "tr_eventLevelProportion":
            MessageLookupByLibrary.simpleMessage("事件等级占比"),
        "tr_eventRecording": MessageLookupByLibrary.simpleMessage("事件录像"),
        "tr_eventSource": MessageLookupByLibrary.simpleMessage("事件来源"),
        "tr_eventStatus": MessageLookupByLibrary.simpleMessage("事件状态"),
        "tr_eventTrends": MessageLookupByLibrary.simpleMessage("事件趋势"),
        "tr_everyDay": MessageLookupByLibrary.simpleMessage("每天"),
        "tr_everyMonth": MessageLookupByLibrary.simpleMessage("每月"),
        "tr_everyMonthNameDay": m25,
        "tr_everyWeek": MessageLookupByLibrary.simpleMessage("每周"),
        "tr_everyWeekNameDay": m26,
        "tr_excludeDeliveryCourier":
            MessageLookupByLibrary.simpleMessage("去除外卖员、快递员"),
        "tr_exeSync": MessageLookupByLibrary.simpleMessage("同步"),
        "tr_executeOpen": MessageLookupByLibrary.simpleMessage("去开启"),
        "tr_executeVerification": MessageLookupByLibrary.simpleMessage("校验"),
        "tr_executionCycle": MessageLookupByLibrary.simpleMessage("执行周期"),
        "tr_executionDate": MessageLookupByLibrary.simpleMessage("执行日期"),
        "tr_executionTime": MessageLookupByLibrary.simpleMessage("执行时间"),
        "tr_exemptFromWatermarkWhitelist":
            MessageLookupByLibrary.simpleMessage("免水印白名单"),
        "tr_existed": MessageLookupByLibrary.simpleMessage("已存在"),
        "tr_exitBTConnectTimeout": MessageLookupByLibrary.simpleMessage("连接超时"),
        "tr_exitBTConnectTimeoutSub":
            MessageLookupByLibrary.simpleMessage("连接超时，请重置设备，恢复出厂设置后重新添加"),
        "tr_exitBTDistributeFail": MessageLookupByLibrary.simpleMessage("密码错误"),
        "tr_exitBTDistributeFailSub":
            MessageLookupByLibrary.simpleMessage("密码错误，请重置设备，恢复出厂设置后重新添加"),
        "tr_exitDraw": MessageLookupByLibrary.simpleMessage("退出"),
        "tr_exitKnow": MessageLookupByLibrary.simpleMessage("知道了"),
        "tr_exitLoginTip": MessageLookupByLibrary.simpleMessage(
            "您的账号已在其他设备登录，若不是您的操作，请重新登录并修改密码"),
        "tr_expand": MessageLookupByLibrary.simpleMessage("展开"),
        "tr_expansionChannel": MessageLookupByLibrary.simpleMessage("扩展通道"),
        "tr_expired": MessageLookupByLibrary.simpleMessage("已过期"),
        "tr_extendRole": MessageLookupByLibrary.simpleMessage("继承角色"),
        "tr_faceDatabase": MessageLookupByLibrary.simpleMessage("人脸库"),
        "tr_faceSimilarity": MessageLookupByLibrary.simpleMessage("人脸相似度"),
        "tr_facialImage": MessageLookupByLibrary.simpleMessage("人脸图"),
        "tr_factoryReset": MessageLookupByLibrary.simpleMessage("恢复出厂设置"),
        "tr_failConnectNetwork":
            MessageLookupByLibrary.simpleMessage("设备联网遇到问题？"),
        "tr_failPassed": MessageLookupByLibrary.simpleMessage("不通过"),
        "tr_failedCount": MessageLookupByLibrary.simpleMessage("不合格次数"),
        "tr_failedToCreateOnsiteInspection":
            MessageLookupByLibrary.simpleMessage("创建现场巡检失败"),
        "tr_failedToGetUrlPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("获取url失败，请重试"),
        "tr_failure": MessageLookupByLibrary.simpleMessage("失败"),
        "tr_female": MessageLookupByLibrary.simpleMessage("女"),
        "tr_femalePercentageName": m27,
        "tr_fetchingImageDimensionsPleaseRetry":
            MessageLookupByLibrary.simpleMessage("正在获取图片尺寸，请稍候重试"),
        "tr_fileDeletionFailed":
            MessageLookupByLibrary.simpleMessage("文件未删除成功"),
        "tr_filter": MessageLookupByLibrary.simpleMessage("筛选"),
        "tr_findingsCount": MessageLookupByLibrary.simpleMessage("发现问题次数"),
        "tr_firmwareManage": MessageLookupByLibrary.simpleMessage("固件管理"),
        "tr_firmwareUpgradeFailure":
            MessageLookupByLibrary.simpleMessage("升级失败"),
        "tr_firmwareUpgradeFailureTip":
            MessageLookupByLibrary.simpleMessage("升级失败，请重试"),
        "tr_firmwareUpgradeSuccess":
            MessageLookupByLibrary.simpleMessage("升级成功"),
        "tr_firmwareUpgradeSuccessTip":
            MessageLookupByLibrary.simpleMessage("升级成功，等待设备重启"),
        "tr_firmwareUpgrading": MessageLookupByLibrary.simpleMessage("升级中"),
        "tr_firmwareUpgradingTip":
            MessageLookupByLibrary.simpleMessage("升级过程中不能进行其他操作，请勿中途退出或按home键"),
        "tr_firmwareVersion": MessageLookupByLibrary.simpleMessage("固件版本"),
        "tr_floorPlan": MessageLookupByLibrary.simpleMessage("平面图"),
        "tr_flowDeviceTip": MessageLookupByLibrary.simpleMessage(
            "仅对门店下支持客流统计的设备做数据分析。不支持客流统计的设备不做统计。"),
        "tr_flowExpirationTime":
            MessageLookupByLibrary.simpleMessage("流量包到期时间："),
        "tr_flowExpired": MessageLookupByLibrary.simpleMessage("（已过期）"),
        "tr_flowRemaining": MessageLookupByLibrary.simpleMessage("剩余"),
        "tr_flowRemainingTip": MessageLookupByLibrary.simpleMessage(
            "想要续费或扩容？请联系**************进行续费！"),
        "tr_flowStatisticsConfigTip": MessageLookupByLibrary.simpleMessage(
            "1、摄像头建议略大于45°俯拍视角，对着店外。\n2、框选区域为店外区域，箭头一侧内为店内。\n3、框选区域可拖动调整大小、位置。"),
        "tr_flowTotal": MessageLookupByLibrary.simpleMessage("总共"),
        "tr_flowTypeEnterStore": MessageLookupByLibrary.simpleMessage("进店"),
        "tr_flowTypePassingStore": MessageLookupByLibrary.simpleMessage("过店"),
        "tr_flowTypeVisitedStore": MessageLookupByLibrary.simpleMessage("进过店"),
        "tr_flowUse": MessageLookupByLibrary.simpleMessage("已用"),
        "tr_flow_statistics": MessageLookupByLibrary.simpleMessage("流量统计"),
        "tr_forever": MessageLookupByLibrary.simpleMessage("永久"),
        "tr_formatStorageCardTip": MessageLookupByLibrary.simpleMessage(
            "格式化存储卡后，设备将离线并重新加载存储卡，整个过程需要持续几分钟，请确认是否继续？"),
        "tr_formatSuccess": MessageLookupByLibrary.simpleMessage("格式化成功"),
        "tr_fraction": MessageLookupByLibrary.simpleMessage("分"),
        "tr_frameRateFPS": MessageLookupByLibrary.simpleMessage("帧率(FPS)"),
        "tr_frequency": MessageLookupByLibrary.simpleMessage("次数"),
        "tr_friday": MessageLookupByLibrary.simpleMessage("周五"),
        "tr_fullFlip": MessageLookupByLibrary.simpleMessage("上下左右翻转"),
        "tr_fullScreen": MessageLookupByLibrary.simpleMessage("全屏"),
        "tr_gatewaySN": MessageLookupByLibrary.simpleMessage("网关序列号"),
        "tr_gatewayStatus": MessageLookupByLibrary.simpleMessage("网关状态"),
        "tr_gatewayType": MessageLookupByLibrary.simpleMessage("网关类型"),
        "tr_genderDistribution": MessageLookupByLibrary.simpleMessage("性别占比"),
        "tr_general": MessageLookupByLibrary.simpleMessage("一般"),
        "tr_generatePlayAddress":
            MessageLookupByLibrary.simpleMessage("生成播放地址"),
        "tr_gentleReminder": MessageLookupByLibrary.simpleMessage("温馨提示"),
        "tr_getDownloadLinkFailed":
            MessageLookupByLibrary.simpleMessage("获取下载链接失败"),
        "tr_getNoLatLng": MessageLookupByLibrary.simpleMessage("未获取经纬度坐标"),
        "tr_getScore": MessageLookupByLibrary.simpleMessage("得分"),
        "tr_gettingVideoStream":
            MessageLookupByLibrary.simpleMessage("正在获取视频流，请稍候"),
        "tr_globalConfiguration": MessageLookupByLibrary.simpleMessage("全局配置"),
        "tr_globalPlaybackPeriod":
            MessageLookupByLibrary.simpleMessage("全局回放时段"),
        "tr_globalSetting": MessageLookupByLibrary.simpleMessage("全局设置"),
        "tr_goAcceptance": MessageLookupByLibrary.simpleMessage("去验收"),
        "tr_goConfigure": MessageLookupByLibrary.simpleMessage("去配置"),
        "tr_goPassedItems": MessageLookupByLibrary.simpleMessage("验收通过项"),
        "tr_goRectification": MessageLookupByLibrary.simpleMessage("去整改"),
        "tr_goToSelect": MessageLookupByLibrary.simpleMessage("前往选择"),
        "tr_goToSelectMultipleChoice":
            MessageLookupByLibrary.simpleMessage("去选择（可多选）"),
        "tr_googlePermissionTips": MessageLookupByLibrary.simpleMessage(
            "蜂云SaaS应用程序收集位置数据，收集照片、视频、相机录像媒体内容或文件数据，即使在应用程序关闭或未使用时也能以读取实时位置数据；识别照片、视频媒体内容或文件信息数据。"),
        "tr_handleError": MessageLookupByLibrary.simpleMessage("句柄错误"),
        "tr_hasBeenDeleted": MessageLookupByLibrary.simpleMessage("已删除"),
        "tr_hasChooseNode":
            MessageLookupByLibrary.simpleMessage("当前节点下无法添加节点，请选择一个节点"),
        "tr_hasNoAuthorization": MessageLookupByLibrary.simpleMessage("无权限操作"),
        "tr_hasSelectDeviceNode":
            MessageLookupByLibrary.simpleMessage("请选择一个节点"),
        "tr_hasStopped": MessageLookupByLibrary.simpleMessage("已停止"),
        "tr_heartbeatPeriod": MessageLookupByLibrary.simpleMessage("心跳周期(秒)"),
        "tr_heartbeatPeriodHint":
            MessageLookupByLibrary.simpleMessage("范围在30-3600秒"),
        "tr_heatArea": MessageLookupByLibrary.simpleMessage("热力区域"),
        "tr_heatAreaConfiguration":
            MessageLookupByLibrary.simpleMessage("热力区域配置"),
        "tr_heatDataDetails": MessageLookupByLibrary.simpleMessage("热力数据详情"),
        "tr_heatMap": MessageLookupByLibrary.simpleMessage("热力图"),
        "tr_heptagon": MessageLookupByLibrary.simpleMessage("七边形"),
        "tr_hexagon": MessageLookupByLibrary.simpleMessage("六边形"),
        "tr_high": MessageLookupByLibrary.simpleMessage("高"),
        "tr_highFrequencyQuestion":
            MessageLookupByLibrary.simpleMessage("高频问题"),
        "tr_history": MessageLookupByLibrary.simpleMessage("历史记录"),
        "tr_holiday": MessageLookupByLibrary.simpleMessage("节假日"),
        "tr_holidaySelection": MessageLookupByLibrary.simpleMessage("节假日选择"),
        "tr_horizontalFlip": MessageLookupByLibrary.simpleMessage("左右翻转"),
        "tr_hour": MessageLookupByLibrary.simpleMessage("时"),
        "tr_hours": MessageLookupByLibrary.simpleMessage("小时"),
        "tr_httpParsingError": MessageLookupByLibrary.simpleMessage("Http解析错误"),
        "tr_humanDetection": MessageLookupByLibrary.simpleMessage("人形检测"),
        "tr_humanDetectionMarking":
            MessageLookupByLibrary.simpleMessage("视频中出现人形时会对人形做画框或划线标记"),
        "tr_humanDetectionTip":
            MessageLookupByLibrary.simpleMessage("默认开启人形检测，人形检测与移动侦测二选一"),
        "tr_iFrameIntervalRange":
            MessageLookupByLibrary.simpleMessage("I帧间隔，取值范围1~12"),
        "tr_iFrameNoSps": MessageLookupByLibrary.simpleMessage("I帧无SPS"),
        "tr_iFrameNotFound": MessageLookupByLibrary.simpleMessage("未找到i帧"),
        "tr_image": MessageLookupByLibrary.simpleMessage("图片"),
        "tr_imageConfig": MessageLookupByLibrary.simpleMessage("图像配置"),
        "tr_imageFlip": MessageLookupByLibrary.simpleMessage("图像翻转"),
        "tr_imageGenerationFailed":
            MessageLookupByLibrary.simpleMessage("生成图片失败"),
        "tr_imageInspection": MessageLookupByLibrary.simpleMessage("图片巡检"),
        "tr_imageInspectionPlan": MessageLookupByLibrary.simpleMessage("抓拍计划"),
        "tr_imageQuality": MessageLookupByLibrary.simpleMessage("画质"),
        "tr_imageRequiredForNonCompliance":
            MessageLookupByLibrary.simpleMessage("不合格必须有图片"),
        "tr_imageStyleTip": MessageLookupByLibrary.simpleMessage(
            "支持扩展名: jpeg、jpg、bmp格式；\n图片限制: 10MB；\n建议分辨率: 626*413（2寸标准证件照）"),
        "tr_inInspection": MessageLookupByLibrary.simpleMessage("巡检中"),
        "tr_inProgress": MessageLookupByLibrary.simpleMessage("进行中"),
        "tr_inStoreBatchRatio": MessageLookupByLibrary.simpleMessage("进店批次占比"),
        "tr_inStoreCustomerCount":
            MessageLookupByLibrary.simpleMessage("进店顾客数"),
        "tr_inStoreCustomerGroup":
            MessageLookupByLibrary.simpleMessage("进店顾客组"),
        "tr_inStoreCustomerGroupTable":
            MessageLookupByLibrary.simpleMessage("进店顾客组统计表格"),
        "tr_inStoreCustomerGroupTip": MessageLookupByLibrary.simpleMessage(
            "进店客流： 去重后，顾客从先经过检测区域，再经过进店客流线，作为进店客流\n单人进店（顾客组）： 去重后，单人进店并且放置时间间隔两后与另其他人进店，作为单人进店\n双人进店（顾客组）： 去重后，2人进店间隔时间相手预设的时间作为同一批次\n三人进店（顾客组）： 去重后，3人进店间隔时间相手预设的时间作为同一批次 20%\n多人进店（顾客组）： 去重后，3人以上进店间隔时间相手预设的时间作为同一批次\n进店客流： 去重后，经过任意非进店客流线的，一条对应测线，作为出店客流\n总客流： 进店客流+出店客流\n进店率： 进店客流/总客流100%"),
        "tr_incompleteConfiguration":
            MessageLookupByLibrary.simpleMessage("未完成配置"),
        "tr_incompleteDeviceInfo":
            MessageLookupByLibrary.simpleMessage("设备信息不完善"),
        "tr_incorrectPassword": MessageLookupByLibrary.simpleMessage("密码不正确"),
        "tr_increaseDetectionFrequency":
            MessageLookupByLibrary.simpleMessage("检测频率提升"),
        "tr_initiatePerson": MessageLookupByLibrary.simpleMessage("发起人"),
        "tr_initiateTime": MessageLookupByLibrary.simpleMessage("发起时间"),
        "tr_inputAccessId": MessageLookupByLibrary.simpleMessage("请输入接入ID"),
        "tr_inputAccessIdNumHint":
            MessageLookupByLibrary.simpleMessage("接入ID为20个数字"),
        "tr_inputAccessPsd": MessageLookupByLibrary.simpleMessage("请输入接入密码"),
        "tr_inputAccessPsdHint":
            MessageLookupByLibrary.simpleMessage("接入密码为8～16位英文、数字、特殊符号"),
        "tr_inputAccount": MessageLookupByLibrary.simpleMessage("请输入账号"),
        "tr_inputAccountPassword":
            MessageLookupByLibrary.simpleMessage("输入账号密码"),
        "tr_inputApplicationName":
            MessageLookupByLibrary.simpleMessage("请输入应用名称"),
        "tr_inputChannels": MessageLookupByLibrary.simpleMessage("请输入通道数"),
        "tr_inputDevicePsd": MessageLookupByLibrary.simpleMessage("请输入设备密码"),
        "tr_inputHeartbeatPeriod":
            MessageLookupByLibrary.simpleMessage("请输入心跳周期(秒)"),
        "tr_inputHeartbeatPeriodHint":
            MessageLookupByLibrary.simpleMessage("心跳周期的范围为60～3600秒"),
        "tr_inputIPV4": MessageLookupByLibrary.simpleMessage("请输入正确的IPV4"),
        "tr_inputNumChannels":
            MessageLookupByLibrary.simpleMessage("您的NVR需要几个通道？"),
        "tr_inputParentPlatName":
            MessageLookupByLibrary.simpleMessage("请输入上级平台名称"),
        "tr_inputPlatformName":
            MessageLookupByLibrary.simpleMessage("请输入本级平台名称"),
        "tr_inputRegistrationCycle":
            MessageLookupByLibrary.simpleMessage("请输入注册周期(秒)"),
        "tr_inputRegistrationCycleHint":
            MessageLookupByLibrary.simpleMessage("注册周期的范围为3600～86400秒"),
        "tr_inputSIPServerIP":
            MessageLookupByLibrary.simpleMessage("请输入SIP服务器IP"),
        "tr_inputSIPServerNumber":
            MessageLookupByLibrary.simpleMessage("请输入SIP服务器号"),
        "tr_inputSIPServerNumberHint":
            MessageLookupByLibrary.simpleMessage("SIP服务器号为20个数字"),
        "tr_inputSIPServerPort":
            MessageLookupByLibrary.simpleMessage("请输入SIP服务器端口"),
        "tr_inputSIPServerPortHint":
            MessageLookupByLibrary.simpleMessage("SIP服务器端口为1-5位的数字"),
        "tr_inputSixSNCode": MessageLookupByLibrary.simpleMessage("请输入6位序列号编码"),
        "tr_inputStoreName": MessageLookupByLibrary.simpleMessage("请输入门店名称"),
        "tr_inputValidityPeriod": MessageLookupByLibrary.simpleMessage("输入时效"),
        "tr_inspected": MessageLookupByLibrary.simpleMessage("已巡检"),
        "tr_inspectionAddress": MessageLookupByLibrary.simpleMessage("巡检地址"),
        "tr_inspectionAlarm": MessageLookupByLibrary.simpleMessage("巡检告警"),
        "tr_inspectionAlarmCount":
            MessageLookupByLibrary.simpleMessage("巡检告警次数"),
        "tr_inspectionCall": MessageLookupByLibrary.simpleMessage("巡检调用"),
        "tr_inspectionCallCount":
            MessageLookupByLibrary.simpleMessage("巡检调用次数"),
        "tr_inspectionCategory": MessageLookupByLibrary.simpleMessage("所属巡检类"),
        "tr_inspectionCompletion": MessageLookupByLibrary.simpleMessage("巡检完成"),
        "tr_inspectionCompletionRate":
            MessageLookupByLibrary.simpleMessage("巡检完成率"),
        "tr_inspectionCompletionTime":
            MessageLookupByLibrary.simpleMessage("巡检完成时间"),
        "tr_inspectionConfirm": MessageLookupByLibrary.simpleMessage("巡检确认"),
        "tr_inspectionDevice": MessageLookupByLibrary.simpleMessage("巡检设备"),
        "tr_inspectionDeviceOfflineOrDeleted":
            MessageLookupByLibrary.simpleMessage("巡检设备离线或已删除，无法进行巡检"),
        "tr_inspectionDimension": MessageLookupByLibrary.simpleMessage("巡检维度"),
        "tr_inspectionItem": MessageLookupByLibrary.simpleMessage("巡检项"),
        "tr_inspectionItemName": MessageLookupByLibrary.simpleMessage("巡检项名称"),
        "tr_inspectionMethod": MessageLookupByLibrary.simpleMessage("巡检方式"),
        "tr_inspectionMethods": MessageLookupByLibrary.simpleMessage(
            "巡检方式总共有“巡检任务,抽查巡检,图片巡检,视频巡检,摇一摇巡检,现场巡检”这几种"),
        "tr_inspectionNode": MessageLookupByLibrary.simpleMessage("巡检节点"),
        "tr_inspectionPerson": MessageLookupByLibrary.simpleMessage("巡检人员"),
        "tr_inspectionPlanDetail":
            MessageLookupByLibrary.simpleMessage("巡检计划详情"),
        "tr_inspectionPlanSettings":
            MessageLookupByLibrary.simpleMessage("巡检计划设置"),
        "tr_inspectionProblemDescription":
            MessageLookupByLibrary.simpleMessage("巡检问题描述"),
        "tr_inspectionProgress": MessageLookupByLibrary.simpleMessage("巡检进度"),
        "tr_inspectionRecordWatermark":
            MessageLookupByLibrary.simpleMessage("巡检记录水印"),
        "tr_inspectionRecordWatermarkTip":
            MessageLookupByLibrary.simpleMessage("开启后，巡检记录页面会携带水印"),
        "tr_inspectionResult": MessageLookupByLibrary.simpleMessage("巡检结果"),
        "tr_inspectionRetPreview":
            MessageLookupByLibrary.simpleMessage("巡店结果预览"),
        "tr_inspectionScope": MessageLookupByLibrary.simpleMessage("巡检范围"),
        "tr_inspectionStatus": MessageLookupByLibrary.simpleMessage("巡检状态"),
        "tr_inspectionStores": MessageLookupByLibrary.simpleMessage("巡检门店"),
        "tr_inspectionTasks": MessageLookupByLibrary.simpleMessage("巡检任务"),
        "tr_inspectionTemplate": MessageLookupByLibrary.simpleMessage("巡检模板"),
        "tr_inspectionTemplateSelection":
            MessageLookupByLibrary.simpleMessage("巡检模板选择"),
        "tr_inspectionTime": MessageLookupByLibrary.simpleMessage("巡检时间"),
        "tr_inspectionType": MessageLookupByLibrary.simpleMessage("巡检类型"),
        "tr_inspectionWatermark": MessageLookupByLibrary.simpleMessage("巡检水印"),
        "tr_inspector": MessageLookupByLibrary.simpleMessage("巡检人"),
        "tr_instructionsForAccessing":
            MessageLookupByLibrary.simpleMessage("国标接入说明"),
        "tr_insufficientApplicationChannels":
            MessageLookupByLibrary.simpleMessage("申请通道数不足"),
        "tr_insufficientAuthorization":
            MessageLookupByLibrary.simpleMessage("授权不足"),
        "tr_insufficientAuthorizationRoutes":
            MessageLookupByLibrary.simpleMessage("授权路数不足"),
        "tr_insufficientChannels": MessageLookupByLibrary.simpleMessage("申请不足"),
        "tr_insufficientDeviceResources":
            MessageLookupByLibrary.simpleMessage("设备资源不足"),
        "tr_insufficientTip":
            MessageLookupByLibrary.simpleMessage("请联系**************扩容"),
        "tr_intervalRangeTip":
            MessageLookupByLibrary.simpleMessage("间隔为0-600秒，推荐30秒"),
        "tr_invalidSVData":
            MessageLookupByLibrary.simpleMessage("无效sn，请扫描正确二维码"),
        "tr_invalidSeekTime":
            MessageLookupByLibrary.simpleMessage("错误的seektime时间"),
        "tr_irLensReverse": MessageLookupByLibrary.simpleMessage("IR镜片反序"),
        "tr_isItOverdue": MessageLookupByLibrary.simpleMessage("是否逾期"),
        "tr_isNotPlayingTip":
            MessageLookupByLibrary.simpleMessage("未在播放状态，请稍候重试"),
        "tr_isQualified": MessageLookupByLibrary.simpleMessage("是否合格"),
        "tr_isStaff": MessageLookupByLibrary.simpleMessage("是店员"),
        "tr_isTheRecordingPlanTurnedOn":
            MessageLookupByLibrary.simpleMessage("是否开启录像计划"),
        "tr_issuanceTime": MessageLookupByLibrary.simpleMessage("下发时间"),
        "tr_issueDetails": MessageLookupByLibrary.simpleMessage("问题详情"),
        "tr_issueDistributionPeriod":
            MessageLookupByLibrary.simpleMessage("问题分布时段"),
        "tr_issueLocations": MessageLookupByLibrary.simpleMessage("问题点位"),
        "tr_item": MessageLookupByLibrary.simpleMessage("项"),
        "tr_itemOther": MessageLookupByLibrary.simpleMessage("其他"),
        "tr_juvenile": MessageLookupByLibrary.simpleMessage("少年"),
        "tr_keyIssueDetails": MessageLookupByLibrary.simpleMessage("突出问题详情"),
        "tr_keyIssues": MessageLookupByLibrary.simpleMessage("突出问题"),
        "tr_lANEquipment": MessageLookupByLibrary.simpleMessage("局域网设备"),
        "tr_lackNecessaryDeviceInfo":
            MessageLookupByLibrary.simpleMessage("缺少必要的设备信息"),
        "tr_languageSimpleChinese":
            MessageLookupByLibrary.simpleMessage("简体中文"),
        "tr_languageUSEnglish": MessageLookupByLibrary.simpleMessage("English"),
        "tr_last30Days": MessageLookupByLibrary.simpleMessage("近30天"),
        "tr_last3Days": MessageLookupByLibrary.simpleMessage("近3天"),
        "tr_last7Days": MessageLookupByLibrary.simpleMessage("近7天"),
        "tr_lastLogin": MessageLookupByLibrary.simpleMessage("上次选择"),
        "tr_lastMonth": MessageLookupByLibrary.simpleMessage("上月"),
        "tr_lastWeek": MessageLookupByLibrary.simpleMessage("上周"),
        "tr_latestUpgradeVersion":
            MessageLookupByLibrary.simpleMessage("已经最新版本"),
        "tr_latitude": MessageLookupByLibrary.simpleMessage("纬度"),
        "tr_layers": MessageLookupByLibrary.simpleMessage("图层"),
        "tr_leastPopulatedArea":
            MessageLookupByLibrary.simpleMessage("最不受欢迎区域"),
        "tr_leastPreferredStayArea":
            MessageLookupByLibrary.simpleMessage("最不感兴趣区域"),
        "tr_levelEventTitle": MessageLookupByLibrary.simpleMessage("事件数据"),
        "tr_levelInspectionDataTitle":
            MessageLookupByLibrary.simpleMessage("巡检数据"),
        "tr_levelInspectionTitle": MessageLookupByLibrary.simpleMessage("巡检"),
        "tr_levelSetEvaluationTitle":
            MessageLookupByLibrary.simpleMessage("设置考评模板"),
        "tr_limitedBitRate": MessageLookupByLibrary.simpleMessage("限定码流"),
        "tr_list": MessageLookupByLibrary.simpleMessage("列表"),
        "tr_liveStreaming": MessageLookupByLibrary.simpleMessage("直播"),
        "tr_loadingDataPleaseWait":
            MessageLookupByLibrary.simpleMessage("正在加载数据，请稍候"),
        "tr_localIP": MessageLookupByLibrary.simpleMessage("本地IP"),
        "tr_localNetworkExceptionDuringServiceValidation":
            MessageLookupByLibrary.simpleMessage("服务校验时本机网络出现异常"),
        "tr_localPlaybackDownloadConflict":
            MessageLookupByLibrary.simpleMessage(
                "同一个设备本地回放预览和下载不可同时进行，继续播放将暂停下载"),
        "tr_localRecording": MessageLookupByLibrary.simpleMessage("本地录像"),
        "tr_locate": MessageLookupByLibrary.simpleMessage("定位"),
        "tr_locating": MessageLookupByLibrary.simpleMessage("正在定位"),
        "tr_location": MessageLookupByLibrary.simpleMessage("位置"),
        "tr_locationInfo": MessageLookupByLibrary.simpleMessage("位置信息"),
        "tr_loginFailed": MessageLookupByLibrary.simpleMessage("登入失败"),
        "tr_loginTimeoutNetworkConnectionFailed":
            MessageLookupByLibrary.simpleMessage("登录超时 (网络连接失败)"),
        "tr_longPressToEditStoreFloorPlan":
            MessageLookupByLibrary.simpleMessage("长按修改门店平面图"),
        "tr_longPressToShowDetails":
            MessageLookupByLibrary.simpleMessage("长按显示详情"),
        "tr_longitude": MessageLookupByLibrary.simpleMessage("经度"),
        "tr_loopAlarmSound": MessageLookupByLibrary.simpleMessage("循环报警音"),
        "tr_loopRecording": MessageLookupByLibrary.simpleMessage("循环录像"),
        "tr_low": MessageLookupByLibrary.simpleMessage("低"),
        "tr_mRectificationTasks": MessageLookupByLibrary.simpleMessage("的整改任务"),
        "tr_mainStream": MessageLookupByLibrary.simpleMessage("主码流"),
        "tr_makeCopy": MessageLookupByLibrary.simpleMessage("抄送"),
        "tr_male": MessageLookupByLibrary.simpleMessage("男"),
        "tr_malePercentageName": m28,
        "tr_manageStore": MessageLookupByLibrary.simpleMessage("门店管理"),
        "tr_manualInspectionAnalysisCoverageByTaskCompletionRate":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析任务完成率巡检覆盖情况数据"),
        "tr_manualInspectionAnalysisEmployeeRankingByFindings":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析发现次数员工排名数据"),
        "tr_manualInspectionAnalysisEmployeeRankingByInspectionCount":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析巡检次数员工排名数据"),
        "tr_manualInspectionAnalysisEmployeeRankingByOverdue":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析逾期次数员工排名数据"),
        "tr_manualInspectionAnalysisFailedInspectionRatio":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析巡检不合格占比数据"),
        "tr_manualInspectionAnalysisFailedPointRanking":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析不合格点位排名数据"),
        "tr_manualInspectionAnalysisInspectionCount":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析巡检次数数据"),
        "tr_manualInspectionAnalysisPassRate":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析巡检合格率数据"),
        "tr_manualInspectionAnalysisStoreRankingByFindings":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析发现问题门店排名数据"),
        "tr_manualInspectionAnalysisTaskCompletionRate":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析巡店任务完成率数据"),
        "tr_manualInspectionAnalysisTaskCompletionRateData":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析巡检任务完成率数据"),
        "tr_manualInspectionCoverageData":
            MessageLookupByLibrary.simpleMessage("以下是人工巡检分析巡检覆盖率数据"),
        "tr_manuallySetStaffNumber":
            MessageLookupByLibrary.simpleMessage("手动设置店员人数"),
        "tr_manuallySetStaffNumberRange":
            MessageLookupByLibrary.simpleMessage("手动设置店员人数范围区间"),
        "tr_manyPersonGroup": MessageLookupByLibrary.simpleMessage("多人进店（顾客组）"),
        "tr_maturity": MessageLookupByLibrary.simpleMessage("到期"),
        "tr_maxDurationCannotBeGreaterThan": m29,
        "tr_maximumNameTasksDownloadLimit": m30,
        "tr_maximumTimeSupport": m31,
        "tr_medium": MessageLookupByLibrary.simpleMessage("中"),
        "tr_meetAbove": MessageLookupByLibrary.simpleMessage("满足以上"),
        "tr_meetAboveConditionsConsideredStaff": m32,
        "tr_memberManagement": MessageLookupByLibrary.simpleMessage("成员管理"),
        "tr_memberManagerDesc":
            MessageLookupByLibrary.simpleMessage("查看平台及非平台成员信息及相关操作"),
        "tr_messageNotificationInterval":
            MessageLookupByLibrary.simpleMessage("消息通知间隔"),
        "tr_middleAged": MessageLookupByLibrary.simpleMessage("中年"),
        "tr_mineAcceptance": MessageLookupByLibrary.simpleMessage("我验收"),
        "tr_mineDuplicate": MessageLookupByLibrary.simpleMessage("抄送我"),
        "tr_mineInitiate": MessageLookupByLibrary.simpleMessage("我发起"),
        "tr_mineRectify": MessageLookupByLibrary.simpleMessage("我的整改"),
        "tr_minor": MessageLookupByLibrary.simpleMessage("轻微"),
        "tr_minute": MessageLookupByLibrary.simpleMessage("分"),
        "tr_modifyAccessIdPwd":
            MessageLookupByLibrary.simpleMessage("修改接入ID密码"),
        "tr_modifyStore": MessageLookupByLibrary.simpleMessage("门店信息"),
        "tr_modifyStoreAddress": MessageLookupByLibrary.simpleMessage("修改门店地址"),
        "tr_modifyStoreName": MessageLookupByLibrary.simpleMessage("修改门店名称"),
        "tr_monday": MessageLookupByLibrary.simpleMessage("周一"),
        "tr_month": MessageLookupByLibrary.simpleMessage("月"),
        "tr_moreApplications": MessageLookupByLibrary.simpleMessage("更多功能"),
        "tr_moreInstructionsMaxHundred":
            MessageLookupByLibrary.simpleMessage("请输入说明，最多100字"),
        "tr_morning": MessageLookupByLibrary.simpleMessage("上午"),
        "tr_mostPopulatedArea": MessageLookupByLibrary.simpleMessage("最受欢迎区域"),
        "tr_mostPreferredStayArea":
            MessageLookupByLibrary.simpleMessage("最感兴趣区域"),
        "tr_motionDetection": MessageLookupByLibrary.simpleMessage("移动侦测"),
        "tr_msgAlarm": MessageLookupByLibrary.simpleMessage("消息告警"),
        "tr_msgCall": MessageLookupByLibrary.simpleMessage("消息调用"),
        "tr_multi": MessageLookupByLibrary.simpleMessage("多目"),
        "tr_multiIPC": MessageLookupByLibrary.simpleMessage("多目IPC"),
        "tr_multiplePeople": MessageLookupByLibrary.simpleMessage("多人"),
        "tr_multipleVisitsPerDayConsideredStaff":
            MessageLookupByLibrary.simpleMessage("一天进店多次认为是店员"),
        "tr_myInitiated": MessageLookupByLibrary.simpleMessage("我发起的"),
        "tr_nameAbnormalRoutes": m33,
        "tr_nameDevices": m34,
        "tr_nameHoursOrMore": m35,
        "tr_nameMinutesOrMore": m36,
        "tr_namePleaseRetry": m37,
        "tr_namePlusAccount": MessageLookupByLibrary.simpleMessage("姓名+账号"),
        "tr_nationalStandardAccessManagement":
            MessageLookupByLibrary.simpleMessage("国标接入管理"),
        "tr_nationalStandardDeviceAccess":
            MessageLookupByLibrary.simpleMessage("国标接入"),
        "tr_nearbyLocation": MessageLookupByLibrary.simpleMessage("附近位置"),
        "tr_nearbyLocationNoData":
            MessageLookupByLibrary.simpleMessage("未查询到相关附近位置信息"),
        "tr_networkAdd": MessageLookupByLibrary.simpleMessage("局域网添加"),
        "tr_networkException": MessageLookupByLibrary.simpleMessage("网络异常"),
        "tr_networkHostNotFound":
            MessageLookupByLibrary.simpleMessage("找不到网络主机"),
        "tr_networkMode": MessageLookupByLibrary.simpleMessage("网络模式"),
        "tr_never": MessageLookupByLibrary.simpleMessage("从不"),
        "tr_newPassword": MessageLookupByLibrary.simpleMessage("新密码"),
        "tr_newRole": MessageLookupByLibrary.simpleMessage("新建角色"),
        "tr_no": MessageLookupByLibrary.simpleMessage("否"),
        "tr_noAlgorithmData": MessageLookupByLibrary.simpleMessage("无算法数据"),
        "tr_noAvailableDevices": MessageLookupByLibrary.simpleMessage("无可用设备"),
        "tr_noChannel": MessageLookupByLibrary.simpleMessage("暂无通道"),
        "tr_noConfigInfoFoundInRedis":
            MessageLookupByLibrary.simpleMessage("在redis里没找到配置信息"),
        "tr_noCorrespondingRecords":
            MessageLookupByLibrary.simpleMessage("没有对应记录"),
        "tr_noDevice": MessageLookupByLibrary.simpleMessage("暂无设备"),
        "tr_noDeviceNode": MessageLookupByLibrary.simpleMessage("暂无设备节点"),
        "tr_noDeviceTrafficDevice":
            MessageLookupByLibrary.simpleMessage("暂无端侧客流设备"),
        "tr_noDevicesUnderStore": MessageLookupByLibrary.simpleMessage("无可用设备"),
        "tr_noEvaluationItemsTemporarily":
            MessageLookupByLibrary.simpleMessage("暂无考评项"),
        "tr_noFloorPlanConfigured":
            MessageLookupByLibrary.simpleMessage("暂未配置平面设计图"),
        "tr_noFrameDataReceived2sBeforeDisconnection":
            MessageLookupByLibrary.simpleMessage("断开前2秒内没有收到帧数据"),
        "tr_noFrameDataReceivedDuringConnection":
            MessageLookupByLibrary.simpleMessage("连接期间没有收到帧数据"),
        "tr_noInputParameter": MessageLookupByLibrary.simpleMessage("没有输入参数"),
        "tr_noMicrophonePermissionTip":
            MessageLookupByLibrary.simpleMessage("没有麦克风权限，请在系统设置中打开麦克风权限"),
        "tr_noObject": MessageLookupByLibrary.simpleMessage("无对象"),
        "tr_noPreciseTrafficFlowDevice":
            MessageLookupByLibrary.simpleMessage("暂无精准客流设备"),
        "tr_noPresetPoint": MessageLookupByLibrary.simpleMessage("暂无预置点"),
        "tr_noProcessingPermissionForTheTimeBeing":
            MessageLookupByLibrary.simpleMessage("暂无处理权限"),
        "tr_noRecording": MessageLookupByLibrary.simpleMessage("无录像"),
        "tr_noRecordingDeviceOrNotRecording":
            MessageLookupByLibrary.simpleMessage("无录像设备或设备未进行录像"),
        "tr_noResendBeforeReply":
            MessageLookupByLibrary.simpleMessage("回复完成之前不允许再次发送消息"),
        "tr_noSdCardOrHardDrive":
            MessageLookupByLibrary.simpleMessage("无SD卡或硬盘"),
        "tr_noStoragePermissionPleaseRetryAfterOpening":
            MessageLookupByLibrary.simpleMessage("没有存储权限，请打开权限后重试"),
        "tr_noStoresAvailable": MessageLookupByLibrary.simpleMessage("暂无门店"),
        "tr_noStoresAvailableForInspection":
            MessageLookupByLibrary.simpleMessage("无可巡检门店"),
        "tr_noTraffic": MessageLookupByLibrary.simpleMessage("无流量"),
        "tr_noTrafficHeatZoneCamerasInStoreContactB2bEmail":
            MessageLookupByLibrary.simpleMessage(
                "当前门店无客流热区摄像头，您可联系**************邮箱联系采购"),
        "tr_noVideoFrameDataReceived2sBeforeDisconnection":
            MessageLookupByLibrary.simpleMessage("断开前2秒内没有收到视频帧数据"),
        "tr_noVideosToDownloadAdjustRegion":
            MessageLookupByLibrary.simpleMessage("无可下载视频，请调整下载区域"),
        "tr_nodeAddRefuseMessage":
            MessageLookupByLibrary.simpleMessage("NVR，平台，ONVIF下不可设置节点，不可添加设备"),
        "tr_nodeAddress": MessageLookupByLibrary.simpleMessage("节点位置"),
        "tr_nodeCount": m38,
        "tr_nodeInfo": MessageLookupByLibrary.simpleMessage("节点信息"),
        "tr_nodeSyncAddress": MessageLookupByLibrary.simpleMessage("同步到设备"),
        "tr_normal": MessageLookupByLibrary.simpleMessage("正常"),
        "tr_normalOnline": MessageLookupByLibrary.simpleMessage("正常在线"),
        "tr_normalProportion": MessageLookupByLibrary.simpleMessage("正常占比"),
        "tr_notActivated": MessageLookupByLibrary.simpleMessage("未激活"),
        "tr_notAddChannel": MessageLookupByLibrary.simpleMessage("暂不添加通道"),
        "tr_notConfiguredHeatArea":
            MessageLookupByLibrary.simpleMessage("未配置热力区域"),
        "tr_notEffective": MessageLookupByLibrary.simpleMessage("未生效"),
        "tr_notModifiable": MessageLookupByLibrary.simpleMessage("不可修改"),
        "tr_notOverdueYet": MessageLookupByLibrary.simpleMessage("未逾期"),
        "tr_notPlatformUser": MessageLookupByLibrary.simpleMessage("非平台用户"),
        "tr_notStart": MessageLookupByLibrary.simpleMessage("未开始"),
        "tr_notSubmitted": MessageLookupByLibrary.simpleMessage("未提交"),
        "tr_notSupportAuxiliaryStream":
            MessageLookupByLibrary.simpleMessage("不支持辅码流播放"),
        "tr_notSupportClientFlowDeviceOnlyTip":
            MessageLookupByLibrary.simpleMessage(
                "端侧客流设备、端云结合客流设备不支持配置云端精准客流，已置灰显示。"),
        "tr_notSupportPassengerFlow":
            MessageLookupByLibrary.simpleMessage("暂无支持客流统计的设备"),
        "tr_notSupported": MessageLookupByLibrary.simpleMessage("不支持"),
        "tr_notYet": MessageLookupByLibrary.simpleMessage("无"),
        "tr_notYetSetCustomWatermarkContentAreYouSureToReturn":
            MessageLookupByLibrary.simpleMessage("还未设置自定义水印内容，确定返回？"),
        "tr_note": MessageLookupByLibrary.simpleMessage("备注："),
        "tr_numberChannel": MessageLookupByLibrary.simpleMessage("通道号："),
        "tr_numberCompletedTasks":
            MessageLookupByLibrary.simpleMessage("完成任务数"),
        "tr_numberFailures": MessageLookupByLibrary.simpleMessage("不合格次数"),
        "tr_numberHighFrequencyQuestion":
            MessageLookupByLibrary.simpleMessage("高频问题对应次数"),
        "tr_numberInspectionTasks":
            MessageLookupByLibrary.simpleMessage("巡检任务数"),
        "tr_numberInspectionTasksCompleted":
            MessageLookupByLibrary.simpleMessage("巡检完成任务数"),
        "tr_numberInspections": MessageLookupByLibrary.simpleMessage("巡检次数"),
        "tr_numberIssuedTasks": MessageLookupByLibrary.simpleMessage("下发任务数"),
        "tr_numberOfEntries": MessageLookupByLibrary.simpleMessage("进入人次"),
        "tr_numberOfPeopleEntering":
            MessageLookupByLibrary.simpleMessage("进入人数"),
        "tr_numberOverdueEvents": MessageLookupByLibrary.simpleMessage("逾期事件数"),
        "tr_numberOverdueTasks": MessageLookupByLibrary.simpleMessage("逾期任务数"),
        "tr_numberQuestions": MessageLookupByLibrary.simpleMessage("问题次数"),
        "tr_numberStoresInspected":
            MessageLookupByLibrary.simpleMessage("巡检门店数"),
        "tr_occlusionAlarm": MessageLookupByLibrary.simpleMessage("遮挡告警"),
        "tr_octagon": MessageLookupByLibrary.simpleMessage("八边形"),
        "tr_okayIKnow": MessageLookupByLibrary.simpleMessage("好的，我知道了"),
        "tr_onlineOfflineAlarm": MessageLookupByLibrary.simpleMessage("上下线告警"),
        "tr_onlineStatus": MessageLookupByLibrary.simpleMessage("在线状态"),
        "tr_onlyCloudStatisticsTip":
            MessageLookupByLibrary.simpleMessage("仅对所选门店下支持云端客流统计的设备做数据统计"),
        "tr_onlyDeviceStatisticsTip":
            MessageLookupByLibrary.simpleMessage("仅对所选门店下支持设备端客流统计的设备做数据统计"),
        "tr_onlySelectStoresWithOnlineDevices":
            MessageLookupByLibrary.simpleMessage("只能选择有在线设备的门店"),
        "tr_onlySupportBroadDeviceTip":
            MessageLookupByLibrary.simpleMessage("不支持广播的设备和离线的设备已置灰显示。"),
        "tr_onlySupportClientFlowDeviceTip":
            MessageLookupByLibrary.simpleMessage(
                "非端云结合客流设备不支持配置端云结合精准客流，已置灰显示。"),
        "tr_onlySupportOnlineDeviceTip":
            MessageLookupByLibrary.simpleMessage("只能选择在线设备"),
        "tr_onvifProtocol": MessageLookupByLibrary.simpleMessage("onvif协议"),
        "tr_onvifSSIDNotRegistered":
            MessageLookupByLibrary.simpleMessage("onvif ssid还没有注册上来"),
        "tr_openBTDevice":
            MessageLookupByLibrary.simpleMessage("请开启蓝牙，才能搜索到设备"),
        "tr_openBTPermission":
            MessageLookupByLibrary.simpleMessage("想访问你的蓝牙权限"),
        "tr_openBTPermissionContent":
            MessageLookupByLibrary.simpleMessage("请开启手机蓝牙，用以扫描附近的蓝牙设备"),
        "tr_openBlueTooth": MessageLookupByLibrary.simpleMessage("请打开蓝牙及相关权限"),
        "tr_openLocalNetwork":
            MessageLookupByLibrary.simpleMessage("请开启本地网络权限"),
        "tr_openPackage": MessageLookupByLibrary.simpleMessage("开通套餐"),
        "tr_openWiFi": MessageLookupByLibrary.simpleMessage("请打开WiFi"),
        "tr_operationSuccessfulProceedingToNext":
            MessageLookupByLibrary.simpleMessage("操作成功，即将进行下一个"),
        "tr_optionalFields": MessageLookupByLibrary.simpleMessage("非必填项"),
        "tr_orgAddDeviceNodeTips": MessageLookupByLibrary.simpleMessage(
            "无设备和子节点\n请点击右上角\'+\'按钮，添加设备和子节点"),
        "tr_organization": MessageLookupByLibrary.simpleMessage("组织"),
        "tr_osdSettings": MessageLookupByLibrary.simpleMessage("OSD设置"),
        "tr_otherCompanyAddExceptions":
            MessageLookupByLibrary.simpleMessage("设备已被其他企业添加，若是您的设备，请联系客服处理。"),
        "tr_outOfRange": MessageLookupByLibrary.simpleMessage("超出范围"),
        "tr_outStoreTraffic": MessageLookupByLibrary.simpleMessage("出店客流"),
        "tr_outStoreTrafficNoDeduplication":
            MessageLookupByLibrary.simpleMessage("出店客流(未去重)"),
        "tr_overallScore": MessageLookupByLibrary.simpleMessage("综合得分"),
        "tr_overdue": MessageLookupByLibrary.simpleMessage("逾期"),
        "tr_overdueCountRanking": MessageLookupByLibrary.simpleMessage("逾期数排名"),
        "tr_overdueRate": MessageLookupByLibrary.simpleMessage("逾期率"),
        "tr_overdueTimes": MessageLookupByLibrary.simpleMessage("逾期次数"),
        "tr_overdueYet": MessageLookupByLibrary.simpleMessage("已逾期"),
        "tr_overlapWithExistingTimePeriods":
            MessageLookupByLibrary.simpleMessage("选择的时间段和已有的时间段有重合"),
        "tr_overwrite": MessageLookupByLibrary.simpleMessage("覆盖"),
        "tr_pageStay": MessageLookupByLibrary.simpleMessage("页面停留"),
        "tr_pairTheParams": MessageLookupByLibrary.simpleMessage("配置参数"),
        "tr_parameterConfiguration":
            MessageLookupByLibrary.simpleMessage("参数配置"),
        "tr_parameterError": MessageLookupByLibrary.simpleMessage("参数错误"),
        "tr_parentCategory": MessageLookupByLibrary.simpleMessage("上级分类"),
        "tr_parentPlatformName": MessageLookupByLibrary.simpleMessage("上级平台"),
        "tr_parentPlatformNameHint":
            MessageLookupByLibrary.simpleMessage("1-20个字，中英文数字均可"),
        "tr_partialDeleteFailedTasks":
            MessageLookupByLibrary.simpleMessage("部分任务删除失败"),
        "tr_passByStore": MessageLookupByLibrary.simpleMessage("过店"),
        "tr_passByTraffic": MessageLookupByLibrary.simpleMessage("过店客流"),
        "tr_passByTrafficNoDeduplication":
            MessageLookupByLibrary.simpleMessage("过店客流(未去重)"),
        "tr_passFail": MessageLookupByLibrary.simpleMessage("未通过"),
        "tr_passRate": MessageLookupByLibrary.simpleMessage("合格率"),
        "tr_passScore": MessageLookupByLibrary.simpleMessage("合格分数线"),
        "tr_passed": MessageLookupByLibrary.simpleMessage("已通过"),
        "tr_passedCount": MessageLookupByLibrary.simpleMessage("合格次数"),
        "tr_passedNumberTimes": MessageLookupByLibrary.simpleMessage("通过验收次数"),
        "tr_passengerCloudFlowTip": MessageLookupByLibrary.simpleMessage(
            "进店客流：从外进入店内的客流\n出店客流：由内走出店外的客流\n过店客流：过而不入店的客流\n总客流 = 进店客流 + 过店客流\n进店率 = 进店客流 / 总客流 * 100% = 进店客流 / (进店客流 + 过店客流) * 100%"),
        "tr_passengerFlowData": MessageLookupByLibrary.simpleMessage("客流数据"),
        "tr_passengerFlowDataInformationClearsEveryDayAt24":
            MessageLookupByLibrary.simpleMessage("客流数据信息每日24:00清零"),
        "tr_passengerFlowDevices": MessageLookupByLibrary.simpleMessage("客流设备"),
        "tr_passengerFlowDevicesTotal": m39,
        "tr_passengerFlowInfo": MessageLookupByLibrary.simpleMessage("客流数"),
        "tr_passengerFlowOsdSwitch":
            MessageLookupByLibrary.simpleMessage("客流OSD开关"),
        "tr_passengerFlowPeerConfiguration":
            MessageLookupByLibrary.simpleMessage("客流统计配置"),
        "tr_passengerFlowStatistics":
            MessageLookupByLibrary.simpleMessage("客流统计"),
        "tr_passengerFlowStatisticsData":
            MessageLookupByLibrary.simpleMessage("客流统计数据"),
        "tr_passengerFlowStatisticsTip": MessageLookupByLibrary.simpleMessage(
            "客流统计：统计维度总客度\n\n1. 总客流\n2. 进店客流\n3. 过店客流\n4. 出店客流\n5. 进店顾客数（去重后）\n6. 出店顾客数（去重后）\n7. 过店顾客数（去重后）\n8. 总顾客数（去重后）\n\n进店峰值：统计时段内，去重后的进店峰值\n\n进店合值：统计时段内，去重后的进店合值"),
        "tr_passingScore": MessageLookupByLibrary.simpleMessage("合格线"),
        "tr_passwordError": MessageLookupByLibrary.simpleMessage("密码错误"),
        "tr_passwordManage": MessageLookupByLibrary.simpleMessage("密码管理"),
        "tr_passwordModify": MessageLookupByLibrary.simpleMessage("密码修改"),
        "tr_patrolStoreDetails": MessageLookupByLibrary.simpleMessage("巡店详情"),
        "tr_paused": MessageLookupByLibrary.simpleMessage("已暂停"),
        "tr_payloadFailed": MessageLookupByLibrary.simpleMessage("payload失败"),
        "tr_pcGateway": MessageLookupByLibrary.simpleMessage("PC网关"),
        "tr_peakCloudDirectConnection":
            MessageLookupByLibrary.simpleMessage("蜂云直连"),
        "tr_pendingAcceptance": MessageLookupByLibrary.simpleMessage("待验收"),
        "tr_pendingEvent": MessageLookupByLibrary.simpleMessage("待提交事件"),
        "tr_pendingForAcceptance":
            MessageLookupByLibrary.simpleMessage("待验收详情"),
        "tr_pendingInspection": MessageLookupByLibrary.simpleMessage("待巡检"),
        "tr_pendingInspectionImages":
            MessageLookupByLibrary.simpleMessage("待巡检图片"),
        "tr_pendingLevel": MessageLookupByLibrary.simpleMessage("待办等级"),
        "tr_pendingPersonAcceptance": m40,
        "tr_pendingPersonRectification": m41,
        "tr_pendingRectification": MessageLookupByLibrary.simpleMessage("待整改"),
        "tr_pendingSubmission": MessageLookupByLibrary.simpleMessage("待提交"),
        "tr_pentagon": MessageLookupByLibrary.simpleMessage("五边形"),
        "tr_permissionAudioSubtitle":
            MessageLookupByLibrary.simpleMessage("用于设备语音对讲等功能"),
        "tr_permissionAudioTitle":
            MessageLookupByLibrary.simpleMessage("访问麦克风"),
        "tr_permissionCameraSubtitle":
            MessageLookupByLibrary.simpleMessage("用于拍照、扫描二维码等功能"),
        "tr_permissionCameraTitle":
            MessageLookupByLibrary.simpleMessage("访问相机"),
        "tr_permissionLocationSubtitle":
            MessageLookupByLibrary.simpleMessage("用于实时定位、设备配网时获取WiFi信息"),
        "tr_permissionLocationTitle":
            MessageLookupByLibrary.simpleMessage("访问位置信息"),
        "tr_permissionNotificationSubtitle":
            MessageLookupByLibrary.simpleMessage("用于APP进行消息推送通知"),
        "tr_permissionNotificationTitle":
            MessageLookupByLibrary.simpleMessage("通知管理权限"),
        "tr_permissionStatement": MessageLookupByLibrary.simpleMessage("权限声明"),
        "tr_permissionStorageSubtitle":
            MessageLookupByLibrary.simpleMessage("用于保存设备端图片、视频等功能"),
        "tr_permissionStorageTitle":
            MessageLookupByLibrary.simpleMessage("访问存储"),
        "tr_person": MessageLookupByLibrary.simpleMessage("人"),
        "tr_personAcceptancePassed": m42,
        "tr_personInitiate": MessageLookupByLibrary.simpleMessage("人工发起"),
        "tr_personInspectionSource":
            MessageLookupByLibrary.simpleMessage("人工巡检"),
        "tr_phoneLocation": MessageLookupByLibrary.simpleMessage("跟随手机定位"),
        "tr_pictureColorShift": MessageLookupByLibrary.simpleMessage("画面偏色"),
        "tr_planName": MessageLookupByLibrary.simpleMessage("计划名称"),
        "tr_planType": MessageLookupByLibrary.simpleMessage("计划类型"),
        "tr_planValidityPeriod": MessageLookupByLibrary.simpleMessage("计划有效期"),
        "tr_plannedInspection": MessageLookupByLibrary.simpleMessage("计划巡检"),
        "tr_platform": MessageLookupByLibrary.simpleMessage("平台"),
        "tr_platformName": MessageLookupByLibrary.simpleMessage("本级平台"),
        "tr_platformParsing": MessageLookupByLibrary.simpleMessage("平台解析"),
        "tr_platformUser": MessageLookupByLibrary.simpleMessage("平台用户"),
        "tr_play": MessageLookupByLibrary.simpleMessage("播放"),
        "tr_playAddress": MessageLookupByLibrary.simpleMessage("播放地址"),
        "tr_playAddressExpiration":
            MessageLookupByLibrary.simpleMessage("过期时间"),
        "tr_playAddressFlv": MessageLookupByLibrary.simpleMessage("FLV播放地址"),
        "tr_playAddressHls": MessageLookupByLibrary.simpleMessage("HLS播放地址"),
        "tr_playAddressRtmp": MessageLookupByLibrary.simpleMessage("RTMP播放地址"),
        "tr_playAddressRtsp": MessageLookupByLibrary.simpleMessage("RTSP播放地址"),
        "tr_playbackAddress": MessageLookupByLibrary.simpleMessage("回放地址"),
        "tr_playbackCompleted": MessageLookupByLibrary.simpleMessage("已完成播放"),
        "tr_playbackFailedPleaseRetry":
            MessageLookupByLibrary.simpleMessage("播放失败，请重试"),
        "tr_playbackFailedXmtsCommunicationFailed":
            MessageLookupByLibrary.simpleMessage("播放失败，xmts通信失败"),
        "tr_playbackTypeError": MessageLookupByLibrary.simpleMessage("播放类型错误"),
        "tr_playerError": MessageLookupByLibrary.simpleMessage("播放器错误"),
        "tr_pleaseCategoryName":
            MessageLookupByLibrary.simpleMessage("请输入分类名称"),
        "tr_pleaseChooseDate": MessageLookupByLibrary.simpleMessage("请选择日期"),
        "tr_pleaseChooseDepartment":
            MessageLookupByLibrary.simpleMessage("请选择部门"),
        "tr_pleaseChooseDevice": MessageLookupByLibrary.simpleMessage("请选择设备"),
        "tr_pleaseChooseDeviceTags":
            MessageLookupByLibrary.simpleMessage("请选择设备标签"),
        "tr_pleaseChooseExecutionTime":
            MessageLookupByLibrary.simpleMessage("请选择执行时间"),
        "tr_pleaseChooseInspectionPerson":
            MessageLookupByLibrary.simpleMessage("请选择巡检人员"),
        "tr_pleaseChoosePersons":
            MessageLookupByLibrary.simpleMessage("请选择用户人员"),
        "tr_pleaseChooseRole": MessageLookupByLibrary.simpleMessage("请选择角色"),
        "tr_pleaseChooseRoles": MessageLookupByLibrary.simpleMessage("请选择角色"),
        "tr_pleaseChooseTagDevices":
            MessageLookupByLibrary.simpleMessage("请选择标签下设备"),
        "tr_pleaseChooseUserType":
            MessageLookupByLibrary.simpleMessage("请选择用户类型"),
        "tr_pleaseChooseValidityPeriod":
            MessageLookupByLibrary.simpleMessage("请选择有效期"),
        "tr_pleaseClick": MessageLookupByLibrary.simpleMessage("请点击"),
        "tr_pleaseEnter": MessageLookupByLibrary.simpleMessage("请输入"),
        "tr_pleaseEnterActivityName":
            MessageLookupByLibrary.simpleMessage("请输入活动名称"),
        "tr_pleaseEnterAreaName":
            MessageLookupByLibrary.simpleMessage("请输入区域名称"),
        "tr_pleaseEnterBitrate": MessageLookupByLibrary.simpleMessage("请输入码流值"),
        "tr_pleaseEnterContinuousDetectionCount":
            MessageLookupByLibrary.simpleMessage("请输入连续检测次数"),
        "tr_pleaseEnterCorrectRange":
            MessageLookupByLibrary.simpleMessage("请输入正确的范围"),
        "tr_pleaseEnterIFrameInterval":
            MessageLookupByLibrary.simpleMessage("请输入I帧间隔"),
        "tr_pleaseEnterStaffNumber":
            MessageLookupByLibrary.simpleMessage("请输入店员人数"),
        "tr_pleaseEnterTemplateName":
            MessageLookupByLibrary.simpleMessage("请输入模板名称"),
        "tr_pleaseEnterText": MessageLookupByLibrary.simpleMessage("请输入文字"),
        "tr_pleaseEnterTheContentOfTheEvaluationItem":
            MessageLookupByLibrary.simpleMessage("请输入考评项内容"),
        "tr_pleaseEnterTheEvaluationClassName":
            MessageLookupByLibrary.simpleMessage("请输入考评类名称"),
        "tr_pleaseEnterTheInspectionNameToSearch":
            MessageLookupByLibrary.simpleMessage("请输入巡检名称搜索"),
        "tr_pleaseEnterTheScoreOfTheEvaluationItem":
            MessageLookupByLibrary.simpleMessage("请输入考评项分数"),
        "tr_pleaseEnterVisitCount":
            MessageLookupByLibrary.simpleMessage("请输入访问次数"),
        "tr_pleaseEntereLigibilityScore":
            MessageLookupByLibrary.simpleMessage("请输入合格分数线"),
        "tr_pleaseEntryPlanName":
            MessageLookupByLibrary.simpleMessage("请输入计划名称"),
        "tr_pleaseEntryPlanNameForSearch":
            MessageLookupByLibrary.simpleMessage("请输入计划名称搜索"),
        "tr_pleaseEntryProblemDescribe":
            MessageLookupByLibrary.simpleMessage("请输入问题描述"),
        "tr_pleaseExitReplayPageAndRetry":
            MessageLookupByLibrary.simpleMessage("请退出回放页面后再进行重试"),
        "tr_pleaseInputChannelNamePushSIPHint":
            MessageLookupByLibrary.simpleMessage("请输入通道名称/推送SIP ID"),
        "tr_pleaseInputDeviceAccount":
            MessageLookupByLibrary.simpleMessage("请输入设备账号"),
        "tr_pleaseInputDeviceName":
            MessageLookupByLibrary.simpleMessage("请输入设备名称搜索"),
        "tr_pleaseInputDevicePassword":
            MessageLookupByLibrary.simpleMessage("请输入设备密码"),
        "tr_pleaseInputEmail": MessageLookupByLibrary.simpleMessage("请输入电子邮箱"),
        "tr_pleaseInputNameForSearch":
            MessageLookupByLibrary.simpleMessage("请输入名称搜索"),
        "tr_pleaseInputNamePhoneEmail":
            MessageLookupByLibrary.simpleMessage("请输入用户名/手机号/邮箱搜索"),
        "tr_pleaseInputPhone": MessageLookupByLibrary.simpleMessage("请输入手机号码"),
        "tr_pleaseInputPlatformNameIDHit":
            MessageLookupByLibrary.simpleMessage("请输入上级平台名称/接入ID"),
        "tr_pleaseInputRegionName":
            MessageLookupByLibrary.simpleMessage("请输入地区名称搜索"),
        "tr_pleaseInputSN": MessageLookupByLibrary.simpleMessage("请输入序列号（S/N）"),
        "tr_pleaseInputTwicePsd":
            MessageLookupByLibrary.simpleMessage("请再次输入密码"),
        "tr_pleaseRetryLater": MessageLookupByLibrary.simpleMessage("请稍后重试"),
        "tr_pleaseSaveCustomAreaFirst":
            MessageLookupByLibrary.simpleMessage("请先保存自定义区域"),
        "tr_pleaseSelectAcceptancePerson":
            MessageLookupByLibrary.simpleMessage("请选择验收人"),
        "tr_pleaseSelectActivityDate":
            MessageLookupByLibrary.simpleMessage("请选择活动日期"),
        "tr_pleaseSelectAlgorithm":
            MessageLookupByLibrary.simpleMessage("请选择算法"),
        "tr_pleaseSelectAlgorithmAndDeviceFirst":
            MessageLookupByLibrary.simpleMessage("请先选择算法和设备"),
        "tr_pleaseSelectArea": MessageLookupByLibrary.simpleMessage("请选择区域"),
        "tr_pleaseSelectAssignee":
            MessageLookupByLibrary.simpleMessage("请选择指派人"),
        "tr_pleaseSelectAtLeastOneArea":
            MessageLookupByLibrary.simpleMessage("请至少选择一个区域"),
        "tr_pleaseSelectCityRegionName":
            MessageLookupByLibrary.simpleMessage("请先选择城市或地区"),
        "tr_pleaseSelectDepartmentToAssociateFaces":
            MessageLookupByLibrary.simpleMessage("请选择需要关联人脸的部门"),
        "tr_pleaseSelectDept": MessageLookupByLibrary.simpleMessage("请先选择部门"),
        "tr_pleaseSelectEndTime":
            MessageLookupByLibrary.simpleMessage("请选择结束时间"),
        "tr_pleaseSelectInspectionDevice":
            MessageLookupByLibrary.simpleMessage("请选择巡检设备"),
        "tr_pleaseSelectInspectionTemplate":
            MessageLookupByLibrary.simpleMessage("请选择巡检模板"),
        "tr_pleaseSelectPassOrFail":
            MessageLookupByLibrary.simpleMessage("请选择合格或者不合格"),
        "tr_pleaseSelectPlan": MessageLookupByLibrary.simpleMessage("请选择计划"),
        "tr_pleaseSelectPlaybackTimeError":
            MessageLookupByLibrary.simpleMessage("目前回放时段是不可跨天，请重新选择时间段"),
        "tr_pleaseSelectRectificationItem":
            MessageLookupByLibrary.simpleMessage("请选择整改项"),
        "tr_pleaseSelectRectificationPerson":
            MessageLookupByLibrary.simpleMessage("请选择整改人"),
        "tr_pleaseSelectStartTime":
            MessageLookupByLibrary.simpleMessage("请选择开始时间"),
        "tr_pleaseSelectStore": MessageLookupByLibrary.simpleMessage("请选择门店"),
        "tr_pleaseSelectTheDeviceFirst":
            MessageLookupByLibrary.simpleMessage("请先选择设备"),
        "tr_pleaseSelectTheRecordingPlan":
            MessageLookupByLibrary.simpleMessage("请选择录像计划"),
        "tr_pleaseSelectTime": MessageLookupByLibrary.simpleMessage("请选择时间"),
        "tr_pleaseSetEvaluationItems":
            MessageLookupByLibrary.simpleMessage("请设置考评项"),
        "tr_pleaseSetExecutionTime":
            MessageLookupByLibrary.simpleMessage("请设置一个执行时间"),
        "tr_pleaseSetStaffNumberRange":
            MessageLookupByLibrary.simpleMessage("请设置店员人数范围区间"),
        "tr_pleaseSign": MessageLookupByLibrary.simpleMessage("请签名"),
        "tr_pleaseTagsCategoryName":
            MessageLookupByLibrary.simpleMessage("请输入标签分类名称"),
        "tr_pleaseTagsName": MessageLookupByLibrary.simpleMessage("请输入标签名称"),
        "tr_pleaseUnbindHasChildren":
            MessageLookupByLibrary.simpleMessage("请先删除节点及下属节点的设备"),
        "tr_pleaseUnbindStoreId":
            MessageLookupByLibrary.simpleMessage("请先解除以下云店绑定后再删除"),
        "tr_pleaseUploadFaceImage":
            MessageLookupByLibrary.simpleMessage("请拍摄或上传人脸照片"),
        "tr_preciseTrafficFlow": MessageLookupByLibrary.simpleMessage("精准客流"),
        "tr_precisionDevicesTotal": m43,
        "tr_presetEvaluationTemplate":
            MessageLookupByLibrary.simpleMessage("预设考评模板"),
        "tr_pressAgainToExitTheApplication":
            MessageLookupByLibrary.simpleMessage("再按一次退出应用"),
        "tr_previewAddress": MessageLookupByLibrary.simpleMessage("直播地址"),
        "tr_previewPlaybackDeviceReturnedNotFound":
            MessageLookupByLibrary.simpleMessage("预览/回放设备返回Not Found"),
        "tr_previewPlaybackFailed":
            MessageLookupByLibrary.simpleMessage("预览/回放失败"),
        "tr_previewPlaybackRequestTimeoutNoResponse":
            MessageLookupByLibrary.simpleMessage("预览/回放请求发送设备超时没有回复"),
        "tr_previewPlaybackServerGatewayCannotProcess":
            MessageLookupByLibrary.simpleMessage(
                "预览/回放由于设备超负载或维护问题，server或gateway不能处理请求"),
        "tr_privacyPolicyFrontTip":
            MessageLookupByLibrary.simpleMessage("查看完整版"),
        "tr_privacyPolicyUpdate":
            MessageLookupByLibrary.simpleMessage("隐私协议更新"),
        "tr_privacyPolicyUpdateTip": MessageLookupByLibrary.simpleMessage(
            "感谢您信任并使用蜂云SaaS的产品和服务。为了更好地维护用户的利益，我们对《隐私政策》进行了更新，特向您推送本提示。请仔细阅读并充分理解相关条款。您点击“同意”，即表示您已阅读并同意更新后的《隐私政策》，蜂云SaaS将尽全力保障您的合法权益。"),
        "tr_problemChain": MessageLookupByLibrary.simpleMessage("问题环比"),
        "tr_problemDescription": MessageLookupByLibrary.simpleMessage("问题描述"),
        "tr_problemDescriptionCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("问题描述不能为空"),
        "tr_problemFound": MessageLookupByLibrary.simpleMessage("发现问题"),
        "tr_processingDeadline": MessageLookupByLibrary.simpleMessage("处理时限"),
        "tr_processingTime": MessageLookupByLibrary.simpleMessage("处理时效"),
        "tr_processor": MessageLookupByLibrary.simpleMessage("处理人"),
        "tr_proportion": MessageLookupByLibrary.simpleMessage("所占比例"),
        "tr_proportionUnqualifiedInspections":
            MessageLookupByLibrary.simpleMessage("巡检不合格占比"),
        "tr_proportionValue": MessageLookupByLibrary.simpleMessage("占比"),
        "tr_protocolChooseTip":
            MessageLookupByLibrary.simpleMessage("网络环境弱时，建议优先TCP"),
        "tr_protocolMessageParsingError":
            MessageLookupByLibrary.simpleMessage("协议报文解析错误"),
        "tr_psdCheckFailureTip":
            MessageLookupByLibrary.simpleMessage("如下密码校验失败，可手动输入正确的密码"),
        "tr_psdCheckNoticeTip":
            MessageLookupByLibrary.simpleMessage("注意：设备离线不允许修改密码！"),
        "tr_psdCheckSuccessTip":
            MessageLookupByLibrary.simpleMessage("校验成功！如下为通道的账号密码"),
        "tr_psdKeyVerify": MessageLookupByLibrary.simpleMessage("密钥验证"),
        "tr_psdVerification": MessageLookupByLibrary.simpleMessage("密码校验"),
        "tr_psdVerificationFail":
            MessageLookupByLibrary.simpleMessage("密码校验失败"),
        "tr_psdVerificationTip":
            MessageLookupByLibrary.simpleMessage("验证当前默认密码是否正确，若不正确可输入密码校验"),
        "tr_pureBlackWhite": MessageLookupByLibrary.simpleMessage("纯黑/纯白"),
        "tr_pushChannel": MessageLookupByLibrary.simpleMessage("推送路数"),
        "tr_pushSIPID": MessageLookupByLibrary.simpleMessage("推送 SIP ID"),
        "tr_pwdConfig": MessageLookupByLibrary.simpleMessage("密码设置"),
        "tr_qrScan": MessageLookupByLibrary.simpleMessage("扫一扫"),
        "tr_quadrilateral": MessageLookupByLibrary.simpleMessage("四边形"),
        "tr_qualified": MessageLookupByLibrary.simpleMessage("合格"),
        "tr_qualifiedItem": MessageLookupByLibrary.simpleMessage("合格项"),
        "tr_queryNoDevicesTip":
            MessageLookupByLibrary.simpleMessage("区域内未找到设备，请重画区域"),
        "tr_queryRecordingFailed":
            MessageLookupByLibrary.simpleMessage("查询录像失败"),
        "tr_randomShake": MessageLookupByLibrary.simpleMessage("随机摇"),
        "tr_ranking": MessageLookupByLibrary.simpleMessage("排名"),
        "tr_rankingOfCameraIncidents":
            MessageLookupByLibrary.simpleMessage("摄像头事件数排名"),
        "tr_realTimeBandwidth": MessageLookupByLibrary.simpleMessage("实时带宽"),
        "tr_reason": MessageLookupByLibrary.simpleMessage("原因"),
        "tr_recordCardAddress": MessageLookupByLibrary.simpleMessage("本地回放地址"),
        "tr_recordCloudAddress": MessageLookupByLibrary.simpleMessage("云回放地址"),
        "tr_record_plan_not_enough_tip": MessageLookupByLibrary.simpleMessage(
            "您的云存储录像套餐授权路数不足，请联系**************开通更多授权。"),
        "tr_record_plan_upgrade_tip":
            MessageLookupByLibrary.simpleMessage("请联系**************升级套餐。"),
        "tr_recordingEndTime": MessageLookupByLibrary.simpleMessage("录像结束时间"),
        "tr_recordingFull": MessageLookupByLibrary.simpleMessage("录像满时"),
        "tr_recordingHasStopped": MessageLookupByLibrary.simpleMessage("录像停止"),
        "tr_recordingPlanEndTime":
            MessageLookupByLibrary.simpleMessage("录像计划结束时间"),
        "tr_recordingPlanStartTime":
            MessageLookupByLibrary.simpleMessage("录像计划开始时间"),
        "tr_recordingSchedule": MessageLookupByLibrary.simpleMessage("录像计划"),
        "tr_recordingSetting": MessageLookupByLibrary.simpleMessage("录像设置"),
        "tr_recordingStartTime": MessageLookupByLibrary.simpleMessage("录像开始时间"),
        "tr_recordingTime": MessageLookupByLibrary.simpleMessage("录像时间"),
        "tr_recordingType": MessageLookupByLibrary.simpleMessage("录像类型"),
        "tr_rectificationComplete":
            MessageLookupByLibrary.simpleMessage("整改完成"),
        "tr_rectificationCompletedLookingForwardToYourAcceptance":
            MessageLookupByLibrary.simpleMessage("整改完成，期待您验收"),
        "tr_rectificationDeadline":
            MessageLookupByLibrary.simpleMessage("截止时间"),
        "tr_rectificationEvent": MessageLookupByLibrary.simpleMessage("整改事件"),
        "tr_rectificationInstructions":
            MessageLookupByLibrary.simpleMessage("整改说明"),
        "tr_rectificationItem": MessageLookupByLibrary.simpleMessage("整改项"),
        "tr_rectificationOverdue": MessageLookupByLibrary.simpleMessage("整改逾期"),
        "tr_rectificationOverdueSort":
            MessageLookupByLibrary.simpleMessage("整改逾期次数排名"),
        "tr_rectificationOverdueTimes":
            MessageLookupByLibrary.simpleMessage("整改逾期次数"),
        "tr_rectificationPerson": MessageLookupByLibrary.simpleMessage("整改人"),
        "tr_rectificationScope": MessageLookupByLibrary.simpleMessage("整改范围"),
        "tr_rectificationStatus": MessageLookupByLibrary.simpleMessage("整改状态"),
        "tr_rectificationSubmissionTime":
            MessageLookupByLibrary.simpleMessage("整改提交时间"),
        "tr_rectificationTime": MessageLookupByLibrary.simpleMessage("整改时间"),
        "tr_rectificationTimeMustBeGreaterThanOneMinute":
            MessageLookupByLibrary.simpleMessage("整改时间必须大于1分钟"),
        "tr_rectify": MessageLookupByLibrary.simpleMessage("整改"),
        "tr_rectifyCount": MessageLookupByLibrary.simpleMessage("已提交整改次数"),
        "tr_referencePicture": MessageLookupByLibrary.simpleMessage("参考图片"),
        "tr_refreshFooterCanLoadingText":
            MessageLookupByLibrary.simpleMessage("松手开始加载数据"),
        "tr_refreshFooterFailedText":
            MessageLookupByLibrary.simpleMessage("加载失败"),
        "tr_refreshFooterIdleText":
            MessageLookupByLibrary.simpleMessage("上拉加载"),
        "tr_refreshFooterLoadingText":
            MessageLookupByLibrary.simpleMessage("加载中..."),
        "tr_refreshFooterNoDataText":
            MessageLookupByLibrary.simpleMessage("没有更多了"),
        "tr_refreshHeaderCompleteText":
            MessageLookupByLibrary.simpleMessage("刷新完成"),
        "tr_refreshHeaderFailedText":
            MessageLookupByLibrary.simpleMessage("刷新失败"),
        "tr_refreshHeaderIdleText":
            MessageLookupByLibrary.simpleMessage("下拉刷新"),
        "tr_refreshHeaderRefreshingText":
            MessageLookupByLibrary.simpleMessage("刷新中"),
        "tr_refreshHeaderReleaseText":
            MessageLookupByLibrary.simpleMessage("松开手刷新"),
        "tr_refreshText": MessageLookupByLibrary.simpleMessage("刷新"),
        "tr_refreshed": MessageLookupByLibrary.simpleMessage("已刷新"),
        "tr_registrationCycle": MessageLookupByLibrary.simpleMessage("注册周期(秒)"),
        "tr_registrationCycleHint":
            MessageLookupByLibrary.simpleMessage("范围在3600-86400秒"),
        "tr_registrationSwitch": MessageLookupByLibrary.simpleMessage("注册开关"),
        "tr_registrationSwitchExplanation":
            MessageLookupByLibrary.simpleMessage(
                "开启状态下，表示与上级平台通信中，不可编辑；关闭注册开关，可编辑国标级联信息"),
        "tr_relatedPerson": MessageLookupByLibrary.simpleMessage("关联人"),
        "tr_releaseDate": MessageLookupByLibrary.simpleMessage("发布日期"),
        "tr_remainingPath": MessageLookupByLibrary.simpleMessage("剩余路"),
        "tr_remainingRoad": m44,
        "tr_remaining_flow": MessageLookupByLibrary.simpleMessage("剩余流量"),
        "tr_remark": MessageLookupByLibrary.simpleMessage("备注"),
        "tr_remove": MessageLookupByLibrary.simpleMessage("移除"),
        "tr_removeSuccessfully": MessageLookupByLibrary.simpleMessage("移除成功"),
        "tr_renewal": MessageLookupByLibrary.simpleMessage("续费"),
        "tr_replayChannelOccupied":
            MessageLookupByLibrary.simpleMessage("回放通道被占用"),
        "tr_requestDeviceInfoFromDSMFailed":
            MessageLookupByLibrary.simpleMessage("向DSM服务请求设备信息失败"),
        "tr_requestNotificationCamera":
            MessageLookupByLibrary.simpleMessage("相机权限使用说明：用于拍照、录制视频等场景"),
        "tr_requestNotificationLocation": MessageLookupByLibrary.simpleMessage(
            "获取位置信息权限使用说明：用于访问确切位置信息、搜索摄像机等设备场景"),
        "tr_requestNotificationPhoto": MessageLookupByLibrary.simpleMessage(
            "读取存储卡权限使用说明：用于读取存储卡上的照片、媒体内容和文件等场景"),
        "tr_requestNotificationStory": MessageLookupByLibrary.simpleMessage(
            "访问存储权限使用说明：用于修改或删除存储卡上的照片、媒体内容和文件场景"),
        "tr_resetDevice": MessageLookupByLibrary.simpleMessage("重置设备"),
        "tr_resetDeviceFailure": MessageLookupByLibrary.simpleMessage("重置设备失败"),
        "tr_resetDeviceNode": MessageLookupByLibrary.simpleMessage("回到默认"),
        "tr_resetDeviceSuccess": MessageLookupByLibrary.simpleMessage("重置设备成功"),
        "tr_resetDraw": MessageLookupByLibrary.simpleMessage("重画"),
        "tr_resetFlowInfo":
            MessageLookupByLibrary.simpleMessage("客流数据信息每日24:00清零"),
        "tr_resetNode": MessageLookupByLibrary.simpleMessage("默认"),
        "tr_resign": MessageLookupByLibrary.simpleMessage("重签"),
        "tr_resolution": MessageLookupByLibrary.simpleMessage("分辨率"),
        "tr_restartDevice": MessageLookupByLibrary.simpleMessage("重启设备"),
        "tr_restartDeviceFailure":
            MessageLookupByLibrary.simpleMessage("重启设备失败"),
        "tr_restartDeviceSuccess":
            MessageLookupByLibrary.simpleMessage("重启设备成功"),
        "tr_retAddDeviceFailed": MessageLookupByLibrary.simpleMessage(
            "1. 请插好电源和数据线，确保设备上电并已经正常运行；\n\n2. 请将设备，手机和路由器尽量靠近（1米以内的范围内），确保信号接收成功；\n\n3. 请确保输入的路由器账号密码是正确的；\n\n4. 可将设备SET/RESET键，将设备恢复出厂设置后重新配网连接。"),
        "tr_retry": MessageLookupByLibrary.simpleMessage("重试"),
        "tr_retryBTConnectFailed":
            MessageLookupByLibrary.simpleMessage("无法连接蓝牙设备，请重试！"),
        "tr_retrying": MessageLookupByLibrary.simpleMessage("重试中..."),
        "tr_retryingNameCount": m45,
        "tr_returnHomePage": MessageLookupByLibrary.simpleMessage("回到首页"),
        "tr_returnTime": MessageLookupByLibrary.simpleMessage("打回时间"),
        "tr_rootNode": MessageLookupByLibrary.simpleMessage("根节点"),
        "tr_rtspProtocolError":
            MessageLookupByLibrary.simpleMessage("RTSP协议错误"),
        "tr_ruleConfiguration": MessageLookupByLibrary.simpleMessage("规则配置"),
        "tr_running": MessageLookupByLibrary.simpleMessage("运行中"),
        "tr_runtime": MessageLookupByLibrary.simpleMessage("运行时间"),
        "tr_runtimePoint": MessageLookupByLibrary.simpleMessage("运行时间点"),
        "tr_safetyManagement": MessageLookupByLibrary.simpleMessage("安全管理"),
        "tr_saturday": MessageLookupByLibrary.simpleMessage("周六"),
        "tr_scanDeviceQRCodeTip":
            MessageLookupByLibrary.simpleMessage("扫描设备上的二维码"),
        "tr_scanLogin": MessageLookupByLibrary.simpleMessage("登录确认"),
        "tr_scanLoginSuccess": MessageLookupByLibrary.simpleMessage("扫码登录成功"),
        "tr_scanOnlyQRCodeTip":
            MessageLookupByLibrary.simpleMessage("二维码放入框内扫描"),
        "tr_scanQRNetworkCodeTips":
            MessageLookupByLibrary.simpleMessage("扫描设备上的二维码，找不到\n请使用局域网进行设备添加"),
        "tr_scanReLoginTip": MessageLookupByLibrary.simpleMessage("请重新扫码登录"),
        "tr_scanWebLogin": MessageLookupByLibrary.simpleMessage("桌面版登录确认"),
        "tr_sceneInspection": MessageLookupByLibrary.simpleMessage("现场巡检"),
        "tr_score": MessageLookupByLibrary.simpleMessage("分"),
        "tr_scoreCannotBeEmpty": MessageLookupByLibrary.simpleMessage("分数不能为空"),
        "tr_scoreValue": MessageLookupByLibrary.simpleMessage("分值"),
        "tr_scoresCanBeOnlyEnteredInNumbers":
            MessageLookupByLibrary.simpleMessage("分数只能输入数字"),
        "tr_scoringItem": MessageLookupByLibrary.simpleMessage("评分项"),
        "tr_scoringRate": MessageLookupByLibrary.simpleMessage("得分率"),
        "tr_screenGlitch": MessageLookupByLibrary.simpleMessage("花屏"),
        "tr_screenshotFailedPleaseRetry":
            MessageLookupByLibrary.simpleMessage("截图失败，请重试"),
        "tr_searchAiBoxDeviceTip":
            MessageLookupByLibrary.simpleMessage("设备和盒子要在同一局域网内"),
        "tr_searchAlgorithmByName":
            MessageLookupByLibrary.simpleMessage("请输入算法名称搜索"),
        "tr_searchChannelTips": MessageLookupByLibrary.simpleMessage("搜索到如下设备"),
        "tr_searchDevice": MessageLookupByLibrary.simpleMessage("搜设备"),
        "tr_searchDeviceNameSN":
            MessageLookupByLibrary.simpleMessage("输入设备名称/序列号"),
        "tr_searchDeviceNoData":
            MessageLookupByLibrary.simpleMessage("未搜索到对应设备信息"),
        "tr_searchDeviceTags": MessageLookupByLibrary.simpleMessage("搜索设备标签"),
        "tr_searchFaceHitText":
            MessageLookupByLibrary.simpleMessage("请输入用户名/手机号搜索"),
        "tr_searchHistory": MessageLookupByLibrary.simpleMessage("搜索历史"),
        "tr_searchHitText": MessageLookupByLibrary.simpleMessage("请输入门店名称搜索"),
        "tr_searchJFDevice": MessageLookupByLibrary.simpleMessage("扫描发现设备"),
        "tr_searchJFDeviceTip":
            MessageLookupByLibrary.simpleMessage("NVR和摄像头要在同一局域网内"),
        "tr_searchJFStatusFailureBehind":
            MessageLookupByLibrary.simpleMessage("重新扫描"),
        "tr_searchJFStatusFailureFront":
            MessageLookupByLibrary.simpleMessage("未扫描到设备，请"),
        "tr_searchJFStatusRefresh":
            MessageLookupByLibrary.simpleMessage("正在搜索设备，请等待…"),
        "tr_searchJFStatusResultBehind":
            MessageLookupByLibrary.simpleMessage("查看详情"),
        "tr_searchJFStatusResultFront": m46,
        "tr_searchNameHitText": MessageLookupByLibrary.simpleMessage("请输入名称搜索"),
        "tr_searchNoAddressData":
            MessageLookupByLibrary.simpleMessage("请搜索或定位获取位置信息"),
        "tr_searchNoDeviceTip":
            MessageLookupByLibrary.simpleMessage("未检测到设备或设备已添加"),
        "tr_searchNodeDevice":
            MessageLookupByLibrary.simpleMessage("输入节点或设备名称"),
        "tr_searchRegion": MessageLookupByLibrary.simpleMessage("搜地区"),
        "tr_searchRegionNoAddressData":
            MessageLookupByLibrary.simpleMessage("未搜索到附近地址信息"),
        "tr_searchRegionNoData":
            MessageLookupByLibrary.simpleMessage("未搜索到地名信息"),
        "tr_searchStoreAddress":
            MessageLookupByLibrary.simpleMessage("去搜索门店地址"),
        "tr_searchStoreAddressAddressInfo":
            MessageLookupByLibrary.simpleMessage("请搜索门店地址位置信息"),
        "tr_searchStoreAddressNoAddressData":
            MessageLookupByLibrary.simpleMessage("请搜索或定位门店地址获取位置信息"),
        "tr_searchingDeviceTip":
            MessageLookupByLibrary.simpleMessage("设备扫描中，请等待..."),
        "tr_searchingMonitoringDevicePleaseWait":
            MessageLookupByLibrary.simpleMessage("正在搜索监控设备，请稍等片刻..."),
        "tr_second": MessageLookupByLibrary.simpleMessage("秒"),
        "tr_secondaryVerification":
            MessageLookupByLibrary.simpleMessage("二次校验"),
        "tr_secondsPerLoop": MessageLookupByLibrary.simpleMessage("秒循环一次"),
        "tr_select": MessageLookupByLibrary.simpleMessage("选择"),
        "tr_selectAcceptancePerson":
            MessageLookupByLibrary.simpleMessage("选择验收人"),
        "tr_selectAlgorithm": MessageLookupByLibrary.simpleMessage("选择算法"),
        "tr_selectAll": MessageLookupByLibrary.simpleMessage("选择全部"),
        "tr_selectAtLeastOneParameter":
            MessageLookupByLibrary.simpleMessage("至少要选择一个参数"),
        "tr_selectCategory": MessageLookupByLibrary.simpleMessage("已选分类"),
        "tr_selectDevice": MessageLookupByLibrary.simpleMessage("选择设备"),
        "tr_selectDeviceNode": MessageLookupByLibrary.simpleMessage("请选择节点"),
        "tr_selectDeviceNodeResources":
            MessageLookupByLibrary.simpleMessage("请选择设备节点"),
        "tr_selectEvaluationCategory":
            MessageLookupByLibrary.simpleMessage("选择考评类"),
        "tr_selectEvaluationTemplate":
            MessageLookupByLibrary.simpleMessage("选择考评模板"),
        "tr_selectLocalFileUpgrade":
            MessageLookupByLibrary.simpleMessage("选本地文件升级"),
        "tr_selectMonth": MessageLookupByLibrary.simpleMessage("选择月"),
        "tr_selectMultiple": MessageLookupByLibrary.simpleMessage("选择多个"),
        "tr_selectMultipleChoices":
            MessageLookupByLibrary.simpleMessage("去选择(可多选)"),
        "tr_selectOrAddConversation":
            MessageLookupByLibrary.simpleMessage("选择一个会话或者添加会话"),
        "tr_selectPerson": MessageLookupByLibrary.simpleMessage("选择人员"),
        "tr_selectPersonnel": MessageLookupByLibrary.simpleMessage("请选择人员"),
        "tr_selectRectificationPerson":
            MessageLookupByLibrary.simpleMessage("选择整改人"),
        "tr_selectStoragePackage":
            MessageLookupByLibrary.simpleMessage("选择存储套餐"),
        "tr_selectStores": MessageLookupByLibrary.simpleMessage("请选择门店"),
        "tr_selectTagsCategory":
            MessageLookupByLibrary.simpleMessage("请选择标签分类"),
        "tr_selectTemplate": MessageLookupByLibrary.simpleMessage("请选择模板"),
        "tr_selectTime": MessageLookupByLibrary.simpleMessage("选择时间"),
        "tr_selectTimePeriod": MessageLookupByLibrary.simpleMessage("选择时间段"),
        "tr_selectUsefulWiFi":
            MessageLookupByLibrary.simpleMessage("请选择可用的WiFi"),
        "tr_selectYear": MessageLookupByLibrary.simpleMessage("选择年"),
        "tr_selectYearAndMonth": MessageLookupByLibrary.simpleMessage("选择年月"),
        "tr_selectableRangeIsInspectorEquipmentPermissions":
            MessageLookupByLibrary.simpleMessage("可选范围为巡检人所拥有设备权限资源"),
        "tr_selectedTime": MessageLookupByLibrary.simpleMessage("已选时间"),
        "tr_sendMessageToDeepseek":
            MessageLookupByLibrary.simpleMessage("给 deepseek 发送消息"),
        "tr_sensitivity": MessageLookupByLibrary.simpleMessage("灵敏度"),
        "tr_serialNumberHasNotExistConnect":
            MessageLookupByLibrary.simpleMessage("序列号不存在/设备未联网"),
        "tr_serverActualAccessChannelRate":
            MessageLookupByLibrary.simpleMessage("接入通道数/申请通道数"),
        "tr_serverGateway": MessageLookupByLibrary.simpleMessage("服务器网关"),
        "tr_serverPortNum": MessageLookupByLibrary.simpleMessage("SIP服务器端口"),
        "tr_serverValidationTimeout":
            MessageLookupByLibrary.simpleMessage("服务器校验时服务出现超时"),
        "tr_serviceValidationException":
            MessageLookupByLibrary.simpleMessage("服务校验时服务异常"),
        "tr_setAMapParam": MessageLookupByLibrary.simpleMessage("地图设置"),
        "tr_setBusinessHours": MessageLookupByLibrary.simpleMessage("设置营业时间"),
        "tr_setDemandNumChannels":
            MessageLookupByLibrary.simpleMessage("设置接入通道数"),
        "tr_setDetectionFrequency":
            MessageLookupByLibrary.simpleMessage("设置检测频率"),
        "tr_setDuration": MessageLookupByLibrary.simpleMessage("设置持续时长"),
        "tr_setNationalStandardConfiguration":
            MessageLookupByLibrary.simpleMessage("设置国标编码配置"),
        "tr_setNoHit": MessageLookupByLibrary.simpleMessage("暂未设置"),
        "tr_setPwdConfig": MessageLookupByLibrary.simpleMessage("设置密码"),
        "tr_setSingleAndTotalStayDuration":
            MessageLookupByLibrary.simpleMessage("设置单次停留时长和总停留时长"),
        "tr_setStaffArrivalDepartureTime":
            MessageLookupByLibrary.simpleMessage("设置店员到店/离店时间"),
        "tr_setStaffDeduplicationRules":
            MessageLookupByLibrary.simpleMessage("设置店员去重规则，条件可任意组合"),
        "tr_setStaffNumber": MessageLookupByLibrary.simpleMessage("设置店员人数"),
        "tr_setStoreManagerHit":
            MessageLookupByLibrary.simpleMessage("请选择设置店长"),
        "tr_setSystemPermission": MessageLookupByLibrary.simpleMessage("系统权限"),
        "tr_setTime": MessageLookupByLibrary.simpleMessage("时间设置"),
        "tr_setUp": MessageLookupByLibrary.simpleMessage("已设置"),
        "tr_setUpToNameTimePeriods": m47,
        "tr_severe": MessageLookupByLibrary.simpleMessage("严重"),
        "tr_shake": MessageLookupByLibrary.simpleMessage("摇一摇"),
        "tr_shakeDevice": MessageLookupByLibrary.simpleMessage("摇设备"),
        "tr_shakeForInspection": MessageLookupByLibrary.simpleMessage("摇一摇巡检"),
        "tr_shakeStore": MessageLookupByLibrary.simpleMessage("摇门店"),
        "tr_shakeToSearchMonitoring":
            MessageLookupByLibrary.simpleMessage("摇一摇搜索监控"),
        "tr_showLanguage": MessageLookupByLibrary.simpleMessage("显示语言"),
        "tr_sign": MessageLookupByLibrary.simpleMessage("点击签名"),
        "tr_signInImage": MessageLookupByLibrary.simpleMessage("签到图片"),
        "tr_signInInfo": MessageLookupByLibrary.simpleMessage("签到信息"),
        "tr_signOutInfo": MessageLookupByLibrary.simpleMessage("签出信息"),
        "tr_signTip": MessageLookupByLibrary.simpleMessage("签名处"),
        "tr_signalingTransmission":
            MessageLookupByLibrary.simpleMessage("信令传输"),
        "tr_similarity": MessageLookupByLibrary.simpleMessage("相似度"),
        "tr_singlePerson": MessageLookupByLibrary.simpleMessage("单人"),
        "tr_singlePersonGroup":
            MessageLookupByLibrary.simpleMessage("单人进店（顾客组）"),
        "tr_singleStayDuration": MessageLookupByLibrary.simpleMessage("单次停留时长"),
        "tr_singleStayDurationAboveMinutes": m48,
        "tr_sipServerPortHint":
            MessageLookupByLibrary.simpleMessage("端口号为1-5位的数字"),
        "tr_sixSNCode": MessageLookupByLibrary.simpleMessage("6位序列号"),
        "tr_sixSNCodeTips": MessageLookupByLibrary.simpleMessage("长度6位，由数字组成"),
        "tr_sixSNNationsCodeTips":
            MessageLookupByLibrary.simpleMessage("长度6位，由数字组成，序列号之间不能相同"),
        "tr_skyCloudStorage": MessageLookupByLibrary.simpleMessage("天云存储"),
        "tr_skyCloudStorageLoopRecording":
            MessageLookupByLibrary.simpleMessage("天云存储循环录像"),
        "tr_smartAlarm": MessageLookupByLibrary.simpleMessage("智能报警"),
        "tr_smartAlert": MessageLookupByLibrary.simpleMessage("智能警戒"),
        "tr_smartAlertTip":
            MessageLookupByLibrary.simpleMessage("配置相关参数，设备可触发人形检测和移动侦测"),
        "tr_smartEncoding": MessageLookupByLibrary.simpleMessage("Smart编码"),
        "tr_snAdd": MessageLookupByLibrary.simpleMessage("序列号添加"),
        "tr_softwareVersion": MessageLookupByLibrary.simpleMessage("软件版本"),
        "tr_softwareVersionReleaseDate":
            MessageLookupByLibrary.simpleMessage("软件版本发布日期"),
        "tr_someDevicesHaveNoConfiguredAlgorithm":
            MessageLookupByLibrary.simpleMessage("还有设备未配置算法"),
        "tr_someOptionsUnSelect": MessageLookupByLibrary.simpleMessage("还有未选项"),
        "tr_speakerVolume": MessageLookupByLibrary.simpleMessage("喇叭音量"),
        "tr_spotCheck": MessageLookupByLibrary.simpleMessage("抽查"),
        "tr_staffAcceptanceRectificationSort":
            MessageLookupByLibrary.simpleMessage("员工完成验收次数排名"),
        "tr_staffCompleteRectificationSort":
            MessageLookupByLibrary.simpleMessage("员工完成整改次数排名"),
        "tr_staffDeduplication": MessageLookupByLibrary.simpleMessage("店员去重"),
        "tr_staffDeduplicationRules":
            MessageLookupByLibrary.simpleMessage("店员去重规则"),
        "tr_staffFilter": MessageLookupByLibrary.simpleMessage("店员过滤"),
        "tr_staffFilterRules": MessageLookupByLibrary.simpleMessage("店员过滤规则"),
        "tr_staffNumber": MessageLookupByLibrary.simpleMessage("店员人数"),
        "tr_staffOverdueRectificationSort":
            MessageLookupByLibrary.simpleMessage("员工整改逾期次数排名"),
        "tr_startDate": MessageLookupByLibrary.simpleMessage("起始日期"),
        "tr_startDownloadTip":
            MessageLookupByLibrary.simpleMessage("下载任务添加成功，请到下载管理中查看"),
        "tr_startTime": MessageLookupByLibrary.simpleMessage("开始时间"),
        "tr_startTimeMustBeBeforeEndTime":
            MessageLookupByLibrary.simpleMessage("开始时间必须小于结束时间"),
        "tr_statisticalDimension": MessageLookupByLibrary.simpleMessage("统计维度"),
        "tr_statisticalPeriod": MessageLookupByLibrary.simpleMessage("统计周期"),
        "tr_statistics": MessageLookupByLibrary.simpleMessage("数据统计"),
        "tr_statistics1": MessageLookupByLibrary.simpleMessage("统计"),
        "tr_statisticsTime": MessageLookupByLibrary.simpleMessage("统计时间"),
        "tr_stayDuration": MessageLookupByLibrary.simpleMessage("店内停留时长"),
        "tr_stayRate": MessageLookupByLibrary.simpleMessage("停留率"),
        "tr_stopBTNetConfigure":
            MessageLookupByLibrary.simpleMessage("确定要中断蓝牙配网？"),
        "tr_stopFailedDownloadingTask":
            MessageLookupByLibrary.simpleMessage("正在下载的任务停止失败"),
        "tr_stopNetConfigure":
            MessageLookupByLibrary.simpleMessage("当前正在快速配网和扫描配网中，您确定要中断配网吗？"),
        "tr_stopRecording": MessageLookupByLibrary.simpleMessage("停止录像"),
        "tr_stopTime": MessageLookupByLibrary.simpleMessage("终止时间"),
        "tr_storageCardManagement":
            MessageLookupByLibrary.simpleMessage("存储卡管理"),
        "tr_storageManagement": MessageLookupByLibrary.simpleMessage("存储卡管理"),
        "tr_store": MessageLookupByLibrary.simpleMessage("门店"),
        "tr_storeAcceptanceCount": MessageLookupByLibrary.simpleMessage("验收数"),
        "tr_storeAddress": MessageLookupByLibrary.simpleMessage("门店地址"),
        "tr_storeAddressHit": MessageLookupByLibrary.simpleMessage("请输入地址"),
        "tr_storeAndAlgorithm": MessageLookupByLibrary.simpleMessage("门店和算法"),
        "tr_storeChannelDevices": m49,
        "tr_storeConversionRate": MessageLookupByLibrary.simpleMessage("进店转化率"),
        "tr_storeConversionRateTip": MessageLookupByLibrary.simpleMessage(
            "进店转化率\n1. 进店转化率=统计时段内未去重进店客流/统计时段内未去重总客流\n2. 总进店率比：未去重进店客流/未去重总客流（进店客流+过店客流）\n3. 进店顾客组：统计时间内，单人/双人/三人/多人批次个数/总批次个数"),
        "tr_storeCoverageRateExplain":
            MessageLookupByLibrary.simpleMessage("统计时间内巡检门店数/所有门店"),
        "tr_storeDataOverview": MessageLookupByLibrary.simpleMessage("门店数据概览"),
        "tr_storeDeleted": MessageLookupByLibrary.simpleMessage("门店被删除"),
        "tr_storeDetail": MessageLookupByLibrary.simpleMessage("门店详情"),
        "tr_storeDeviceInformation":
            MessageLookupByLibrary.simpleMessage("门店设备信息"),
        "tr_storeDevices": MessageLookupByLibrary.simpleMessage("门店设备"),
        "tr_storeDimension": MessageLookupByLibrary.simpleMessage("门店维度"),
        "tr_storeEntry": MessageLookupByLibrary.simpleMessage("进过店"),
        "tr_storeEntryExitConsideredStaff": m50,
        "tr_storeEntryRate": MessageLookupByLibrary.simpleMessage("进店率"),
        "tr_storeEventAnalysis": MessageLookupByLibrary.simpleMessage("门店事件分析"),
        "tr_storeEventCount": MessageLookupByLibrary.simpleMessage("事件数"),
        "tr_storeEventRanking": MessageLookupByLibrary.simpleMessage("门店事件排名"),
        "tr_storeExceptionInspectionNotPossible":
            MessageLookupByLibrary.simpleMessage("存在门店异常无法巡检"),
        "tr_storeFloorPlan": MessageLookupByLibrary.simpleMessage("门店平面图"),
        "tr_storeFlow": MessageLookupByLibrary.simpleMessage("门店客流"),
        "tr_storeFlowRanking": MessageLookupByLibrary.simpleMessage("门店客流排名"),
        "tr_storeHasNoPassengerFlowCameraPleaseAddFirst":
            MessageLookupByLibrary.simpleMessage("门店无客流统计摄像头，请先添加客流统计摄像头。"),
        "tr_storeHeatMapConfiguration":
            MessageLookupByLibrary.simpleMessage("门店热力图配置"),
        "tr_storeInspectionAnalysis":
            MessageLookupByLibrary.simpleMessage("门店巡检分析"),
        "tr_storeInspectionCompletionRate":
            MessageLookupByLibrary.simpleMessage("巡店任务完成率"),
        "tr_storeInspectionCoverageDetail":
            MessageLookupByLibrary.simpleMessage("巡检覆盖情况"),
        "tr_storeInspectionCoverageRate":
            MessageLookupByLibrary.simpleMessage("巡店覆盖率"),
        "tr_storeInspectionOverview":
            MessageLookupByLibrary.simpleMessage("巡店概览"),
        "tr_storeInspectorSign": MessageLookupByLibrary.simpleMessage("巡店人签字"),
        "tr_storeManager": MessageLookupByLibrary.simpleMessage("店长"),
        "tr_storeManagerSign": MessageLookupByLibrary.simpleMessage("店长签字"),
        "tr_storeName": MessageLookupByLibrary.simpleMessage("门店名称"),
        "tr_storeNameHit": MessageLookupByLibrary.simpleMessage("请输入门店名称"),
        "tr_storeNoPermission": MessageLookupByLibrary.simpleMessage("门店没权限"),
        "tr_storeOverdueCount": MessageLookupByLibrary.simpleMessage("逾期数"),
        "tr_storeQuestionRanking":
            MessageLookupByLibrary.simpleMessage("门店发现问题排名"),
        "tr_storeResources": MessageLookupByLibrary.simpleMessage("门店资源"),
        "tr_storeSelfInspection": MessageLookupByLibrary.simpleMessage("门店自检"),
        "tr_storeTraffic": MessageLookupByLibrary.simpleMessage("进店客流"),
        "tr_storeTrafficNoDeduplication":
            MessageLookupByLibrary.simpleMessage("进店客流(未去重)"),
        "tr_storeTrafficSettings":
            MessageLookupByLibrary.simpleMessage("门店客流设置"),
        "tr_storeTrafficStatistics":
            MessageLookupByLibrary.simpleMessage("门店客流统计"),
        "tr_storesPendingInspectionContinue":
            MessageLookupByLibrary.simpleMessage("本次抽查巡检中还有门店未巡检，是否继续巡检？"),
        "tr_subAccountActivation":
            MessageLookupByLibrary.simpleMessage("子账户激活"),
        "tr_subDate": MessageLookupByLibrary.simpleMessage("日期"),
        "tr_subStream": MessageLookupByLibrary.simpleMessage("辅码流"),
        "tr_subTitlePermission": MessageLookupByLibrary.simpleMessage("权限"),
        "tr_subTitleReset": MessageLookupByLibrary.simpleMessage(
            "设备恢复出厂设置后，导致设备无法连接WIFI，相关功能无法正常运行"),
        "tr_subTitleRestart":
            MessageLookupByLibrary.simpleMessage("设备重启后需要稍作等候，功能即可恢复正常"),
        "tr_submissionTime": MessageLookupByLibrary.simpleMessage("提交时间"),
        "tr_submitRectificationTaskToYou":
            MessageLookupByLibrary.simpleMessage("提交的事件"),
        "tr_submitted": MessageLookupByLibrary.simpleMessage("已提交"),
        "tr_submittedEvent": MessageLookupByLibrary.simpleMessage("已提交事件"),
        "tr_submittedSuccessfully":
            MessageLookupByLibrary.simpleMessage("提交成功"),
        "tr_success": MessageLookupByLibrary.simpleMessage("成功"),
        "tr_successPassed": MessageLookupByLibrary.simpleMessage("通过"),
        "tr_sunday": MessageLookupByLibrary.simpleMessage("周日"),
        "tr_supervisionInspection":
            MessageLookupByLibrary.simpleMessage("督导巡店"),
        "tr_support": MessageLookupByLibrary.simpleMessage("支持"),
        "tr_supportAlgorithms": MessageLookupByLibrary.simpleMessage("支持算法"),
        "tr_supportedCentralAlgorithms":
            MessageLookupByLibrary.simpleMessage("支持的中心算法"),
        "tr_supportedEdgeAlgorithms":
            MessageLookupByLibrary.simpleMessage("支持的端侧算法"),
        "tr_surplusNameWay": m51,
        "tr_surrounding": MessageLookupByLibrary.simpleMessage("周边"),
        "tr_switchEmail": MessageLookupByLibrary.simpleMessage("切换邮箱"),
        "tr_switchPhone": MessageLookupByLibrary.simpleMessage("切换手机号"),
        "tr_switchPlatform": MessageLookupByLibrary.simpleMessage("转平台"),
        "tr_switchPlatformSuccessfully":
            MessageLookupByLibrary.simpleMessage("转平台成功"),
        "tr_switchPlatformTip":
            MessageLookupByLibrary.simpleMessage("以下为非平台用户，可转为平台用户"),
        "tr_switchingDevicesPleaseWait":
            MessageLookupByLibrary.simpleMessage("正在切换设备，请稍候"),
        "tr_switchingStreamPleaseWait":
            MessageLookupByLibrary.simpleMessage("正在切换主辅码流，请稍候"),
        "tr_syncInterceptTitle": MessageLookupByLibrary.simpleMessage("授权拦截详情"),
        "tr_syncSubordinateChannels":
            MessageLookupByLibrary.simpleMessage("同步修改下属通道"),
        "tr_tagHasNoDeviceTags":
            MessageLookupByLibrary.simpleMessage("暂未添加任何设备标签"),
        "tr_takePhotoForSignin": MessageLookupByLibrary.simpleMessage("拍照签到"),
        "tr_takePhotoForSigninTip":
            MessageLookupByLibrary.simpleMessage("请拍摄现场照片打卡认证"),
        "tr_talk": MessageLookupByLibrary.simpleMessage("对讲"),
        "tr_talkChannelOccupied":
            MessageLookupByLibrary.simpleMessage("对讲通道被占用"),
        "tr_tapToAdd": MessageLookupByLibrary.simpleMessage("点击添加"),
        "tr_targetBox": MessageLookupByLibrary.simpleMessage("目标框"),
        "tr_targetInformation": MessageLookupByLibrary.simpleMessage("目标信息"),
        "tr_task": MessageLookupByLibrary.simpleMessage("任务"),
        "tr_taskCreationTime": MessageLookupByLibrary.simpleMessage("任务创建时间"),
        "tr_taskExecutionDate": MessageLookupByLibrary.simpleMessage("任务执行日期"),
        "tr_taskExecutionMaximum": m52,
        "tr_taskExecutionTime": MessageLookupByLibrary.simpleMessage("任务执行时间"),
        "tr_taskExists": MessageLookupByLibrary.simpleMessage("已经有相同任务，无需重复添加"),
        "tr_taskIssuanceTime": MessageLookupByLibrary.simpleMessage("任务下发时间"),
        "tr_taskIssued": MessageLookupByLibrary.simpleMessage("任务下发"),
        "tr_taskName": MessageLookupByLibrary.simpleMessage("任务名称"),
        "tr_taskSubmissionTime": MessageLookupByLibrary.simpleMessage("任务提交时间"),
        "tr_templateDetails": MessageLookupByLibrary.simpleMessage("模板详情"),
        "tr_templateName": MessageLookupByLibrary.simpleMessage("模板名称"),
        "tr_text": MessageLookupByLibrary.simpleMessage("文字"),
        "tr_theDayBeforeYesterday": MessageLookupByLibrary.simpleMessage("前天"),
        "tr_thenExecuteAction": MessageLookupByLibrary.simpleMessage("就（执行动作）"),
        "tr_thereAreStillAssessmentItemsWithoutResultsSet":
            MessageLookupByLibrary.simpleMessage("还有考评项未设置结果"),
        "tr_thereMustBeAtLeastOneEvaluationItem":
            MessageLookupByLibrary.simpleMessage("至少要有一个考评项"),
        "tr_thinking": MessageLookupByLibrary.simpleMessage("思考中"),
        "tr_thinkingFailed": MessageLookupByLibrary.simpleMessage("思考失败"),
        "tr_thinkingFailedTip":
            MessageLookupByLibrary.simpleMessage("服务繁忙，请稍后再试"),
        "tr_thinkingPleaseWait":
            MessageLookupByLibrary.simpleMessage("正在思考中，请稍后再试"),
        "tr_thisCompanyAddExceptions": MessageLookupByLibrary.simpleMessage(
            "设备已被本企业添加，请联系管理员确认。若有需要，可联系管理员将设备分配给您。"),
        "tr_thisDeviceHasNoConfiguredAlgorithm":
            MessageLookupByLibrary.simpleMessage("该设备暂无配置算法"),
        "tr_thisMonth": MessageLookupByLibrary.simpleMessage("本月"),
        "tr_thisPlatformName": MessageLookupByLibrary.simpleMessage("本平台名称"),
        "tr_thisRecordHasNoImage":
            MessageLookupByLibrary.simpleMessage("该条记录没有图片"),
        "tr_thisWeek": MessageLookupByLibrary.simpleMessage("本周"),
        "tr_thisYear": MessageLookupByLibrary.simpleMessage("本年"),
        "tr_this_month": MessageLookupByLibrary.simpleMessage("本月"),
        "tr_this_week": MessageLookupByLibrary.simpleMessage("本周"),
        "tr_this_year": MessageLookupByLibrary.simpleMessage("本年"),
        "tr_threePeople": MessageLookupByLibrary.simpleMessage("三人"),
        "tr_threePersonGroup":
            MessageLookupByLibrary.simpleMessage("三人进店（顾客组）"),
        "tr_thursday": MessageLookupByLibrary.simpleMessage("周四"),
        "tr_time": MessageLookupByLibrary.simpleMessage("时间"),
        "tr_timeFilter": MessageLookupByLibrary.simpleMessage("时间筛选"),
        "tr_timeOverdueTimes": MessageLookupByLibrary.simpleMessage("时间逾期次数"),
        "tr_timePeriod": MessageLookupByLibrary.simpleMessage("时间段"),
        "tr_timePoint": MessageLookupByLibrary.simpleMessage("时间点"),
        "tr_time_c": MessageLookupByLibrary.simpleMessage("次"),
        "tr_timesNoWarning": MessageLookupByLibrary.simpleMessage("次无警告"),
        "tr_titleAIAlgorithm": MessageLookupByLibrary.simpleMessage("AI算法"),
        "tr_titleCollect": MessageLookupByLibrary.simpleMessage("收藏"),
        "tr_titleContent": MessageLookupByLibrary.simpleMessage("标题内容"),
        "tr_titleDeviceList": MessageLookupByLibrary.simpleMessage("设备列表"),
        "tr_titleDeviceNodeResources":
            MessageLookupByLibrary.simpleMessage("设备节点资源"),
        "tr_titleEventAnalysis": MessageLookupByLibrary.simpleMessage("事件分析"),
        "tr_titleImprovements": MessageLookupByLibrary.simpleMessage("整改进展"),
        "tr_titleReset": MessageLookupByLibrary.simpleMessage("确认恢复出厂设置"),
        "tr_titleRestart": MessageLookupByLibrary.simpleMessage("确认重启设备"),
        "tr_titleSelectStore": MessageLookupByLibrary.simpleMessage("选择门店"),
        "tr_titleTime": MessageLookupByLibrary.simpleMessage("标题时间"),
        "tr_titleViewPassedItems":
            MessageLookupByLibrary.simpleMessage("已通过验收项"),
        "tr_titleWorkbenchApplication":
            MessageLookupByLibrary.simpleMessage("功能"),
        "tr_to": MessageLookupByLibrary.simpleMessage("至"),
        "tr_today": MessageLookupByLibrary.simpleMessage("今天"),
        "tr_todayTraffic": MessageLookupByLibrary.simpleMessage("今日客流"),
        "tr_tokenParsingFailed":
            MessageLookupByLibrary.simpleMessage("token解析失败"),
        "tr_totalAlarmCount": MessageLookupByLibrary.simpleMessage("总告警次数"),
        "tr_totalCallCount": MessageLookupByLibrary.simpleMessage("总调用次数"),
        "tr_totalCount": MessageLookupByLibrary.simpleMessage("总次数"),
        "tr_totalNameWay": m53,
        "tr_totalNumberFailures":
            MessageLookupByLibrary.simpleMessage("不合格总次数"),
        "tr_totalPeople": MessageLookupByLibrary.simpleMessage("总人数"),
        "tr_totalScore": MessageLookupByLibrary.simpleMessage("总分"),
        "tr_totalStayDuration": MessageLookupByLibrary.simpleMessage("总停留时长"),
        "tr_totalStayDurationAboveHours": m54,
        "tr_totalStoreEntryRateProportion":
            MessageLookupByLibrary.simpleMessage("总进店率占比"),
        "tr_totalTraffic": MessageLookupByLibrary.simpleMessage("总客流"),
        "tr_totalTrafficNoDeduplication":
            MessageLookupByLibrary.simpleMessage("总客流(未去重)"),
        "tr_tr_btConnectNetworkConfiguration":
            MessageLookupByLibrary.simpleMessage("连接过程大概需要1-2分钟，请您稍等片刻。"),
        "tr_tr_btConnectNetworkConfigurationTip":
            MessageLookupByLibrary.simpleMessage("路由器、手机和设备尽量靠近..."),
        "tr_trackingTrajectory": MessageLookupByLibrary.simpleMessage("跟踪轨迹"),
        "tr_trafficExcess": MessageLookupByLibrary.simpleMessage("流量已超额"),
        "tr_trafficHeatZone": MessageLookupByLibrary.simpleMessage("客流热区"),
        "tr_trafficHeatZoneAlgorithmNotEnabledContactB2b":
            MessageLookupByLibrary.simpleMessage(
                "暂未开通客流热区算法，无法进行配置，请联系**************开通"),
        "tr_trafficTrend": MessageLookupByLibrary.simpleMessage("客流趋势"),
        "tr_transcodingInProgress": MessageLookupByLibrary.simpleMessage("转码中"),
        "tr_transcodingTemplate": MessageLookupByLibrary.simpleMessage("转码模版"),
        "tr_transparentAudioTemplate":
            MessageLookupByLibrary.simpleMessage("透传去音频模板"),
        "tr_transparentTransmission":
            MessageLookupByLibrary.simpleMessage("透传"),
        "tr_triangle": MessageLookupByLibrary.simpleMessage("三角形"),
        "tr_tryCaptureAction": MessageLookupByLibrary.simpleMessage("重新抓拍"),
        "tr_tuesday": MessageLookupByLibrary.simpleMessage("周二"),
        "tr_turnOff": MessageLookupByLibrary.simpleMessage("关闭"),
        "tr_turnOn": MessageLookupByLibrary.simpleMessage("开启"),
        "tr_twiceAddDevice": MessageLookupByLibrary.simpleMessage("继续添加"),
        "tr_twoPeople": MessageLookupByLibrary.simpleMessage("双人"),
        "tr_type": MessageLookupByLibrary.simpleMessage("类型"),
        "tr_typeNormal": MessageLookupByLibrary.simpleMessage("标准"),
        "tr_typeSatellite": MessageLookupByLibrary.simpleMessage("卫星"),
        "tr_unableToGetImage": MessageLookupByLibrary.simpleMessage("无法获取图片"),
        "tr_uncompleted": MessageLookupByLibrary.simpleMessage("未完成"),
        "tr_underAcceptance": MessageLookupByLibrary.simpleMessage("验收中"),
        "tr_underRectification": MessageLookupByLibrary.simpleMessage("整改中"),
        "tr_unitTimes": MessageLookupByLibrary.simpleMessage("单位：次"),
        "tr_unnormal": MessageLookupByLibrary.simpleMessage("异常"),
        "tr_unqualified": MessageLookupByLibrary.simpleMessage("不合格"),
        "tr_unqualifiedCameraRanking":
            MessageLookupByLibrary.simpleMessage("不合格点位排名"),
        "tr_unqualifiedItem": MessageLookupByLibrary.simpleMessage("不合格项"),
        "tr_upcomingTasks": MessageLookupByLibrary.simpleMessage("待办任务"),
        "tr_updateRedisFailed":
            MessageLookupByLibrary.simpleMessage("更新到redis失败"),
        "tr_updateTime": MessageLookupByLibrary.simpleMessage("更新时间"),
        "tr_upgradeFirmwareTip":
            MessageLookupByLibrary.simpleMessage("此设备有新的设备固件可升级，是否升级？"),
        "tr_upgradePackage": MessageLookupByLibrary.simpleMessage("升级套餐"),
        "tr_uploadFaceImage": MessageLookupByLibrary.simpleMessage("上传人脸"),
        "tr_uploadFaceImageBtn":
            MessageLookupByLibrary.simpleMessage("点击拍摄/上传"),
        "tr_uploadStoreFloorPlan":
            MessageLookupByLibrary.simpleMessage("请上传门店平面图"),
        "tr_uploadUpgradeFail":
            MessageLookupByLibrary.simpleMessage("固件上传升级失败"),
        "tr_uploadingImagePleaseWait":
            MessageLookupByLibrary.simpleMessage("正在上传图片，请稍候"),
        "tr_urlAuthenticationFailed":
            MessageLookupByLibrary.simpleMessage("URL鉴权失败"),
        "tr_urlConcurrencyLimited":
            MessageLookupByLibrary.simpleMessage("URL并发受限"),
        "tr_urlExceedsAllowedConcurrency":
            MessageLookupByLibrary.simpleMessage("URL超过允许并发数"),
        "tr_urlExpired": MessageLookupByLibrary.simpleMessage("URL过期"),
        "tr_urlFormatError": MessageLookupByLibrary.simpleMessage("URL格式错误"),
        "tr_urlNotAllowedToPlay":
            MessageLookupByLibrary.simpleMessage("URL不被允许播放"),
        "tr_urlVerificationFailedGwmCommunicationFailed":
            MessageLookupByLibrary.simpleMessage("向gwm校验url失败，通信失败"),
        "tr_usageRatio": MessageLookupByLibrary.simpleMessage("使用占比"),
        "tr_useCase": MessageLookupByLibrary.simpleMessage("使用情况"),
        "tr_useTemplate": MessageLookupByLibrary.simpleMessage("使用模板"),
        "tr_usedAlgorithms": MessageLookupByLibrary.simpleMessage("已用算法"),
        "tr_usedNameWay": m55,
        "tr_usedNumChannels": MessageLookupByLibrary.simpleMessage("已使用通道数"),
        "tr_userForeverPeriod": MessageLookupByLibrary.simpleMessage("永久有效"),
        "tr_userHasBeenDeleted": MessageLookupByLibrary.simpleMessage("用户已删除"),
        "tr_userHasPeriod": MessageLookupByLibrary.simpleMessage("用户有效期"),
        "tr_userNoQueryPermission":
            MessageLookupByLibrary.simpleMessage("此用户无查询权限"),
        "tr_userNotBoundToFacebook":
            MessageLookupByLibrary.simpleMessage("用户未绑定facebook"),
        "tr_userNotBoundToGoogle":
            MessageLookupByLibrary.simpleMessage("用户未绑定google"),
        "tr_userPermissionConfig":
            MessageLookupByLibrary.simpleMessage("用户权限配置"),
        "tr_userType": MessageLookupByLibrary.simpleMessage("用户类型"),
        "tr_userTypeNotPlatformTip":
            MessageLookupByLibrary.simpleMessage("非平台用户(仅用于人脸库管理)"),
        "tr_userTypePlatformTip":
            MessageLookupByLibrary.simpleMessage("平台用户(仅用于人脸库管理)"),
        "tr_validityPeriod": MessageLookupByLibrary.simpleMessage("有效期"),
        "tr_variableBitRate": MessageLookupByLibrary.simpleMessage("可变码流"),
        "tr_verification": MessageLookupByLibrary.simpleMessage("去校验"),
        "tr_verificationActivation":
            MessageLookupByLibrary.simpleMessage("验证码激活"),
        "tr_verificationFail": MessageLookupByLibrary.simpleMessage("校验失败"),
        "tr_verificationSuccess": MessageLookupByLibrary.simpleMessage("校验成功"),
        "tr_verifyCodeLogin": MessageLookupByLibrary.simpleMessage("验证码登录"),
        "tr_versionUpdateCancel": MessageLookupByLibrary.simpleMessage("暂不升级"),
        "tr_versionUpdateConfirm": MessageLookupByLibrary.simpleMessage("立即升级"),
        "tr_versionUpdateContent": MessageLookupByLibrary.simpleMessage("更新内容"),
        "tr_versionUpdateFindLatestVersion":
            MessageLookupByLibrary.simpleMessage("发现新版本"),
        "tr_versionUpdateLatestDownload":
            MessageLookupByLibrary.simpleMessage("更新版本"),
        "tr_versionUpdateVersionLatestTip":
            MessageLookupByLibrary.simpleMessage("已经是最新版本"),
        "tr_verticalFlip": MessageLookupByLibrary.simpleMessage("上下翻转"),
        "tr_video": MessageLookupByLibrary.simpleMessage("视频"),
        "tr_videoBitrateKbps":
            MessageLookupByLibrary.simpleMessage("视频码流(kbps)，码流值取值范围0~8192"),
        "tr_videoBlocking": MessageLookupByLibrary.simpleMessage("视频遮挡"),
        "tr_videoBlockingTip":
            MessageLookupByLibrary.simpleMessage("摄像头画面被物体遮挡时，触发报警"),
        "tr_videoCloudBase": MessageLookupByLibrary.simpleMessage("视频云底座"),
        "tr_videoDisplayAlarmRules":
            MessageLookupByLibrary.simpleMessage("视频中显示告警规则"),
        "tr_videoDuration": MessageLookupByLibrary.simpleMessage("视频时长"),
        "tr_videoInspection": MessageLookupByLibrary.simpleMessage("视频巡检"),
        "tr_videoOcclusionAlarm":
            MessageLookupByLibrary.simpleMessage("视频遮挡告警"),
        "tr_videoPlayAddress": MessageLookupByLibrary.simpleMessage("视频播放地址"),
        "tr_videoWatermark": MessageLookupByLibrary.simpleMessage("视频水印"),
        "tr_viewDepartmentInfoOperation":
            MessageLookupByLibrary.simpleMessage("查看部门结构及相关操作"),
        "tr_viewDownloadList": MessageLookupByLibrary.simpleMessage("查看下载列表"),
        "tr_viewEvent": MessageLookupByLibrary.simpleMessage("查看事件"),
        "tr_viewIssueDetails": MessageLookupByLibrary.simpleMessage("查看问题详情"),
        "tr_viewRecordingSchedule":
            MessageLookupByLibrary.simpleMessage("录像详情"),
        "tr_viewUserInfoOperation":
            MessageLookupByLibrary.simpleMessage("查看管理用户信息及相关操作"),
        "tr_viewUserRoleInfoOperation":
            MessageLookupByLibrary.simpleMessage("查看管理人员角色权限信息及相关操作"),
        "tr_visibleDeptTip":
            MessageLookupByLibrary.simpleMessage("以上为所属部门，所属部门默认为可见部门"),
        "tr_visibleRangeDept": MessageLookupByLibrary.simpleMessage("可见部门"),
        "tr_visitCount": MessageLookupByLibrary.simpleMessage("访问次数"),
        "tr_waitDiscoveredDevices":
            MessageLookupByLibrary.simpleMessage("扫描到可用设备才能执行添加操作"),
        "tr_waitForGwmValidationTimeout":
            MessageLookupByLibrary.simpleMessage("等待gwm的校验结果超时"),
        "tr_waitingMineAcceptance":
            MessageLookupByLibrary.simpleMessage("待我验收"),
        "tr_waitingMineRectify": MessageLookupByLibrary.simpleMessage("待我整改"),
        "tr_watchConcurrency": MessageLookupByLibrary.simpleMessage("观看并发数"),
        "tr_watchCopyTask": MessageLookupByLibrary.simpleMessage("查看抄送任务"),
        "tr_watchRectifyTask": MessageLookupByLibrary.simpleMessage("等待整改"),
        "tr_watermarkContent": MessageLookupByLibrary.simpleMessage("水印内容"),
        "tr_watermarkContentCanNotBeEmpty":
            MessageLookupByLibrary.simpleMessage("水印内容不能为空"),
        "tr_watermarkContentTip":
            MessageLookupByLibrary.simpleMessage("回显自定义的水印内容"),
        "tr_watermarkSettings": MessageLookupByLibrary.simpleMessage("水印设置"),
        "tr_watermarkSettingsTip":
            MessageLookupByLibrary.simpleMessage("开启后视频播放、下载、截图都会携带水印"),
        "tr_wednesday": MessageLookupByLibrary.simpleMessage("周三"),
        "tr_west": MessageLookupByLibrary.simpleMessage("西"),
        "tr_whenTriggerCondition":
            MessageLookupByLibrary.simpleMessage("当（触发条件）"),
        "tr_wideDynamicConfig": MessageLookupByLibrary.simpleMessage("宽动态配置"),
        "tr_workbenchApplicationsConfiguration":
            MessageLookupByLibrary.simpleMessage("配置功能"),
        "tr_workbenchExpireDaysTip": m56,
        "tr_workbenchHelloTip": MessageLookupByLibrary.simpleMessage("Hello"),
        "tr_workbenchSubTip":
            MessageLookupByLibrary.simpleMessage("欢迎使用蜂云 SaaS App"),
        "tr_year": MessageLookupByLibrary.simpleMessage("年"),
        "tr_yearOnYear": MessageLookupByLibrary.simpleMessage("同比"),
        "tr_yes": MessageLookupByLibrary.simpleMessage("是"),
        "tr_yesterday": MessageLookupByLibrary.simpleMessage("昨天"),
        "tr_youDoNotHavePermissionToHandleThisTask":
            MessageLookupByLibrary.simpleMessage("您无权限处理此任务"),
        "tr_youHaveNotYetOpenedTheCloudStorageRecordingPackageAuthorization":
            MessageLookupByLibrary.simpleMessage("您还未开通云存储录像套餐授权"),
        "tr_youth": MessageLookupByLibrary.simpleMessage("青年"),
        "tr_youthMiddleAged": MessageLookupByLibrary.simpleMessage("中青年")
      };
}
