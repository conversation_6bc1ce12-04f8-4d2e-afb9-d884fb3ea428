import 'package:bcloud/public/network/http_request.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

extension NetExtensionAlgorighm on NetQueryUtil {
  /// 所有的算法
  Future<dynamic> algorithmListAll({String? algorithmName}) async {
    String url = fullUrl_device('/algorithm/config/pageQuery');
    Map<String, dynamic> bodyParams = {
      "pageSize": 9999,
      "pageNo": 1,
      "param": {"algorithmName": algorithmName ?? ""}
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 根据设备获取算法
  Future<dynamic> algorithmListFromDevice(
      {required List<String> deviceIds}) async {
    String url = fullUrl_device('/algorithm/config/queryGatherList');
    Map<String, dynamic> bodyParams = {"deviceIdList": deviceIds};
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 根据设备获取算法-AiBox
  Future<dynamic> algorithmListAiBoxDevice({required int pageNo,
        required int pageSize,}) async {
    String url = fullUrl_device('/aibox/system/algorithm/pageQuery');
    Map<String, dynamic> bodyParams = {
      "pageNo": pageNo,
      "pageSize": pageSize,
      "sort": {},
      "param": {}
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// "D_2111491,D_2111509,D_2111533"
  /// 设备资源,格式O_id,D_id,O代表选择的是节点后面''跟节点id,D代表选择的是设备后面''跟设备id
  Future<dynamic> algorithmCheckDetectDuplicates(
      {required String deviceRange}) async {
    String url = fullUrl_gather('/patrol/scheme/addAiScheme/detectDuplicates');
    Map<String, dynamic> bodyParams = {"deviceRange": deviceRange};
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  Future<dynamic> algorithmDetectAlgorithmCount(
      {required String deviceRange, required int skipOrOverwrite}) async {
    String url =
        fullUrl_gather('/patrol/scheme/addAiScheme/detectAlgorithmCount');
    Map<String, dynamic> bodyParams = {
      "deviceRange": deviceRange,
      "skipOrOverwrite": skipOrOverwrite
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 算法列表
  /// algorithmName 和 algorithmId用来过滤
  Future<dynamic> algorithmList(
      {required int pageNo,
      required int pageSize,
      required String algorithmName,
      String? algorithmId}) async {
    String url = fullUrl_device('/algorithm/config/countPageQuery');
    Map<String, dynamic> bodyParams = {
      "pageNo": pageNo,
      "pageSize": pageSize,
      "sort": {},
      "param": {"algorithmName": algorithmName}
    };
    if (algorithmId != null) {
      bodyParams["param"]["algorithmId"] = algorithmId;
    }
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 算法对应的设备列表
  Future<dynamic> algorithmContainDeviceList(
      {required int pageNo,
      required int pageSize,
      required String algorithmId,
      required String deviceName,
      required String openSwitch,
      required String status}) async {
    String url = fullUrl_device('/ai/open/task/config/pageQuery');
    Map<String, dynamic> bodyParams = {
      "pageNo": pageNo,
      "pageSize": pageSize,
      "sort": {},
      "param": {
        "algorithmId": algorithmId,
        "deviceName": deviceName,
        "openSwitch": openSwitch,
        "status": status,
        "type": 0
      }
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// AiBox获取算法列表
  Future<dynamic> deviceAiBoxAlgorithmList(
      {required int pageNo,
        required int pageSize,
        required String deviceId,
        required String algorithmName}) async {
    String url = fullUrl_device('/aibox/task/config/pageQuery');
    Map<String, dynamic> bodyParams = {
      "pageNo": pageNo,
      "pageSize": pageSize,
      "sort": {},
      "param": {
        "deviceId": deviceId,
        "openStatus": "",
        "algorithmName": algorithmName,
        "status": "",
      }
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 获取算法详情
  Future<dynamic> algorithmDetail({required String id}) async {
    String url = fullUrl_device('/algorithm/config/detail/$id');
    return HttpRequest.get(url);
  }

  /// 获取算法详情
  Future<dynamic> algorithmDetailById({required String id}) async {
    String url = fullUrl_device('/algorithm/config/detailByAlgorithmId/$id');
    return HttpRequest.get(url);
  }

  /// 删除(算法删除设备、设备删除算法)
  Future<dynamic> algorithmDeleteItem({required String id}) async {
    String url = fullUrl_device('/ai/open/task/config/delete/$id');
    return HttpRequest.delete(url);
  }

  /// 删除(AiBox)
  Future<dynamic> algorithmAiBoxDeleteItem({required String id}) async {
    String url = fullUrl_device('/aibox/task/config/delete/$id');
    return HttpRequest.delete(url);
  }

  /// 删除(算法删除设备、设备删除算法)
  Future<dynamic> algorithmDeleteItems({required List<String> idList}) async {
    String url = fullUrl_device('/ai/open/task/config/batchDelete');
    return HttpRequest.post(url, bodyParams: {"idList": idList});
  }

  /// 算法绑定设备
  Future<dynamic> algorithmBindDevice(
      {required int algorithmId, required List<String> deviceIdList}) async {
    String url = fullUrl_device('/ai/open/task/config/algorithmBindDevice');
    Map<String, dynamic> bodyParams = {
      "deviceIdList": deviceIdList,
      "algorithmId": algorithmId
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 算法批量绑定设备
  Future<dynamic> algorithmBindDeviceConfigList(
      {required int algorithmId,
      required List<Map<String, dynamic>> deviceConfigList}) async {
    String url =
        fullUrl_device('/ai/open/task/config/batchAlgorithmBindDevice');
    Map<String, dynamic> bodyParams = {
      "dataList": deviceConfigList,
      "algorithmId": algorithmId
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 设备开启和关闭
  Future<dynamic> algorithmStateChange(
      {required String id, required bool open}) async {
    String url = fullUrl_device('/ai/open/task/config/editOpenStatus');
    Map<String, dynamic> bodyParams = {"id": id, "openSwitch": open ? 1 : 0};
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 设备开启和关闭-AiBox
  Future<dynamic> algorithmAiBoxStateChange(
      {required String id, required bool open}) async {
    String url = fullUrl_device('/aibox/task/config/edit');
    Map<String, dynamic> bodyParams = {"id": id, "openStatus": open ? 1 : 0};
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 算法绑定设备页面，详情actionSheet的数据
  Future<dynamic> algorithmDeviceDetails({required String algorithmId}) async {
    String url = fullUrl_device('/ai/open/task/config/pageQueryAlgorithm');
    Map<String, dynamic> bodyParams = {
      "pageSize": 1000,
      "pageNo": 1,
      "param": {"algorithmId": algorithmId}
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 设备对应的算法列表
  Future<dynamic> deviceContainAlgorithmList(
      {required int pageNo,
      required int pageSize,
      required String deviceId,
      required String algorithmName}) async {
    String url = fullUrl_device('/ai/open/task/config/pageQuery');
    Map<String, dynamic> bodyParams = {
      "pageNo": pageNo,
      "pageSize": pageSize,
      "sort": {},
      "param": {
        "deviceId": deviceId,
        "openSwitch": "",
        "algorithmName": algorithmName,
        "status": "",
        "type": 0
      }
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 获取设备已绑定的算法
  Future<dynamic> algorithmDeviceBind({required String deviceId}) async {
    String url =
        fullUrl_device('/ai/open/task/config/alarm/queryByDeviceId/$deviceId');
    return HttpRequest.get(url);
  }

  /// 设备绑定算法
  Future<dynamic> deviceBindAlgorithm(
      {required String deviceId, required List<String> algorithmIdList}) async {
    String url = fullUrl_device('/ai/open/task/config/deviceBindAlgorithm');
    Map<String, dynamic> bodyParams = {
      "deviceId": deviceId,
      "algorithmIdList": algorithmIdList
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 设备绑定算法-AiBox
  Future<dynamic> deviceAiBoxBindAlgorithm(
      {required String deviceId, required List algorithmParamList}) async {
    String url = fullUrl_device('/aibox/task/config/config');
    Map<String, dynamic> bodyParams = {
      "deviceId": deviceId,
      "paramBeans": algorithmParamList
    };
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 编辑算法配置
  Future<dynamic> algorithmEdit(
      {required int step,
      required int id,
      required String threshold,
      required String includeAreas,
      required String visibleRange,
      required String taskTime,
      required int durationTime,
      required int dedInterval,
      required int brainPowerAdjustSwitch,
      required int? addStep,
      required int? noAlarmAdjustCount,
      required int? adjustMaxStep,
      required int? alarmTacticsType,
      required String extra,
      required String? specialParam,
      required int? flowType,
      required int? deliveryDriversStatus,
      required String? hotspotAreaParam,
      required int? showOsdStatus}) async {
    String url = fullUrl_device('/ai/open/task/config/configEdit');
    Map<String, dynamic> bodyParams = {
      "step": step,
      "id": id,
      "threshold": threshold,
      "visibleRange": visibleRange,
      "includeAreas": includeAreas,
      "durationTime": durationTime,
      "dedInterval": dedInterval,
      "extra": extra,
      "taskTime": taskTime,
      "brainPowerAdjustSwitch": brainPowerAdjustSwitch,
      "flowType": flowType,
      "deliveryDriversStatus": deliveryDriversStatus
    };
    if (addStep != null) {
      bodyParams["addStep"] = addStep;
    }
    if (noAlarmAdjustCount != null) {
      bodyParams["noAlarmAdjustCount"] = noAlarmAdjustCount;
    }
    if (adjustMaxStep != null) {
      bodyParams["adjustMaxStep"] = adjustMaxStep;
    }
    if (alarmTacticsType != null) {
      bodyParams["alarmTacticsType"] = alarmTacticsType;
    }
    if (specialParam != null) {
      bodyParams["specialParam"] = specialParam;
    }
    if (hotspotAreaParam != null) {
      bodyParams["hotspotAreaParam"] = hotspotAreaParam;
    }
    if (showOsdStatus != null) {
      bodyParams["showOsdStatus"] = showOsdStatus;
    }
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 编辑算法配置 -AiBox
  Future<dynamic> algorithmAiBoxEdit(
      {required int step,
        required String id,
        required String deviceId,
        required String aiboxAlgorithmId,
        required String threshold,
        required int? timeInterval,
        required String? includeAreas,
        required String? excludeAreas,
        required String? visibleRange,
        required String? taskTime,
        required int status,
        required int openStatus,
        required String? extra,
        required int taskOpenStatus,
        required String? specialParam}) async {
    String url = fullUrl_device('/aibox/task/config/edit');
    Map<String, dynamic> bodyParams = {
      "step": step,
      "id": id,
      "deviceId": deviceId,
      "aiboxAlgorithmId": aiboxAlgorithmId,
      "threshold": threshold,
      "status": status,
      "openStatus": openStatus,
      "taskOpenStatus": taskOpenStatus
    };
    if (timeInterval != null) {
      bodyParams["timeInterval"] = timeInterval;
    }
    if (excludeAreas != null) {
      bodyParams["excludeAreas"] = excludeAreas;
    }
    if (includeAreas != null) {
      bodyParams["includeAreas"] = includeAreas;
    }
    if (visibleRange != null) {
      bodyParams["visibleRange"] = visibleRange;
    }
    if (taskTime != null) {
      bodyParams["taskTime"] = taskTime;
    }
    if (extra != null) {
      bodyParams["extra"] = extra;
    }
    if (specialParam != null) {
      bodyParams["specialParam"] = specialParam;
    }
    return HttpRequest.post(url, bodyParams: bodyParams);
  }

  /// 获取详情
  Future<dynamic> algorithmTaskDetail({required String id}) async {
    String url = fullUrl_device('/ai/open/task/config/detail/$id');
    return HttpRequest.get(url);
  }

  /// 获取详情--AiBox
  Future<dynamic> algorithmAiBoxTaskDetail({required String id}) async {
    String url = fullUrl_device('/aibox/task/config/detail/$id');
    return HttpRequest.get(url);
  }

  /// 判断是否有客流热区算法
  Future<bool> getHeatMapAlgorithmAuth() async {
    String url = fullUrl_device('/algorithm/config/queryByDealType');
    final ret = await HttpRequest.post(url, bodyParams: {"dealType": 10});
    if (ret != null && ret is Map && ret['openSwitch'] == 1) {
      return true;
    }
    return false;
  }
}
