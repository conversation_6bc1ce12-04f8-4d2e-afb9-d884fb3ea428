import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class TopIconBottomTitleBtn extends StatelessWidget {
  final String img;
  final String title;
  final Color? titleColor;
  final double? fontSize;
  final double? gap;
  final TextAlign? titleAlign;
  final VoidCallback? onTap;
  final double paddingH;
  final double paddingV;
  final bool needTapEffect;
  final double? imgWidth;
  final int? maxLines;
  final double? gapBottom;
  final double? gasWidth;
  final bool? isMobileMark;
  final bool? isArrowOnsiteInspection;

  const TopIconBottomTitleBtn(
      {required this.img,
      required this.title,
      this.imgWidth,
      this.titleColor,
      this.fontSize,
      this.gap,
      this.titleAlign,
      this.onTap,
      this.paddingH = 0,
      this.paddingV = 0,
      this.needTapEffect = true,
      this.maxLines,
      this.gapBottom,
      this.gasWidth,
      this.isMobileMark = false,
      this.isArrowOnsiteInspection = false,
      Key? key})
      : super(key: key);

  double get _width {
    return imgWidth != null ? imgWidth! : 18.5.w;
  }

  double get _gasWidth {
    return gasWidth ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return XBButton(
      needTapEffect: needTapEffect,
      disableColor: Colors.transparent,
      onTap: onTap,
      child: Stack(
        alignment: AlignmentDirectional.topCenter,
        children: [
          Container(
            padding: EdgeInsets.only(top: spaces.leftLess,),
            color: Colors.transparent,
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.only(
                  left: paddingH, right: paddingH, top: paddingV, bottom: paddingV),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: _width - _gasWidth,
                    height: _width,
                    child: XBImage(
                      img,
                      width: _width,
                    ),
                  ),
                  SizedBox(
                    height: gap ?? 2.w,
                  ),
                  Text(title,
                      textAlign: titleAlign,
                      maxLines: maxLines,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: fontSize ?? app.fontSizes.s10,
                          color: titleColor ?? colors.black)),
                  SizedBox(
                    height: gapBottom ?? 0.w,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            right: 9.w,
            top: 6.w,
            child: Visibility(
                visible: isMobileMark ?? false,
                child: GestureDetector(
                  onTap: onTap,
                  child: Padding(
                    padding: EdgeInsets.only(top: 4.w, bottom: 12.w, left: 12.w, right: 8.w),
                    child: Assets.images.iconMobileMark
                        .image(width: 16.w, height: 16.w),
                  ),
                )
            ),
          ),
          Positioned(
            top: 0.w,
            child: Visibility(
                visible: isArrowOnsiteInspection ?? false,
                child: Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: Assets.images.iconArrowOnsiteInspection
                      .image(width: 12, height: 12),
                ),
            ),
          ),
        ],
      ),
    );
  }
}
