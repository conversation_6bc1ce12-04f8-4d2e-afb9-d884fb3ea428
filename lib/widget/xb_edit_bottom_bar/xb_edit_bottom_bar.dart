import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/widget/top_icon_bottom_title_btn.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class XBEditBottomBar extends XBWidget<EditOperaBarVM> {
  final VoidCallback onSelectedAll;
  final VoidCallback onDelete;
  final VoidCallback? onShare;
  final VoidCallback? onDownload;
  final bool isSelectedAll;
  final bool isNeedShare;
  final bool isCanShare;
  final bool isNeedDownload;
  final bool isCanDownload;
  const XBEditBottomBar(
      {required this.onSelectedAll,
      required this.onDelete,
      this.onShare,
      this.onDownload,
      required this.isSelectedAll,
      this.isNeedShare = false,
      this.isCanShare = false,
      this.isNeedDownload = false,
      this.isCanDownload = false,
      super.key});

  @override
  generateVM(BuildContext context) {
    return EditOperaBarVM(context: context);
  }

  @override
  Widget buildWidget(EditOperaBarVM vm, BuildContext context) {
    return XBShadowContainer(
      child: Container(
        color: colors.white,
        margin: EdgeInsets.only(bottom: safeAreaBottom),
        child: SizedBox(
          height: 56,
          child: Row(
            children: [
              Expanded(
                  child: XBButton(
                    preventMultiTapMilliseconds: 0,
                    onTap: onSelectedAll,
                    child: TopIconBottomTitleBtn(
                      img: images.ic_common_select_all,
                      imgWidth: 27,
                      title: isSelectedAll
                          ? TR.current.tr_cancelAllSelections
                          : TR.current.tr_Common_SelectAll,
                      titleColor: colors.blue,
                    ),
                  )),
              Visibility(
                visible: isNeedShare,
                child: Expanded(
                    child: XBButton(
                      onTap: () {
                        onShare?.call();
                      },
                      child: TopIconBottomTitleBtn(
                        img: isCanShare
                            ? images.ic_common_select_file_share
                            : images.ic_common_unchecked_file_share,
                        imgWidth: 27,
                        title: TR.current.tr_Share,
                        titleColor: isCanShare ? colors.blue : colors.black4C,
                      ),
                    )),
              ),
              Visibility(
                visible: isNeedDownload,
                child: Expanded(
                    child: XBButton(
                      onTap: () {
                        onDownload?.call();
                      },
                      child: TopIconBottomTitleBtn(
                        img: isCanDownload
                            ? images.icon_download_blue
                            : images.icon_download_grey,
                        imgWidth: 27,
                        title: TR.current.tr_CommonSave,
                        titleColor: isCanDownload ? colors.blue : colors.black4C,
                      ),
                    )),
              ),
              Expanded(
                  child: XBButton(
                    onTap: onDelete,
                    child: TopIconBottomTitleBtn(
                      img: images.ic_common_delete,
                      imgWidth: 27,
                      title: TR.current.tr_Common_Delete,
                      titleColor: colors.blue,
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}

class EditOperaBarVM extends XBVM<XBEditBottomBar> {
  EditOperaBarVM({required super.context});
}
