import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/widget/x_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class XBottomAlertAction {
  final String name;
  TextStyle? textStyle;
  Function()? onTap;

  XBottomAlertAction({required this.name, this.textStyle, this.onTap});
}

class XBottomAlert extends StatefulWidget {
  static show(
      {required BuildContext context,
      required List<XBottomAlertAction> actionList,
      String? title,
      String? cancelTitle}) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return XBottomAlert(
            actionList: actionList,
          );
        });
  }

  String? title;
  String? cancelTitle;
  final List<XBottomAlertAction> actionList;

  XBottomAlert(
      {Key? key, required this.actionList, this.title, this.cancelTitle})
      : super(key: key);

  @override
  State<XBottomAlert> createState() => _XBottomAlertState();
}

class _XBottomAlertState extends State<XBottomAlert> {
  _widgets(BuildContext context) {
    List<Widget> _widgetList = [];

    if (widget.title != null) {
      TextStyle titleStyle = TextStyle(
          fontSize: fontSizes.s18,
          fontWeight: fontWeights.semiBold,
          color: colors.black);
      _widgetList.add(SizedBox(
        height: 50,
        child: Column(
          children: [
            Expanded(
                child: Center(
              child: Text(
                widget.title!,
                style: titleStyle,
              ),
            )),
            const XDivider(),
          ],
        ),
      ));
    }

    for (XBottomAlertAction model in widget.actionList) {
      TextStyle actionStyle = model.textStyle ??
          TextStyle(
              fontSize: fontSizes.s18,
              fontWeight: fontWeights.semiBold,
              color: kColorHexStr('#4C4C4C'));
      _widgetList.add(XBButton(
        onTap: () {
          Navigator.of(context).pop();
          if (model.onTap != null) {
            model.onTap!();
          }
        },
        child: Container(
          color: Colors.transparent,
          height: 55,
          child: Column(
            children: [
              Expanded(
                  child: Center(
                child: Text(
                  model.name,
                  style: actionStyle,
                ),
              )),
              XDivider(
                color: kColorHexStr('#E7E7E7'),
              )
            ],
          ),
        ),
      ));
    }

    _widgetList.add(Container(
      height: 5,
      color: kColorHexStr('#E7E7E7'),
    ));

    TextStyle cancelTitleStyle = TextStyle(
        fontSize: fontSizes.s18,
        fontWeight: fontWeights.semiBold,
        color: kColorHexStr('#4C4C4C'));
    _widgetList.add(XBButton(
      onTap: () {
        Navigator.of(context).pop();
      },
      child: Container(
        color: Colors.transparent,
        height: (50 + safeAreaBottom).toDouble(),
        child: Column(
          children: [
            Expanded(
                child: Center(
              child: Text(
                widget.cancelTitle ?? TR.current.tr_Cancel,
                style: cancelTitleStyle,
              ),
            )),
            SizedBox(
              height: safeAreaBottom,
            )
          ],
        ),
      ),
    ));
    return _widgetList;
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topRight: Radius.circular(9.w),
        topLeft: Radius.circular(9.w),
      ),
      child: Container(
        color: Colors.white,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _widgets(context),
          ),
        ),
      ),
    );
  }
}
