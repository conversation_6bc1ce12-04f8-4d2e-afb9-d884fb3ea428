import 'dart:io';

import 'package:bcloud/api/bc_api.dart';
import 'package:bcloud/api/workbench_api.dart';
import 'package:bcloud/config/app_plugin.dart';
import 'package:bcloud/config/events.dart';
import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/home/<USER>/customization_channel_info_model.dart';
import 'package:bcloud/pages/login/model/account_info.dart';
import 'package:bcloud/public/network/net_extension_echo.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/utils/privacy_policy_util.dart';
import 'package:bcloud/utils/sp_utils.dart';
import 'package:bcloud/utils/x_common_bottom_webview.dart';
import 'package:bcloud/widget/x_dialog.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'opera_log_util/opera_log_util.dart';

class PrivacyPolicyUpdateTask {
  static final PrivacyPolicyUpdateTask _singletonPattern =
      PrivacyPolicyUpdateTask._internal();

  ///工厂构造函数
  factory PrivacyPolicyUpdateTask() {
    return _singletonPattern;
  }

  ///单例模式-方法
  static PrivacyPolicyUpdateTask getInstance() {
    return _singletonPattern;
  }

  ///构造函数私有化，防止被误创建
  PrivacyPolicyUpdateTask._internal();

  queryUpdateTask({bool isShowUpdate = true}) {
    workbenchAPI.getCustomizationChannelInfo().then((value) async {
      if (value != null) {
        CustomizationChannelInfoModel model =
            CustomizationChannelInfoModel.fromJson(value);
        String privacyPolicyUpdateUrl =
            PrivacyPolicyUtil.privacyPolicyUpdateUrl;
        String userAgreementUpdateUrl =
            PrivacyPolicyUtil.userAgreementUpdateUrl;
        if (model.appUserAgreement?.isNotEmpty ?? false) {
          bool isHasUpdate = userAgreementUpdateUrl != model.appUserAgreement!;
          if (isHasUpdate) {
            userAgreementUpdateUrl = model.appUserAgreement!;
            PrivacyPolicyUtil.userAgreementUpdateUrl = model.appUserAgreement!;
          }
        }
        if (model.appLogOffAgreement?.isNotEmpty ?? false) {
          String logOffAgreementUpdateUrl =
              PrivacyPolicyUtil.logOffAgreementUpdateUrl;
          bool isHasUpdate =
              logOffAgreementUpdateUrl != model.appLogOffAgreement!;
          if (isHasUpdate) {
            PrivacyPolicyUtil.logOffAgreementUpdateUrl =
                model.appLogOffAgreement!;
          }
        }
        NetQueryUtil.getInstance().echo(
            content:
                "privacyPolicyUpdateUrl:$privacyPolicyUpdateUrl,model.appPrivacyAgreement!:${model.appPrivacyAgreement!},privacyPolicyUpdateUrl != model.appPrivacyAgreement!:${privacyPolicyUpdateUrl != model.appPrivacyAgreement! ? "1" : 0}");
        if (model.appPrivacyAgreement?.isNotEmpty ?? false) {
          bool isHasUpdate =
              privacyPolicyUpdateUrl != model.appPrivacyAgreement!;
          if (isHasUpdate) {
            privacyPolicyUpdateUrl = model.appPrivacyAgreement!;
            PrivacyPolicyUtil.privacyPolicyUpdateUrl =
                model.appPrivacyAgreement!;
          }
          if (isShowUpdate && isHasUpdate) {
            await showUpdateDialog(
                privacyPolicyUpdateUrl, userAgreementUpdateUrl);
          }
        }
      }
    }).catchError((error) {});
  }

  Future<void> showUpdateDialog(
      String privacyPolicyUrl, String userAgreementUpdateUrl) async {
    await showDialog(
      barrierDismissible: false,
      context: xbGlobalContext,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () => Future.value(false),
          child: Dialog(
              insetPadding: const EdgeInsets.symmetric(
                horizontal: 31.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    gradient: const LinearGradient(
                      colors: [Color(0xFFD3E2FB), Color(0xFFFFFFFF)],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    )),
                width: screenW - 63,
                height: 324.w,
                // constraints: BoxConstraints(
                //     maxHeight: MediaQuery.of(context).size.height * 0.42),
                child: Padding(
                  padding:
                      const EdgeInsets.only(top: 24.5, left: 20, right: 20),
                  child: Column(
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(bottom: spaces.leftLess),
                        child: Text(
                          TR.current.tr_privacyPolicyUpdate,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: colors.black,
                              fontSize: fontSizes.s18,
                              fontWeight: fontWeights.semiBold),
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                            child: Padding(
                          padding: const EdgeInsets.only(left: 5, right: 5),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                TR.current.tr_privacyPolicyUpdateTip,
                                textAlign: TextAlign.start,
                                style: TextStyle(
                                    color: colors.black4C,
                                    fontSize: fontSizes.s14,
                                    fontWeight: fontWeights.normal),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    top: spaces.leftLess, left: 7, right: 7),
                                child: RichText(
                                  text: TextSpan(
                                      text: TR.current.tr_privacyPolicyFrontTip,
                                      style: TextStyle(
                                          fontSize: fontSizes.s12,
                                          color: colors.grey808080),
                                      children: [
                                        TextSpan(
                                            text:
                                                '《${TR.current.tr_UserAgreement}》',
                                            style: TextStyle(
                                                fontSize: fontSizes.s12,
                                                color: colors.blue),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () async {
                                                showBottomWebView(
                                                    context,
                                                    userAgreementUpdateUrl,
                                                    TR.current
                                                        .tr_UserAgreement);
                                              }),
                                        TextSpan(
                                          text: TR.current.tr_And,
                                          style: TextStyle(
                                              fontSize: fontSizes.s12,
                                              color: colors.grey808080),
                                        ),
                                        TextSpan(
                                            text:
                                                '《${TR.current.tr_PrivacyPolicy}》',
                                            style: TextStyle(
                                                fontSize: fontSizes.s12,
                                                color: colors.blue),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () async {
                                                showBottomWebView(
                                                    context,
                                                    privacyPolicyUrl,
                                                    TR.current
                                                        .tr_PrivacyPolicy);
                                              }),
                                      ]),
                                ),
                              ),
                            ],
                          ),
                        )),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 18.5, bottom: 24, left: 2.5, right: 2.5),
                        child: SizedBox(
                          height: 43.w,
                          // width: double.infinity,
                          child: Row(
                            children: [
                              Expanded(child: _chooseExitButton(context, true)),
                              SizedBox(
                                width: 11.w,
                              ),
                              Expanded(
                                  child: _chooseExitButton(context, false)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        );
      },
    );
  }

  Widget _chooseExitButton(BuildContext context, bool isExit) {
    return XBButton(
      onTap: () {
        if (isExit) {
          showLoadingGlobal();
          BCApi.bcAccount.loginOut().then((response) async {
            AccountInfo.instance.updateLoginStatus(false);
            AccountInfo.instance.unsubscribeAlarm();
            if (Platform.isAndroid) {
              FlutterAppPlugin.removePolicy();
            }
            SPUtils.removeKey();
            eventBus.fire(EventRefreshHomePage());
            hideLoadingGlobal();
            Navigator.of(context).pop(DialogAction.cancel);
            popToRoot();
          }).catchError((error) {
            hideLoadingGlobal();
          });
        } else {
          OperaLogUtil.permissionManage.updatePrivacyPolicy();
          Navigator.of(context).pop(DialogAction.confirm);
        }
      },
      child: Container(
        height: 43.w,
        decoration: BoxDecoration(
            border: isExit
                ? Border.all(
                    color: kColorHexStr('#C6C9CF'), // 边框颜色
                    width: 0.5, // 边框宽度
                  )
                : null,
            borderRadius: BorderRadius.circular(21.5.r),
            color: isExit ? colors.white : colors.blue),
        child: Center(
            child: Text(
          isExit ? TR.current.tr_Reject : TR.current.tr_agree,
          style: TextStyle(
              fontSize: fontSizes.s14,
              color: isExit ? colors.black4C : colors.white,
              fontWeight: fontWeights.medium),
        )),
      ),
    );
  }
}
