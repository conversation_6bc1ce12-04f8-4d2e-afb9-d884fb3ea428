import 'package:bcloud/utils/xb_app_mark_util/xb_app_mark_util.dart';

/// 隐私协议
String get XUrl_YinSiXieYi {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://dl.jftech.pro/Zvot/Zvot_APP_Privacy_Agreement_zh.html';
  }
  return 'https://dl.jftech.pro/app/bcloud_privacy_policy_zh.html';
}

String get XUrl_YinSiXieYi_en {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://dl.jftech.pro/Zvot/Zvot_APP_Privacy_Agreement_en.html';
  }
  return 'https://dl.jftech.pro/app/bcloud_privacy_policy_en.html';
}

/// 用户协议
String get XUrl_YonghuXieYi {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://dl.jftech.pro/Zvot/Zvot_User_Agreement_zh.html';
  }
  return 'https://support-help.xmcsrv.com/developers/textInformationConfig/25.html';
}

String get XUrl_YonghuXieYi_en {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://dl.jftech.pro/Zvot/Zvot_User_Agreement_en.html';
  }
  return 'https://dl.jftech.pro/********-en.html';
}

/// 注销协议
String get XUrl_ZhuXiaoXieYi {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://dl.jftech.pro/Zvot/Zvot_Account_Cancellation_Agreement_zh.html';
  }
  return 'https://support-help.xmcsrv.com/developers/textInformationConfig/26.html';
}

String get XUrl_ZhuXiaoXieYi_en {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://dl.jftech.pro/Zvot/Zvot_Account_Cancellation_Agreement_en.html';
  }
  return 'https://dl.jftech.pro/********.html';
}

///杭州杰峰科技官网
String get XUrl_HangzhouJF0fficial_Website {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://zerovot.com/';
  }
  return 'https://www.jftech.com/#/home';
}

String get XUrl_HangzhouJF0fficial_Website_en {
  if (XBAppMarkUtil.isZvot()) {
    return 'https://zerovot.com/';
  }
  return 'https://en.jftech.com/#/home';
}

///国标设备接入操作说明
String accessingInstructionsUrl = "https://dl.jftech.pro/inform/%E5%9B%BD%E6%A0%87%E8%AE%BE%E5%A4%87%E6%8E%A5%E5%85%A5%E6%93%8D%E4%BD%9C%E8%AF%B4%E6%98%8E.pdf";
