import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/arithmetics/arithmetic_config.dart';
import 'package:bcloud/pages/arithmetics/model/visible_range_bean.dart';
import 'package:bcloud/pages/arithmetics/util/arithmetic_rate_auto_dispatch_datasource.dart';
import 'package:bcloud/public/network/net_extension_algorithm.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/utils/xb_video_stream_manager/xb_live_stream_manager.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'model/algorithm_task_detail.dart';
import 'model/arithmetic_config_store_model.dart';

class ArithmeticConfigVM extends XBPageVM<ArithmeticConfig> {
  List<VisibleRangeBean>? visibleRangeBeans;

  late ArithmeticConfigStoreModel configStoreModel;

  ArithmeticConfigVM({required super.context}) {
    configStoreModel = ArithmeticConfigStoreModel(
        step: widget.step,
        durationTime: widget.durationTime,
        dedInterval: widget.dedInterval,
        threshold: widget.threshold,
        includeAreas: widget.includeAreas,
        isNeedArrow: widget.isNeedArrow,
        visibleRange: widget.visibleRange,
        executeTime: widget.executeTime,
        extra: "",
        autoDispatchDatasource: ArithmeticRateAutoDispatchDatasource(
          isOn: widget.brainPowerAdjustSwitch == 1,
          noAlarmAdjustCount: widget.noAlarmAdjustCount,
          addStep: widget.addStep,
          adjustMaxStep: widget.adjustMaxStep,
          adjustMaxStepMax: widget.adjustMaxStepMax,
          alarmTacticsType: widget.alarmTacticsType,
        ),
        use8192: widget.use8192,
        specialParam: widget.specialParam,
        flowType: widget.flowType,
        deliveryDriversStatus: widget.deliveryDriversStatus,
        showOsdStatus: widget.showOsdStatus);
    _queryStream();
    if (widget.isAiBoxConfig) {
      _queryAiBoxDetail();
    } else {
      _queryDetail();
    }
  }

  AlgorithmTaskDetail? _detail;

  /// 获取详情，详情中有要不要显示关联人脸字段
  _queryDetail() {
    NetQueryUtil.getInstance().algorithmTaskDetail(id: widget.id).then((value) {
      _detail = AlgorithmTaskDetail.fromJson(value);
      configStoreModel.durationTime = _detail?.durationTime ?? 0;
      configStoreModel.dedInterval = _detail?.dedInterval ?? 0;
      configStoreModel.showOsdStatus = _detail?.showOsdStatus ?? 0;
      visibleRangeBeans = _detail?.visibleRangeBeans ?? [];
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  /// 获取详情，详情中有要不要显示关联人脸字段
  _queryAiBoxDetail() {
    NetQueryUtil.getInstance().algorithmAiBoxTaskDetail(id: widget.id).then((value) {
      // _detail = AlgorithmTaskDetail.fromJson(value);
      // configStoreModel.durationTime = _detail?.durationTime ?? 0;
      // configStoreModel.dedInterval = _detail?.dedInterval ?? 0;
      // configStoreModel.showOsdStatus = _detail?.showOsdStatus ?? 0;
      // visibleRangeBeans = _detail?.visibleRangeBeans ?? [];
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  bool get isNeedAssociateFace {
    if (_detail == null) return false;
    // return _detail!.bindStatus == 1;
    return _detail!.isShowFace;
  }

  /// 获取视频流
  _queryStream() async {
    if (widget.deviceId == null || widget.deviceId!.isEmpty) return;
    final id = widget.deviceId!;
    XBLiveStreamManager().getVideoStreamForDeviceIds(
        ids: [id],
        onDone: (value) {
          videoUrl = value[id]?.rtspPriUrl;
          videoDisableTip = value[id]?.notice;
          notify();
        });
  }

  String? videoUrl;

  String? videoDisableTip;

  save() {
    if (widget.isShowStep &&
        widget.isShowDuration &&
        configStoreModel.step > configStoreModel.durationTime) {
      toastFailure(TR.current.tr_durationCannotBeLessThanDetectionFrequency);
      return;
    }

    if (isNeedAssociateFace && configStoreModel.visibleRange.isEmpty) {
      toastFailure(TR.current.tr_pleaseSelectDepartmentToAssociateFaces);
      return;
    }

    widget.onSave(configStoreModel);
    pop();
    if (canEditArea == false) {
      toast(TR.current.tr_canNotEditAreaTip);
    }
  }

  bool _isTaping = false;
  bool get isTaping => _isTaping;

  onTaping(value) {
    _isTaping = value;
    notify();
  }

  onChoose() {}

  onPrevious() {}

  onNext() {}

  onStepChanged(value) {
    configStoreModel.step = value;
    notify();
  }

  onDurationTimeChanged(value) {
    configStoreModel.durationTime = value;
    notify();
  }

  onDedIntervalChanged(value) {
    configStoreModel.dedInterval = value;
    notify();
  }

  onThresholdChanged(value) {
    configStoreModel.threshold = value;
    notify();
  }

  onIncludeAreaChanged(value) {
    configStoreModel.includeAreas = value;
    notify();
  }

  onHotspotAreaParamChanged(value) {
    configStoreModel.hotspotAreaParam = value;
    notify();
  }

  onVisibleRangeChanged(value) {
    configStoreModel.visibleRange = value;
    notify();
  }

  onExtraChanged(value) {
    configStoreModel.extra = value;
    notify();
  }

  bool _canEditArea = true;
  bool get canEditArea => _canEditArea;
  onCanEditArea(value) {
    _canEditArea = value;
    notify();
  }

  onExecuteTimeChanged(List<String> list) {
    configStoreModel.executeTime = list;
    notify();
  }

  // late ArithmeticRateAutoDispatchDatasource _autoDispatchDatasource;
  onAutoDispatchDataChanged(ArithmeticRateAutoDispatchDatasource data) {
    configStoreModel.autoDispatchDatasource = data;
    notify();
  }

  onShowOsdStatusChanged(int value) {
    configStoreModel.showOsdStatus = value;
    notify();
  }

  onSpecialParamChanged(String value) {
    configStoreModel.specialParam = value;
    notify();
  }

  onFlowTypeChanged(int value) {
    configStoreModel.flowType = value;
    notify();
  }

  onDeliveryDriversStatusChanged(int value) {
    configStoreModel.deliveryDriversStatus = value;
    notify();
  }
}
