import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/choose_arithmetic.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/choose_arithmetic_vm_add_ai_box.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/model/choose_arithmetic_model.dart';
import 'package:bcloud/public/network/net_extension_algorithm.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_vm_refresh.dart';
import 'package:bcloud/utils/opera_log_util/opera_log_util.dart';
import 'package:bcloud/utils/print_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'arithmetic_config.dart';
import 'arithmetics_ai_box_arithmetics_list_page.dart';
import 'model/algorithm_task_detail.dart';
import 'model/arithmetics_ai_box_arithmetics_list_response.dart';

class ArithmeticsAiBoxArithmeticsListVM
    extends XBVMRefresh<ArithmeticsAiBoxArithmeticsListPage> {
  ArithmeticsAiBoxArithmeticsListVM({required super.context});
  final Map<AlgorithmAiBoxTaskDetail, BuildContext> cxtMap = {};

  String _algorithmName = "";
  onSearch(keyWord) {
    if (_algorithmName == keyWord) return;
    _algorithmName = keyWord;
    refreshController.refresh();
  }

  @override
  int get dataLen => _datas.length;

  final List<AlgorithmAiBoxTaskDetail> _datas = [];

  String title(int index) {
    return _datas[index].algorithmName ?? "";
  }

  String subTitle(int index) {
    return '';
    // final maxCount = _datas[index].maxCount ?? 0;
    // final usedCount = _datas[index].useCount ?? 0;
    // return "${TR.current.tr_totalNameWay(maxCount)}，${TR.current.tr_usedNameWay(usedCount)}";
  }

  AlgorithmAiBoxTaskDetail? indexItemEntity(int index) {
    if (_datas.isNotEmpty) {
      return _datas[index];
    }
    return null;
  }

  bool isOn(int index) => _datas[index].openStatus == 1;

  bool isNormal(int index) => _datas[index].status == 1;

  List<ChooseArithmeticModel> models = [];

  @override
  queryRefresh() {
    NetQueryUtil.getInstance()
        .deviceAiBoxAlgorithmList(
            pageNo: pageNo,
            pageSize: pageSize,
            deviceId: widget.deviceId,
            algorithmName: _algorithmName)
        .then((value) {
      if (pageNo == 1) {
        cxtMap.clear();
        _datas.clear();
      }
      final response = ArithmeticsAiBoxArithmeticsListResponse.fromJson(value);
      if (response.datas?.isNotEmpty ?? false) {
        _datas.replaceOrAddAll(
            list: response.datas!,
            equal: (obj1, obj2) => obj1.id == obj2.id);
        models = _datas.map((e) {
          ChooseArithmeticModel tempModel = ChooseArithmeticModel();
          tempModel.algorithmId = e.aiboxAlgorithmId;
          // tempModel.appDefaultImage = e.appDefaultImage;
          return tempModel;
        }).toList();
      }
      loadSuccessState((response.datas ?? []).isNotEmpty);
      notify();
    }).catchError((e) {
      loadFailureState();
      toastFailure(kErrorMsg(e));
    });
  }

  onChangedState(int index) {
    showLoading();
    final oldIsOpen = _datas[index].openStatus == 1;
    NetQueryUtil.getInstance()
        .algorithmAiBoxStateChange(id: _datas[index].id!, open: !oldIsOpen)
        .then((value) {
      hideLoading();
      _datas[index].openStatus = !oldIsOpen ? 1 : 0;
      notify();
    }).catchError((e) {
      hideLoading();
      toastFailure(kErrorMsg(e));
    });
  }

  onTap(int index) {
    AlgorithmAiBoxTaskDetail model = _datas[index];
    AlgorithmTaskDetail data = AlgorithmTaskDetail(
        id: model.id,
        deviceId: model.deviceId,
        algorithmId: model.aiboxAlgorithmId,
        algorithmName: model.algorithmName,
        openSwitch: model.openStatus,
        deviceName: null,
        maxCount: null,
        useCount: null,
        aiBind: null,
        step: model.step,
        timeInterval: model.timeInterval,
        threshold: model.threshold,
        alarmStatus: model.taskOpenStatus,
        gatherStatus: model.enableStatus,
        stepRange: null,
        durationTime: null,
        dedInterval: null,
        visibleRange: model.visibleRange,
        bindStatus: model.taskStatus,
        visibleRangeBeans: null,
        minStep: null,
        maxStep: null,
        remark: null,
        includeAreas: model.includeAreas,
        deviceStatus: model.enableStatus,
        extra: model.extra,
        taskTime: [],
        paramShowSwitch: model.enableStatus,
        brainPowerAdjustSwitch: model.enableStatus,
        addStep: null,
        noAlarmAdjustCount: null,
        adjustMaxStep: null,
        alarmTacticsType: null,
        dealType: null,
        appDefaultImage: null,
        sceneShowSwitch: null,
        specialParam: model.specialParam,
        flowType: null,
        deliveryDriversStatus: null,
        hotspotAreaParam: null,
        datasetShowSwitch: null,
        showOsdStatus: null
    );
    push(arithmeticConfigFromData(data, (value) {
      Console.error("xxx---extra:${value.extra}");
      showLoading();
      NetQueryUtil.getInstance()
          .algorithmAiBoxEdit(
              step: value.step,
              id: model.id!,
              deviceId: model.deviceId!,
              aiboxAlgorithmId: model.aiboxAlgorithmId!,
              threshold: value.threshold,
              includeAreas: value.includeAreas,
              excludeAreas: model.excludeAreas,
              visibleRange: value.visibleRange,
              taskTime: model.taskTime,
              taskOpenStatus: model.taskOpenStatus ?? 1,
              status: model.status ?? 1,
              openStatus: model.openStatus ?? 1,
              timeInterval: model.timeInterval,
              extra: value.extra,
              // taskTime: AlgorithmTaskDetail.taskTimeFor(value.executeTime),
              specialParam: value.specialParam,)
          .then((value) {
        OperaLogUtil.cloudArithmetic.logEditDeviceAlgorithm(
            data.deviceId ?? "", data.algorithmName ?? "");
        hideLoading();
        refreshController.refresh();
      }).catchError((e) {
        hideLoading();
        toastFailure(kErrorMsg(e));
      });
    }, isAiBoxConfig: true));
  }

  add() async {
    closeSlidable();
    final ret = await push(ChooseArithmetic(
      minChannelCount: 1,
      needSerachWidget: false,
      vmBuilder: (context) {
        return ChooseArithmeticVmAddAiBox(context: context, parentId: widget.parentId);
      },
      initModels: models,
    ));
    if (ret != null && ret is List<ChooseArithmeticModel>) {
      showLoading();
      NetQueryUtil.getInstance()
          .deviceAiBoxBindAlgorithm(
              deviceId: widget.deviceId,
          algorithmParamList: ret.map((e) => {
            'aiboxAlgorithmId' : e.algorithmId,
            'step': e.step,
            'threshold': e.threshold,
          }).toList())
          .then((value) {
        OperaLogUtil.videoMonitor
            .logSetArithmetic(deviceName: widget.deviceName);
        hideLoading();
        toastSuccess();
        refreshController.refresh();
      }).catchError((e) {
        hideLoading();
        toastFailure(kErrorMsg(e));
      });
    }
  }

  closeSlidable() {
    if (cxtMap.isNotEmpty) {
      for (var element in cxtMap.values) {
        if (element.mounted) {
          Slidable.of(element)?.close();
        }
      }
    }
  }

  showUseage(int index) {
    // actionSheetWidget(
    //     widget: ArithmeticUsageDetailActionSheet(
    //         algorithmId: _datas[index].aiboxAlgorithmId!,
    //         title: "${_datas[index].algorithmName}${TR.current.tr_useCase}"));
  }

  cellKey(int index) {
    return _datas[index].id;
  }

  delete(int index) {
    dialog(
        title: TR.current.tr_gentleReminder,
        msg: TR.current.tr_SureDelete,
        btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
        btnHighLightColor: colors.blue,
        onSelected: (dlIndex) {
          if (dlIndex == 1) {
            showLoading();
            toastSuccess();
            NetQueryUtil.getInstance()
                .algorithmAiBoxDeleteItem(id: _datas[index].id!)
                .then((value) {
              OperaLogUtil.cloudArithmetic.logDeleteDeviceAlgorithm(
                  _datas[index].deviceId ?? "",
                  _datas[index].algorithmName ?? "");
              hideLoading();
              refreshController.refresh();
            }).catchError((e) {
              hideLoading();
              toastFailure(kErrorMsg(e));
            });
          }
        });
  }
}
