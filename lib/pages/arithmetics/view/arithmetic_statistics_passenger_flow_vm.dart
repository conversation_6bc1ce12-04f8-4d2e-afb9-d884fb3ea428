import 'package:bcloud/config/constant.dart';
import 'package:bcloud/config/events.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/arithmetics/model/customers_statistical_data_model.dart';
import 'package:bcloud/pages/login/model/account_info.dart';
import 'package:bcloud/pages/preview/model/in_bound_model.dart';
import 'package:bcloud/pages/preview/model/passenger_flow_data_response_page_query.dart';
import 'package:bcloud/pages/preview/model/passenger_flow_data_statistical_query.dart';
import 'package:bcloud/pages/preview/view/xb_select_time_bar_new.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/utils/xb_language_util.dart';
import 'package:bcloud/widget/action_sheet_multi/action_sheet_multi_2.dart';
import 'package:flutter/material.dart';
import 'package:xb_chart/xb_chart.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'arithmetic_statistics_passenger_flow.dart';
import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/public/network/net_extension_passenger_flow_statistics.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';

abstract class ArithmeticStatisticsPassengerFlowVM
    extends XBVM<ArithmeticStatisticsPassengerFlow> {
  ArithmeticStatisticsPassengerFlowVM({required super.context});

  @override
  void widgetDidBuilt() {
    super.widgetDidBuilt();
    initTitles;
    listen<EventArithmeticStatisticsPageRefresh>((p0) => query());
    listen<EventUpdateUserConfig>((p0) {
      initTitles;
      notify();
    });
  }

  List<XBAnnulusChartModel> get inStoreCustomerGroupModels => [
        XBAnnulusChartModel(
            name: TR.current.tr_singlePerson,
            color: kColorHexStr('#6CABF5'),
            value: singleBatchCount),
        XBAnnulusChartModel(
            name: TR.current.tr_twoPeople,
            color: kColorHexStr('#71DADA'),
            value: doubleBatchCount),
        XBAnnulusChartModel(
            name: TR.current.tr_threePeople,
            color: kColorHexStr('#AA82E3'),
            value: threeBatchCount),
        XBAnnulusChartModel(
            name: TR.current.tr_multiplePeople,
            color: kColorHexStr('#B5D5FA'),
            value: manyBatchCount)
      ];

  List<XBAnnulusChartModel> get ageModels => [
        XBAnnulusChartModel(
            name: TR.current.tr_juvenile, //TR.current.tr_children
            color: kColorHexStr('#6CABF5'),
            value: childCount),
        XBAnnulusChartModel(
            name: TR.current.tr_youth,
            color: kColorHexStr('#71DADA'),
            value: youngCount),
        XBAnnulusChartModel(
            name: TR.current.tr_middleAged,
            color: kColorHexStr('#AA82E3'),
            value: middleCount),
        XBAnnulusChartModel(
            name: TR.current.tr_elderly,
            color: kColorHexStr('#B5D5FA'),
            value: oldCount)
      ];

  /// 设备id
  String get deviceId => widget.deviceId;

  XBSelectTimeBarNewRet timeBarRet =
      XBSelectTimeBarNewRet(type: XBSelectTimeBarNewType.month);

  /*
  0-日 1-周 2-月 4-年 null-自定义 5-本周 6-本月
  */
  int? get queryType => timeBarRet.queryType;

  // 列表数据
  PassengerFlowDataResponsePageQuery? pageQuery;

  PassengerFlowDataStatisticalQuery? statisticalQuery;
  PassengerFlowDataStatisticalQuery? queryCount;

  CustomersStatisticalDataModel? customersStatisticalDataModel;

  double get singleBatchCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumSingleBatchCount) ?? 0;
  }

  double get doubleBatchCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumDoubleBatchCount) ?? 0;
  }

  double get threeBatchCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumThreeBatchCount) ?? 0;
  }

  double get manyBatchCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumManyBatchCount) ?? 0;
  }

  double get childCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumChildrenCount) ?? 0;
  }

  double get youngCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumYoungCount) ?? 0;
  }

  double get middleCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumMiddleCount) ?? 0;
  }

  double get oldCount {
    if (queryCount == null) return 0;
    return xbParse<double>(queryCount?.sumOldCount) ?? 0;
  }

  int get sumInboundCount {
    return queryCount?.sumInboundCount ?? 0;
  }

  int get sumManCount {
    return queryCount?.sumManCount ?? 0;
  }

  int get sumWomanCount {
    return queryCount?.sumWomanCount ?? 0;
  }

  double get maleRate {
    if (sumManCount == 0 || sumWomanCount == 0) return 0;
    return 1.0 * sumManCount / (sumManCount + sumWomanCount);
  }

  double get femaleRate {
    if (sumManCount == 0 || sumWomanCount == 0) return 0;
    return 1.0 * sumWomanCount / (sumManCount + sumWomanCount);
  }

  // 日期开始时间
  DateTime? get startDateTime => timeBarRet.startDateTime;

  String get startTime => timeBarRet.startTime;

  // 日期结束时间
  DateTime? get endDateTime => timeBarRet.endDateTime;

  String get endTime => timeBarRet.endTime;

  String get timeFormat => timeBarRet.timeFormat;

  /// 日期变化回调
  onSelectedTimeChanged(XBSelectTimeBarNewRet ret) {
    xbError("onSelectedTimeChanged: $ret");
    timeBarRet = ret;
    query();
  }

  /// 总进店人数
  int get totalInFlow {
    int ret = 0;
    final List<double> valuesStoreTraffic = [];
    if (pageQuery != null &&
        pageQuery!.datas != null &&
        pageQuery!.datas!.isNotEmpty) {
      for (var element in pageQuery!.datas!) {
        valuesStoreTraffic.add((element.sumInboundCount ?? 0).toDouble());
      }
    }
    for (var element in valuesStoreTraffic) {
      ret += element.toInt();
    }
    return ret;
  }

  /// 总进店率
  double get totalInboundRate {
    if (queryCount == null) {
      return 0;
    }
    return (queryCount!.inboundRate ?? 0) * 0.01;
  }

  /// 总进店客流
  int get totalStoreTraffic {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumInboundCount ?? 0;
  }

  /// 总进店客流-去重
  int get totalStoreDedTraffic {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumRemoveDuplicateInboundCount ?? 0;
  }

  /// 总进店同比
  double? get totalStoreTrafficTongbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.yoyInbound;
  }

  /// 总进店环比
  double? get totalStoreTrafficHuanbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.qoqInbound;
  }

  /// 总过店客流
  int get totalPassByTraffic {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumPassCount ?? 0;
  }

  /// 总过店客流-去重
  int get totalPassByDedTraffic {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumRemoveDuplicatePassCount ?? 0;
  }

  /// 总过店同比
  double? get totalPassByTrafficTongbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.yoyPass;
  }

  /// 总过店环比
  double? get totalPassByTrafficHuanbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.qoqPass;
  }

  /// 总出店客流
  int get totalOutStoreTraffic {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumOutboundCount ?? 0;
  }

  /// 总出店客流-去重
  int get totalOutStoreDedTraffic {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumRemoveDuplicateOutboundCount ?? 0;
  }

  /// 总出店同比
  double? get totalOutStoreTrafficTongbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.yoyOutbound;
  }

  /// 总出店环比
  double? get totalOutStoreTrafficHuanbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.qoqOutbound;
  }

  /// 总客流
  int get totalTraffic {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumFlowCount ?? 0;
  }

  /// 总客流-去重
  int get totalTrafficDed {
    if (queryCount == null) {
      return 0;
    }
    return queryCount!.sumRemoveDuplicateFlowCount ?? 0;
  }

  /// 总出店同比
  double? get totalTrafficTongbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.yoyFlow;
  }

  /// 总出店环比
  double? get totalTrafficHuanbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.qoqFlow;
  }

  /// 总进店率同比
  double? get totalInboundRateTongbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.yoyInboundRate;
  }

  /// 总进店率环比
  double? get totalInboundRateHuanbi {
    if (queryCount == null) {
      return null;
    }
    return queryCount!.qoqInboundRate;
  }

  /// 进店峰值
  InboundModel? get maxInboundCount {
    if (statisticalQuery == null ||
        statisticalQuery!.maxInboundCount == null ||
        statisticalQuery!.maxInboundTime == null) {
      return null;
    }
    return InboundModel(
        maxInboundRate: statisticalQuery!.maxInboundRate ?? 0,
        minInboundRate: statisticalQuery!.minInboundRate ?? 0,
        count: statisticalQuery!.maxInboundCount!,
        time: statisticalQuery!.maxInboundTime!);
  }

  /// 进店谷值
  InboundModel? get minInboundCount {
    if (statisticalQuery == null ||
        statisticalQuery!.minInboundCount == null ||
        statisticalQuery!.minInboundTime == null) {
      return null;
    }
    return InboundModel(
        maxInboundRate: statisticalQuery!.maxInboundRate ?? 0,
        minInboundRate: statisticalQuery!.minInboundRate ?? 0,
        count: statisticalQuery!.minInboundCount!,
        time: statisticalQuery!.minInboundTime!);
  }

  bool xTitleIsShow(int index) {
    return index % 2 == 0;
  }

  List<XBLineChartXTitle> get xTitlesConversionRate {
    List<XBLineChartXTitle> ret = [];
    if (pageQuery != null &&
        pageQuery!.datas != null &&
        pageQuery!.datas!.isNotEmpty) {
      for (var element in pageQuery!.datas!) {
        ret.add(XBLineChartXTitle(
            text: element.statisticalTime ?? "",
            isShow: xTitleIsShow(pageQuery!.datas!.indexOf(element))));
      }
    }
    return ret;
  }

  List<XBLineChartModel> get modelsConversionRate {
    List<XBLineChartModel> ret = [];
    if (pageQuery != null &&
        pageQuery!.datas != null &&
        pageQuery!.datas!.isNotEmpty) {
      /// 进店转化率数据
      final List<double> values = [];

      for (var element in pageQuery!.datas!) {
        values.add((element.inboundRate ?? 0).toDouble());
      }

      /// 进店转化率
      ret.add(XBLineChartModel(
          name: '${TR.current.tr_storeConversionRate} (%)',
          color: colors.blue,
          values: values));
    }
    return ret;
  }

  List<XBLineChartXTitle> get xTitlesFlow {
    List<XBLineChartXTitle> ret = [];
    if (pageQuery != null &&
        pageQuery!.datas != null &&
        pageQuery!.datas!.isNotEmpty) {
      for (var element in pageQuery!.datas!) {
        ret.add(XBLineChartXTitle(
            text: element.statisticalTime ?? "",
            isShow: xTitleIsShow(pageQuery!.datas!.indexOf(element))));
      }
    }
    return ret;
  }

  bool isSelectedTitle(String title) {
    if (actionSheetMulti2Models == null || actionSheetMulti2Models!.isEmpty) {
      return false;
    }
    return actionSheetMulti2Models!
            .firstWhereOrNull((element) => element.title == title)
            ?.isSelected ??
        false;
  }

  List<XBLineChartModel> get modelsFlow {
    List<XBLineChartModel> ret = [];
    if (pageQuery != null &&
        pageQuery!.datas != null &&
        pageQuery!.datas!.isNotEmpty) {
      /// 进店客流数据
      final List<double> valuesStoreTraffic = [];

      /// 进店客流数据-去重
      final List<double> valuesStoreTrafficDed = [];

      /// 过店客流数据
      final List<double> valuesPassByTraffic = [];

      /// 过店客流数据-去重
      final List<double> valuesPassByTrafficDed = [];

      /// 出店客流数据
      final List<double> valuesOutStoreTraffic = [];

      /// 出店客流数据-去重
      final List<double> valuesOutStoreTrafficDed = [];

      /// 总客流数据
      final List<double> valuesTotalTraffic = [];

      /// 总客流数据-去重
      final List<double> valuesTotalTrafficDed = [];

      for (var element in pageQuery!.datas!) {
        valuesStoreTraffic.add((element.sumInboundCount ?? 0).toDouble());
        valuesStoreTrafficDed
            .add((element.sumRemoveDuplicateInboundCount ?? 0).toDouble());
        valuesPassByTraffic.add((element.sumPassCount ?? 0).toDouble());
        valuesPassByTrafficDed
            .add((element.sumRemoveDuplicatePassCount ?? 0).toDouble());
        valuesOutStoreTraffic.add((element.sumOutboundCount ?? 0).toDouble());
        valuesOutStoreTrafficDed
            .add((element.sumRemoveDuplicateOutboundCount ?? 0).toDouble());
        valuesTotalTraffic.add((element.sumFlowCount ?? 0).toDouble());
        valuesTotalTrafficDed
            .add((element.sumRemoveDuplicateFlowCount ?? 0).toDouble());
      }

      if (isSelectedTitle(TR.current.tr_storeTraffic)) {
        /// 进店客流
        ret.add(XBLineChartModel(
            name: TR.current.tr_storeTraffic,
            color: colors.blue,
            values: valuesStoreTraffic));
      }

      if (isSelectedTitle(TR.current.tr_storeTrafficNoDeduplication)) {
        /// 进店客流-去重
        ret.add(XBLineChartModel(
            name: TR.current.tr_storeTrafficNoDeduplication,
            color: colors.blue.withAlpha(120),
            values: valuesStoreTrafficDed));
      }

      if (isSelectedTitle(TR.current.tr_passByTraffic)) {
        /// 过店客流
        ret.add(XBLineChartModel(
            name: TR.current.tr_passByTraffic,
            color: Colors.orange,
            values: valuesPassByTraffic));
      }

      if (AccountInfo.getInstance().config.isFlowDuplicateRemoval &&
          isSelectedTitle(TR.current.tr_passByTrafficNoDeduplication)) {
        /// 过店客流-去重
        ret.add(XBLineChartModel(
            name: TR.current.tr_passByTrafficNoDeduplication,
            color: Colors.orange.withAlpha(120),
            values: valuesPassByTrafficDed));
      }

      if (isSelectedTitle(TR.current.tr_outStoreTraffic)) {
        /// 出店客流
        ret.add(XBLineChartModel(
            name: TR.current.tr_outStoreTraffic,
            color: colors.red,
            values: valuesOutStoreTraffic));
      }

      if (isSelectedTitle(TR.current.tr_outStoreTrafficNoDeduplication)) {
        /// 出店客流-去重
        ret.add(XBLineChartModel(
            name: TR.current.tr_outStoreTrafficNoDeduplication,
            color: colors.red.withAlpha(120),
            values: valuesOutStoreTrafficDed));
      }

      if (isSelectedTitle(TR.current.tr_totalTraffic)) {
        /// 总客流
        ret.add(XBLineChartModel(
            name: TR.current.tr_totalTraffic,
            color: colors.green,
            values: valuesTotalTraffic));
      }

      if (AccountInfo.getInstance().config.isFlowDuplicateRemoval &&
          isSelectedTitle(TR.current.tr_totalTrafficNoDeduplication)) {
        /// 总客流-去重
        ret.add(XBLineChartModel(
            name: TR.current.tr_totalTrafficNoDeduplication,
            color: colors.green.withAlpha(120),
            values: valuesTotalTrafficDed));
      }
    }
    return ret;
  }

  bool get isChinese => XBLanguageUtil.isChinese;

  String get maxTitleFlow {
    final max = maxInboundCount;
    final subStr = max == null ? "" : (isChinese ? max.showCHN : max.showENG);
    return isChinese
        ? "${TR.current.tr_entryPeak}$subStr"
        : "${TR.current.tr_entryPeak} $subStr";
  }

  String get minTitleFlow {
    final min = minInboundCount;
    final subStr = min == null ? "" : (isChinese ? min.showCHN : min.showENG);
    return isChinese
        ? "${TR.current.tr_entryTrough}$subStr"
        : "${TR.current.tr_entryTrough} $subStr";
  }

  String get dropdownTitle {
    return TR.current.tr_statisticalDimension;
  }

  List<ActionSheetMulti2Model>? actionSheetMulti2Models;

  List<ActionSheetMulti2Model> get initTitles {
    if (actionSheetMulti2Models == null) {
      // 是否去重
      bool isFlowDuplicateRemoval =
          AccountInfo.getInstance().config.isFlowDuplicateRemoval;
      actionSheetMulti2Models = [];

      actionSheetMulti2Models!.add(ActionSheetMulti2Model(
        title: TR.current.tr_storeTraffic,
        isSelected: true,
      ));
      actionSheetMulti2Models!.add(ActionSheetMulti2Model(
        title: TR.current.tr_passByTraffic,
        isSelected: true,
      ));
      actionSheetMulti2Models!.add(ActionSheetMulti2Model(
        title: TR.current.tr_outStoreTraffic,
        isSelected: true,
      ));
      actionSheetMulti2Models!.add(ActionSheetMulti2Model(
        title: TR.current.tr_totalTraffic,
        isSelected: true,
      ));
      actionSheetMulti2Models!.add(ActionSheetMulti2Model(
        title: TR.current.tr_storeTrafficNoDeduplication,
        isSelected: false,
      ));
      if (isFlowDuplicateRemoval) {
        actionSheetMulti2Models!.add(ActionSheetMulti2Model(
          title: TR.current.tr_passByTrafficNoDeduplication,
          isSelected: false,
        ));
      }
      actionSheetMulti2Models!.add(ActionSheetMulti2Model(
        title: TR.current.tr_outStoreTrafficNoDeduplication,
        isSelected: false,
      ));
      if (isFlowDuplicateRemoval) {
        actionSheetMulti2Models!.add(ActionSheetMulti2Model(
          title: TR.current.tr_totalTrafficNoDeduplication,
          isSelected: false,
        ));
      }
    }

    return actionSheetMulti2Models!;
  }

  onTapDropdown() {
    actionSheetWidget(
        widget: ActionSheetMulti2(
            initTitles: initTitles,
            pageTitle: TR.current.tr_statisticalDimension,
            leftBtnTitle: TR.current.tr_Cancel,
            rightBtnTitle: TR.current.tr_Confirm,
            onTapLeftBtn: () {},
            onTapRightBtn: (titles) {
              actionSheetMulti2Models = titles;
              notify();
            }));
  }

  /// 客群分析
  Future<dynamic> queryCustomersStatisticalQuery(int? queryTypeSer) async {
    return NetQueryUtil.getInstance()
        .customersStatisticalQueryAI(
            storeIdList: widget.storeIdList,
            deviceId: deviceId,
            startTime: startTime,
            endTime: endTime)
        .then((value) {
      customersStatisticalDataModel =
          CustomersStatisticalDataModel.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  bool get notDeDuplicatedFlag => false;

  /// 分段数据
  Future<dynamic> queryPassengerFlowPageQuery(int? queryTypeSer) async {
    return NetQueryUtil.getInstance()
        .passengerFlowPageQueryAI(
            storeIdList: widget.storeIdList,
            deviceId: deviceId,
            queryType: queryTypeSer,
            startTime: startTime,
            endTime: endTime,
            notDeDuplicatedFlag: notDeDuplicatedFlag)
        .then((value) {
      pageQuery = PassengerFlowDataResponsePageQuery.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  /// 统计数据
  Future<dynamic> queryPassengerFlowQueryCount(int? queryTypeSer) async {
    return NetQueryUtil.getInstance()
        .passengerFlowQueryCountAI(
            storeIdList: widget.storeIdList,
            deviceId: deviceId,
            queryType: queryTypeSer,
            startTime: startTime,
            endTime: endTime,
            notDeDuplicatedFlag: notDeDuplicatedFlag)
        .then((value) {
      queryCount = PassengerFlowDataStatisticalQuery.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  /// 分析数据
  Future<dynamic> queryPassengerFlowStatisticalQuery(int? queryTypeSer) async {
    return NetQueryUtil.getInstance()
        .passengerFlowStatisticalQueryAI(
            storeIdList: widget.storeIdList,
            deviceId: deviceId,
            queryType: queryTypeSer,
            startTime: startTime,
            endTime: endTime,
            notDeDuplicatedFlag: notDeDuplicatedFlag)
        .then((value) {
      statisticalQuery = PassengerFlowDataStatisticalQuery.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  query() {
    Future.wait([
      queryPassengerFlowPageQuery(queryType),
      queryPassengerFlowQueryCount(queryType),
      queryPassengerFlowStatisticalQuery(queryType),
      queryCustomersStatisticalQuery(queryType),
    ]).whenComplete(() {
      eventBus.fire(EventArithmeticStatisticsPageEndRefresh());
    });
  }
}
