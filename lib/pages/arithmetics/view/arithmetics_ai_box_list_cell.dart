import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/widget/cell/auto_switch_image_btn_cell.dart';
import 'package:bcloud/widget/xb_state_widget/xb_state_mark_two_corner_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import '../arithmetics_ai_box_arithmetics_list_vm.dart';
import '../model/arithmetics_ai_box_arithmetics_list_response.dart';

class ArithmeticsAiBoxListCell extends StatelessWidget {
  final String title;
  final bool isOn;
  final bool isNormal;
  final String subTitle;
  final bool isPaired;
  final VoidCallback onChangedState;
  final VoidCallback onTap;
  final VoidCallback onTapSubTitle;
  final ArithmeticsAiBoxArithmeticsListVM vm;
  final AlgorithmAiBoxTaskDetail? itemEntity;
  const ArithmeticsAiBoxListCell(
      {required this.title,
      required this.isOn,
      required this.isNormal,
      required this.subTitle,
      required this.isPaired,
      required this.onChangedState,
      required this.onTap,
      required this.onTapSubTitle,
      required this.vm,
      required this.itemEntity,
      super.key});

  @override
  Widget build(BuildContext context) {
    if (itemEntity != null) {
      vm.cxtMap[itemEntity!] = context;
    }
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: Container(
            color: colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                XBButton(
                    onTap: onTap,
                    child: Container(
                      color: Colors.transparent,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left,
                            right: spaces.j8,
                            top: spaces.leftLess,
                            bottom: spaces.leftLess),
                        child: Row(
                          children: [
                            Expanded(
                              child: Container(
                                constraints: BoxConstraints(
                                    maxWidth:
                                        (screenW - spaces.left * 4) * 0.5),
                                child: Text(
                                  title,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      fontSize: fontSizes.s18,
                                      fontWeight: fontWeights.semiBold),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Visibility(
                              visible: isOn && !isNormal,
                              child: XBStateMarkTwoCornerWidget(
                                  titleSize: fontSizes.s10,
                                  title: TR.current.tr_algorithmAnomaly,
                                  color: colors.red),
                            ),
                            AutoSwitchImageBtnCell(
                              hasOperate: true,
                              isOpen: isOn,
                              onTap: onChangedState,
                              paddingTop: spaces.j8,
                              paddingBottom: spaces.j8,
                              paddingRight: spaces.j4,
                            ),
                          ],
                        ),
                      ),
                    )),
                XBButton(
                  onTap: onTapSubTitle,
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: spaces.left,
                        right: spaces.left,
                        bottom: spaces.leftLess),
                    child: Text(
                      subTitle,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(color: colors.grey808080),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
