import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/arithmetics/arithmetic_config_vm.dart';
import 'package:bcloud/pages/arithmetics/model/algorithm_task_detail.dart';
import 'package:bcloud/pages/arithmetics/model/arithmetic_config_store_model.dart';
import 'package:bcloud/pages/common_pages/config_arithmetic/config_arithmetic_widget.dart';
import 'package:bcloud/pages/common_pages/config_arithmetic/config_arithmetic_widget_client_cloud_mix.dart';
import 'package:bcloud/pages/common_pages/config_arithmetic/config_arithmetic_widget_heat_map.dart';
import 'package:bcloud/pages/common_pages/config_arithmetic/config_arithmetic_widget_video_quality.dart';
import 'package:bcloud/pages/common_pages/config_arithmetic/model/config_arithmetic_widget_model.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/widget/rounded_button.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class ArithmeticConfigFromDataModel {
  int step;
  String threshold;
  String includeAreas;
  String visibleRange;
  int durationTime;
  int dedInterval;
  String extra;
  List<String> executeTime;
  int brainPowerAdjustSwitch;
  int? addStep;
  int? noAlarmAdjustCount;
  int? adjustMaxStep;
  int? alarmTacticsType;
  String? specialParam;
  int? flowType;
  int? deliveryDriversStatus;
  String? hotspotAreaParam;
  int? showOsdStatus;
  ArithmeticConfigFromDataModel({
    required this.step,
    required this.threshold,
    required this.includeAreas,
    required this.visibleRange,
    required this.durationTime,
    required this.dedInterval,
    required this.extra,
    required this.executeTime,
    required this.brainPowerAdjustSwitch,
    required this.hotspotAreaParam,
    required this.showOsdStatus,
  });
}

ArithmeticConfig arithmeticConfigFromData(AlgorithmTaskDetail data,
    ValueChanged<ArithmeticConfigFromDataModel> onSave, {bool isAiBoxConfig = false}) {
  return ArithmeticConfig(
      use8192: data.isCloudMixDeviceFlow,
      isNeedArrow: data.isCloudMixDeviceFlow || data.isCloudFlow,
      adjustMaxStepMax: data.maxStep,
      brainPowerAdjustSwitch: data.brainPowerAdjustSwitch ?? 0,
      addStep: data.addStep,
      noAlarmAdjustCount: data.noAlarmAdjustCount,
      adjustMaxStep: data.adjustMaxStep,
      alarmTacticsType: data.alarmTacticsType,
      dedInterval: data.dedInterval ?? 0,
      executeTime: data.taskTimeStrList,
      paramShowSwitch: data.paramShowSwitch ?? 0,
      datasetShowSwitch: data.datasetShowSwitch ?? 0,
      id: data.id!,
      step: data.step ?? 0,
      durationTime: data.durationTime ?? 0,
      threshold: double.parse(data.threshold ?? "0"),
      includeAreas: data.includeAreas,
      rawSize: data.extraSize,
      deviceId: data.deviceId,
      visibleRange: data.visibleRange ?? "",
      isVideoQuality: data.isVideoQuality,
      specialParam: data.specialParam,
      isCloudMixDeviceFlow: data.isCloudMixDeviceFlow,
      flowType: data.flowType,
      deliveryDriversStatus: data.deliveryDriversStatus,
      hotspotAreaParam: data.hotspotAreaParam,
      isHeatMap: data.isHeatMap,
      showOsdStatus: data.showOsdStatus,
      isAiBoxConfig: isAiBoxConfig,
      onSave: (value) {
        if (data.isVideoQuality) {
          if (value.executeTime.isEmpty) {
            toastFailure(TR.current.tr_deviceNotAddedRuntimePoint);
            return;
          }
        }
        ArithmeticConfigFromDataModel model = ArithmeticConfigFromDataModel(
            step: value.step,
            threshold: '${value.threshold}',
            includeAreas: value.includeAreas ?? "",
            hotspotAreaParam: value.hotspotAreaParam,
            visibleRange: value.visibleRange,
            durationTime: value.durationTime,
            dedInterval: value.dedInterval,
            extra: value.extra.isNotEmpty ? value.extra : data.extra ?? "",
            executeTime: value.executeTime,
            brainPowerAdjustSwitch:
                value.autoDispatchDatasource.brainPowerAdjustSwitch,
            showOsdStatus: value.showOsdStatus);
        model.addStep = value.autoDispatchDatasource.addStep;
        model.noAlarmAdjustCount =
            value.autoDispatchDatasource.noAlarmAdjustCount;
        model.adjustMaxStep = value.autoDispatchDatasource.adjustMaxStep;
        model.alarmTacticsType = value.autoDispatchDatasource.alarmTacticsType;
        model.specialParam = value.specialParam;
        model.flowType = value.flowType;
        model.deliveryDriversStatus = value.deliveryDriversStatus;
        onSave(model);
      });
}

class ArithmeticConfig extends XBPageOver<ArithmeticConfigVM> {
  final String id;
  final int step;
  final int durationTime;
  final int dedInterval;
  final double threshold;
  final String? includeAreas;
  final Size? rawSize;
  final int paramShowSwitch;
  final String? deviceId;
  final String visibleRange;
  final List<String> executeTime;
  final ValueChanged<ArithmeticConfigStoreModel> onSave;
  final int brainPowerAdjustSwitch;
  final int? addStep;
  final int? noAlarmAdjustCount;
  final int? adjustMaxStep;
  final int? adjustMaxStepMax;
  final int? alarmTacticsType;
  final bool use8192;
  final bool isNeedArrow;
  final String? specialParam;
  final bool isHeatMap;
  final bool isVideoQuality;
  final bool isCloudMixDeviceFlow;
  final int? flowType;
  final int? deliveryDriversStatus;
  final String? hotspotAreaParam;
  final int? datasetShowSwitch;
  final int? showOsdStatus;
  final bool isAiBoxConfig;
  const ArithmeticConfig(
      {required this.id,
      required this.step,
      required this.durationTime,
      required this.dedInterval,
      required this.threshold,
      required this.onSave,
      required this.includeAreas,
      required this.rawSize,
      required this.visibleRange,
      required this.executeTime,
      required this.paramShowSwitch,
      required this.brainPowerAdjustSwitch,
      required this.addStep,
      required this.noAlarmAdjustCount,
      required this.adjustMaxStep,
      required this.adjustMaxStepMax,
      required this.alarmTacticsType,
      required this.isCloudMixDeviceFlow,
      this.specialParam,
      this.isVideoQuality = false,
      this.isHeatMap = false,
      this.deviceId,
      this.use8192 = false,
      this.isNeedArrow = false,
      required this.flowType,
      required this.deliveryDriversStatus,
      this.hotspotAreaParam,
      required this.datasetShowSwitch,
      required this.showOsdStatus,
      this.isAiBoxConfig = false,
      super.key});

  bool get isShowStep => AlgorithmTaskDetail.isShowParam(
      paramShowSwitch, AlgorithmTaskDetail.stepMask);

  bool get isShowDuration => AlgorithmTaskDetail.isShowParam(
      paramShowSwitch, AlgorithmTaskDetail.durationTimeMask);

  @override
  generateVM(BuildContext context) {
    return ArithmeticConfigVM(context: context);
  }

  @override
  String setTitle(ArithmeticConfigVM vm) {
    return TR.current.tr_parameterConfiguration;
  }

  @override
  List<Widget>? actions(ArithmeticConfigVM vm) {
    return [
      XBButton(
        onTap: vm.save,
        child: Container(
          color: Colors.transparent,
          alignment: Alignment.center,
          child: Padding(
            padding: EdgeInsets.only(
                left: spaces.left,
                right: spaces.left,
                top: spaces.j8,
                bottom: spaces.j8),
            child: RoundedButton(
              color: colors.blue,
              width: 55,
              height: 32,
              radius: 6,
              title: TR.current.tr_CommonSave,
            ),
          ),
        ),
      )
    ];
  }

  @override
  Widget buildPage(ArithmeticConfigVM vm, BuildContext context) {
    final model = ConfigArithmeticWidgetModel(
        brainPowerAdjustSwitch: brainPowerAdjustSwitch,
        addStep: addStep,
        noAlarmAdjustCount: noAlarmAdjustCount,
        adjustMaxStep: adjustMaxStep,
        alarmTacticsType: alarmTacticsType,
        dedInterval: vm.configStoreModel.dedInterval,
        onDedIntervalChanged: vm.onDedIntervalChanged,
        paramShowSwitch: paramShowSwitch,
        datasetShowSwitch: datasetShowSwitch,
        onTaping: vm.onTaping,
        step: vm.configStoreModel.step,
        executeTime: vm.configStoreModel.executeTime,
        durationTime: vm.configStoreModel.durationTime,
        threshold: vm.configStoreModel.threshold,
        includeAreas: vm.configStoreModel.includeAreas,
        visibleRangeBeans: vm.visibleRangeBeans,
        onStepChanged: vm.onStepChanged,
        onDurationTimeChanged: vm.onDurationTimeChanged,
        onThresholdChanged: vm.onThresholdChanged,
        onIncludeAreaChanged: vm.onIncludeAreaChanged,
        onHotspotAreaParamChanged: vm.onHotspotAreaParamChanged,
        onVisibleRangeChanged: vm.onVisibleRangeChanged,
        onExtraChanged: vm.onExtraChanged,
        onCanEditArea: vm.onCanEditArea,
        onExecuteTimeChanged: vm.onExecuteTimeChanged,
        onAutoDispatchDataChanged: vm.onAutoDispatchDataChanged,
        videoUrl: vm.videoUrl,
        videoDisableTip: vm.videoDisableTip,
        rawSize: rawSize,
        use8192: use8192,
        isNeedArrow: isNeedArrow,
        specialParam: vm.configStoreModel.specialParam,
        onSpecialParamChanged: isVideoQuality ? vm.onSpecialParamChanged : null,
        isNeedRunTimeRangeModule: isVideoQuality ? false : true,
        isCloudMixDeviceFlow: isCloudMixDeviceFlow,
        flowType: vm.configStoreModel.flowType,
        deliveryDriversStatus: vm.configStoreModel.deliveryDriversStatus,
        onFlowTypeChanged: vm.onFlowTypeChanged,
        onDeliveryDriversStatusChanged: vm.onDeliveryDriversStatusChanged,
        isNeedAssociateFace: vm.isNeedAssociateFace,
        hotspotAreaParam: hotspotAreaParam,
        deviceId: deviceId,
        showOsdStatus: vm.configStoreModel.showOsdStatus,
        onShowOsdStatusChanged: vm.onShowOsdStatusChanged);
    Widget child;
    if (isVideoQuality) {
      child = ConfigArithmeticWidgetVideoQuality(model: model);
    } else if (isHeatMap) {
      child = ConfigArithmeticWidgetHeatMap(model: model);
    } else {
      if (isCloudMixDeviceFlow) {
        child = ConfigArithmeticWidgetClientCloudMix(model: model);
      } else {
        child = ConfigArithmeticWidget(model: model);
      }
    }
    return SingleChildScrollView(
      physics: vm.isTaping
          ? const NeverScrollableScrollPhysics()
          : const AlwaysScrollableScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.only(top: spaces.leftLess),
        child: child,
      ),
    );
  }
}
