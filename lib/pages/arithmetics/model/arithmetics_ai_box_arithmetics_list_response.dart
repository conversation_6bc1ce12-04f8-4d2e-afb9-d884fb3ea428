
import 'package:xb_scaffold/xb_scaffold.dart';

class ArithmeticsAiBoxArithmeticsListResponse {
  String? pageSize;
  String? pageNo;
  String? totalCount;
  String? totalPage;
  List<AlgorithmAiBoxTaskDetail>? datas;
  bool? hasNext;

  ArithmeticsAiBoxArithmeticsListResponse(
      {this.pageSize,
      this.pageNo,
      this.totalCount,
      this.totalPage,
      this.datas,
      this.hasNext});

  ArithmeticsAiBoxArithmeticsListResponse.fromJson(
      Map<String, dynamic> json) {
    pageSize = json['pageSize'];
    pageNo = json['pageNo'];
    totalCount = json['totalCount'];
    totalPage = json['totalPage'];
    if (json['datas'] != null) {
      datas = <AlgorithmAiBoxTaskDetail>[];
      json['datas'].forEach((v) {
        datas!.add(AlgorithmAiBoxTaskDetail.fromJson(v));
      });
    }
    hasNext = json['hasNext'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['pageSize'] = this.pageSize;
    data['pageNo'] = this.pageNo;
    data['totalCount'] = this.totalCount;
    data['totalPage'] = this.totalPage;
    if (this.datas != null) {
      data['datas'] = this.datas!.map((v) => v.toJson()).toList();
    }
    data['hasNext'] = this.hasNext;
    return data;
  }
}

class AlgorithmAiBoxTaskDetail {
  String? id;
  String? cid;
  String? parentsId;
  String? deviceId;
  String? aiboxAlgorithmId;
  int? step;
  int? timeInterval;
  String? threshold;
  String? includeAreas;
  String? excludeAreas;
  String? extra;
  int? isDeleted;
  String? creator;
  String? modifier;
  String? gmtCreated;
  String? gmtModified;
  int? status;
  int? openStatus;
  int? enableStatus;
  String? algorithmName;
  String? visibleRange;
  int? taskStatus;
  String? nodeId;
  String? taskTime;
  int? taskOpenStatus;
  String? specialParam;
  String? storeId;

  AlgorithmAiBoxTaskDetail(
      { this.id,
        this.cid,
        this.parentsId,
        this.deviceId,
        this.aiboxAlgorithmId,
        this.step,
        this.timeInterval,
        this.threshold,
        this.includeAreas,
        this.excludeAreas,
        this.extra,
        this.isDeleted,
        this.creator,
        this.modifier,
        this.gmtCreated,
        this.gmtModified,
        this.status,
        this.openStatus,
        this.enableStatus,
        this.algorithmName,
        this.visibleRange,
        this.taskStatus,
        this.nodeId,
        this.taskTime,
        this.taskOpenStatus,
        this.specialParam,
        this.storeId});

  AlgorithmAiBoxTaskDetail.fromJson(Map<String, dynamic> json) {
    id = xbParse<String>(json['id']);
    cid = xbParse<String>(json['cid']);
    parentsId = xbParse<String>(json['parentsId']);
    deviceId = xbParse<String>(json['deviceId']);
    aiboxAlgorithmId = xbParse<String>(json['aiboxAlgorithmId']);
    step = xbParse<int>(json['step']);
    timeInterval = xbParse<int>(json['timeInterval']);
    threshold = json['threshold'];
    includeAreas = json['includeAreas'];
    excludeAreas = json['excludeAreas'];
    extra = json['extra'];
    isDeleted = json['isDeleted'];
    creator = json['creator'];
    modifier = json['modifier'];
    gmtCreated = json['gmtCreated'];
    gmtModified = json['gmtModified'];
    status = json['status'];
    openStatus = json['openStatus'];
    enableStatus = json['enableStatus'];
    algorithmName = json['algorithmName'];
    visibleRange = json['visibleRange'];
    taskStatus = json['taskStatus'];
    nodeId = xbParse<String>(json['nodeId']);
    taskTime = json['taskTime'];
    taskOpenStatus = json['taskOpenStatus'];
    specialParam = json['specialParam'];
    storeId = json['storeId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['cid'] = this.cid;
    data['parentsId'] = this.parentsId;
    data['deviceId'] = this.deviceId;
    data['aiboxAlgorithmId'] = this.aiboxAlgorithmId;
    data['step'] = this.step;
    data['timeInterval'] = this.timeInterval;
    data['threshold'] = this.threshold;
    data['includeAreas'] = this.includeAreas;
    data['excludeAreas'] = this.excludeAreas;
    data['extra'] = this.extra;
    data['isDeleted'] = this.isDeleted;
    data['creator'] = this.creator;
    data['modifier'] = this.modifier;
    data['gmtCreated'] = this.gmtCreated;
    data['gmtModified'] = this.gmtModified;
    data['status'] = this.status;
    data['openStatus'] = this.openStatus;
    data['enableStatus'] = this.enableStatus;
    data['algorithmName'] = this.algorithmName;
    data['visibleRange'] = this.visibleRange;
    data['taskStatus'] = this.taskStatus;
    data['nodeId'] = this.nodeId;
    data['taskTime'] = this.taskTime;
    data['taskOpenStatus'] = this.taskOpenStatus;
    data['specialParam'] = this.specialParam;
    data['storeId'] = this.storeId;
    return data;
  }
}
