import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/arithmetics/view/arithmetics_ai_box_list_cell.dart';
import 'package:bcloud/pages/function/video_inspection/view/search_widget.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_refresh_list_view.dart';
import 'package:bcloud/widget/xb_delete_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'arithmetics_ai_box_arithmetics_list_vm.dart';

class ArithmeticsAiBoxArithmeticsListPage
    extends XBPageOver<ArithmeticsAiBoxArithmeticsListVM> {
  final String deviceId;
  final String deviceName;
  final String parentId;
  const ArithmeticsAiBoxArithmeticsListPage(
      {required this.deviceId,
      required this.deviceName,
      required this.parentId,
      super.key});

  @override
  generateVM(BuildContext context) {
    return ArithmeticsAiBoxArithmeticsListVM(context: context);
  }

  @override
  bool needLoading(ArithmeticsAiBoxArithmeticsListVM vm) {
    return true;
  }

  @override
  String setTitle(ArithmeticsAiBoxArithmeticsListVM vm) {
    return deviceName;
  }

  @override
  List<Widget>? actions(ArithmeticsAiBoxArithmeticsListVM vm) {
    return [
      XBButton(
        needTapEffect: true,
        onTap: vm.add,
        child: Container(
          color: Colors.transparent,
          alignment: Alignment.center,
          child: Padding(
            padding: EdgeInsets.only(
                left: spaces.left,
                right: spaces.left,
                top: spaces.j8,
                bottom: spaces.j8),
            child: XBImage(
              images.ic_common_add_black,
              width: 25,
            ),
          ),
        ),
      )
    ];
  }

  @override
  Widget buildPage(ArithmeticsAiBoxArithmeticsListVM vm, BuildContext context) {
    return Column(
      children: [
        Container(
            color: colors.white,
            child: Padding(
              padding: EdgeInsets.only(bottom: spaces.left),
              child: Padding(
                padding: EdgeInsets.only(
                    top: spaces.j8, left: spaces.left, right: spaces.left),
                child: XBSearchBar(
                    placeholder: TR.current.tr_searchAlgorithmByName,
                    onFocus: () {
                      vm.closeSlidable();
                    },
                    onCommit: vm.onSearch),
              ),
            )),
        Expanded(
         child: SlidableAutoCloseBehavior(
          child: XBRefreshListView(
            vm: vm,
            itemBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.only(
                    left: spaces.left,
                    right: spaces.left,
                    top: index == 0 ? spaces.left : 0,
                    bottom: spaces.left +
                        (index == vm.itemCount - 1 ? safeAreaBottom : 0)),
                child: XBDeleteWidget(
                  key: ValueKey(vm.cellKey(index)),
                  onDelete: () {
                    vm.delete(index);
                  },
                  child: ArithmeticsAiBoxListCell(
                    onTapSubTitle: () {
                      vm.showUseage(index);
                    },
                    title: vm.title(index),
                    isOn: vm.isOn(index),
                    isNormal: vm.isNormal(index),
                    subTitle: vm.subTitle(index),
                    isPaired: index % 3 == 0,
                    vm: vm,
                    itemEntity: vm.indexItemEntity(index),
                    onChangedState: () {
                      vm.onChangedState(index);
                    },
                    onTap: () {
                      vm.onTap(index);
                    },
                  ),
                ),
              );
            },
          ),
        ))
      ],
    );
  }
}
