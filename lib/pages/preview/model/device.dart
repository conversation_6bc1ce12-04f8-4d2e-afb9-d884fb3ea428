import 'dart:convert' as convert;
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/utils/print_util.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class Device {
  String uuid;
  String? deviceName;
  String? url;
  int? deviceType;
  int? favoriteStatus;
  int isMultiLevel = 0;

  bool get isGBDevice => deviceType == 0;
  bool get isOnVifDevice => deviceType == 1;
  bool get isJFDevice => deviceType == 2;

  bool get isFavorite => favoriteStatus == 2;

  String get deviceTypePrefix {
    if (isJFDevice) {
      return 'j';
    } else if (isOnVifDevice) {
      return 'o';
    } else if (isGBDevice) {
      return 'g';
    } else {
      return '';
    }
  }

  Device(
      {required this.uuid,
      this.deviceName,
      this.url,
      this.deviceType,
      this.favoriteStatus,
      this.isMultiLevel = 0});
}

class DeviceStream {
  int deviceId = -1;
  int accessType = 0;
  String deviceName = '';
  String hlsUrl = '';
  String rtspUrl = '';
  String rtspPriUrl = '';
  String notice = '';
  int deviceType = -1;
  String stream = '';

  DeviceStream.fromJson(Map<String, dynamic> json) {
    Console.error(json);
    deviceId = int.parse(json['deviceId']);
    accessType = int.parse('${json['accessType'] ?? 0}');
    hlsUrl = json['hlsUrl'] ?? '';
    rtspUrl = json['rtspUrl'] ?? '';
    rtspPriUrl = json['rtspPriUrl'] ?? '';
    deviceName = json['deviceName'] ?? '';
    notice = json['notice'] ?? '';
    deviceType = json['deviceType'] ?? -1;
    stream = json['stream'] ?? '';
  }
}

///设备详情信息
///在设备树和预览页面用不到这么多数据
///目前只有在设备设置详情使用到
///之后可以考虑统一
class DeviceInfo {
  String id = '0';
  String cid = '0';

  ///0-国标设备，1-ONVIF设备，2-杰峰设备
  int deviceType = 2;
  String deviceName = '';

  ///所属节点
  String nodeId = '0';

  ///接入类型：0-IPC、1-NVR、2-DVR、3-平台
  int accessType = -1;

  ///同步数据类型：0-本级，1-国标下级平台
  int syncDataType = 0;
  String parentId = '';

  bool get isGBDevice => deviceType == 0;
  bool get isOnVifDevice => deviceType == 1;
  bool get isJFDevice => deviceType == 2;

  String get accessTypeString {
    switch (accessType) {
      case 0:
        return TR.current.tr_IPC;
      case 1:
        return TR.current.tr_NVR;
      case 2:
        return TR.current.tr_DVR;
      case 3:
        if (syncDataType == 1) {
          return TR.current.tr_centralServer;
        } else {
          return TR.current.tr_platform;
        }
      default:
        return '';
    }
  }

  ///序列号
  String deviceSerialNumber = '';

  ///用户名
  String deviceUsername = '';
  String devicePassword = '';

  ///接入通道数
  int accessChannels = 0;

  ///通道id
  String accessId = '';

  ///接入id
  String directoryId = '';

  ///接入id密码
  String accessIdPwd = '';

  ///接入状态：0-未注册，1-已注册
  int accessStatus = 0;

  ///sip服务器ip
  String sipServer = '';
  String sipDomain = '';
  String sipServerNum = '';
  int serverPortNum = 0;

  ///注册有效期
  String registerExpiry = '';

  ///心跳周期
  String heartbeatCycle = '';

  ///接入协议：0-UDP，1-TCP
  int linkType = 0;

  ///生成ID个数
  int generateIdNum = 0;

  ///接入密码类型：0-随机生成，1-自定义
  int accessPwdType = -1;

  ///设备状态：0-离线，1-在线，2-无
  int runStatus = 2;

  ///节点名称
  String nodeName = '';

  String levelCodeStr = '';

  ///网关类型
  int deviceNetworkType = 0;

  ///网关序列号
  String gatewaySN = "";

  ///网关状态
  int gatewayStatus = 0;

  ///经度
  double longitude = -1;

  ///维度
  double latitude = -1;

  ///设备位置
  String location = "";

  ///城市
  String cityName = "";

  ///地区
  String adName = "";

  /// 设备时间
  String? opTimeQuery;

  /// 硬件版本号
  String? hardWare;

  /// 软件版本号
  String? softWareVersion;

  /// 软件版本发布日期
  String? buildTime;

  /// 设备IP
  String? hostIP;

  /// 设备时区 东某区用负值，西某区用正值，比如东八区就是返回-8
  int? timeMin;

  /// 网络制式
  String? pattern;
  String? loginNetworkMode;

  int actualAccessChannels = 0;
  String? channelName;
  String? channelNumber;
  VersionModel? versionModel;
  int? type;
  String keyVerify = TR.current.tr_failure;

  ///通道列表
  List<DeviceInfo> channelList = [];

  ///接入通道数/申请通道数
  String serverActualAccessChannelRateStr = TR.current.tr_notYet;

  int? openSwitch;

  int isDeleted = 1;

  ///子类型：空或1表示-单目，2-多目
  int subtypes = 1;

  ///是否存在最新版本
  bool isNewVFirmware = false;

  DeviceInfo();

  /// 是否支持客流计数
  bool get suportFlowCount =>
      versionModel != null && versionModel!.supportFlowCount;

  /// 是否支持端云结合客流统计
  bool get supportClientCloudFlow {
    final list = deviceAbilityList.where((e) => e.abilityCode == 'preciseFlow');
    return list.isNotEmpty && list.first.isAbility == '1';
  }

  /// 是否支持客流热区
  bool get supportFlowHotArea {
    final list = deviceAbilityList.where((e) => e.abilityCode == 'hot');
    return list.isNotEmpty && list.first.isAbility == '1';
  }

  /// 是否开启客流能力
  bool get isFlowOn => openSwitch != null && openSwitch == 1;

  void updateFlowOn(bool on) {
    openSwitch = on ? 1 : 0;
  }

  String deviceTagStr = "";
  String deviceAiBoxTaskConfigStr = "";

  ///设备标签列表
  List<DeviceTagModel> deviceTagList = [];

  ///设备能力集列表
  List<DeviceAbilityModel> deviceAbilityList = [];

  ///盒子边缘算法列表
  List<DeviceAiBoxTaskConfigModel> aiBoxTaskConfigList = [];

  bool isSelect = false;

  int isSyncPosition = 1;

  String channelNo = '';

  ///通道账号
  String? channelUsername;
  ///通道密码
  String? channelPassword;

  DeviceInfo.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '0';
    cid = json['cid'] ?? '0';
    deviceType = json['deviceType'] ?? 2;
    deviceName = json['deviceName'] ?? '';
    nodeId = json['nodeId'] ?? '0';
    syncDataType = json['syncDataType'] ?? 0;
    accessType = json['accessType'] ?? -1;
    parentId = json['parentId'] ?? id;
    actualAccessChannels = json['actualAccessChannels'] ?? 0;
    channelName = json['channelName'];
    channelNumber = json['channelNumber'];
    deviceSerialNumber = json['deviceSerialNumber'] ?? '';
    loginNetworkMode = json['loginNetworkMode'] ?? '';
    deviceUsername = json['deviceUsername'] ?? '';
    devicePassword = json['devicePassword'] ?? '';
    // var psd = json['devicePassword'];
    // devicePassword = (psd?.isEmpty ?? true) ? TR.current.tr_notYet : psd;
    accessId = json['accessId'] ?? '';
    directoryId = json['directoryId'] ?? '';
    accessIdPwd = json['accessIdPwd'] ?? '';
    accessStatus = json['accessStatus'] ?? 0;
    sipServer = json['sipServer'] ?? '';
    sipDomain = json['sipDomain'] ?? '';
    sipServerNum = json['sipServerNum'] ?? '';
    serverPortNum = json['serverPortNum'] ?? 0;
    registerExpiry = json['registerExpiry'] ?? TR.current.tr_notYet;
    heartbeatCycle = json['heartbeatCycle'] ?? TR.current.tr_notYet;
    linkType = json['linkType'] ?? 0;
    generateIdNum = json['generateIdNum'] ?? 0;
    accessPwdType = json['accessPwdType'] ?? -1;
    runStatus = json['runStatus'] ?? 2;
    nodeName = json['nodeName'] ?? '';
    levelCodeStr = json['levelCodeStr'] ?? '';
    accessChannels = json['accessChannels'] ?? 0;
    type = json['type'];
    isNewVFirmware = xbParse<bool>(json['isNewVFirmware']) ?? false;
    keyVerify =
        json['keyVerify'] == 1 ? TR.current.tr_success : TR.current.tr_failure;
    if (json['channeList'] != null) {
      channelList = <DeviceInfo>[];
      json['channeList'].forEach((v) {
        channelList!.add(DeviceInfo.fromJson(v));
      });
    }
    if (actualAccessChannels >= accessChannels && accessChannels > 0) {
      serverActualAccessChannelRateStr =
          "$actualAccessChannels/$actualAccessChannels";
    } else {
      serverActualAccessChannelRateStr =
          "$actualAccessChannels/$accessChannels";
    }
    String? version = json['version'] ?? '{}';
    if (version == "") {
      version = '{}';
    }
    if (version != null) {
      //转json
      Map<String, dynamic> jsonMap = convert.jsonDecode(version);
      versionModel = VersionModel.fromJson(jsonMap);
    }
    openSwitch = xbParse<int>(json['openSwitch']);
    // longitude = json['longitude'] ?? -1;
    // latitude = json['latitude'] ?? -1;
    latitude = xbParse<double>(json['latitude']) ?? -1;
    longitude = xbParse<double>(json['longitude']) ?? -1;
    location = json['location'] ?? '';
    if (json['deviceTagList'] != null) {
      deviceTagList = <DeviceTagModel>[];
      json['deviceTagList'].forEach((v) {
        deviceTagList.add(DeviceTagModel.fromJson(v));
      });
      List<String> list = deviceTagList.map((e) => e.tagName).toList();
      deviceTagStr = list.join("、");
    }
    if (json['deviceAbilityList'] != null) {
      deviceAbilityList = <DeviceAbilityModel>[];
      json['deviceAbilityList'].forEach((v) {
        deviceAbilityList.add(DeviceAbilityModel.fromJson(v));
      });
    }
    if (json['aiboxTaskConfigList'] != null) {
      aiBoxTaskConfigList = <DeviceAiBoxTaskConfigModel>[];
      json['aiboxTaskConfigList'].forEach((v) {
        aiBoxTaskConfigList.add(DeviceAiBoxTaskConfigModel.fromJson(v));
      });
      List<String> list = aiBoxTaskConfigList.map((e) => e.algorithmName).toList();
      deviceAiBoxTaskConfigStr = list.join("、");
    }
    deviceNetworkType = json['deviceNetworkType'] ?? 0;
    gatewaySN = json['gwSerialNumber'] ?? "";
    gatewayStatus = json['gwStatus'] ?? 0;
    isDeleted = json['isDeleted'] ?? 1;
    cityName = json['cityName'] ?? "";
    adName = json['adName'] ?? "";
    isSyncPosition = xbParse<int>(json['isSyncPosition']) ?? 1;
    opTimeQuery = json['opTimeQuery'];
    hardWare = json['hardWare'];
    softWareVersion = json['softWareVersion'];
    buildTime = json['buildTime'];
    hostIP = json['hostIP'];
    timeMin = json['timeMin'];
    pattern = json['pattern'];
    subtypes = xbParse<int>(json['subtypes']) ?? 1;
    channelUsername = json['channelUsername'];
    channelPassword = json['channelPassword'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['cid'] = cid;
    data['deviceType'] = deviceType;
    data['deviceName'] = deviceName;
    data['nodeId'] = nodeId;
    data['accessType'] = accessType;
    data['deviceSerialNumber'] = deviceSerialNumber;
    data['deviceUsername'] = deviceUsername;
    data['devicePassword'] = devicePassword;
    data['accessId'] = accessId;
    data['accessIdPwd'] = accessIdPwd;
    data['accessStatus'] = accessStatus;
    data['sipServer'] = sipServer;
    data['sipDomain'] = sipDomain;
    data['sipServerNum'] = sipServerNum;
    data['serverPortNum'] = serverPortNum;
    data['linkType'] = linkType;
    data['generateIdNum'] = generateIdNum;
    data['accessPwdType'] = accessPwdType;
    data['runStatus'] = runStatus;
    data['nodeName'] = nodeName;
    data['openSwitch'] = openSwitch;
    data['opTimeQuery'] = opTimeQuery;
    data['hardWare'] = hardWare;
    data['softWareVersion'] = softWareVersion;
    data['buildTime'] = buildTime;
    data['hostIP'] = hostIP;
    data['timeMin'] = timeMin;
    data['pattern'] = pattern;
    return data;
  }
}

class VersionModel {
  ///能力集：json字符串{"netDAS":"1","talk":"0", "flowCount":"1"}netDAS是否支持das,talk是否支持对讲,flowCount是否支持客流统计 0不支持 1支持
  String? netDAS;
  String? talk;
  String? flowCount;

  /// 是否支持 端侧客流统计
  bool get supportFlowCount => flowCount == TR.current.tr_support;

  VersionModel.fromJson(Map<String, dynamic> json) {
    netDAS = xbParse<String>(json['netDAS']) == '1'
        ? TR.current.tr_support
        : TR.current.tr_notSupported;
    talk = xbParse<String>(json['talk']) == '1'
        ? TR.current.tr_support
        : TR.current.tr_notSupported;
    flowCount = xbParse<String>(json['flowCount']) == '1'
        ? TR.current.tr_support
        : TR.current.tr_notSupported;
  }
}

class DeviceAiBoxTaskConfigModel {
  String id = "";
  String deviceId = "";
  String aiboxAlgorithmId = "";
  String algorithmName = "";

  DeviceAiBoxTaskConfigModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '';
    deviceId = json['deviceId'] ?? '';
    aiboxAlgorithmId = json['aiboxAlgorithmId'] ?? '';
    algorithmName = json['algorithmName'] ?? '';
  }
}

class DeviceAbilityModel {
  String abilityName = "";
  String abilityCode = "";
  String isAbility = "0";

  ///是否具有能力：“0”：表示不具有，“1”：表示具有

  DeviceAbilityModel.fromJson(Map<String, dynamic> json) {
    abilityName = json['abilityName'] ?? '';
    abilityCode = json['abilityCode'] ?? '';
    isAbility = json['isAbility'] ?? '0';
  }
}

class DeviceTagModel {
  String tagId = "";
  String tagName = "";
  String levelCodeTagCategoryStr = "";

  DeviceTagModel.fromJson(Map<String, dynamic> json) {
    tagId = json['tagId'] ?? "";
    tagName = json['tagName'] ?? '';
    levelCodeTagCategoryStr = json['levelCodeTagCategoryStr'] ?? '';
  }
}
