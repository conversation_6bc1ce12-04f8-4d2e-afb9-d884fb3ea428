import 'dart:async';
import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/config/authority_check_config.dart';
import 'package:bcloud/config/events.dart';
import 'package:bcloud/config/name_reg.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/device_add/ai_box/ai_box_channel_list_page.dart';
import 'package:bcloud/pages/device_tree/device_blocking_alarm_page.dart';
import 'package:bcloud/pages/device_tree/device_channel_list_page.dart';
import 'package:bcloud/pages/preview/about_device_page.dart';
import 'package:bcloud/pages/preview/device_alarm_setting/device_alarm_setting_page.dart';
import 'package:bcloud/pages/preview/device_setting_page.dart';
import 'package:bcloud/pages/preview/model/device.dart';
import 'package:bcloud/pages/preview/nvr_setting_pages/nvr_local_record.dart';
import 'package:bcloud/pages/preview/nvr_setting_pages/nvr_storage_card_management.dart';
import 'package:bcloud/pages/preview/passenger_flow_config.dart';
import 'package:bcloud/pages/preview/setting/storage_card_setting_page.dart';
import 'package:bcloud/public/network/net_extension_passenger_flow_statistics.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/utils/opera_log_util/opera_log_util.dart';
import 'package:bcloud/widget/x_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class DeviceSettingPageVM extends XBPageVM<DeviceSettingPage> {
  late StreamSubscription subscribe;
  bool isHasSmartAlert = false;
  bool isHasVideoBlocking = false;
  DeviceSettingPageVM({required super.context}) {
    if (widget.type == 0) {
      _request();
    } else {
      _requestNVR();
    }
    _addNotice();
  }

  _addNotice() {
    subscribe = eventBus.on<EventRefreshModifyPsd>().listen((event) {
      deviceTreeAPI.getDeviceInfo(widget.deviceId).then((value) {
        deviceInfo = value;
        notify();
      }).catchError((e) {});
    });
  }

  _request() {
    showLoading();
    Future.wait([
      deviceTreeAPI.getDeviceInfo(widget.deviceId).then((value) {
        deviceInfo = value;
        notify();
      }),
      deviceTreeAPI
          .queryDeviceCapabilityByDeviceIdAndType(
              type: 1, deviceId: widget.deviceId)
          .then((value) {
        isHasSmartAlert = value.deviceId != null ? true : false;
        notify();
      }),
      deviceTreeAPI
          .queryDeviceCapabilityByDeviceIdAndType(
              type: 2, deviceId: widget.deviceId)
          .then((value) {
        isHasVideoBlocking = value.deviceId != null ? true : false;
        notify();
      })
    ]).whenComplete(() => hideLoading()).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  _requestNVR() {
    showLoading();
    deviceTreeAPI.getDeviceInfo(widget.deviceId).then((value) {
      hideLoading();
      deviceInfo = value;
      notify();
    }).catchError((e) {
      hideLoading();
      toastFailure(kErrorMsg(e));
    });
  }

  //播放地址权限
  bool get isHasPlaybackAddress =>
      AuthorityCheckConfig().isHasConfigLiveAddress;

  pushToFlowConfig() {
    push(PassengerFlowConfig(deviceId: widget.deviceId));
  }

  List<int> get itemCounts {
    return _sectionTitleList.map((e) => e.length).toList();
  }

  List<String> titleListForSecion(int section) {
    return _sectionTitleList[section];
  }

  List<String> get _titleList {
    if (deviceInfo == null) {
      return [];
    }
    bool isJFDevice = deviceInfo != null && deviceInfo!.isJFDevice;
    bool isJFDeviceIPC = isJFDevice && deviceInfo!.accessType == 0;
    bool isAiBox= deviceInfo != null && deviceInfo!.accessType == 1 && deviceInfo!.deviceType == 4;
    bool isJFNvr= isJFDevice && deviceInfo!.accessType == 1;
    bool isJFNvrNotIPC = isJFDevice &&
        deviceInfo!.accessType == 1 &&
        deviceInfo!.channelNumber == null;
    bool isJFNvrIPC = isJFDevice &&
        deviceInfo!.accessType == 1 &&
        deviceInfo!.channelNumber != null;
    bool isNVR = deviceInfo != null && deviceInfo!.accessType == 1 &&
        deviceInfo!.channelNumber == null;
    bool isNVRDevice = deviceInfo != null && deviceInfo!.accessType == 1 &&
        deviceInfo!.channelNumber != null;
    List<String> ret = [];
    if (widget.type == 2) {
      // ret.add(TR.current.tr_DeviceName);
      ret.add(TR.current.tr_DeviceInfo);
      // ret.add(TR.current.tr_channelManagement); //功能暂时不发布
    } else if (widget.type == 1) {
      ret.add(TR.current.tr_DeviceName);
      ret.add(TR.current.tr_DeviceInfo);
      if (isJFNvrNotIPC) {
        ret.add(TR.current.tr_channelManagement);
        ret.add(TR.current.tr_localRecording);
        ret.add(TR.current.tr_storageCardManagement);
      }
      if ((isJFDeviceIPC || isJFNvr) &&
          (widget.isOnline ||
              (deviceInfo != null && deviceInfo!.runStatus == 1))) {
        ret.add(TR.current.tr_aboutDevice);
      }
    } else {
      if (deviceInfo != null) {
        if (isJFDevice) {
          ret.add(TR.current.tr_deviceConfiguration);
        } else {
          if (isAiBox) {
            //ret.add(TR.current.tr_edgeComputing); //功能暂时不发布
          } else {
            ret.add(TR.current.tr_DeviceName);
          }
        }
      }
      ret.add(TR.current.tr_DeviceInfo);
      if (AuthorityCheckConfig().isHasAlgorithmList) {
        ret.add(TR.current.tr_Algorithm);
      }
      if (isHasPlaybackAddress) {
        ret.add(TR.current.tr_videoPlayAddress);
      }

      if (deviceInfo == null || !deviceInfo!.suportFlowCount) {
      } else {
        ret.add(TR.current.tr_passengerFlowStatistics);
        if (isOn) {
          ret.add(TR.current.tr_passengerFlowPeerConfiguration);
        }
        ret.add(TR.current.tr_passengerFlowStatisticsData);
      }
      if ((isJFDeviceIPC || isJFNvr) &&
          (widget.isOnline ||
              (deviceInfo != null && deviceInfo!.runStatus == 1))) {
        ret.add(TR.current.tr_aboutDevice);
      }
      if (isHasSmartAlert) {
        ret.add(TR.current.tr_smartAlert);
      }
      if (isHasVideoBlocking) {
        ret.add(TR.current.tr_occlusionAlarm);
      }
      // 杰峰ipc需要存储卡设置入口
      if (isJFDeviceIPC) {
        ret.add(TR.current.tr_StorageCardSetting);
      }
    }

    /// 关于设备移动到最后面
    if (ret.contains(TR.current.tr_aboutDevice)) {
      ret.remove(TR.current.tr_aboutDevice);
      ret.add(TR.current.tr_aboutDevice);
    }
    return ret;
  }

  List<List<String>> get _sectionTitleList {
    List<List<String>> sectionTitleList = [];
    List<String> tempAllTitleList = _titleList;

    List<String> section1 = [];
    List<String> section2 = [];
    List<String> section3 = [];
    List<String> section4 = [];

    for (var element in tempAllTitleList) {
      if (element == TR.current.tr_DeviceInfo ||
          element == TR.current.tr_videoPlayAddress) {
        section1.add(element);
      } else if (element == TR.current.tr_Algorithm) {
        section2.add(element);
      } else if (element == TR.current.tr_passengerFlowStatistics ||
          element == TR.current.tr_passengerFlowPeerConfiguration ||
          element == TR.current.tr_passengerFlowStatisticsData ||
          element == TR.current.tr_edgeComputing
      ) {
        section3.add(element);
      } else {
        section4.add(element);
      }
    }

    sectionTitleList.add(section1);
    sectionTitleList.add(section2);
    sectionTitleList.add(section3);
    sectionTitleList.add(section4);

    return sectionTitleList;
  }

  bool get isOn => deviceInfo != null && deviceInfo!.isFlowOn;
  set isOn(bool newValue) {
    if (deviceInfo == null) {
      return;
    }
    if (deviceInfo!.runStatus != 1) {
      toastFailure(TR.current.tr_deviceOffline);
      return;
    }
    showLoading();
    NetQueryUtil.getInstance()
        .passengerFlowOpenOrClose(
            deviceId: widget.deviceId, openStatus: newValue ? 1 : 0)
        .then((value) {
      hideLoading();
      deviceInfo!.updateFlowOn(newValue);
      notify();
    }).catchError((e) {
      hideLoading();
      toastFailure(kErrorMsg(e));
    });
  }

  onChangedState() {
    isOn = !isOn;
  }

  String get deviceName => deviceInfo?.deviceName ?? '';

  final TextEditingController nameEditingController = TextEditingController();
  final TextEditingController psdEditingController = TextEditingController();
  DeviceInfo? deviceInfo;

  void changeDeviceName() async {
    if (deviceInfo == null) {
      return;
    }
    nameEditingController.clear();
    if (deviceName.isNotEmpty) {
      nameEditingController.value =
          nameEditingController.value.copyWith(text: deviceName);
      nameEditingController.selection =
          TextSelection.fromPosition(TextPosition(offset: deviceName.length));
    }
    dialogContent(
        title: TR.current.tr_ModifyDeviceNickName,
        content: Padding(
          padding: EdgeInsets.only(left: spaces.left, right: spaces.left),
          child: XTextField(
            autofocus: true,
            hintText: TR.current.tr_InputDeviceNickName,
            maxLength: 64,
            inputFormatters: [
              InputContentFormatter(filterPattern: RegExp(nameReg2)),
            ],
            isShowClearIcon: true,
            borderRadius: BorderRadius.circular(6),
            onValueChanged: (text) {},
            controller: nameEditingController,
          ),
        ),
        btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
        btnHighLightColor: colors.blue,
        onSelected: (dlIndex) async {
          if (dlIndex == 1) {
            try {
              String deviceName = nameEditingController.text.trim();
              if (deviceName.isEmpty) {
                toast(TR.current.tr_ErrorCode_Minus_213605);
                return;
              }
              await deviceTreeAPI.editDevice(deviceInfo!.id, deviceInfo!.nodeId,
                  deviceInfo!.deviceSerialNumber,
                  name: deviceName,
                  userName: deviceInfo!.deviceUsername,
                  password: deviceInfo!.devicePassword);

              deviceInfo!.deviceName = deviceName;
              notify();
              toastSuccess(TR.current.tr_ModifySuccess);
              eventBus.fire(ChangeDeviceNameEvent(deviceName: deviceName));
            } catch (e) {
              toastFailure(kErrorMsg(e));
            }
          }
        });
  }

  void deleteDevice() async {
    checkPassword(widget.deviceId, (password) {
      if (password != null && password.isEmpty) {
        return;
      }
      dialog(
          title: TR.current.tr_MakeSureDeleteDevice,
          msg: TR.current.tr_DeleteDeviceTips,
          btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
          btnHighLightColor: colors.blue,
          onSelected: (dlIndex) async {
            if (dlIndex == 1) {
              await deviceTreeAPI.deleteDevice(widget.deviceId,
                  password: password);
              OperaLogUtil.videoMonitor.logDeleteDevice(deviceName: deviceName);
              toastSuccess(TR.current.tr_Common_DeleteSuccess);
              await Future.delayed(const Duration(milliseconds: 500));
              eventBus.fire(DeleteDeviceEvent());
              popToRoot();
              eventBus.fire(EventRefreshHomePage());
            }
          });
    });
  }

  ///返回  null 是不需要校验
  ///返回 '' 是点击了取消
  ///返回 'password' 是输入了密码
  checkPassword(String id, ValueChanged onDone) async {
    showLoadingGlobal();
    psdEditingController.clear();
    bool pass = await deviceTreeAPI.queryIdentify(int.parse(id));
    hideLoadingGlobal();
    if (!pass) {
      dialogContent(
          title: TR.current.tr_Prompt,
          content: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFFDCDEE3), // 边框颜色
                width: onePixel, // 边框宽度
              ),
              borderRadius: BorderRadius.circular(3.r),
              color: Colors.white,
            ),
            child: XTextField(
              height: 40,
              hintText: TR.current.tr_Common_InputPassword,
              borderRadius: BorderRadius.circular(3.r),
              backgroundColor: Colors.white,
              onValueChanged: (text) {},
              controller: psdEditingController,
            ),
          ),
          btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
          btnHighLightColor: colors.blue,
          onSelected: (dlIndex) {
            if (dlIndex == 0) {
              onDone('');
            } else {
              onDone(psdEditingController.text);
            }
          });
    } else {
      onDone(null);
    }
  }

  goNextAboutDevicePage() {
    if (deviceInfo == null) {
      return;
    }
    if (deviceInfo!.runStatus != 1) {
      toastFailure(TR.current.tr_deviceOffline);
      return;
    }
    bool isJFNvrIPC = deviceInfo != null && deviceInfo!.isJFDevice &&
        deviceInfo!.accessType == 1 &&
        deviceInfo!.channelNumber != null;
    push(AboutDevicePage(
      initDeviceInfo: deviceInfo!,
      type: isJFNvrIPC ? 2 : widget.type,
    ));
  }

  goNextAlarmSettingPage() {
    if (deviceInfo == null) {
      return;
    }
    if (deviceInfo!.runStatus != 1) {
      toastFailure(TR.current.tr_deviceOffline);
      return;
    }
    push(DeviceAlarmSettingPage(deviceId: widget.deviceId));
  }

  goNextBlockingAlarmPage() {
    if (deviceInfo == null) {
      return;
    }
    if (deviceInfo!.runStatus != 1) {
      toastFailure(TR.current.tr_deviceOffline);
      return;
    }
    push(DeviceBlockingAlarmPage(deviceId: widget.deviceId));
  }

  goNextStorageCardPage() {
    if (deviceInfo == null) {
      return;
    }
    if (deviceInfo!.runStatus != 1) {
      toastFailure(TR.current.tr_deviceOffline);
      return;
    }
    push(StorageCardSettingPage(deviceId: widget.deviceId));
  }

  goChannelManagementPage() {
    if (deviceInfo == null) {
      return;
    }
    if (widget.type == 2) {
      push(AiBoxChannelListPage(
        deviceId: widget.deviceId,
        nodeId: deviceInfo!.nodeId,
        deviceSN: deviceInfo!.deviceSerialNumber,
      ));
    } else {
      push(DeviceChannelListPage(
        deviceId: widget.deviceId,
        nodeId: deviceInfo!.nodeId,
      ));
    }
  }

  goToNvrLocalRecordPage() {
    push(NvrLocalRecord(deviceId: widget.deviceId));
  }

  goToNvrStorageCardManagementPage() {
    push(NvrStorageCardManagement(deviceId: widget.deviceId));
  }

  @override
  void dispose() {
    subscribe.cancel();
    nameEditingController.dispose();
    psdEditingController.dispose();
    super.dispose();
  }
}
