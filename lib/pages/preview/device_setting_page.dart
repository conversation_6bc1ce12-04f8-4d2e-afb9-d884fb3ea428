import 'package:bcloud/config/authority_check_config.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/arithmetics/arithmetics_ai_box_arithmetics_list_page.dart';
import 'package:bcloud/pages/arithmetics/arithmetics_device_contain_arithmetics_list.dart';
import 'package:bcloud/pages/preview/device_config/device_config_page.dart';
import 'package:bcloud/pages/preview/device_info_page.dart';
import 'package:bcloud/pages/preview/device_play_address_page.dart';
import 'package:bcloud/pages/preview/device_setting_page_vm.dart';
import 'package:bcloud/pages/preview/passenger_flow_data_page.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/widget/cell/title_sub_title_cell.dart';
import 'package:bcloud/widget/cell/title_sub_title_right_icon_cell.dart';
import 'package:bcloud/widget/cell/title_switch_cell.dart';
import 'package:bcloud/widget/x_button.dart';
import 'package:bcloud/widget/xb_clip_r_rect.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class DeviceSettingPage extends XBPageOver<DeviceSettingPageVM> {
  final String deviceId;
  final String deviceName;
  final bool isOnline;
  final int type; // 0 设备 1 NVR节点 2 盒子节点
  final int? nodeId;
  final int? isMultiLevel; // 0 -非多目，1-多目本级 2-多目子级
  const DeviceSettingPage(
      {required this.deviceId,
      required this.deviceName,
      this.isOnline = true,
      this.type = 0,
      this.nodeId,
      this.isMultiLevel = 0,
      super.key});

  @override
  generateVM(BuildContext context) {
    return DeviceSettingPageVM(context: context);
  }

  @override
  String setTitle(DeviceSettingPageVM vm) {
    return TR.current.tr_NormalSetting;
  }

  @override
  bool needLoading(DeviceSettingPageVM vm) {
    return true;
  }

  @override
  bool needInitLoading(DeviceSettingPageVM vm) {
    return true;
  }

  double get height => 55;

  double get titleWidth => 100;

  @override
  Widget buildPage(DeviceSettingPageVM vm, BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: spaces.left, right: spaces.left),
      child: Column(
        children: [
          Expanded(
            child: XBHoveringHeaderList(
              ///分组信息，每组有几个item
              itemCounts: vm.itemCounts,

              ///header builder
              sectionHeaderBuild: (ctx, section) {
                return Container(
                    // color: colors.randColor,
                    );
              },

              ///header高度
              headerHeightForSection: (section) {
                int itemCount = vm.itemCounts[section];
                if (itemCount == 0) return 0;
                return spaces.leftLess;
              },

              ///item builder
              itemBuilder: (ctx, indexPath, height) {
                List<String> titleList =
                    vm.titleListForSecion(indexPath.section);
                bool isFirst = indexPath.index == 0;
                bool isLast = indexPath.index == titleList.length - 1;
                final title = titleList[indexPath.index];
                Widget? child;
                if (title == TR.current.tr_DeviceName) {
                  child = Container(
                    color: colors.white,
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: spaces.left, right: spaces.left),
                      child: AuthorityCheckConfig().isHasConfigDeviceOptModify
                          ? XBButton(
                              onTap: vm.changeDeviceName,
                              child: TitleSubTitleRightIconCell(
                                  titleWidth: titleWidth,
                                  title: title,
                                  subTitle: vm.deviceName),
                            )
                          : TitleSubTitleCell(
                              titleWidth: titleWidth,
                              title: title,
                              subTitle: vm.deviceName),
                    ),
                  );
                } else if (title == TR.current.tr_deviceConfiguration) {
                  child = XBButton(
                    onTap: () {
                      if (vm.deviceInfo!.runStatus != 1) {
                        toastFailure(TR.current.tr_deviceOffline);
                        return;
                      }
                      push(DeviceConfigPage(
                        deviceInfo: vm.deviceInfo!,
                      ));
                    },
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 130, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_DeviceInfo) {
                  child = XBButton(
                    onTap: () {
                      push(DeviceInfoPage(
                        deviceId: deviceId,
                        isMultiLevel: isMultiLevel,
                      ));
                    },
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 130, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_Algorithm) {
                  child = XBButton(
                    onTap: () {
                      push(ArithmeticsDeviceContainArithmeticsList(
                          deviceId: deviceId,
                          deviceName: deviceName,
                          pageTitle: vm.deviceInfo?.deviceName ?? '',
                          isSupportClientCloudFlow:
                              vm.deviceInfo?.supportClientCloudFlow ?? false,
                          isSupportFlowHotArea:
                              vm.deviceInfo?.supportFlowHotArea ?? false));
                    },
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: titleWidth, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_edgeComputing) {
                  child = XBButton(
                    onTap: () {
                      if (vm.deviceInfo == null) {
                        return;
                      }
                      push(ArithmeticsAiBoxArithmeticsListPage(
                          deviceId: deviceId,
                          deviceName: vm.deviceInfo!.deviceName,
                          parentId: vm.deviceInfo!.parentId,
                      ));
                    },
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: titleWidth, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_videoPlayAddress) {
                  child = XBButton(
                    onTap: () {
                      push(DevicePlayAddressPage(
                        deviceId: deviceId,
                        deviceName: deviceName,
                      ));
                    },
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 130, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_passengerFlowStatistics) {
                  child = Container(
                    color: colors.white,
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: spaces.left, right: spaces.left),
                      child: TitleSwitchCell(
                          title: title,
                          isOn: vm.isOn,
                          onTap: () {},
                          onChangedState: vm.onChangedState),
                    ),
                  );
                } else if (title ==
                    TR.current.tr_passengerFlowPeerConfiguration) {
                  child = XBButton(
                    onTap: vm.pushToFlowConfig,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: titleWidth, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_passengerFlowStatisticsData) {
                  child = XBButton(
                    onTap: () {
                      push(PassengerFlowDataPage(deviceId: deviceId));
                    },
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_aboutDevice) {
                  child = XBButton(
                    onTap: vm.goNextAboutDevicePage,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_smartAlert) {
                  child = XBButton(
                    onTap: vm.goNextAlarmSettingPage,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_occlusionAlarm) {
                  child = XBButton(
                    onTap: vm.goNextBlockingAlarmPage,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_StorageCardSetting) {
                  child = XBButton(
                    onTap: vm.goNextStorageCardPage,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_channelManagement) {
                  child = XBButton(
                    onTap: vm.goChannelManagementPage,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_localRecording) {
                  child = XBButton(
                    onTap: vm.goToNvrLocalRecordPage,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                } else if (title == TR.current.tr_storageCardManagement) {
                  child = XBButton(
                    onTap: vm.goToNvrStorageCardManagementPage,
                    child: Container(
                      color: colors.white,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: spaces.left, right: spaces.left),
                        child: TitleSubTitleRightIconCell(
                            titleWidth: 200, title: title, subTitle: ""),
                      ),
                    ),
                  );
                }
                child ??= Container();

                return XBClipRRect(
                  top: 6,
                  bottom: 6,
                  needClipTop: isFirst,
                  needClipBottom: isLast,
                  child: child,
                );
              },

              ///item高度
              itemHeightForIndexPath: (indexPath) {
                return height;
              },

              ///分割线builder
              separatorBuilder: (ctx, indexPath, height, isLast) {
                List<String> titleList =
                    vm.titleListForSecion(indexPath.section);
                bool isLast = indexPath.index == titleList.length - 1;
                if (isLast) return Container();
                return Container(
                  color: colors.line2,
                );
              },

              ///分割线高度
              separatorHeightForIndexPath: (indexPath, isLast) {
                return onePixel;
              },

              ///滚动到底部和离开底部的回调
              onEndChanged: (end) {
                //          print("end : $end");
              },

              ///offset改变回调
              onOffsetChanged: (offset, maxOffset) {
                //        print("111111:offset : $offset");
              },

              ///滚动到顶部和离开顶部的回调
              onTopChanged: (top) {
                //          print("top:$top");
              },

              ///是否需要悬停header
              hover: false,

              //  needSafeArea: true,
            ),
          ),

          /// 删除按钮
          Visibility(
            visible: AuthorityCheckConfig().isHasConfigDeviceOptRemove &&
                (isMultiLevel ?? 0) != 2,
            child: XButton(
              onTap: () {
                vm.deleteDevice();
              },
              text: TR.current.tr_DeleteDevice,
              margin: EdgeInsets.only(
                  left: spaces.left,
                  right: spaces.left,
                  top: spaces.left,
                  bottom: spaces.left + safeAreaBottom),
              textStyle:
                  TextStyle(fontSize: fontSizes.s16, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
