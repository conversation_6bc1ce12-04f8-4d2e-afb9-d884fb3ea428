import 'dart:async';
import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/config/name_reg.dart';
import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/config/authority_check_config.dart';
import 'package:bcloud/config/events.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/device_attribute/device_attribute_page.dart';
import 'package:bcloud/pages/device_tree/model/device_tree.dart';
import 'package:bcloud/pages/function/amap/amap_search_address_page.dart';
import 'package:bcloud/pages/function/amap/model/gd_map_place_around_model.dart';
import 'package:bcloud/pages/preview/model/device.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/utils/opera_log_util/opera_log_util.dart';
import 'package:bcloud/widget/x_dialog.dart';
import 'package:bcloud/widget/x_node_cascade.dart';
import 'package:bcloud/widget/x_text_field.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'view/channel_info_change_widget.dart';
import 'device_info_page.dart';
import 'device_info_psd_page.dart';
import 'device_link_type_model.dart';

class DeviceInfoVM extends XBPageVM<DeviceInfoPage> {
  final Map<String, String> abilityCodeMap = {
    "talkBroadcast": images.ic_talk_broadcast,
    "talk": images.ic_audio_talk,
    "flowCount": images.ic_flow_count,
    "cloudControl": images.ic_cloud_control,
    "preciseFlow": images.ic_precise_flow,
    //"humanoidDetection": images.ic_humanoid_detection, //人形检测
    "hot": images.ic_passenger_flow_hot_spots, //客流热区
    "dect": images.ic_passenger_motion_detection, //移动侦测
    "blind": images.icon_video_blocking, //视频遮挡
    "intellAlert": images.ic_intell_alert, //智能警戒
  };
  final Map<String, String> abilityGrayCodeMap = {
    "talkBroadcast": images.ic_talk_broadcast_gray,
    "talk": images.ic_audio_talk_gray,
    "flowCount": images.ic_flow_count_gray,
    "cloudControl": images.ic_cloud_control_gray,
    "preciseFlow": images.ic_precise_flow_gray,
    //"humanoidDetection": images.ic_humanoid_detection_gray,
    "hot": images.ic_passenger_flow_hot_spots_gray,
    "dect": images.ic_passenger_motion_detection_gray,
    "blind": images.icon_video_blocking_gray,
    "intellAlert": images.ic_intell_alert_gray,
  };
  List<SettingTitleInfo>? infoList;
  late StreamSubscription subscribe;
  final String deviceId;
  final TreeNode? treeNode;
  DeviceInfo deviceInfo = DeviceInfo();
  bool isEdit = false;
  DeviceLinkTypeModel? linkTypeModel;
  final TextEditingController editingController = TextEditingController();

  DeviceInfoVM(
      {required super.context, required this.deviceId, this.treeNode}) {
    _request();
    _addNotice();
  }

  _addNotice() {
    subscribe = eventBus.on<EventRefreshModifyPsd>().listen((event) {
      queryDeviceInfo();
    });
  }

  _reloadDeviceInfo({bool isNeedNotify = true}) {
    infoList = _parseDeviceInfo(deviceInfo);
    if (isNeedNotify) {
      notify();
    }
  }

  bool get isOnline => deviceInfo.runStatus == 1;

  bool get isSupportTCP => (linkTypeModel?.tcp ?? 0) == 1;

  bool get isSupportUDP => (linkTypeModel?.udp ?? 0) == 1;

  Future<dynamic> queryDeviceInfo({bool isNeedNotify = true}) async {
    return await deviceTreeAPI.getDeviceInfo(deviceId).then((value) {
      deviceInfo = value;
      infoList = _parseDeviceInfo(deviceInfo);
      _reloadDeviceInfo(isNeedNotify: isNeedNotify);
      return value;
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  refreshDeviceAbility() async {
    showLoading();
    await deviceTreeAPI.refreshDeviceAbility([deviceId]).then((value) {
      hideLoading();
      toast(TR.current.tr_refreshed);
      deviceInfo.deviceAbilityList = value.deviceAbilityList;
      infoList = _parseDeviceInfo(deviceInfo);
      _reloadDeviceInfo();
    }).catchError((onError) {});
  }

  Future<dynamic> queryDeviceLinkType({bool isNeedNotify = true}) async {
    return await deviceTreeAPI.getDeviceLinkType(deviceId).then((value) {
      linkTypeModel = DeviceLinkTypeModel.fromJson(value);
      _reloadDeviceInfo(isNeedNotify: isNeedNotify);
    }).catchError((onError) {});
  }

  _request() async {
    showLoading();
    Future.wait([
      queryDeviceInfo(isNeedNotify: false),
      queryDeviceLinkType(isNeedNotify: false),
    ]).whenComplete(() => hideLoading());
  }

  List<SettingTitleInfo> _parseDeviceInfo(DeviceInfo info) {
    bool isIPC = info.accessType == 0;
    bool isNvr = info.accessType == 1;
    bool isPlatform = info.accessType == 3;
    bool isJFDevice = info.deviceType == 2;
    bool isOnvif = info.deviceType == 1;
    bool isAiBox = info.deviceType == 4;
    bool isDevice = info.channelNumber != null;
    List<SettingTitleInfo> settingInfoList = [];
    List<SettingItemInfo> baseInfo = [];
    List<SettingItemInfo> addressInfo = [];
    List<SettingItemInfo> deviceAbilityInfo = [];

    if (isAiBox && !isDevice) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessType, info: TR.current.tr_aiBox));
    }
    if (info.subtypes == 2) {
      List<SettingItemInfo> accountInfo = [];
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessProtocol,
          info: info.linkType == 0 ? 'UDP' : 'TCP',
          isShowNext: isEdit && isSupportTCP && isSupportUDP,
          onTapType: 3));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessType, info: info.accessTypeString));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessIds,
          info: info.directoryId,
          type: 3,
          onTapType: 6));

      if ((isNvr && info.channelNumber != null) ||
          (info.syncDataType == 1 && info.accessType != 3)) {
        accountInfo.add(SettingItemInfo(
            key: TR.current.tr_DeviceAccessIDPwd,
            info: info.accessIdPwd,
            type: 3,
            onTapType: 6));
      } else {
        accountInfo.add(SettingItemInfo(
            key: TR.current.tr_DeviceAccessIDPwd,
            info: info.accessIdPwd,
            isShowNext: info.syncDataType == 0 ? isEdit : false,
            type: 3,
            onTapType: info.syncDataType == 0 ? 8 : 6));
      }
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_SIPServerId,
          info: info.sipServerNum,
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_SIPServerIP,
          info: info.sipServer,
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_serverPortNum,
          info: '${info.serverPortNum}',
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_SIPServerDomain2,
          info: info.sipDomain,
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_CancellationValidityPeriod,
          info: info.registerExpiry));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_HeartbeatCycle, info: info.heartbeatCycle));

      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_IPCType, info: TR.current.tr_multi));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_channelNum, info: "${info.channelList.length}"));
      accountInfo.add(widget.isMultiLevel == 1
          ? SettingItemInfo(
              key: TR.current.tr_AccessNode,
              info: info.levelCodeStr,
              isShowNext: isEdit,
              onTapType: 5)
          : SettingItemInfo(
              key: TR.current.tr_AccessNode, info: info.levelCodeStr));

      SettingTitleInfo accountSettingTitleInfo = SettingTitleInfo(
          title: TR.current.tr_accessInfo, infoList: accountInfo);
      settingInfoList.add(accountSettingTitleInfo);

      if (info.channelList.isNotEmpty) {
        int startIndex = 1;
        for (DeviceInfo channelInfo in info.channelList) {
          baseInfo.add(SettingItemInfo(
              key: TR.current.tr_channelNumInfo(startIndex++),
              info: "",
              type: 6));
          baseInfo.add(SettingItemInfo(
            key: TR.current.tr_channelName,
            info: channelInfo.deviceName,
          ));
          baseInfo.add(SettingItemInfo(
              key: TR.current.tr_channelStatus,
              info: channelInfo.runStatus == 1
                  ? TR.current.tr_DeviceStateOnline
                  : TR.current.tr_DeviceStateOffline,
              type: 1,
              status: info.runStatus == 1 ? 1 : 0));
          baseInfo.add(SettingItemInfo(
            key: TR.current.tr_DeviceChannelID,
            info: channelInfo.accessId,
          ));
          baseInfo.add(SettingItemInfo(
              key: TR.current.tr_channelSN,
              info: channelInfo.deviceSerialNumber,
              type: channelInfo.deviceTagStr.isEmpty ? 7 : 0));
          if (channelInfo.deviceTagStr.isNotEmpty) {
            baseInfo.add(SettingItemInfo(
                key: TR.current.tr_channelLabel,
                info: channelInfo.deviceTagStr,
                type: 7));
          }
        }
        SettingTitleInfo baseSettingTitleInfo = SettingTitleInfo(
            title: widget.isMultiLevel == 1
                ? TR.current.tr_channelInfo
                : TR.current.tr_BasicInfo,
            infoList: baseInfo);
        settingInfoList.add(baseSettingTitleInfo);
      }

      if (info.location.isNotEmpty) {
        if (info.syncDataType == 1 && info.accessType != 3) {
          addressInfo.add(SettingItemInfo(
              key: TR.current.tr_location, info: info.location));
        } else {
          addressInfo.add(SettingItemInfo(
              key: TR.current.tr_location,
              info: info.location,
              isShowNext: isEdit,
              onTapType: 4));
        }
        if (isNvr && info.channelNumber == null) {
          addressInfo.add(SettingItemInfo(
              key: TR.current.tr_syncSubordinateChannels,
              info: '${info.isSyncPosition}',
              type: 4,
              isShowNext: false,
              onTapType: 9));
        }
      }
      if (addressInfo.isNotEmpty) {
        SettingTitleInfo addressSettingTitleInfo = SettingTitleInfo(
            title: TR.current.tr_locationInfo, infoList: addressInfo);
        settingInfoList.add(addressSettingTitleInfo);
        if (widget.isMultiLevel == 1) {
          addressInfo.add(SettingItemInfo(
              key: TR.current.tr_syncSubordinateChannels,
              info: '${info.isSyncPosition}',
              type: 4,
              isShowNext: false,
              onTapType: 9));
        }
      }
      return settingInfoList;
    }

    //基础信息
    if (isJFDevice) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessType, info: info.accessTypeString));
    }
    baseInfo.add(SettingItemInfo(
        key: TR.current.tr_DeviceName,
        info: info.deviceName,
        isShowNext: isEdit,
        onTapType: 1));
    if (info.deviceType == 0 && (isNvr || isPlatform)) {
      if (info.accessId.isNotEmpty) {
        baseInfo.add(SettingItemInfo(
            key: TR.current.tr_DeviceChannelID, info: info.accessId));
      }
    }
    baseInfo.add(SettingItemInfo(
        key: TR.current.tr_DeviceState,
        info: info.runStatus == 1
            ? TR.current.tr_DeviceStateOnline
            : TR.current.tr_DeviceStateOffline,
        type: 1,
        status: info.runStatus == 1 ? 1 : 0));

    if (isJFDevice && isNvr) {
      if (info.accessId.isNotEmpty) {
        baseInfo.add(SettingItemInfo(
            key: TR.current.tr_DeviceChannelID, info: info.accessId));
      }
    }

    baseInfo.add(SettingItemInfo(
        key: TR.current.tr_detailUuid,
        info: info.deviceSerialNumber,
        type: 3,
        onTapType: 6));
    // if (isJFDevice) {
    //   if (info.versionModel != null) {
    //     baseInfo.add(SettingItemInfo(
    //         key: TR.current.tr_dasProtocol,
    //         info: info.versionModel?.netDAS ?? TR.current.tr_notSupported));
    //   }
    // }

    if (isOnvif) {
      if (info.deviceNetworkType == 3 || info.deviceNetworkType == 4) {
        String type = "";
        if (info.deviceNetworkType == 3) {
          type = TR.current.tr_pcGateway;
        } else if (info.deviceNetworkType == 4) {
          type = TR.current.tr_serverGateway;
        }
        baseInfo
            .add(SettingItemInfo(key: TR.current.tr_gatewayType, info: type));
      }
      baseInfo.add(
          SettingItemInfo(key: TR.current.tr_gatewaySN, info: info.gatewaySN));

      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_gatewayStatus,
          info: info.gatewayStatus == 1
              ? TR.current.tr_DeviceStateOnline
              : TR.current.tr_DeviceStateOffline,
          type: 1,
          status: info.gatewayStatus == 1 ? 1 : 0));
    }
    if ((isNvr && info.channelNumber == null) ||
        (info.syncDataType == 1 && isPlatform)) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_DeviceNode,
          info: info.levelCodeStr,
          isShowNext: isEdit,
          onTapType: 5));
    } else if ((isNvr && info.channelNumber != null) ||
        (info.syncDataType == 1 && info.accessType != 3)) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_DeviceNode, info: info.levelCodeStr));
    } else {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_DeviceNode,
          info: info.levelCodeStr,
          isShowNext: isEdit,
          onTapType: 2));
    }
    if (info.deviceTagStr.isNotEmpty) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_deviceTag,
          info: info.deviceTagStr,
          isShowNext: isEdit,
          onTapType: 7));
    }
    if ((isJFDevice && (isNvr || isPlatform)) || isAiBox) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_DeviceAccessChannelNum,
          info: isAiBox ? '${info.actualAccessChannels}/${info.accessChannels}' : '${info.actualAccessChannels}'));
    }

    if (isAiBox && !isDevice) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_softwareVersion, info: info.softWareVersion ?? ''));
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_softwareVersionReleaseDate, info: info.buildTime ?? ''));
    }

    if (isAiBox && isDevice && info.deviceAiBoxTaskConfigStr.isNotEmpty) {
      baseInfo.add(SettingItemInfo(
          key: TR.current.tr_supportAlgorithms,
          info: info.deviceAiBoxTaskConfigStr,
          type: 7));
    }

    if (isOnvif ||
        (isJFDevice && (info.accessType == 0 || info.accessType == 1)) || isAiBox) {
      SettingTitleInfo baseSettingTitleInfo =
          SettingTitleInfo(title: TR.current.tr_BasicInfo, infoList: baseInfo);
      settingInfoList.add(baseSettingTitleInfo);
    }

    if (isAiBox || isJFDevice || isOnvif) {
      List<SettingItemInfo> accountInfo = [];
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_Account, info: info.deviceUsername));

      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_Common_Password, info: info.devicePassword));
      if (isNvr && info.channelNumber != null) {
        accountInfo.add(SettingItemInfo(
            key: TR.current.tr_psdKeyVerify,
            info: info.keyVerify,
            type: 1,
            status: info.keyVerify == TR.current.tr_failure ? 0 : 1
        ));
      } else {
        accountInfo.add(SettingItemInfo(
            key: TR.current.tr_psdKeyVerify,
            info: info.keyVerify,
            isShowNext: isEdit && (!isOnvif),
            type: 1,
            status: info.keyVerify == TR.current.tr_failure ? 0 : 1));
      }

      // if (isNvr) {
      //   accountInfo.add(SettingItemInfo(
      //       key: TR.current.tr_DeviceAccessChannelNum,
      //       info: info.accessChannels.toString()));
      // }
      SettingTitleInfo accountSettingTitleInfo = SettingTitleInfo(
          title: TR.current.tr_BasicInfo,
          infoList: accountInfo,
          isShowTitle: false);
      settingInfoList.add(accountSettingTitleInfo);
    } else if (info.deviceType == 0) {
      //国标
      List<SettingItemInfo> accountInfo = [];
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessProtocol,
          info: info.linkType == 0 ? 'UDP' : 'TCP',
          isShowNext: isEdit && isSupportTCP && isSupportUDP,
          onTapType: 3));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessType, info: info.accessTypeString));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_AccessIds,
          info: info.directoryId,
          type: 3,
          onTapType: 6));

      if ((isNvr && info.channelNumber != null) ||
          (info.syncDataType == 1 && info.accessType != 3)) {
        accountInfo.add(SettingItemInfo(
            key: TR.current.tr_DeviceAccessIDPwd,
            info: info.accessIdPwd,
            type: 3,
            onTapType: 6));
      } else {
        accountInfo.add(SettingItemInfo(
            key: TR.current.tr_DeviceAccessIDPwd,
            info: info.accessIdPwd,
            isShowNext: info.syncDataType == 0 ? isEdit : false,
            type: 3,
            onTapType: info.syncDataType == 0 ? 8 : 6));
      }
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_SIPServerId,
          info: info.sipServerNum,
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_SIPServerIP,
          info: info.sipServer,
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_serverPortNum,
          info: '${info.serverPortNum}',
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_SIPServerDomain2,
          info: info.sipDomain,
          type: 3,
          onTapType: 6));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_CancellationValidityPeriod,
          info: info.registerExpiry));
      accountInfo.add(SettingItemInfo(
          key: TR.current.tr_HeartbeatCycle, info: info.heartbeatCycle));
      // if (info.accessType != 0) {
      //   accountInfo.add(SettingItemInfo(
      //       key: TR.current.tr_serverActualAccessChannelRate,
      //       info: info.serverActualAccessChannelRateStr));
      // }
      if (isNvr || isPlatform) {
        accountInfo.add(SettingItemInfo(
            key: TR.current.tr_serverActualAccessChannelRate,
            info: info.serverActualAccessChannelRateStr));
      }
      SettingTitleInfo accountSettingTitleInfo = SettingTitleInfo(
          title: TR.current.tr_accessInfo, infoList: accountInfo);
      settingInfoList.add(accountSettingTitleInfo);

      SettingTitleInfo baseSettingTitleInfo =
          SettingTitleInfo(title: TR.current.tr_BasicInfo, infoList: baseInfo);
      settingInfoList.add(baseSettingTitleInfo);
    }
    if (info.location.isNotEmpty) {
      if ( //(isNvr && info.channelNumber != null) ||
          (info.syncDataType == 1 && info.accessType != 3)) {
        addressInfo.add(
            SettingItemInfo(key: TR.current.tr_location, info: info.location));
      } else {
        addressInfo.add(SettingItemInfo(
            key: TR.current.tr_location,
            info: info.location,
            isShowNext: isEdit,
            onTapType: 4));
      }
      if (isNvr && info.channelNumber == null) {
        addressInfo.add(SettingItemInfo(
            key: TR.current.tr_syncSubordinateChannels,
            info: '${info.isSyncPosition}',
            type: 4,
            isShowNext: false,
            onTapType: 9));
      }
    }
    if (addressInfo.isNotEmpty) {
      SettingTitleInfo addressSettingTitleInfo = SettingTitleInfo(
          title: TR.current.tr_locationInfo, infoList: addressInfo);
      settingInfoList.add(addressSettingTitleInfo);
    }

    if (isJFDevice &&
        (isIPC || (isNvr && info.channelNumber != null)) &&
        info.deviceAbilityList.isNotEmpty) {
      for (var element in info.deviceAbilityList) {
        deviceAbilityInfo.add(SettingItemInfo(
            key: element.abilityName,
            info: element.abilityCode,
            type: 2,
            status: element.isAbility == '1' ? 1 : 0));
      }
    }

    if (deviceAbilityInfo.isNotEmpty) {
      SettingTitleInfo abilitySettingTitleInfo = SettingTitleInfo(
          title: TR.current.tr_deviceCapacity,
          infoList: deviceAbilityInfo,
          type: 1,
          subType: 1);
      settingInfoList.add(abilitySettingTitleInfo);
    }

    //国标 展示通道
    //if (info.channelList.isNotEmpty && info.deviceType == 0) {
    if (info.channelList.isNotEmpty && info.accessType != 0) {
      List<SettingItemInfo> channelInfoList = [];
      int startIndex = 0;
      channelInfoList.addAll(info.channelList.map((e) => SettingItemInfo(
          key: "${TR.current.tr_DeviceChannelID}_${startIndex++}",
          //info: info.deviceType == 1 ? info.accessId : info.deviceSerialNumber)
          info: e.deviceName,
          type: 5,
          status: e.runStatus == 1 ? 1 : 0)));
      if (info.deviceType == 0 && info.accessType == 1) {
        SettingTitleInfo channelSettingTitleInfo = SettingTitleInfo(
            title: TR.current.tr_channelInfo,
            isSpecialTitle: true,
            infoList: channelInfoList);
        settingInfoList.add(channelSettingTitleInfo);
      } else {
        SettingTitleInfo channelSettingTitleInfo = SettingTitleInfo(
            title: TR.current.tr_channelInfo,
            isSpecialTitle: false,
            infoList: channelInfoList);
        settingInfoList.add(channelSettingTitleInfo);
      }
    }

    return settingInfoList;
  }

  String getAbilityImagePath(String abilityCode, int status) {
    if (status == 0) {
      return abilityGrayCodeMap[abilityCode] ?? '';
    }
    return abilityCodeMap[abilityCode] ?? '';
  }

  changeDeviceName() async {
    editingController.clear();
    String deviceName = deviceInfo.deviceName;
    editingController.value =
        editingController.value.copyWith(text: deviceName);
    editingController.selection =
        TextSelection.fromPosition(TextPosition(offset: deviceName.length));
    dialogContent(
        title: TR.current.tr_ModifyDeviceNickName,
        content: Padding(
          padding: EdgeInsets.only(left: spaces.left, right: spaces.left),
          child: XTextField(
            autofocus: true,
            hintText: TR.current.tr_InputDeviceNickName,
            maxLength: 64,
            inputFormatters: [
              InputContentFormatter(filterPattern: RegExp(nameReg2)),
            ],
            isShowClearIcon: true,
            borderRadius: BorderRadius.circular(6),
            onValueChanged: (text) {},
            controller: editingController,
          ),
        ),
        btnTitles: [TR.current.tr_Cancel, TR.current.tr_CommonSave],
        btnHighLightColor: colors.blue,
        onSelected: (dlIndex) async {
          if (dlIndex == 1) {
            try {
              String deviceName = editingController.text.trim();
              if (deviceName.isEmpty) {
                toast(TR.current.tr_ErrorCode_Minus_213605);
                return;
              }
              await deviceTreeAPI.editDeviceName(
                deviceInfo.id,
                deviceInfo.nodeId,
                deviceInfo.deviceSerialNumber,
                name: deviceName,
              );
              OperaLogUtil.videoMonitor
                  .logModifyDeviceName(deviceName: deviceName);

              deviceInfo.deviceName = deviceName;
              notify();
              toastSuccess(TR.current.tr_ModifySuccess);
              queryDeviceInfo();
              eventBus.fire(ChangeDeviceNameEvent(deviceName: deviceName));
            } catch (e) {
              toastFailure(kErrorMsg(e));
            }
          }
        });
  }

  changeAccessIdPwd() async {
    editingController.clear();
    String accessIdPwd = deviceInfo.accessIdPwd;
    editingController.value =
        editingController.value.copyWith(text: accessIdPwd);
    editingController.selection =
        TextSelection.fromPosition(TextPosition(offset: accessIdPwd.length));
    dialogContent(
        title: TR.current.tr_modifyAccessIdPwd,
        content: Padding(
          padding: EdgeInsets.only(left: spaces.left, right: spaces.left),
          child: XTextField(
            autofocus: true,
            hintText: TR.current.tr_InputPassword,
            maxLength: 64,
            inputFormatters: [
              InputContentFormatter(filterPattern: RegExp(nameReg2)),
            ],
            isShowClearIcon: true,
            borderRadius: BorderRadius.circular(6),
            onValueChanged: (text) {},
            controller: editingController,
          ),
        ),
        btnTitles: [TR.current.tr_Cancel, TR.current.tr_CommonSave],
        btnHighLightColor: colors.blue,
        onSelected: (dlIndex) async {
          if (dlIndex == 1) {
            try {
              String accessIdPwd = editingController.text.trim();
              if (accessIdPwd.isEmpty) {
                toast(TR.current.tr_InputPassword);
                return;
              }
              int deviceId = xbParse<int>(deviceInfo.id) ?? 0;
              await deviceTreeAPI.modifyAccessIdPwd(
                [deviceId],
                accessIdPwd,
              );
              deviceInfo.accessIdPwd = accessIdPwd;
              notify();
              toastSuccess(TR.current.tr_ModifySuccess);
              queryDeviceInfo();
            } catch (e) {
              toastFailure(kErrorMsg(e));
            }
          }
        });
  }

  cancelAction() {
    isEdit = false;
    _reloadDeviceInfo();
  }

  editAction() {
    isEdit = true;
    _reloadDeviceInfo();
  }

  onTap(SettingItemInfo info) async {
    switch (info.onTapType) {
      case 0:
        if (!isOnline) {
          toast(TR.current.tr_deviceOfflineEdit);
          return;
        }
        await push(DeviceInfoPsdPage(
          deviceInfo: deviceInfo,
        ));
        break;
      case 1:
        changeDeviceName();
        break;
      case 2:
        chooseDeviceTreeNode();
        break;
      case 3:
        modifyProtocol();
        break;
      case 4:
        modifyAmapSearchAddress();
        break;
      case 5:
        moveGroupTreeNode();
        break;
      case 6:
        //复制
        execClipboardCopy(info.info);
        break;
      case 7:
        //修改标签
        changeDeviceTag();
        break;
      case 8:
        if (isEdit) {
          changeAccessIdPwd();
        } else {
          execClipboardCopy(info.info);
        }
        break;
      case 9:
        if (isEdit) {
          deviceInfo.isSyncPosition = deviceInfo.isSyncPosition == 0 ? 1 : 0;
          _reloadDeviceInfo();
          updateTreeInfo();
        }
        break;
      default:
        break;
    }
  }

  changeDeviceTag() async {
    var ret = await push(DeviceAttributePage(
        type: 2, deviceId: deviceId, deviceTagList: deviceInfo.deviceTagList));
    if (ret != null) {
      queryDeviceInfo();
    }
  }

  chooseDeviceTreeNode() {
    XNodeCascade.show(
        virtualNodeEnable: true,
        onSelect: (DeviceTreeNode selectNode) async {
          try {
            int id = int.parse(deviceInfo.id);
            await deviceTreeAPI.editIotDevice(
              id: id,
              nodeId: selectNode.id,
            );
            toastSuccess(TR.current.tr_ModifySuccess);
            queryDeviceInfo();
            eventBus.fire(EventRefreshHomePage());
          } catch (e) {
            toastFailure(kErrorMsg(e));
          }
        });
  }

  modifyProtocol() {
    actionSheet(
        titles: protocolList,
        dismissTitle: TR.current.tr_Cancel,
        selectedColor: colors.blue,
        onSelected: (index) {
          if (deviceInfo.linkType != index) {
            changeProtocol(index);
          }
        });
  }

  modifyAmapSearchAddress() async {
    PlaceAroundPoiModel? targetItemEntity = PlaceAroundPoiModel(
        cityname: deviceInfo.cityName,
        address: deviceInfo.location,
        longitude: deviceInfo.longitude,
        latitude: deviceInfo.latitude);
    var retModel = await push(AmapSearchAddressPage(
      type: 3,
      echoTargetItemEntity: targetItemEntity,
    ));
    if (retModel != null && retModel is PlaceAroundPoiModel) {
      deviceInfo.location = retModel.address!;
      deviceInfo.latitude = retModel.latitude!;
      deviceInfo.longitude = retModel.longitude!;
      notify();
      String address = deviceInfo.location;
      // String? cityName = targetItemEntity?.cityname;
      // if ((cityName?.isNotEmpty ?? false) &&
      //     (address?.isNotEmpty ?? false) &&
      //     (!address!.contains(cityName!))) {
      //   address = '${cityName!}$address';
      // }

      if (deviceInfo.accessType == 1 && deviceInfo.channelNumber == null) {
        int id = treeNode?.id ?? 0;
        String name = treeNode?.name ?? '';
        await deviceTreeAPI
            .updateTreeNode(id, name,
                isSyncPosition: deviceInfo.isSyncPosition,
                location: address,
                longitude: deviceInfo.longitude,
                latitude: deviceInfo.latitude)
            .then((value) {
          toastSuccess(TR.current.tr_ModifySuccess);
        }).whenComplete(() {
          queryDeviceInfo();
        }).catchError((err) {
          toastFailure(kErrorMsg(err));
        });
      } else {
        int id = xbParse<int>(deviceInfo.id) ?? 0;
        await deviceTreeAPI
            .editIotDevice(
                id: id,
                location: address,
                longitude: deviceInfo.longitude,
                latitude: deviceInfo.latitude)
            .then((value) {
          toastSuccess(TR.current.tr_ModifySuccess);
        }).whenComplete(() {
          queryDeviceInfo();
        }).catchError((e) {
          toastFailure(kErrorMsg(e));
        });
      }
    }
  }

  moveGroupTreeNode() {
    XNodeCascade.show(
        virtualNodeEnable: true,
        showNotNormalOrg: false,
        onSelect: (DeviceTreeNode selectNode) async {
          try {
            await deviceTreeAPI.moveGroupTreeNode(
              treeNode?.id ?? 0,
              selectNode.id,
            );
            toastSuccess(TR.current.tr_ModifySuccess);
            queryDeviceInfo();
            eventBus.fire(EventRefreshHomePage());
          } catch (e) {
            toastFailure(kErrorMsg(e));
          }
        });
  }

  execClipboardCopy(String value) {
    Clipboard.setData(ClipboardData(text: value)).then((_) {
      toast(TR.current.tr_CopySuccess);
    });
  }

  List<String> get protocolList => ['UDP', 'TCP'];

  changeProtocol(int type) async {
    try {
      int id = int.parse(deviceInfo.id);
      await deviceTreeAPI.editIotDevice(id: id, linkType: type);
      toastSuccess(TR.current.tr_ModifySuccess);
      queryDeviceInfo();
      eventBus.fire(EventRefreshHomePage());
    } catch (e) {
      toastFailure(kErrorMsg(e));
    }
  }

  expandChannels() async {
    if (!AuthorityCheckConfig().isHasConfigDeviceOptRemove) {
      toast(TR.current.tr_hasNoAuthorization);
      return;
    }
    await expandChannelAction();
  }

  Future<void> expandChannelAction() async {
    editingController.clear();
    editingController.value = editingController.value.copyWith(text: "1");
    editingController.selection =
        TextSelection.fromPosition(const TextPosition(offset: 1));
    await showDialog(
        context: context,
        builder: (context) {
          return XDialog(
            title: TR.current.tr_expansionChannel,
            isCheckInput: true,
            content: Container(
              decoration: BoxDecoration(
                border: Border.all(color: colors.black4C, width: onePixel),
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              margin: EdgeInsets.only(top: 8.w, bottom: 2.w),
              child: Row(
                children: [
                  XBButton(
                      onTap: () {
                        onAddReduceAction();
                      },
                      disableColor: Colors.transparent,
                      child: Container(
                        alignment: Alignment.centerLeft,
                        margin: EdgeInsets.only(
                            left: spaces.left,
                            top: spaces.leftLess,
                            bottom: spaces.leftLess),
                        width: 50.w,
                        child: Container(
                          width: 20.w,
                          height: 20.w,
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: colors.black4C, width: 0.5.w),
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Center(
                            child: Assets.images.iconMathReduce
                                .image(width: 12.w, height: 12.w),
                          ),
                        ),
                      )),
                  Expanded(
                      child: XTextField(
                    height: 48.w,
                    backgroundColor: Colors.white,
                    textInputType: TextInputType.number,
                    inputFormatters: [
                      InputContentFormatter(filterPattern: RegExp("[0-9]")),
                      InputLengthFormatter(2),
                    ],
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.r),
                        topRight: Radius.circular(10.r)),
                    onValueChanged: (text) {},
                    controller: editingController,
                  )),
                  XBButton(
                      onTap: () {
                        onAddReduceAction(isAdd: true);
                      },
                      disableColor: Colors.transparent,
                      child: Container(
                        alignment: Alignment.centerRight,
                        margin: EdgeInsets.only(
                            right: spaces.left,
                            top: spaces.leftLess,
                            bottom: spaces.leftLess),
                        width: 50.w,
                        child: Container(
                          width: 20.w,
                          height: 20.w,
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: colors.black4C, width: 0.5.w),
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Center(
                            child: Assets.images.iconMathAdd
                                .image(width: 12.w, height: 12.w),
                          ),
                        ),
                      )),
                ],
              ),
            ),
            confirmTextStyle: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: colors.blue),
            onConfirm: () async {
              String channelNumStr = editingController.text;
              if (channelNumStr.isEmpty) {
                toast(TR.current.tr_inputChannels);
                return;
              }
              int channelNum = 0;
              try {
                channelNum = int.parse(channelNumStr);
              } catch (e) {
                toast(TR.current.tr_inputChannels);
                return;
              }
              if (channelNum < 1 || channelNum > 20) {
                toast(TR.current.tr_channelUnreasonable);
                return;
              }
              Navigator.of(context).pop(DialogAction.confirm);
              int id = int.parse(deviceId);
              int nodeId = int.parse(deviceInfo.nodeId);
              await deviceTreeAPI
                  .addIotDevice(
                      id: id,
                      nodeId: nodeId,
                      accessChannels: channelNum,
                      accessType: deviceInfo.accessType,
                      deviceType: deviceInfo.deviceType,
                      addGbNvrChannel: 1)
                  .then((value) => toastSuccess(TR.current.tr_ModifySuccess))
                  .whenComplete(() {
                queryDeviceInfo();
                eventBus.fire(EventRefreshHomePage());
              }).catchError((error) {
                toastFailure(kErrorMsg(error));
              });
            },
          );
        });
  }

  onAddReduceAction({bool isAdd = false}) {
    String channelNumStr = editingController.text;
    if (channelNumStr.isEmpty) {
      toast(TR.current.tr_inputChannels);
      return;
    }
    int channelNum = 0;
    try {
      channelNum = int.parse(channelNumStr);
    } catch (e) {
      toast(TR.current.tr_inputChannels);
      return;
    }
    if ((channelNum <= 1 && !isAdd) || (channelNum >= 20 && isAdd)) {
      return;
    }
    if (isAdd) {
      channelNum++;
    } else {
      channelNum--;
    }
    editingController.value =
        editingController.value.copyWith(text: "$channelNum");
    notify();
  }

  startStopChannels() async {
    List<DeviceInfo> channelList = deviceInfo.channelList;
    if (channelList.isEmpty) {
      return;
    }
    await showDialog(
      context: xbGlobalContext,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () => Future.value(false),
          child: Dialog(
            insetPadding: EdgeInsets.symmetric(
              horizontal: spaces.left,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: Container(
                color: colors.white,
                width: double.infinity,
                constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5),
                child: SizedBox(
                  width: double.infinity,
                  height: double.infinity,
                  child: ChannelInfoChangeWidget(
                    channelList: channelList,
                    startCallback: (deviceIds) {
                      startTap(deviceIds);
                    },
                    stopCallback: (ids, names) {
                      stopTap(ids, names);
                    },
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  startTap(List<int> deviceIds) {
    executeEditDeviceIds(deviceIds);
  }

  stopTap(String ids, List<String> names) {
    checkPasswordInputDialog(ids, names);
  }

  Future checkPasswordInputDialog(String ids, List<String> names) async {
    editingController.clear();
    await showDialog(
        context: context,
        builder: (context) {
          return XDialog(
            title: TR.current.tr_Prompt,
            isCheckInput: true,
            content: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0xFFDCDEE3), // 边框颜色
                  width: onePixel, // 边框宽度
                ),
                borderRadius: BorderRadius.circular(3.r),
                color: Colors.white,
              ),
              child: XTextField(
                height: 40,
                hintText: TR.current.tr_Common_InputPassword,
                borderRadius: BorderRadius.circular(3.r),
                backgroundColor: Colors.white,
                onValueChanged: (text) {},
                controller: editingController,
              ),
            ),
            confirmTextStyle: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: colors.blue),
            onConfirm: () async {
              String password = editingController.text;
              if (password.isEmpty) {
                toast(TR.current.tr_Common_InputPassword);
                return;
              }
              Navigator.of(context).pop(DialogAction.confirm);
              executeDeleteIds(ids, names, password: password);
            },
          );
        });
  }

  executeEditDeviceIds(List<int> deviceIds) async {
    await deviceTreeAPI
        .editIotDevice(deviceIds: deviceIds, isDeleted: 0)
        .then((value) {
      toast(TR.current.tr_ModifySuccess);
      queryDeviceInfo();
      eventBus.fire(EventRefreshHomePage());
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }

  executeDeleteIds(String ids, List<String> names, {String? password}) async {
    await deviceTreeAPI.deleteDevice(ids, password: password).then((value) {
      for (var deviceName in names) {
        OperaLogUtil.videoMonitor.logDeleteDevice(deviceName: deviceName);
      }
      toast(TR.current.tr_ModifySuccess);
      queryDeviceInfo();
      eventBus.fire(EventRefreshHomePage());
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }

  updateTreeInfo() async {
    double latitude = deviceInfo.latitude;
    double longitude = deviceInfo.longitude;
    String address = deviceInfo.location;
    if (address.isEmpty || longitude < 0 || latitude < 0) {
      return;
    }
    int id = treeNode?.id ?? 0;
    String name = treeNode?.name ?? '';
    await deviceTreeAPI
        .updateTreeNode(id, name,
            isSyncPosition: deviceInfo.isSyncPosition,
            location: address,
            longitude: longitude,
            latitude: latitude)
        .then((value) {
      toastSuccess(TR.current.tr_ModifySuccess);
    }).whenComplete(() {
      queryDeviceInfo();
    }).catchError((err) {
      toastFailure(kErrorMsg(err));
    });
  }

  @override
  void dispose() {
    subscribe.cancel();
    editingController.dispose();
    super.dispose();
  }
}
