import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/preview/view/compare_ratio_widget.dart';
import 'package:bcloud/pages/preview/view/xb_mark_title_right_icon.dart';
import 'package:bcloud/widget/cell/xb_cell/xb_cell_split_text/xb_cell_split_text_expand_left.dart';
import 'package:bcloud/widget/xb_progress/xb_progress_circular.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/widget/cell/title_sub_title_cell.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class PassengerFlowDataStoreEntryRate extends StatelessWidget {
  final double inboundRate;
  final int totalTraffic;
  final int storeTraffic;
  final String maxInboundCount;
  final String minInboundCount;
  final double? totalInboundRateTongbi;
  final double? totalInboundRateHuanbi;
  const PassengerFlowDataStoreEntryRate(
      {required this.inboundRate,
      required this.totalTraffic,
      required this.storeTraffic,
      required this.maxInboundCount,
      required this.minInboundCount,
      required this.totalInboundRateTongbi,
      required this.totalInboundRateHuanbi,
      super.key});

  double get _padding => 3;

  double get _circleW => 120;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: Container(
        color: colors.white,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(left: spaces.leftLess),
              child: XBMarkTitleRightIcon(
                  title: TR.current.tr_totalStoreEntryRateProportion),
            ),
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      left: spaces.leftLess, right: spaces.leftLess),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular((_circleW + _padding) * 0.5),
                        border: Border.all(width: 1, color: colors.line1)),
                    child: Padding(
                      padding: EdgeInsets.all(_padding),
                      child: SizedBox(
                        width: _circleW,
                        height: _circleW,
                        child: XBProgressCircular(
                          progress: inboundRate,
                          foregroundColor: colors.blue,
                          backgroundColor: colors.line1,
                          textColor: colors.grey5B5B5B,
                          fontSize: fontSizes.s18,
                          lineWidth: 5,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                    child: Padding(
                  padding: EdgeInsets.only(right: spaces.leftLess),
                  child: Column(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(3),
                        child: Container(
                            color: colors.line2,
                            child: Padding(
                              padding: EdgeInsets.only(
                                  left: spaces.j8, right: spaces.j8),
                              child: SizedBox(
                                height: 24,
                                child: XBCellSplitTextExpandLeft(
                                    textLeftStyle: TextStyle(
                                        fontSize: fontSizes.s12,
                                        color: colors.black),
                                    textRightStyle: TextStyle(
                                        fontSize: fontSizes.s12,
                                        color: colors.black),
                                    textLeftOverflow: TextOverflow.ellipsis,
                                    textLeft: TR.current.tr_storeEntryRate,
                                    textRight:
                                        '${(inboundRate * 100).toStringAsFixed(2)}%'),
                              ),
                            )),
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      _Cell(
                        title: TR.current.tr_totalTraffic,
                        subTitle: '$totalTraffic',
                      ),
                      _Cell(
                        title: TR.current.tr_storeTraffic,
                        subTitle: '$storeTraffic',
                      ),
                      _Cell(
                        title: TR.current.tr_entryPeak,
                        subTitle: '$maxInboundCount',
                      ),
                      _Cell(
                        title: TR.current.tr_entryTrough,
                        subTitle: '$minInboundCount',
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      CompareRatioWidget(
                          subLeft: totalInboundRateTongbi,
                          subRight: totalInboundRateHuanbi),
                      SizedBox(
                        height: spaces.leftLess,
                      ),
                    ],
                  ),
                ))
              ],
            )
          ],
        ),
      ),
    );
  }
}

class _Cell extends StatelessWidget {
  final String title;
  final String subTitle;
  const _Cell({required this.title, required this.subTitle, super.key});

  double get _w => 7;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 24,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                ClipRRect(
                    borderRadius: BorderRadius.circular(_w * 0.5),
                    child: Container(
                      width: _w,
                      height: _w,
                      color: colors.blue,
                    )),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Text(
                    title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: fontSizes.s10, color: colors.grey808080),
                  ),
                ),
              ],
            ),
          ),
          Text(
            subTitle,
            style: TextStyle(fontSize: fontSizes.s10, color: colors.grey808080),
          )
        ],
      ),
    );
  }
}
