import 'package:bcloud/pages/common_pages/edit_image/config/xb_edit_img_config.dart';
import 'package:bcloud/pages/common_pages/edit_image/model/xb_area_mask_widget_change_info.dart';
import 'package:bcloud/pages/common_pages/edit_image/model/xb_graph_model.dart';
import 'package:bcloud/pages/common_pages/edit_image/xb_area_mask_painter_factory.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/utils/print_util.dart';
import 'package:bcloud/widget/top_icon_bottom_title_btn.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'xb_area_mask_painter_widget.dart';

class XBQuadrangleMaskWidget extends XBWidget<XBAreaMaskWidgetVM> {
  final Widget child;

  final bool needMask;

  final XBAreaMaskWidgetRetryBuilder? retryBuilder;

  /// 宽高比
  final double whScale;

  final XBGraphModel? initModel;

  /// 区域变化回调
  final ValueChanged<XBAreaMaskWidgetChangeInfo> onAreaChanged;

  final ValueChanged<bool> onTaping;

  final bool isNeedArrow;

  const XBQuadrangleMaskWidget(
      {required this.child,
      required this.onAreaChanged,
      required this.onTaping,
      this.retryBuilder,
      this.needMask = true,
      this.initModel,
      this.whScale = 16.0 / 9,
      this.isNeedArrow = false,
      super.key});

  @override
  generateVM(BuildContext context) {
    return XBAreaMaskWidgetVM(context: context);
  }

  @override
  Widget buildWidget(XBAreaMaskWidgetVM vm, BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(3),
          child: Stack(
            children: [
              GestureDetector(
                onPanDown: (details) {
                  Console.log("onPanDown ----");
                  onTaping(true);
                  final touchX = details.localPosition.dx;
                  final touchY = details.localPosition.dy;
                  // Console.log("touchX:$touchX,touchY:$touchY");
                  final point =
                      vm.selectedModel.findInsidePoint(touchX, touchY);
                  if (point != null) {
                    point.isSelected = true;
                  } else {
                    vm.isPointInsidePolygon =
                        vm.selectedModel.isPointInsidePolygon(touchX, touchY);
                    vm.lastPoint = Offset(touchX, touchY);
                    // Console.error("isInside :${vm.isPointInsidePolygon}");
                  }
                },
                onPanStart: (details) {},
                onPanUpdate: (details) {
                  if (vm.isPointInsidePolygon) {
                    // Console.error(
                    //     "1 x:${details.localPosition.dx - vm.lastPoint.dx},y:${details.localPosition.dy - vm.lastPoint.dy}");
                    // Console.error("2 x:${details.delta.dx},y:${details.delta.dy}");
                    final offset = Offset(
                        details.localPosition.dx - vm.lastPoint.dx,
                        details.localPosition.dy - vm.lastPoint.dy);
                    // if (!vm.selectedModel.isAllPointInBoundsAfterOffset(
                    //     offset, 0, vm.maxW, 0, vm.maxH)) {
                    //   return;
                    // }
                    vm.selectedModel.translatePoints(offset);
                    vm.lastPoint = details.localPosition;
                    vm.globalKey.currentState?.updateModel([vm.selectedModel]);

                    onAreaChanged(XBAreaMaskWidgetChangeInfo(
                        model: vm.selectedModel, displaySize: vm.displaySize));
                  } else {
                    final point = vm.selectedModel.findSelectedPoint();
                    var newX = details.localPosition.dx;
                    var newY = details.localPosition.dy;
                    if (newX < 0) {
                      newX = 0;
                    }
                    if (newY < 0) {
                      newY = 0;
                    }
                    if (newX > vm.maxW) {
                      newX = vm.maxW;
                    }
                    if (newY > vm.maxH) {
                      newY = vm.maxH;
                    }
                    if (point != null) {
                      if (!vm.selectedModel
                          .isDistanceGreaterThanRadiusToAll(newX, newY)) {
                        return;
                      }
                      point.x = newX;
                      point.y = newY;
                      vm.globalKey.currentState
                          ?.updateModel([vm.selectedModel]);

                      onAreaChanged(XBAreaMaskWidgetChangeInfo(
                          model: vm.selectedModel,
                          displaySize: vm.displaySize));
                    }
                  }
                },
                onPanCancel: () {
                  Console.log("onPanCancel ----");
                  onTaping(false);
                  final point = vm.selectedModel.findSelectedPoint();
                  if (point != null) {
                    point.isSelected = false;
                  }
                  vm.isPointInsidePolygon = false;
                },
                onPanEnd: (details) {
                  Console.log("onPanEnd ----");
                  onTaping(false);
                  final point = vm.selectedModel.findSelectedPoint();
                  if (point != null) {
                    point.isSelected = false;
                  }
                  vm.isPointInsidePolygon = false;
                },
                child: Stack(
                  children: [
                    child,
                    needMask
                        ? LayoutBuilder(
                            builder: (context, constraints) {
                              vm.maxW = constraints.maxWidth;
                              return XBAreaMaskPainterWidget(
                                key: vm.globalKey,
                                width: vm.maxW,
                                height: vm.maxH,
                                initModels: [vm.selectedModel],
                                type: isNeedArrow
                                    ? XBAreaMaskPainterType
                                        .areaWithBorderAndPointArrow
                                    : XBAreaMaskPainterType
                                        .areaWithBorderAndPoint,
                              );
                            },
                          )
                        : Container()
                  ],
                ),
              ),
              Positioned.fill(
                child: retryBuilder != null
                    ? (retryBuilder!(context) ?? Container())
                    : Container(),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget _btn(
      {required String resource,
      required bool isSelected,
      required VoidCallback onTap,
      required String img}) {
    return XBButton(
      onTap: onTap,
      child: TopIconBottomTitleBtn(
          fontSize: fontSizes.s14,
          imgWidth: 35,
          img: img,
          title: resource,
          titleColor: isSelected ? colors.blue : colors.black),
    );
  }
}

class XBAreaMaskWidgetVM extends XBVM<XBQuadrangleMaskWidget> {
  GlobalKey<XBAreaMaskPainterWidgetState> globalKey = GlobalKey();
  double _maxW = screenW;
  double get maxW => _maxW;
  bool isPointInsidePolygon = false;
  Offset lastPoint = Offset.zero;
  set maxW(double newValue) {
    if (_maxW == newValue) {
      return;
    }
    _maxW = newValue;
    _generateModels();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      notify();
    });
  }

  double get maxH => maxW / widget.whScale;
  XBAreaMaskWidgetVM({required super.context}) {
    _rgba = XBGraphModel.defRgba;
    _generateModels(true);
  }

  Size get displaySize => Size(maxW, maxH);

  _generateModels([bool replaceSelectedIndex = false]) {
    models = [
      // /// 三角形
      // XBGraphModel(
      //     type: XBGraphModel.typeArea,
      //     id: XBGraphModel.getId,
      //     rgba: _rgba,
      //     abType: 0,
      //     name: XBGraphModel.getName(XBGraphModel.typeArea),
      //     points: [
      //       XBGraphModelPoints(x: maxW / 2, y: (maxH * 0.2)), // 顶点1
      //       XBGraphModelPoints(x: (maxW * 0.3), y: (maxH * 0.8)), // 顶点2
      //       XBGraphModelPoints(x: (maxW * 0.7), y: (maxH * 0.8)), // 顶点3
      //     ]),

      /// 四边形
      XBGraphModel(
          type: XBGraphModel.typeArea,
          id: XBGraphModel.getId,
          rgba: _rgba,
          abType: widget.isNeedArrow
              ? XBGraphModel.abTypeArrow
              : XBGraphModel.abTypeDef,
          name: XBGraphModel.getName(XBGraphModel.typeArea),
          points: [
            XBGraphModelPoints(x: (maxW * 0.25), y: (maxH * 0.25)),
            XBGraphModelPoints(x: (maxW * 0.75), y: (maxH * 0.25)),
            XBGraphModelPoints(x: (maxW * 0.75), y: (maxH * 0.75)),
            XBGraphModelPoints(x: (maxW * 0.25), y: (maxH * 0.75))
          ]),

      // /// 五边形
      // XBGraphModel(
      //     type: XBGraphModel.typeArea,
      //     id: XBGraphModel.getId,
      //     rgba: _rgba,
      //     abType: 0,
      //     name: XBGraphModel.getName(XBGraphModel.typeArea),
      //     points: [
      //       XBGraphModelPoints(x: maxW / 2, y: (maxH * 0.175)),
      //       XBGraphModelPoints(x: (maxW * 0.32), y: (maxH * 0.46)),
      //       XBGraphModelPoints(x: (maxW * 0.39), y: (maxH * 0.825)),
      //       XBGraphModelPoints(x: (maxW * 0.61), y: (maxH * 0.825)),
      //       XBGraphModelPoints(x: (maxW * 0.68), y: (maxH * 0.46))
      //     ]),

      // /// 六边形
      // XBGraphModel(
      //     type: XBGraphModel.typeArea,
      //     id: XBGraphModel.getId,
      //     rgba: _rgba,
      //     abType: 0,
      //     name: XBGraphModel.getName(XBGraphModel.typeArea),
      //     points: [
      //       XBGraphModelPoints(x: maxW / 2, y: (maxH * 0.15)),
      //       XBGraphModelPoints(x: (maxW * 0.325), y: (maxH * 0.32)),
      //       XBGraphModelPoints(x: (maxW * 0.325), y: (maxH * 0.68)),
      //       XBGraphModelPoints(x: maxW / 2, y: (maxH * 0.85)),
      //       XBGraphModelPoints(x: (maxW * 0.675), y: (maxH * 0.68)),
      //       XBGraphModelPoints(x: (maxW * 0.675), y: (maxH * 0.32))
      //     ]),
    ];

    _loadInitModel(replaceSelectedIndex);

    widget.onAreaChanged(XBAreaMaskWidgetChangeInfo(
        model: selectedModel, displaySize: displaySize));
  }

  _loadInitModel([bool replaceSelectedIndex = false]) {
    if (widget.initModel != null &&
        widget.initModel!.points != null &&
        widget.initModel!.points!.isNotEmpty &&
        widget.initModel!.points!.length >= 4) {
      int initModelIndex = 0;
      if (initModelIndex <= 3) {
        models[initModelIndex] = widget.initModel!;
        if (replaceSelectedIndex) {
          _selectedIndex = initModelIndex;
        }
      }
    }
  }

  XBGraphModel get selectedModel => models[selectedIndex];

  updateSelectedModel(XBGraphModel newValue) {
    models[selectedIndex] = newValue;
    notify();
  }

  late List<int> _rgba;

  late List<XBGraphModel> models;

  int _selectedIndex = 0;
  int get selectedIndex => _selectedIndex;
  set selectedIndex(int value) {
    if (_selectedIndex == value) {
      return;
    }
    _selectedIndex = value;
    _generateModels();
    globalKey.currentState?.updateModel([selectedModel]);
    widget.onAreaChanged(XBAreaMaskWidgetChangeInfo(
        model: selectedModel, displaySize: displaySize));
    notify();
  }
}
