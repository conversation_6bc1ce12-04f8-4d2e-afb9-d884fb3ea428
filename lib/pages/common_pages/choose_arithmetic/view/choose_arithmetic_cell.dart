import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class ChooseArithmeticCell extends StatelessWidget {
  final String title;
  final bool isSelected;
  final String subTitle;
  final bool isEnable;
  final bool hasSubTitle;
  final VoidCallback onTap;
  const ChooseArithmeticCell(
      {required this.title,
      required this.isSelected,
      required this.subTitle,
      required this.onTap,
      this.isEnable = true,
      this.hasSubTitle = true,
      super.key});

  @override
  Widget build(BuildContext context) {
    return XBButton(
      effect: isEnable ? XBButtonTapEffect.opacity : XBButtonTapEffect.none,
      onTap: () {
        if (isEnable) {
          onTap();
        }
      },
      child: Stack(
        children: [
          Opacity(
            opacity: isEnable ? 1 : 0.7,
            child: Container(
              color: colors.white,
              child: Padding(
                padding: EdgeInsets.only(
                    left: spaces.left,
                    right: spaces.left,
                    top: spaces.left,
                    bottom: spaces.left),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: spaces.left),
                      child: XBImage(
                        isSelected
                            ? images.ic_common_check
                            : images.ic_common_uncheck,
                        width: 22,
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: fontSizes.s16,
                                fontWeight: fontWeights.semiBold),
                          ),
                          Visibility(
                            visible: hasSubTitle,
                            child: Padding(
                              padding: EdgeInsets.only(top: 4),
                              child:Text(
                                subTitle,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(color: colors.grey808080),
                              ),
                            )
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Visibility(
          //   visible: !isEnable,
          //   child: Positioned(
          //     top: 0,
          //     bottom: 0,
          //     left: 0,
          //     right: 0,
          //     child: Container(
          //       color: isEnable ? null : colors.grey5B5B5B.withAlpha(50),
          //     ),
          //   ),
          // )
        ],
      ),
    );
  }
}
