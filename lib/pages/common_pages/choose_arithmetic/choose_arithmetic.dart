import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/choose_arithmetic_vm.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/model/choose_arithmetic_model.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/view/choose_arithmetic_cell.dart';
import 'package:bcloud/pages/function/video_inspection/view/search_widget.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_refresh_list_view.dart';
import 'package:bcloud/widget/xb_action_btn.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

typedef ChooseArithmeticVMBuilder = ChooseArithmeticVM Function(
    BuildContext context);

class ChooseArithmetic extends XBPageOver<ChooseArithmeticVM> {
  final bool needSerachWidget;
  final List<ChooseArithmeticModel>? initModels;
  final ChooseArithmeticVMBuilder vmBuilder;
  final int? maxSelectedCount;

  /// 是否支持端云结合算法
  final bool isSupportClientCloudFlow;

  /// 选择的算法最低需要的通道数量，避免添加失败
  final int minChannelCount;

  /// 是否支持客流热区
  final bool isSupportFlowHotArea;
  const ChooseArithmetic(
      {this.initModels,
      required this.vmBuilder,
      this.maxSelectedCount,
      this.needSerachWidget = true,
      required this.minChannelCount,
      this.isSupportClientCloudFlow = false,
      this.isSupportFlowHotArea = false,
      super.key});

  @override
  generateVM(BuildContext context) {
    return vmBuilder(context);
  }

  @override
  String setTitle(ChooseArithmeticVM vm) {
    return TR.current.tr_selectAlgorithm;
  }

  @override
  List<Widget>? actions(ChooseArithmeticVM vm) {
    return [
      XBButton(
        needTapEffect: false,
        onTap: vm.save,
        child: Container(
          color: Colors.transparent,
          alignment: Alignment.center,
          child: Padding(
            padding: EdgeInsets.only(
                left: spaces.left,
                right: spaces.left,
                top: spaces.j8,
                bottom: spaces.j8),
            child: XBActionBtn(
              title: TR.current.tr_CommonSave,
            ),
          ),
        ),
      )
    ];
  }

  @override
  Widget buildPage(ChooseArithmeticVM vm, BuildContext context) {
    return Column(
      children: [
        Visibility(
          visible: needSerachWidget,
          child: Column(
            children: [
              Container(
                color: Colors.white,
                child: Padding(
                  padding: EdgeInsets.only(bottom: spaces.left),
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: spaces.j8, left: spaces.left, right: spaces.left),
                    child: XBSearchBar(
                        placeholder: TR.current.tr_searchAlgorithmByName,
                        onCommit: vm.onKeyWordChanged),
                  ),
                ),
              ),
              const SizedBox(
                height: 1,
              ),
            ],
          ),
        ),
        Visibility(
          visible: vm.needAiBoxChannelConfigTipWidget,
          child: Container(
            margin: EdgeInsets.only(
                top: spaces.leftLess, left: spaces.left, right: spaces.left),
            alignment: Alignment.center,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.w),
                color: kColorHexStr('#4796F2', alpha: 0.1),
                border: Border.all(
                    color: kColorHexStr('#4796F2', alpha: 0.2), width: onePixel)),
            child: Padding(
              padding:
              EdgeInsets.only(left: 9.w, right: 8.w, top: 6.w, bottom: 6.w),
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      right: 5.5.w,
                    ),
                    child: XBImage(
                      images.ic_facial_switch_platform_tip,
                      width: 14.w,
                      height: 14.w,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      TR.current.tr_aiBoxChannelConfigLimitTip,
                      maxLines: 2,
                      textAlign: TextAlign.left,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: kFontWeight_regular,
                          color: colors.black4C),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Expanded(
          child: XBRefreshListView(
            vm: vm,
            needLoadMore: false,
            itemBuilder: (context, index) {
              ChooseArithmeticModel model = vm.models[index];
              bool needDisable =
                  (!isSupportClientCloudFlow && model.isCloudMixDeviceFlow) ||
                      (!isSupportFlowHotArea && model.isFlowHotArea);
              return XBDisable(
                disable: needDisable,
                child: Opacity(
                  opacity: needDisable ? 0.4 : 1,
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: index == 0 ? spaces.leftLess : 0,
                        bottom: spaces.leftLess +
                            (index == vm.itemCount - 1 ? safeAreaBottom : 0),
                        left: spaces.left,
                        right: spaces.left),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: ChooseArithmeticCell(
                          isEnable: vm.isEnable(index),
                          hasSubTitle: vm.hasSubTitle(index),
                          title: model.algorithmName ?? "",
                          isSelected: vm.isSelected(model),
                          subTitle: vm.subTitle(model),
                          onTap: () {
                            vm.onTap(model);
                          }),
                    ),
                  ),
                ),
              );
            },
          ),
        )
      ],
    );
  }
}
