import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/choose_arithmetic.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/model/choose_arithmetic_model.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_vm_refresh.dart';
import 'package:bcloud/utils/print_util.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

abstract class ChooseArithmeticVM extends XBVMRefresh<ChooseArithmetic> {
  ChooseArithmeticVM({required super.context});

  final List<ChooseArithmeticModel> models = [];

  @override
  int get itemCount => dataLen;

  @override
  bool get needNoData => dataLen == 0;

  @override
  int get dataLen => models.length;

  save() async {
    /// 选出所有选中的model
    List<ChooseArithmeticModel> selectedModels = [];
    for (int i = 0; i < models.length; i++) {
      if (isSelected(models[i])) {
        selectedModels.add(models[i]);
      }
    }
    List<ChooseArithmeticModel> notEnoughModels = [];
    for (var element in selectedModels) {
      bool isInInit = false;
      for (var initElement in (widget.initModels ?? [])) {
        if (element.algorithmId == initElement.algorithmId) {
          isInInit = true;
          break;
        }
      }
      int surplus = element.surplus + (isInInit ? 1 : 0);
      if (surplus < widget.minChannelCount) {
        notEnoughModels.add(element);
      }
    }
    if (notEnoughModels.isNotEmpty) {
      String msg = "";
      for (var element in notEnoughModels) {
        msg +=
            "${TR.current.tr_arithmeticChannelNotEnoughTip(element.algorithmName ?? "")}\n\r";
      }
      msg += "\n\r${TR.current.tr_insufficientTip}";
      Console.log("msg:$msg");
      dialogContent(
          title: TR.current.tr_gentleReminder,
          content: Container(
            // width: 200,
            constraints: BoxConstraints(
                maxHeight: 300,
                maxWidth: screenW - spaces.leftLarge * 2 - spaces.left * 2),
            child: SingleChildScrollView(
              child: Container(
                // color: colors.randColor,
                alignment: Alignment.centerLeft,
                child: Text(
                  msg,
                  // overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
          btnTitles: [TR.current.tr_Confirm],
          btnHighLightColor: colors.blue,
          onSelected: (dlIndex) {});
      return;
    }
    if (selectedModels.isEmpty) {
      toastFailure(TR.current.tr_pleaseSelectAlgorithm);
      return;
    }
    pop(selectedModels);
  }

  Map<String, bool> selectedMap = {};

  bool isSelected(ChooseArithmeticModel model) =>
      selectedMap[model.algorithmId!] ?? false;

  String algorithmName = "";

  onKeyWordChanged(String text) {
    models.clear();
    algorithmName = text;
    refreshController.refresh();
  }

  int get _selectedCount {
    int ret = 0;
    selectedMap.forEach((key, value) {
      if (value) {
        ret++;
      }
    });
    return ret;
  }

  onTap(ChooseArithmeticModel model) {
    if (isSelected(model)) {
      selectedMap[model.algorithmId!] = false;
    } else {
      if (widget.maxSelectedCount != null &&
          _selectedCount >= widget.maxSelectedCount!) {
        toastFailure(
            TR.current.tr_chooseAtMostNameAlgorithm(widget.maxSelectedCount!));
      } else {
        selectedMap[model.algorithmId!] = true;
      }
    }
    notify();
  }

  isEnable(int index);

  String subTitle(ChooseArithmeticModel model);

  bool get needAiBoxChannelConfigTipWidget => false;

  bool hasSubTitle(int index) => true;
}
