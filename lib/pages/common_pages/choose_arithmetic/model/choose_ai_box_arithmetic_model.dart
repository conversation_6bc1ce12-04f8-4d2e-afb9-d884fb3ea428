import 'package:xb_scaffold/xb_scaffold.dart';

class ChooseAiBoxArithmeticResponse {
  String? pageSize;
  String? pageNo;
  String? totalCount;
  String? totalPage;
  List<ChooseAiBoxArithmeticModel>? models;
  bool? hasNext;

  ChooseAiBoxArithmeticResponse(
      {this.pageSize,
        this.pageNo,
        this.totalCount,
        this.totalPage,
        this.models,
        this.hasNext});

  ChooseAiBoxArithmeticResponse.fromJson(Map<String, dynamic> json) {
    pageSize = json['pageSize'];
    pageNo = json['pageNo'];
    totalCount = json['totalCount'];
    totalPage = json['totalPage'];
    if (json['datas'] != null) {
      models = <ChooseAiBoxArithmeticModel>[];
      json['datas'].forEach((v) {
        models!.add(ChooseAiBoxArithmeticModel.fromJson(v));
      });
    }
    hasNext = json['hasNext'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['pageSize'] = this.pageSize;
    data['pageNo'] = this.pageNo;
    data['totalCount'] = this.totalCount;
    data['totalPage'] = this.totalPage;
    if (this.models != null) {
      data['datas'] = this.models!.map((v) => v.toJson()).toList();
    }
    data['hasNext'] = this.hasNext;
    return data;
  }
}

class ChooseAiBoxArithmeticModel {
  dynamic? ids;
  String? id;
  String? name;
  String? detailConfig;
  String? defaultParams;
  int? step;
  int? timeInterval;
  String? threshold;
  String? excludeAreas;
  String? includeAreas;
  String? extra;
  int? isDeleted;
  String? creator;
  String? modifier;
  String? gmtCreated;
  String? gmtModified;
  String? code;
  String? remark;
  String? stepRange;
  int? bindStatus;
  int? minStep;
  int? maxStep;
  int? durationTime;
  int? dedInterval;
  int? analysisCycle;
  String? dealBean;
  int? dealType;
  String? defaultImage;
  String? appDefaultImage;
  String? specialParam;
  String? colorCode;

  ChooseAiBoxArithmeticModel(
      {this.ids,
        this.id,
        this.name,
        this.detailConfig,
        this.defaultParams,
        this.step,
        this.timeInterval,
        this.threshold,
        this.excludeAreas,
        this.includeAreas,
        this.extra,
        this.isDeleted,
        this.creator,
        this.modifier,
        this.gmtCreated,
        this.gmtModified,
        this.code,
        this.remark,
        this.stepRange,
        this.bindStatus,
        this.minStep,
        this.maxStep,
        this.durationTime,
        this.dedInterval,
        this.analysisCycle,
        this.dealBean,
        this.dealType,
        this.defaultImage,
        this.appDefaultImage,
        this.specialParam,
        this.colorCode});

  ChooseAiBoxArithmeticModel.fromJson(Map<String, dynamic> json) {
    ids = json['ids'];
    id = xbParse<String>(json['id']);
    name = json['name'];
    detailConfig = json['detailConfig'];
    defaultParams = json['defaultParams'];
    step = json['step'];
    timeInterval = json['timeInterval'];
    threshold = json['threshold'];
    excludeAreas = json['excludeAreas'];
    includeAreas = json['includeAreas'];
    extra = json['extra'];
    isDeleted = json['isDeleted'];
    creator = json['creator'];
    modifier = json['modifier'];
    gmtCreated = json['gmtCreated'];
    gmtModified = json['gmtModified'];
    code = json['code'];
    remark = json['remark'];
    stepRange = json['stepRange'];
    bindStatus = json['bindStatus'];
    minStep = json['minStep'];
    maxStep = json['maxStep'];
    durationTime = json['durationTime'];
    dedInterval = json['dedInterval'];
    analysisCycle = json['analysisCycle'];
    dealBean = json['dealBean'];
    dealType = json['dealType'];
    defaultImage = json['defaultImage'];
    appDefaultImage = json['appDefaultImage'];
    specialParam = json['specialParam'];
    colorCode = json['colorCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ids'] = this.ids;
    data['id'] = this.id;
    data['name'] = this.name;
    data['detailConfig'] = this.detailConfig;
    data['defaultParams'] = this.defaultParams;
    data['step'] = this.step;
    data['timeInterval'] = this.timeInterval;
    data['threshold'] = this.threshold;
    data['excludeAreas'] = this.excludeAreas;
    data['includeAreas'] = this.includeAreas;
    data['extra'] = this.extra;
    data['isDeleted'] = this.isDeleted;
    data['creator'] = this.creator;
    data['modifier'] = this.modifier;
    data['gmtCreated'] = this.gmtCreated;
    data['gmtModified'] = this.gmtModified;
    data['code'] = this.code;
    data['remark'] = this.remark;
    data['stepRange'] = this.stepRange;
    data['bindStatus'] = this.bindStatus;
    data['minStep'] = this.minStep;
    data['maxStep'] = this.maxStep;
    data['durationTime'] = this.durationTime;
    data['dedInterval'] = this.dedInterval;
    data['analysisCycle'] = this.analysisCycle;
    data['dealBean'] = this.dealBean;
    data['dealType'] = this.dealType;
    data['defaultImage'] = this.defaultImage;
    data['appDefaultImage'] = this.appDefaultImage;
    data['specialParam'] = this.specialParam;
    data['colorCode'] = this.colorCode;
    return data;
  }
}