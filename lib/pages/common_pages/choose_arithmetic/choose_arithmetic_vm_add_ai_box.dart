import 'package:bcloud/api/bc_api.dart';
import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/choose_arithmetic_vm.dart';
import 'package:bcloud/pages/common_pages/choose_arithmetic/model/choose_arithmetic_model.dart';
import 'package:bcloud/public/network/net_extension_algorithm.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'model/choose_ai_box_arithmetic_model.dart';

class ChooseArithmeticVmAddAiBox extends ChooseArithmeticVM {
  final String parentId;
  int _useCount = 0;
  int maxCount = 8;
  ChooseArithmeticVmAddAiBox({required super.context, required this.parentId}) {
    queryConfigCount(parentId);
  }

  queryConfigCount(String parentId) {
    BCApi.bcDevice.queryAiBoxTaskConfigCountByParentsId(parentId).then((value) {
      if (value != null) {
        _useCount = xbParse<int>(value) ?? 0;
        notify();
      }
    }).catchError((error) {});
  }

  int get useCount => _useCount;

  @override
  queryRefresh() {
    NetQueryUtil.getInstance()
        .algorithmListAiBoxDevice(pageNo: pageNo, pageSize: pageSize)
        .then((value) {
      if (pageNo == 1) {
        models.clear();
      }
      loadSuccessState(false);
      ChooseAiBoxArithmeticResponse response = ChooseAiBoxArithmeticResponse.fromJson(value);
      if (response.models?.isNotEmpty ?? false) {
        models.replaceOrAddAll(
          list: response.models!.map((e) => ChooseArithmeticModel(
            id: e.id,
            algorithmId: e.id,
            algorithmName: e.name,
            step: e.step,
            threshold: e.threshold,
            durationTime: e.durationTime,
            bindStatus: e.bindStatus,
            minStep: e.minStep,
            maxStep: e.maxStep,
            dealType: e.dealType,
            useCount: 0,
            maxCount: 8
          )).toList(),
          equal: (obj1, obj2) {
            return obj1.id == obj2.id;
          },
        );
      }

      if (selectedMap.isEmpty) {
        /// 没有操作过
        if (widget.initModels != null) {
          for (var initElement in widget.initModels!) {
            for (var element in models) {
              if (element.algorithmId == initElement.algorithmId) {
                selectedMap[element.algorithmId!] = true;
              }
            }
          }
        }
      } else {
        /// 操作过
        Map<String, bool> tempSelectedMap = {};
        for (var model in models) {
          tempSelectedMap[model.algorithmId!] = isSelected(model);
        }
        selectedMap = tempSelectedMap;
      }
      notify();
    }).catchError((e) {
      loadFailureState();
      toastFailure(kErrorMsg(e));
    });
  }

  @override
  String subTitle(ChooseArithmeticModel model) =>
      '${TR.current.tr_totalNameWay(model.maxCount ?? 0)}，${TR.current.tr_usedNameWay(model.useCount ?? 0)}';

  @override
  isEnable(int index) {
    ChooseArithmeticModel model = models[index];
    bool isSelected = selectedMap[model.algorithmId!] == true;
    if (isSelected) return true;
    for (var initElement in (widget.initModels ?? [])) {
      if (model.algorithmId == initElement.algorithmId) {
        return true;
      }
    }
    return true;
  }

  @override
  bool get needAiBoxChannelConfigTipWidget => _useCount >= maxCount;

  @override
  hasSubTitle(int index) {
    ChooseArithmeticModel model = models[index];
    return false;
  }
}
