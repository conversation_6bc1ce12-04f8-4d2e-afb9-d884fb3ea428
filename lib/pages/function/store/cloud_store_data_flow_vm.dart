import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/api/inspection_api.dart';
import 'package:bcloud/api/model/page_model.dart';
import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/inspection/model/inspection_analysis_items.dart';
import 'package:bcloud/pages/inspection/model/inspection_analysis_staff.dart';
import 'package:bcloud/pages/inspection/model/inspection_analysis_statistics.dart';
import 'package:bcloud/pages/login/model/account_info.dart';
import 'package:bcloud/pages/preview/model/passenger_flow_data_response_page_query.dart';
import 'package:bcloud/pages/preview/model/passenger_flow_data_statistical_query.dart';
import 'package:bcloud/public/network/net_extension_passenger_flow_statistics.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_chart_extension/xb_chart_common_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/utils/extentions.dart';
import 'package:bcloud/utils/xb_time_util/xb_time_util.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:xb_chart/xb_chart.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'cloud_store_data_flow_page.dart';
import 'cloud_store_event_ranking_page.dart';
import 'cloud_store_inspection_completion_rate_page.dart';
import 'cloud_store_inspection_coverage_detail_page.dart';
import 'cloud_store_inspection_coverage_rate_chart_page.dart';
import 'cloud_store_inspection_coverage_rate_page.dart';
import 'cloud_store_list_page.dart';
import 'cloud_store_list_vm.dart';
import 'cloud_store_passenger_flow_ranking_page.dart';
import 'cloud_store_single_data_flow_page.dart';
import 'model/cloud_store_item_model.dart';
import 'model/cloud_store_statistical_event_model.dart';
import 'model/store_flow_item_model.dart';
import 'model/store_patrol_coverage_rate_model_.dart';

class CloudStoreDataFlowVM extends XBPageVM<CloudStoreDataFlowPage> {
  final Map<int, AssetGenImage> imageMap = {
    0: Assets.images.iconAnalysisDeviceItem1,
    1: Assets.images.iconAnalysisDeviceItem2,
    2: Assets.images.iconAnalysisDeviceItem3,
    3: Assets.images.iconAnalysisDeviceItem4,
    4: Assets.images.iconAnalysisDeviceItem5,
  };
  double staffMaxLen = 60;
  int flowModeType = 3; //0-两种都选 1-精准客流 2-端侧客流
  int currentFlowMode = 0; //0-精准客流 1-端侧客流
  int selectIndex = 0;
  int selectIndexRanking = 0;
  late List<DateTime> _curWeek;
  late List<DateTime> _curMonth;
  late List<DateTime> _curYear;
  List<CloudStoreItemEntity> selectedStoreList = [];
  final List<StoreFlowItemModel> passengerFlowRankingList = [];
  final List<InspectionAnalysisItems> itemsList = [];
  final List<InspectionAnalysisStaff> staffList = [];
  final List<StorePatrolCoverageRateModel> patrolCoverageRateList = [];
  final List<CloudStoreStatisticalEventModel> statisticalEventList = [];
  final Map<int, String> typeMap = {
    0: TR.current.tr_numberInspections, //"巡检次数"
    1: TR.current.tr_problemFound, //"发现问题",
    2: TR.current.tr_overdueTimes, //"逾期次数",
  };
  /*
  0-日 1-周 2-月 4-年 null-自定义 5-本周 6-本月
  */
  int? queryType = 1;
  int staffType = 1;
  int typeTime = 0;
  // 日期开始时间
  DateTime startDateTime = DateTime.now();
  String get startTime =>
      XBTimeUtil.dateTime2Str(dateTime: startDateTime, format: timeFormat);
  // 日期结束时间
  DateTime endDateTime = DateTime.now();
  String get endTime =>
      XBTimeUtil.dateTime2Str(dateTime: endDateTime, format: timeFormat);

  String get startDay => XBTimeUtil.dateTime2Str(
      dateTime: startDateTime, format: XBTimeUtil.formatYMD);
  String get endDay => XBTimeUtil.dateTime2Str(
      dateTime: endDateTime, format: XBTimeUtil.formatYMD);

  ///精准客流-统计数据
  PassengerFlowDataStatisticalQuery? precisionQueryCount;

  ///端侧客流-统计数据
  PassengerFlowDataStatisticalQuery? deviceQueryCount;
  List<InspectionAnalysisStatistics> statisticsList = [];
  List<XBAnnulusChartModel> xbAnnulusChartModels = [];

  // 列表数据
  PassengerFlowDataResponsePageQuery? precisionPageQuery;
  PassengerFlowDataResponsePageQuery? devicePageQuery;

  CloudStoreDataFlowVM({required super.context}) {
    initType();
    query();
  }

  List<String> get storeIdList {
    return selectedStoreList.map((e) => e.id ?? "").toList();
  }

  String get selectedStoreName {
    if (selectedStoreList.isEmpty) return TR.current.tr_allStores;
    return selectedStoreList.map((e) => e.storeName ?? "").join(",");
  }

  /// 总客流
  int get totalTraffic {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.sumFlowCount ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.sumFlowCount ?? 0;
    }
  }

  /// 总客流环比
  double? get totalTrafficHuanbi {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.qoqFlow;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.qoqFlow;
    }
  }

  /// 总进店客流
  int get totalStoreTraffic {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.sumInboundCount ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.sumInboundCount ?? 0;
    }
  }

  /// 总进店环比
  double? get totalStoreTrafficHuanbi {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.qoqInbound;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.qoqInbound;
    }
  }

  /// 总过店客流
  int get totalPassByTraffic {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.sumPassCount ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.sumPassCount ?? 0;
    }
  }

  /// 总过店环比
  double? get totalPassByTrafficHuanbi {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.qoqPass;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.qoqPass;
    }
  }

  /// 总进店率
  double get totalInboundRate {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.inboundRate ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.inboundRate ?? 0;
    }
  }

  /// 总进店率环比
  double? get totalInboundRateHuanbi {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return precisionQueryCount!.qoqInboundRate;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return deviceQueryCount!.qoqInboundRate;
    }
  }

  /// 总进店客流-去重后
  int get totalInboundRemoveDuplicate {
    if (currentFlowMode == 0) {
      return precisionQueryCount?.sumRemoveDuplicateInboundCount ?? 0;
    } else {
      return deviceQueryCount?.sumRemoveDuplicateInboundCount ?? 0;
    }
  }

  double get childCount {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return xbParse<double>(precisionQueryCount?.sumChildrenCount) ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return xbParse<double>(deviceQueryCount?.sumChildrenCount) ?? 0;
    }
  }

  double get youngCount {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return xbParse<double>(precisionQueryCount?.sumYoungCount) ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return xbParse<double>(deviceQueryCount?.sumYoungCount) ?? 0;
    }
  }

  double get middleCount {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return xbParse<double>(precisionQueryCount?.sumMiddleCount) ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return xbParse<double>(deviceQueryCount?.sumMiddleCount) ?? 0;
    }
  }

  double get oldCount {
    if (currentFlowMode == 0) {
      if (precisionQueryCount == null) {
        return 0;
      }
      return xbParse<double>(precisionQueryCount?.sumOldCount) ?? 0;
    } else {
      if (deviceQueryCount == null) {
        return 0;
      }
      return xbParse<double>(deviceQueryCount?.sumOldCount) ?? 0;
    }
  }

  int get sumInboundCount {
    if (currentFlowMode == 0) {
      return precisionQueryCount?.sumInboundCount ?? 0;
    } else {
      return deviceQueryCount?.sumInboundCount ?? 0;
    }
  }

  int get sumManCount {
    if (currentFlowMode == 0) {
      return precisionQueryCount?.sumManCount ?? 0;
    } else {
      return deviceQueryCount?.sumManCount ?? 0;
    }
  }

  int get sumWomanCount {
    if (currentFlowMode == 0) {
      return precisionQueryCount?.sumWomanCount ?? 0;
    } else {
      return deviceQueryCount?.sumWomanCount ?? 0;
    }
  }

  double get maleRate {
    if (sumManCount == 0 || sumWomanCount == 0) return 0;
    return 1.0 * sumManCount / (sumManCount + sumWomanCount);
  }

  double get femaleRate {
    if (sumManCount == 0 || sumWomanCount == 0) return 0;
    return 1.0 * sumWomanCount / (sumManCount + sumWomanCount);
  }

  List<XBAnnulusChartModel> get ageModels => [
        XBAnnulusChartModel(
            name: TR.current.tr_juvenile, //TR.current.tr_children
            color: kColorHexStr('#6CABF5'),
            value: childCount),
        XBAnnulusChartModel(
            name: TR.current.tr_youth,
            color: kColorHexStr('#71DADA'),
            value: youngCount),
        XBAnnulusChartModel(
            name: TR.current.tr_middleAged,
            color: kColorHexStr('#AA82E3'),
            value: middleCount),
        XBAnnulusChartModel(
            name: TR.current.tr_elderly,
            color: kColorHexStr('#B5D5FA'),
            value: oldCount)
      ];

  bool xTitleIsShow(int index) {
    return index % 2 == 0;
  }

  List<XBLineChartXTitle> get xTitlesStatistics {
    List<XBLineChartXTitle> ret = [];
    if (statisticsList != null && statisticsList.isNotEmpty) {
      for (var element in statisticsList) {
        ret.add(XBLineChartXTitle(
            text: element.statisticalTime ?? "",
            isShow: xTitleIsShow(statisticsList.indexOf(element))));
      }
    }
    return ret;
  }

  List<XBLineChartModel> get modelsStatistics {
    List<XBLineChartModel> ret = [];
    if (statisticsList != null && statisticsList.isNotEmpty) {
      /// 巡检次数数据
      final List<double> values = [];

      for (var element in statisticsList) {
        int count =
            (element.patrolUnpassCount ?? 0) + (element.patrolPassCount ?? 0);
        values.add(count.toDouble());
      }

      /// 巡检次数
      ret.add(XBLineChartModel(
          name: '${TR.current.tr_numberInspections} (${TR.current.tr_time_c})',
          color: colors.blue,
          values: values));
    }
    return ret;
  }

  /// 下发任务数
  int get issuedTasks {
    if (currentFlowMode == 0) {
      // if (precisionQueryCount == null || sumInboundCount == 0) return 0;
      return 100;
    } else {
      // if (deviceQueryCount == null || sumInboundCount == 0) return 0;
      return 200;
    }
  }

  /// 完成任务数
  int get completedTasks {
    if (currentFlowMode == 0) {
      // if (precisionQueryCount == null || sumInboundCount == 0) return 0;
      return 30;
    } else {
      // if (deviceQueryCount == null || sumInboundCount == 0) return 0;
      return 50;
    }
  }

  ///完成率
  double get completedTaskRate {
    if (currentFlowMode == 0) {
      if (issuedTasks == null || completedTasks == 0) return 0;
      return 1.0 * completedTasks / issuedTasks;
    } else {
      if (issuedTasks == null || completedTasks == 0) return 0;
      return 1.0 * completedTasks / issuedTasks;
    }
  }

  int compareFun(XBHistogramChartYModel a, XBHistogramChartYModel b) {
    var ret = b.value.compareTo(a.value);
    if (ret == 0) {
      ret = b.name.compareTo(a.name);
    }
    return ret;
  }

  List<XBHistogramChartYModel> get yModels {
    if (staffType == 1) {
      return staffList
          .map((e) => XBHistogramChartYModel(
              name: e.custName ?? "",
              value: (e.sumPatrolCount ?? 0).toDouble()))
          .toList()
        ..sort(compareFun);
    } else if (staffType == 2) {
      return staffList
          .map((e) => XBHistogramChartYModel(
              name: e.custName ?? "",
              value: (e.sumPatrolFindCount ?? 0).toDouble()))
          .toList()
        ..sort(compareFun);
    } else if (staffType == 3) {
      return staffList
          .map((e) => XBHistogramChartYModel(
              name: e.custName ?? "",
              value: (e.sumPatrolBeOverdueCount ?? 0).toDouble()))
          .toList()
        ..sort(compareFun);
    } else {
      return [];
    }
  }

  List<XBLineChartXTitle> get xTitlesCoverageRate {
    List<XBLineChartXTitle> ret = [];
    if (patrolCoverageRateList.isNotEmpty) {
      for (var element in patrolCoverageRateList) {
        ret.add(XBLineChartXTitle(
            text: element.statisticalTime ?? "",
            isShow: xTitleIsShow(patrolCoverageRateList.indexOf(element))));
      }
    }
    return ret;
  }

  List<XBLineChartModel> get modelsCoverageRate {
    List<XBLineChartModel> ret = [];
    if (patrolCoverageRateList.isNotEmpty) {
      /// 数据
      final List<double> values = [];

      for (var element in patrolCoverageRateList) {
        values.add((element.coverRate ?? 0).toDouble());
      }

      /// 巡店覆盖率
      ret.add(XBLineChartModel(
          name: '${TR.current.tr_storeInspectionCoverageRate} (%)',
          color: colors.blue,
          values: values));
    }
    return ret;
  }

  onChooseStore() async {
    var ret = await push(CloudStoreListPage(
      type: 1,
      storeIdList: storeIdList.isEmpty ? [CloudStoreListVM.allId] : storeIdList,
      isDataFlow: true,
    ));
    if (ret != null && ret is List) {
      selectedStoreList = (ret as List<CloudStoreItemEntity>)
          .where((element) => element.id != CloudStoreListVM.allId)
          .toList();
      query();
    }
  }

  bool get isRankingReversed => selectIndexRanking == 1;

  int get passengerFlowRankingCount => passengerFlowRankingList.length;

  List<StoreFlowItemModel> get passengerFlowRankingReversedList {
    if (isRankingReversed) {
      return passengerFlowRankingList.reversed.toList();
    } else {
      return passengerFlowRankingList;
    }
  }

  int get statisticalEventCount => statisticalEventList.length;

  String get flowModeName => currentFlowMode == 0
      ? TR.current.tr_preciseTrafficFlow
      : TR.current.tr_deviceTraffic;

  String get timeFormat {
    if (queryType == 2 || queryType == 6) {
      return XBTimeUtil.formatYM;
    } else if (queryType == 4) {
      return XBTimeUtil.formatY;
    } else {
      return XBTimeUtil.formatYMD;
    }
  }

  String get chooseTimeText {
    if (selectIndex == 3) {
      return '${DateFormat(XBTimeUtil.formatYMD).format(startDateTime)} ～ ${DateFormat(XBTimeUtil.formatYMD).format(endDateTime)}';
    } else if (selectIndex == 1) {
      return DateFormat(XBTimeUtil.formatYM).format(startDateTime);
      // return '${DateFormat(_formatYM).format(_start)}-${DateFormat(_formatYM).format(_end)}';
    } else if (selectIndex == 2) {
      return DateFormat(XBTimeUtil.formatY).format(startDateTime);
      // return '${DateFormat(_formatY).format(_start)}-${DateFormat(_formatY).format(_end)}';
    } else {
      //日\周
      if (startDateTime == endDateTime) {
        return DateFormat(XBTimeUtil.formatYMD).format(startDateTime);
      }
      return '${DateFormat(XBTimeUtil.formatYMD).format(startDateTime)} ～ ${DateFormat(XBTimeUtil.formatYMD).format(endDateTime)}';
    }
  }

  initType() {
    ///0-两种都选 1-精准客流 2-端侧客流
    flowModeType = AccountInfo.instance.userCountLimitConfig.flowModeType ?? 3;
    if (flowModeType == 2) {
      currentFlowMode = 1;
    } else {
      currentFlowMode = 0;
    }
    _generateCurWeek();
    _generateCurMonth();
    _generateCurYear();
    selectIndex = 0;
    queryType = 1;
    startDateTime = _curWeek[0];
    endDateTime = _curWeek[1];
    notify();
  }

  changeFlowModeMode() {
    if (flowModeType != 0) {
      return;
    }
    if (currentFlowMode == 0) {
      currentFlowMode = 1;
      queryDevicePassengerFlow();
    } else {
      currentFlowMode = 0;
      queryPrecisionPassengerFlow();
    }
  }

  selectIndexTab(int currentIndex) {
    if (selectIndex == currentIndex && currentIndex != 3) {
      return;
    }
    selectIndex = currentIndex;
    if (selectIndex == 0) {
      //本周
      queryType = 1;
      startDateTime = _curWeek[0];
      endDateTime = _curWeek[1];
      query();
    } else if (selectIndex == 1) {
      //本月
      queryType = 2;
      startDateTime = _curMonth[0];
      endDateTime = _curMonth[1];
      query();
    } else if (selectIndex == 2) {
      //本年
      queryType = 4;
      startDateTime = _curYear[0];
      endDateTime = _curYear[1];
      query();
    } else {
      if (queryType == 2 || queryType == 4) {
        startDateTime = _curWeek[0];
        endDateTime = _curWeek[1];
      }
      queryType = null;
      chooseTime();
    }
  }

  selectIndexRankingBar(int currentIndex) {
    selectIndexRanking = currentIndex;
    notify();
  }

  _generateCurWeek() {
    DateTime now = DateTime.now();
    int currentDay = now.weekday;
    DateTime startWeek = DateTime(now.year, now.month, now.day)
        .subtract(Duration(days: currentDay - 1)); // 本周第一天
    DateTime endWeek = DateTime(now.year, now.month, now.day)
        .add(Duration(days: 7 - currentDay)); // 本周最后一天
    endWeek = DateTime(endWeek.year, endWeek.month, endWeek.day);
    _curWeek = [startWeek, endWeek];
  }

  _generateCurMonth() {
    DateTime now = DateTime.now();
    DateTime startMonth = DateTime(now.year, now.month, 1);
    DateTime endMonth =
        DateTime(now.year, now.month + 1, 1).subtract(const Duration(days: 1));
    _curMonth = [startMonth, endMonth];
  }

  _generateCurYear() {
    DateTime now = DateTime.now();
    DateTime startYear = DateTime(now.year, 1, 1);
    DateTime endYear =
        DateTime(now.year + 1, 1, 1).subtract(const Duration(days: 1));
    _curYear = [startYear, endYear];
  }

  bool get isTp1 => AccountInfo.instance.config.isTp1;

  chooseTime() async {
    DateTime now = DateTime.now();
    showCalendar(
        selectedDates: [startDateTime, endDateTime],
        maxEnableDateTime: isTp1
            ? DateTime(now.year, now.month, now.day, 23, 59, 59)
                .subtract(const Duration(days: 1))
            : null,
        onDone: (value) {
          startDateTime = value.first;
          endDateTime = value.last;
          query();
        });
  }

  changeTypeTime(String e) {
    typeTime = typeMap.valueKey(e);
    staffType = typeTime + 1;
    notify();
    queryStoreStaffs(queryType);
  }

  //未使用
  enterInspectionCompletionRate() {
    push(CloudStoreInspectionCompletionRatePage(
      startDay: startDay,
      endDay: endDay,
      storeIdList: storeIdList,
    ));
  }

  //未使用
  enterInspectionCoverageRate() {
    push(CloudStoreInspectionCoverageRatePage(
        startDay: startDay, endDay: endDay));
  }

  //未使用
  enterInspectionCoverageDetail() {
    push(const CloudStoreInspectionCoverageDetailPage());
  }

  /// 门店客流排名
  enterPassengerFlowRanking() {
    push(CloudStorePassengerFlowRankingPage(
      flowMode: currentFlowMode,
      startTime: startTime,
      endTime: endTime,
      startDay: startDay,
      endDay: endDay,
      queryType: queryType,
      storeIdList: storeIdList,
    ));
  }

  /// 门店客流排名 --单项点击事件
  choosePassengerFlowAction(StoreFlowItemModel model) {
    push(CloudStoreSingleDataFlowPage(
      model: model,
      flowMode: currentFlowMode,
      startTime: startTime,
      endTime: endTime,
      startDay: startDay,
      endDay: endDay,
      queryType: queryType,
    ));
  }

  /// 门店事件排名
  enterEventRanking() {
    push(CloudStoreEventRankingPage(
      startDay: startDay,
      endDay: endDay,
      storeIdList: storeIdList,
    ));
  }

  query() {
    if (currentFlowMode == 0) {
      queryPrecisionPassengerFlow();
    } else {
      queryDevicePassengerFlow();
    }
    queryStoreStatistics(queryType);
    queryStoreItems(queryType);
    // queryStoreStaffs(queryType);
    getStoreAiPassengerFlowRank(queryType);
    if (storeIdList.isEmpty) {
      queryStoreCoverRate();
      // queryStatisticalCoverageDetail();
    }
    queryStoreEvent();
  }

  queryPrecisionPassengerFlow() {
    queryPrecisionPassengerFlowQueryCount(queryType);
    // queryPrecisionPassengerFlowPageQuery(queryType);
  }

  /// 精准客流-统计数据
  queryPrecisionPassengerFlowQueryCount(int? queryTypeSer) {
    bool isTp1 = AccountInfo.instance.config.isTp1;
    bool isToday = (startTime == endTime) && XBTimeUtil.isToday(startDateTime);
    final notDeDuplicatedFlag = isTp1 && isToday;
    NetQueryUtil.getInstance()
        .passengerFlowQueryCountAI(
            storeIdList: storeIdList,
            deviceId: "",
            queryType: queryTypeSer,
            startTime: startTime,
            endTime: endTime,
            notDeDuplicatedFlag: notDeDuplicatedFlag)
        .then((value) {
      precisionQueryCount = PassengerFlowDataStatisticalQuery.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  /// 精准客流-分段数据
  queryPrecisionPassengerFlowPageQuery(int? queryTypeSer) {
    NetQueryUtil.getInstance()
        .passengerFlowPageQueryAI(
            storeIdList: storeIdList,
            deviceId: "",
            queryType: queryTypeSer,
            startTime: startTime,
            endTime: endTime,
            notDeDuplicatedFlag: false)
        .then((value) {
      precisionPageQuery = PassengerFlowDataResponsePageQuery.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  queryDevicePassengerFlow() {
    queryDevicePassengerFlowQueryCount(queryType);
    // queryDevicePassengerFlowPageQuery(queryType);
  }

  /// 端侧客流-统计数据
  queryDevicePassengerFlowQueryCount(int? queryTypeSer) {
    NetQueryUtil.getInstance()
        .passengerFlowQueryCount(
            storeIdList: storeIdList,
            deviceId: "",
            queryType: queryTypeSer,
            startTime: startTime,
            endTime: endTime)
        .then((value) {
      deviceQueryCount = PassengerFlowDataStatisticalQuery.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  /// 端侧客流-分段数据
  queryDevicePassengerFlowPageQuery(int? queryTypeSer) {
    NetQueryUtil.getInstance()
        .passengerFlowPageQuery(
            storeIdList: storeIdList,
            deviceId: '',
            queryType: queryTypeSer,
            startTime: startTime,
            endTime: endTime)
        .then((value) {
      devicePageQuery = PassengerFlowDataResponsePageQuery.fromJson(value);
      notify();
    }).catchError((e) {
      toastFailure(kErrorMsg(e));
    });
  }

  ///门店巡检次数
  queryStoreStatistics(int? queryTypeSer) async {
    Map<String, dynamic> map = {"startTime": startDay, "endTime": endDay};
    if (storeIdList.isNotEmpty) {
      map["storeIdList"] = storeIdList;
    }
    PageResult<dynamic> pageResult =
        await inspectionAPI.getStoreInspectionAnalysisStatisticsList(
            PageParam(pageNo: 1, pageSize: 10000, param: map));
    statisticsList.clear();
    if (pageResult.datas?.isNotEmpty ?? false) {
      statisticsList.addAll(pageResult.datas!
          .map<InspectionAnalysisStatistics>(
              (e) => InspectionAnalysisStatistics.fromJson(e))
          .toList());
    }
    notify();
  }

  ///门店巡检不合格占比
  queryStoreItems(int? queryTypeSer) async {
    Map<String, dynamic> map = {
      "startTime": startDay,
      "endTime": endDay,
    };
    if (storeIdList.isNotEmpty) {
      map["storeIdList"] = storeIdList;
    }
    PageResult<dynamic> pageResult =
        await inspectionAPI.getStoreInspectionAnalysisItemList(
            PageParam(pageNo: 1, pageSize: 5, param: map));
    itemsList.clear();
    xbAnnulusChartModels.clear();
    if (pageResult.datas != null) {
      itemsList.addAll(pageResult.datas!
          .map<InspectionAnalysisItems>(
              (e) => InspectionAnalysisItems.fromJson(e))
          .toList());
      int itemsTotal = itemsList[0].totalPatrolUnpassCount!;
      int colorIndex = 0;
      for (InspectionAnalysisItems element in itemsList) {
        xbAnnulusChartModels.add(XBAnnulusChartModel(
            name: element.itemName ?? "",
            value: (element.sumPatrolUnpassCount ?? 0).toDouble(),
            color: chartColorForIndex(colorIndex)));
        itemsTotal -= element.sumPatrolUnpassCount!;
        colorIndex++;
      }
      if (itemsTotal > 0) {
        xbAnnulusChartModels.add(XBAnnulusChartModel(
            name: TR.current.tr_itemOther,
            value: itemsTotal.toDouble(),
            color: chartColorForIndex(colorIndex)));
        colorIndex++;
      }
    }
    notify();
  }

  queryStoreStaffs(int? queryTypeSer) async {
    Map<String, dynamic> map = {
      "startTime": startDay,
      "endTime": endDay,
      "type": staffType,
    };
    if (storeIdList.isNotEmpty) {
      map["storeIdList"] = storeIdList;
    }
    PageResult<dynamic> pageResult =
        await inspectionAPI.getStoreInspectionAnalysisStaffList(
            PageParam(pageNo: 1, pageSize: 10, param: map));
    staffList.clear();
    if (pageResult.datas != null) {
      staffList.addAll(pageResult.datas!
          .map<InspectionAnalysisStaff>(
              (e) => InspectionAnalysisStaff.fromJson(e))
          .toList());
      for (InspectionAnalysisStaff element in staffList) {
        element.custName = element.custName!.length > 6
            ? element.custName!.substring(0, 6)
            : element.custName!;
        if (element.sumPatrolBeOverdueCount! > staffMaxLen) {
          staffMaxLen = element.sumPatrolBeOverdueCount! + 5;
        }
      }
    }
    notify();
  }

  /// 查询门店覆盖率
  queryStoreCoverRate() {
    inspectionAPI
        .queryStoreCoverRate(startTime: startDay, endTime: endDay)
        .then((value) {
      patrolCoverageRateList.clear();
      if (value != null && value is List) {
        patrolCoverageRateList.addAll(value
            .map((e) => StorePatrolCoverageRateModel.fromJson(e))
            .toList());
      }
      notify();
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }

  /// 查询事件门店排名
  queryStoreEvent() {
    Map<String, dynamic> map = {
      "startTime": startDay,
      "endTime": endDay,
    };
    if (storeIdList.isNotEmpty) {
      map["storeIdList"] = storeIdList;
    }
    inspectionAPI
        .getStoreEventStorePageQuery(
            PageParam(pageNo: 1, pageSize: 5, param: map))
        .then((value) {
      statisticalEventList.clear();
      if (value.datas != null && value.datas is List) {
        statisticalEventList.addAll(value.datas!
            .map((e) => CloudStoreStatisticalEventModel.fromJson(e))
            .toList());
      }
      notify();
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }

  /// 巡检覆盖情况
  queryStatisticalCoverageDetail() {
    inspectionAPI
        .queryStatisticalCoverageDetail(startTime: startDay, endTime: endDay)
        .then((value) {
      // patrolCoverageDetailList.clear();
      // if (value != null && value is List) {
      //   patrolCoverageDetailList.addAll(value.map((e) => patrolCoverageDetailModel.fromJson(e)).toList());
      // }
      notify();
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }

  ///门店客流排名
  getStoreAiPassengerFlowRank(int? queryTypeSer) {
    Map<String, dynamic> map = {"startTime": startTime, "endTime": endTime};
    if (queryTypeSer != null) {
      map["queryType"] = queryTypeSer;
    }
    if (storeIdList.isNotEmpty) {
      map["storeIdList"] = storeIdList;
    }
    inspectionAPI
        .getStoreAiPassengerFlowRank(
            PageParam(pageNo: 1, pageSize: 5, param: map))
        .then((value) {
      passengerFlowRankingList.clear();
      if (value.datas?.isNotEmpty ?? false) {
        passengerFlowRankingList.addAll(value.datas!
            .map<StoreFlowItemModel>((e) => StoreFlowItemModel.fromJson(e))
            .toList());
      }
      notify();
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }
}
