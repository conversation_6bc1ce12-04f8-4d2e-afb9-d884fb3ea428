import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/function/image_inspection/view/right_icon_btn.dart';
import 'package:bcloud/pages/function/store/view/data_flow_event_ranking_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_inspection_completion_rate_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_inspection_coverage_detail_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_inspection_coverage_rate_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_inspection_items_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_inspection_staff_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_inspection_time_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_passenger_customer_analysis_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_passenger_flow_data_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_passenger_flow_ranking_widget.dart';
import 'package:bcloud/pages/function/store/view/data_flow_select_date_widget.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/utils/xb_language_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'cloud_store_data_flow_vm.dart';

class CloudStoreDataFlowPage extends XBPageOver<CloudStoreDataFlowVM> {
  const CloudStoreDataFlowPage({super.key});

  @override
  generateVM(BuildContext context) {
    return CloudStoreDataFlowVM(context: context);
  }

  @override
  Widget? buildTitle(CloudStoreDataFlowVM vm) {
    return Column(
      children: [
        XBRightIconBtn(
            title: TR.current.tr_dataDashboard,
            titleFontSize: fontSizes.s17,
            titleFontWeight: fontWeights.medium,
            titleColor: colors.black,
            iconW: 16,
            iconH: 16,
            expandedTitle: false,
            onTap: vm.onChooseStore),
        Text(
          vm.selectedStoreName,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(fontSize: fontSizes.s13, color: colors.blue),
        )
      ],
    );
  }

  @override
  List<Widget>? actions(CloudStoreDataFlowVM vm) {
    if (vm.flowModeType == 0) {
      return [
        GestureDetector(
          onTap: () {
            vm.changeFlowModeMode();
          },
          child: Container(
            width: 88,
            padding: EdgeInsets.only(right: spaces.leftLess),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    vm.flowModeName,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    textAlign: XBLanguageUtil.isChinese
                        ? TextAlign.end
                        : TextAlign.center,
                    style: TextStyle(
                        fontSize: XBLanguageUtil.isChinese ? 13.sp : 12.sp,
                        color: colors.black4C,
                        fontWeight: fontWeights.normal),
                  ),
                ),
                SizedBox(
                  width: spaces.j4,
                ),
                XBLanguageUtil.isChinese
                    ? Assets.images.iconChangeModeRefresh
                        .image(width: 13.w, height: 13.w)
                    : Assets.images.iconChangeModeRefresh
                        .image(width: 14.w, height: 14.w)
              ],
            ),
          ),
        )
      ];
    } else {
      return [
        const SizedBox(
          width: 88,
        )
      ];
    }
  }

  @override
  String setTitle(CloudStoreDataFlowVM vm) {
    return TR.current.tr_dataDashboard;
  }

  @override
  Widget buildPage(CloudStoreDataFlowVM vm, BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          /// 日期选择
          DataFlowSelectDateWidget(vm: vm),

          /// 客流数
          DataFlowPassengerFlowDataWidget(vm: vm),

          /// 客群分析
          Visibility(
            visible: vm.currentFlowMode == 0,
            child: DataFlowPassengerCustomerAnalysisWidget(vm: vm),
          ),

          /// 门店客流排名
          DataFlowPassengerFlowRankingWidget(vm: vm),

          /// 巡检次数
          DataFlowInspectionTimeWidget(vm: vm),

          /// 巡检不合格占比
          DataFlowInspectionItemsWidget(vm: vm),

          /// 员工排名
          // DataFlowInspectionStaffWidget(vm: vm),

          /// 巡店任务完成率
          // DataFlowInspectionCompletionRateWidget(vm: vm),

          /// 巡店覆盖率
          Visibility(
            visible: vm.storeIdList.isEmpty,
            child: DataFlowInspectionCoverageRateWidget(vm: vm),
          ),

          /// 巡检覆盖情况
          // DataFlowInspectionCoverageDetailWidget(vm: vm),

          /// 门店事件排名
          DataFlowEventRankingWidget(vm: vm),

          SizedBox(
            height: spaces.leftLess + safeAreaBottom,
          ),
        ],
      ),
    );
  }
}
