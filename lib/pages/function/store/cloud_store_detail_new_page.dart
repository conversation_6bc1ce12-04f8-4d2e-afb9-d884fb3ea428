import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/arithmetics/view/arithmetic_statistics_passenger_flow_filter_cloud.dart';
import 'package:bcloud/pages/arithmetics/view/arithmetic_statistics_passenger_flow_filter_device.dart';
import 'package:bcloud/pages/function/store/staff_derepeat/staff_derepeat_page.dart';
import 'package:bcloud/pages/function/store/view/cloud_store_detail_heatmap.dart';
import 'package:bcloud/pages/function/store/view/store_grid_device_item.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/widget/bc_refresh.dart';
import 'package:bcloud/widget/cell/auto_switch_image_btn_cell.dart';
import 'package:bcloud/widget/cell/star_title_tip_content_right_icon_cell.dart';
import 'package:bcloud/widget/x_divider.dart';
import 'package:bcloud/widget/xb_action_btn.dart';
import 'package:bcloud/widget/xb_select_cond_widget/xb_select_cond_gradient_widget_line.dart';
import 'package:bcloud/widget/xb_select_cond_widget/xb_select_tag_cond_widget_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_refresh/xb_refresh.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'cloud_store_detail_new_vm.dart';
import 'model/cloud_store_item_model.dart';

class CloudStoreDetailNewPage extends XBPageOver<CloudStoreDetailNewVM> {
  final CloudStoreItemEntity itemEntity;
  const CloudStoreDetailNewPage({super.key, required this.itemEntity});

  @override
  generateVM(BuildContext context) {
    return CloudStoreDetailNewVM(context: context, itemEntity: itemEntity);
  }

  @override
  String setTitle(CloudStoreDetailNewVM vm) {
    return (itemEntity.storeName ?? '') +
        (itemEntity.storeName ?? '') +
        (itemEntity.storeName ?? '');
  }

  @override
  bool needShowContentFromScreenTop(CloudStoreDetailNewVM vm) {
    return true;
  }

  @override
  Widget buildPage(CloudStoreDetailNewVM vm, BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
            child: XBImage(
          images.ic_store_detail_bg,
          fit: BoxFit.fill,
        )),
        Column(
          children: [
            Container(
              height: stateBarH,
            ),
            SizedBox(
              height: 56,
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  XBButton(
                      onTap: () {
                        vm.back();
                      },
                      child: Container(
                          color: Colors.transparent,
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: EdgeInsets.only(
                                left: spaces.j5, right: spaces.j4),
                            child: XBImage(
                              images.ic_common_back,
                              width: 25,
                              height: 23,
                            ),
                          ))),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 34, right: 32),
                      child: Text(setTitle(vm),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: navigationBarTitleColor(vm),
                            fontWeight: navigationBarTitleFontWeight(vm),
                            fontSize: navigationBarTitleSize(vm),
                          )),
                    ),
                  ),
                  // XBButton(
                  //   onTap: () {
                  //     push(StaffDerepeatPage(storeId: itemEntity.id!));
                  //   },
                  //   coverTransparentWhileOpacity: true,
                  //   child: Padding(
                  //     padding: EdgeInsets.only(
                  //         left: spaces.left,
                  //         right: spaces.left,
                  //         top: 8,
                  //         bottom: 8),
                  //     child: XBImage(
                  //       images.icon_setting_blue,
                  //       width: 23,
                  //     ),
                  //   ),
                  // )
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 36.w,
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: spaces.left),
                    decoration: BoxDecoration(
                      color: colors.white,
                      borderRadius: BorderRadius.circular(18.r),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: spaces.leftLess),
                    child: XBButton(
                      effect: XBButtonTapEffect.none,
                      onTap: vm.seeStoreAddress,
                      child: Row(
                        children: [
                          Expanded(
                              child: Text(
                            vm.storeAddress,
                            maxLines: 1,
                            textAlign: TextAlign.start,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                color: colors.grey808080,
                                fontSize: fontSizes.s14,
                                fontWeight: kFontWeight_regular),
                          )),
                          SizedBox(
                            width: spaces.j4,
                          ),
                          Assets.images.icMapFuncLocation
                              .image(width: 16.w, height: 16.w)
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    top: spaces.j6,
                    left: spaces.left,
                    right: spaces.left,
                  ),
                  child: XBSelectCondGradientWidgetLine(
                      paddingV: 3,
                      itemHeight: 30,
                      selectedIndex: vm.curTypeIndex,
                      models: vm.tabItems,
                      onSelected: (index) {
                        vm.onSelected(index);
                      }),
                ),
              ],
            ),
            Expanded(
                child: PageView(
              allowImplicitScrolling: true,
              controller: vm.controller,
              children: vm.tabItemPages,
              onPageChanged: (value) {
                vm.onPageChanged(value);
              },
            )),
          ],
        )
      ],
    );
  }
}

class CloudStoreDeviceTagView extends XBWidget {
  const CloudStoreDeviceTagView({super.key, required this.detailVM});
  final CloudStoreDetailNewVM detailVM;

  double get scaleWidth => 160;
  double get scaleHeight => 128;

  double get mainAxisSpacing => 8.w;

  Widget _emptyView() {
    return Container(
      padding: EdgeInsets.only(
          left: 52.w, right: 52.w, top: spaces.leftLess, bottom: spaces.left),
      child: Column(
        children: [
          Assets.images.icCommonNoDataB.image(width: 205.w, height: 177.w),
          Text(
            detailVM.hasNoDevicesSelect(),
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 15.sp, color: const Color(0xFF808080)),
          )
        ],
      ),
    );
  }

  @override
  Widget buildWidget(XBVM vm, BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          left: spaces.left,
          right: spaces.left,
          bottom: spaces.leftLess + safeAreaBottom),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(vertical: spaces.leftLess),
            height: 30.w,
            child: ListView.separated(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: detailVM.deviceTagCount,
              itemBuilder: (context, index) {
                return XBButton(
                    effect: XBButtonTapEffect.opacity,
                    onTap: () {
                      detailVM.selectDetailTypeIndex(index);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: detailVM.currentDeviceTypeSelect == index
                            ? colors.blue
                            : colors.white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      constraints: BoxConstraints(minWidth: 107.5.w),
                      child: Text(detailVM.deviceTagItems[index],
                          style: TextStyle(
                              fontSize: 13.sp,
                              color: detailVM.currentDeviceTypeSelect == index
                                  ? colors.white
                                  : colors.grey808080)),
                    ));
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  width: 11.5.w,
                );
              },
            ),
          ),
          Visibility(
            visible: detailVM.currentDeviceTypeSelect == 1,
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: spaces.leftLess),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: Container(
                      // color: colors.blueLight.withAlpha(50),
                      decoration: BoxDecoration(
                        color: colors.blueLight.withAlpha(50),
                        borderRadius: BorderRadius.circular(6),
                        border:
                            Border.all(color: colors.blueLight.withAlpha(50)),
                      ),
                      width: double.infinity,
                      child: Padding(
                        padding: EdgeInsets.only(
                            right: spaces.j4, top: 4, bottom: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding:
                                  EdgeInsets.only(top: 4, left: 8, right: 6),
                              child: XBImage(
                                images.ic_facial_switch_platform_tip,
                                width: 14,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                TR.current.tr_deduplicationTip,
                                style: TextStyle(color: colors.black4C),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                XBButton(
                  onTap: () {
                    push(StaffDerepeatPage(storeId: detailVM.itemEntity.id!));
                  },
                  coverTransparentWhileOpacity: true,
                  child: Padding(
                    padding: EdgeInsets.only(bottom: spaces.leftLess),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Container(
                        color: colors.white,
                        height: 50,
                        width: double.infinity,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.only(left: spaces.leftLess),
                                child: Text(
                                  TR.current.tr_storeTrafficSettings,
                                  style: TextStyle(color: colors.black4C),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 8, right: spaces.leftLess),
                              child: XBImage(
                                images.icon_common_setting,
                                width: 18,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: BCRefresh(
              onRefresh: detailVM.refreshDevice,
              controller: detailVM.deviceRefreshController,
              child: detailVM.deviceInfoList.isEmpty
                  ? _emptyView()
                  : GridView.builder(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          mainAxisSpacing: mainAxisSpacing,
                          crossAxisSpacing: 8.w,
                          childAspectRatio: scaleWidth / scaleHeight),
                      itemBuilder: (context, index) {
                        return Container(
                          padding: EdgeInsets.only(
                              top: spaces.j10,
                              left: spaces.j10,
                              right: spaces.j10,
                              bottom: spaces.j8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(9.r),
                          ),
                          child: StoreGridDeviceItem(
                            device: detailVM.deviceInfoList[index],
                            chooseItemDevice: () {
                              detailVM.toDevice(detailVM.deviceInfoList[index]);
                            },
                          ),
                        );
                      },
                      itemCount: detailVM.deviceInfoList.length,
                    ),
            ),
          )
        ],
      ),
    );
    // return Container(
    //   padding: EdgeInsets.only(left: spaces.left, right: spaces.left, bottom: spaces.leftLess + safeAreaBottom),
    //   child: Column(
    //     children: [
    //       Container(
    //         margin: EdgeInsets.symmetric(vertical: spaces.leftLess),
    //         height: 30.w,
    //         child: ListView.separated(
    //           shrinkWrap: true,
    //           scrollDirection: Axis.horizontal,
    //           itemCount: detailVM.deviceTagCount,
    //           itemBuilder: (context, index) {
    //             return XBButton(
    //                 effect: XBButtonTapEffect.opacity,
    //                 onTap: () {
    //                   detailVM.selectDetailTypeIndex(index);
    //                 },
    //                 child: Container(
    //                   alignment: Alignment.center,
    //                   decoration: BoxDecoration(
    //                     color: detailVM.currentDeviceTypeSelect == index ? colors.blue : colors.white,
    //                     borderRadius: BorderRadius.circular(4.r),
    //                   ),
    //                   constraints: BoxConstraints(minWidth: 107.5.w),
    //                   child: Text(detailVM.deviceTagItems[index], style: TextStyle(
    //                       fontSize: 13.sp,
    //                       color: detailVM.currentDeviceTypeSelect == index ?
    //                       colors.white : colors.grey808080)
    //                   ),
    //                 )
    //             );
    //           },
    //           separatorBuilder: (context, index) {
    //             return SizedBox(width: 11.5.w,);
    //           },
    //         ),
    //       ),
    //       Expanded(
    //         child: StoreImgGridWidget(
    //           key: ValueKey(detailVM.currentDeviceTypeSelect),
    //           emptyTip: detailVM.hasNoDevicesSelect(),
    //           deviceInfoList: detailVM.deviceInfoList,
    //           onChooseDevice: (deviceNode) {
    //             detailVM.toDevice(deviceNode);
    //           },
    //         ),
    //       )
    //     ],
    //   ),
    // );
  }

  @override
  XBVM generateVM(BuildContext context) {
    return XBVM(context: context);
  }
}

class CloudStoreInfoView extends XBWidget {
  const CloudStoreInfoView({super.key, required this.detailVM});

  final CloudStoreDetailNewVM detailVM;

  @override
  Widget buildWidget(XBVM vm, BuildContext context) {
    return BCRefresh(
      onRefresh: detailVM.refreshInfo,
      controller: detailVM.infoRefreshController,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(
                top: spaces.leftLess, left: spaces.left, right: spaces.left),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: spaces.left),
                        child: StarTitleTipContentRightIconCell(
                          height: 54.w,
                          title: TR.current.tr_storeName,
                          titleColor: colors.black4C,
                          content: detailVM.storeName,
                          contentColor: detailVM.storeNameColor,
                          onTap: detailVM.modifyStoreName,
                          titleWidth: 76.w,
                          leftPadding: 0,
                        ),
                      ),
                      XDivider(
                        color: colors.lineDivider,
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: spaces.left),
                        child: StarTitleTipContentRightIconCell(
                          height: 54.w,
                          title: TR.current.tr_storeAddress,
                          titleColor: colors.black4C,
                          content: detailVM.storeAddressContent,
                          contentColor: detailVM.storeAddressColor,
                          onTap: detailVM.amapSearchAction,
                          titleWidth: 76.w,
                          leftPadding: 0,
                        ),
                      ),
                      XDivider(
                        color: colors.lineDivider,
                      ),
                      AutoSwitchImageRowCell(
                        title: TR.current.tr_asyncStoreLatLng,
                        hasOperate: true,
                        isOpen: detailVM.isOpen,
                        onTap: () {
                          detailVM.onChangedState();
                        },
                        fontWeight: fontWeights.normal,
                      ),
                      XDivider(
                        color: colors.lineDivider,
                      ),
                      StarTitleTipContentRightIconCell(
                        height: 54.w,
                        title: TR.current.tr_storeManager,
                        titleColor: colors.black4C,
                        content: detailVM.storeManagerNameContent,
                        contentColor: detailVM.storeManagerNameColor,
                        onTap: detailVM.chooseStoreManager,
                        titleWidth: 91.w,
                        // leftPadding: 0,
                      ),
                    ],
                  )),
            ),
          ),
          const Expanded(child: SizedBox()),
        ],
      ),
    );
  }

  @override
  XBVM generateVM(BuildContext context) {
    return XBVM(context: context);
  }
}

class PassengerFlowDataTagView extends XBWidget {
  const PassengerFlowDataTagView({super.key, required this.detailVM});
  final CloudStoreDetailNewVM detailVM;

  Widget _content() {
    if (detailVM.isShowHeatMap) {
      return CloudStoreDetailHeatmap(
        storeId: detailVM.itemEntity.id!,
        nodeId: detailVM.itemEntity.nodeId!,
      );
    }
    return Padding(
      padding: EdgeInsets.only(left: spaces.left, right: spaces.left),
      child: detailVM.isCloud
          ? ArithmeticStatisticsPassengerFlowFilterCloud(
              key: ValueKey(detailVM.statisticsKey),
              deviceId: detailVM.precisionDeviceDeviceId,
              storeIdList: detailVM.storeIdList,
              onTapFilter: detailVM.onChoosePrecisionDevice,
              isNeedFilter: detailVM.isNeedDeviceFilter,
            )
          : ArithmeticStatisticsPassengerFlowFilterDevice(
              key: ValueKey(detailVM.statisticsKey),
              deviceId: detailVM.passengerFlowDeviceId,
              storeIdList: detailVM.storeIdList,
              onTapFilter: detailVM.onChoosePassengerFlowDevice,
              isNeedFilter: detailVM.isNeedDeviceFilter,
            ),
    );
  }

  @override
  Widget buildWidget(XBVM vm, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 54,
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.symmetric(horizontal: spaces.left),
          child: XBSelectTagCondWidgetLine(
              selectedIndex: detailVM.selectedTypeIndex,
              models: detailVM.titles,
              onSelected: detailVM.onSelectedType),
        ),

        /// 统计数据
        Expanded(
            child: BCRefresh(
          onRefresh: detailVM.refreshPassengerFlow,
          controller: detailVM.passengerRefreshController,
          child: CustomScrollView(slivers: [
            SliverToBoxAdapter(
              child: SingleChildScrollView(
                child: _content(),
              ),
            )
          ]),
          // child: SingleChildScrollView(
          //   child: Padding(
          //     padding: EdgeInsets.only(
          //         left: spaces.left, right: spaces.left),
          //     child: detailVM.isCloud
          //         ? ArithmeticStatisticsPassengerFlowFilterCloud(
          //       key: ValueKey(detailVM.statisticsKey),
          //       deviceId: detailVM.precisionDeviceDeviceId,
          //       storeIdList: detailVM.storeIdList,
          //       onTapFilter: detailVM.onChoosePrecisionDevice,
          //       isNeedFilter: detailVM.isNeedDeviceFilter,
          //     )
          //         : ArithmeticStatisticsPassengerFlowFilterDevice(
          //       key: ValueKey(detailVM.statisticsKey),
          //       deviceId: detailVM.passengerFlowDeviceId,
          //       storeIdList: detailVM.storeIdList,
          //       onTapFilter: detailVM.onChoosePassengerFlowDevice,
          //       isNeedFilter: detailVM.isNeedDeviceFilter,
          //     ),
          //   ),
          // ),
        ))
      ],
    );
  }

  @override
  XBVM generateVM(BuildContext context) {
    return XBVM(context: context);
  }
}
