import 'dart:async';

import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/config/events.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/arithmetics/arithmetics_ai_box_arithmetics_list_page.dart';
import 'package:bcloud/pages/device_add/model/device_add_center.dart';
import 'package:bcloud/pages/preview/model/device.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:xb_refresh/xb_refresh_controller.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'ai_box_add_channel_page.dart';
import 'ai_box_channel_list_page.dart';

class AiBoxChannelListVM extends XBPageVM<AiBoxChannelListPage> {
  final XBRefreshController refreshController = XBRefreshController();
  late StreamSubscription subscribe;
  final List<DeviceInfo> itemList = [];
  AiBoxChannelListVM({required super.context}) {
    _addNotice();
    queryChannels();
  }

  _addNotice() {
    subscribe = eventBus.on<EventAiBoxChannelChange>().listen((event) {
      queryChannels(isShowLoading: false);
    });
  }

  refresh() {
    queryChannels(isShowLoading: false);
  }

  queryChannels({bool isShowLoading = true}) {
    if (isShowLoading) {
      showLoadingGlobal();
    }
    Map<String, dynamic> param = {
      "pageCurrent": 1,
      "pageSize": 20,
      "completeDeviceSn": widget.deviceSN,
      "deviceType": 4,
    };
    deviceTreeAPI.queryPageIotDevice(param).then((value) {
      if (isShowLoading) {
        hideLoadingGlobal();
      }
      refreshController.endRefresh();
      itemList.clear();
      if ((value.datas?.isNotEmpty ?? false) && value.datas is List) {
        List<DeviceInfo> list = value.datas!.map((e) => DeviceInfo.fromJson(e)).toList();
        itemList.addAll(list);
      }
      notify();
    }).catchError((e) {
      if (isShowLoading) {
        hideLoadingGlobal();
      }
      refreshController.endRefresh();
      toastFailure(kErrorMsg(e));
    });
  }

  String protocolName(int deviceNetworkType) {
    if (deviceNetworkType == 5) {
      return '(${TR.current.tr_onvifProtocol})';
    } else if (deviceNetworkType == 6) {
      return '(${TR.current.tr_JFProtocol})';
    } else {
      return '';
    }
  }

  bool get isEmpty => itemList.isEmpty;
  int get itemCount => itemList.length;

  addChannel() async {
    DeviceAddCenter.instance.baseDeviceType = DeviceAddBaseDeviceType.aiBox;
    await push(AiBoxAddChannelPage(deviceId: widget.deviceId, nodeId: widget.nodeId,));
  }

  onTap(DeviceInfo deviceInfo) async {
    String id = deviceInfo.id;
    String deviceName = deviceInfo.deviceName;
    String parentId = deviceInfo.parentId;
    await push(ArithmeticsAiBoxArithmeticsListPage(
      deviceId: id,
      deviceName: deviceName,
      parentId: parentId,
    ));
  }

  @override
  void dispose() {
    subscribe?.cancel();
    super.dispose();
  }
}
