import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/device_tree/model/device_channel_list_model.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/widget/x_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'ai_box_batch_add_channels_vm.dart';

class AiBoxBatchAddChannelsPage extends XBPageOver<AiBoxBatchAddChannelsVM> {
  final String deviceId;
  final List<DeviceChannelModel> itemList;
  final String? nodeId;
  const AiBoxBatchAddChannelsPage({super.key, required this.deviceId, required this.itemList, this.nodeId});

  @override
  generateVM(BuildContext context) {
    return AiBoxBatchAddChannelsVM(context: context);
  }

  @override
  String setTitle(AiBoxBatchAddChannelsVM vm) {
    return TR.current.tr_addBatches;
  }

  @override
  bool needAdaptKeyboard(AiBoxBatchAddChannelsVM vm) {
    return true;
  }

  @override
  Widget buildPage(AiBoxBatchAddChannelsVM vm, BuildContext context) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.symmetric(
              vertical: spaces.leftLess, horizontal: spaces.left),
          alignment: Alignment.center,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.w),
              color: kColorHexStr('#4796F2', alpha: 0.1),
              border: Border.all(
                  color: kColorHexStr('#4796F2', alpha: 0.2), width: onePixel)),
          child: Padding(
            padding: EdgeInsets.only(left: 9.w, right: 8.w, top: 6.5.w, bottom: 7.w),
            child: Row(
              // crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    right: 5.5.w,
                  ),
                  child: XBImage(
                    images.ic_facial_switch_platform_tip,
                    width: 14.w,
                    height: 14.w,
                  ),
                ),
                Expanded(
                  child: Text(
                    TR.current.tr_batchAddChannelTips,
                    maxLines: 4,
                    textAlign: TextAlign.start,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: fontWeights.normal,
                        color: colors.black4C),
                  ),
                ),
              ],
            ),
          ),
        ),
        Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: spaces.left),
              decoration: BoxDecoration(
                color: colors.white,
                borderRadius: BorderRadius.all(Radius.circular(6.r)),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: spaces.left, top: 12.5, bottom: 7.5),
                        child: Text(
                          TR.current.tr_pleaseChooseDevice,
                          style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: fontWeights.medium,
                              color: colors.black),
                          textAlign: TextAlign.start,
                        ),
                      ),
                      const Spacer(),
                    ],
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemBuilder: (BuildContext context, int index) {
                        DeviceChannelModel model = vm.itemList[index];
                        return XBButton(
                          onTap: () {
                            vm.onTapItem(model);
                          },
                          child: (model.exceptionMsg?.isNotEmpty ?? false)
                              ? Padding(
                            padding: EdgeInsets.symmetric(horizontal: spaces.left),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  height: spaces.leftLess,
                                ),
                                Row(
                                  children: [
                                    Assets.images.icDeviceTreeIpc
                                        .image(width: 24.w, height: 24.w),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Expanded(
                                      child: Text(
                                        model.hostIP ?? '',
                                        style: TextStyle(
                                            fontSize: 14.sp,
                                            fontWeight: fontWeights.medium,
                                            color: colors.black4C),
                                        textAlign: TextAlign.start,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 12.w,
                                    ),
                                    model.isSelect ? Assets.images.iconChooseDeviceSelected.image(width: 20.w, height: 20.w)
                                        : Assets.images.iconChooseDeviceUnselected.image(width: 20.w, height: 20.w),
                                  ],
                                ),
                                SizedBox(
                                  height: spaces.j4,
                                ),
                                Container(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '${model.exceptionMsg}',
                                    style: TextStyle(
                                        fontSize: 12.sp,
                                        fontWeight: fontWeights.normal,
                                        color: kColorHexStr('#FB6148')),
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                                SizedBox(
                                  height: spaces.j4,
                                ),
                              ],
                            ),
                          )
                              : Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: spaces.left,
                              vertical: spaces.leftLess,
                            ),
                            child: Row(
                              children: [
                                Assets.images.icDeviceTreeIpc
                                    .image(width: 24.w, height: 24.w),
                                SizedBox(
                                  width: 8.w,
                                ),
                                Expanded(
                                  child: Text(
                                    model.hostIP ?? '',
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: fontWeights.medium,
                                        color: colors.black4C),
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                                SizedBox(
                                  width: 12.w,
                                ),
                                model.isSelect ? Assets.images.iconChooseDeviceSelected.image(width: 20.w, height: 20.w)
                                    : Assets.images.iconChooseDeviceUnselected.image(width: 20.w, height: 20.w),
                              ],
                            ),
                          ),
                        );
                      },
                      itemCount: vm.itemCount,
                    ),
                  ),
                ],
              ),
            )
        ),
        SizedBox(
          height: spaces.leftLess,
        ),

        Container(
          color: colors.white,
          height: 58.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(bottom: safeAreaBottom),
          padding: EdgeInsets.only(left: 28, right: 30),
          child: Row(
            children: [
              XBButton(
                  onTap: vm.onBatchTap,
                  child: Container(
                    color: Colors.transparent,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        vm.isAllSelect ? Assets.images.iconChooseDeviceSelected.image(width: 20.w, height: 20.w)
                            : Assets.images.iconChooseDeviceUnselected.image(width: 20.w, height: 20.w),
                        SizedBox(width: spaces.leftLess,),
                        Text(
                          vm.isAllSelect ? TR.current.tr_Cancel : TR.current.tr_Common_SelectAll,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: fontWeights.semiBold,
                              color: colors.black4C
                          ),
                        ),
                      ],
                    ),
                  )
              ),
              const Spacer(),
              XButton(
                text: TR.current.tr_Next,
                height: 28.w,
                width: 65.w,
                onTap: () {
                  vm.onNextTap();
                },
              )
            ],
          ),
        )
      ],
    );
  }
}
