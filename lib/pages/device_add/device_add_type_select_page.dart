import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/widget/x_divider.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'device_add_type_select_vm.dart';

class DeviceAddTypeSelectPage extends XBPageOver<DeviceAddTypeSelectVM> {
  const DeviceAddTypeSelectPage({Key? key}) : super(key: key);

  @override
  generateVM(BuildContext context) {
    return DeviceAddTypeSelectVM(context: context);
  }

  @override
  Color? backgroundColor(DeviceAddTypeSelectVM vm) => Colors.white;

  @override
  String setTitle(DeviceAddTypeSelectVM vm) {
    return TR.current.tr_DeviceAdd;
  }

  @override
  Widget buildPage(DeviceAddTypeSelectVM vm, BuildContext context) {
    return Column(
      children: [
        XDivider(
          color: colors.lineDivider,
        ),
        Container(
          constraints: BoxConstraints(
            minHeight: 54.w,
          ),
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                TR.current.tr_Node,
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: kFontWeight_Semibold,
                    color: kColorHexStr('#808080')),
                textAlign: TextAlign.start,
              ),
              SizedBox(
                width: spaces.left,
              ),
              Expanded(
                  child: XBButton(
                      onTap: () {
                        vm.onTap();
                      },
                      child: Container(
                        margin: EdgeInsets.only(left: spaces.j8),
                        color: Colors.transparent,
                        alignment: Alignment.centerRight,
                        child: Row(
                          children: [
                            Expanded(
                                child: Container(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    vm.currentNodeName,
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        fontWeight: kFontWeight_Semibold,
                                        color: colors.black),
                                    textAlign: TextAlign.end,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                )),
                            Icon(
                              Icons.arrow_forward_ios, //arrow_back
                              color: colors.black,
                              size: 13.8,
                            ),
                          ],
                        ),
                      ))),
            ],
          ),
        ),
        XDivider(
          color: colors.lineDivider,
          preIndent: spaces.left,
          sufIndent: spaces.left,
        ),
        Container(
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.symmetric(vertical: spaces.leftLess, horizontal: spaces.left),
          padding: EdgeInsets.symmetric(vertical: spaces.j8, horizontal: spaces.leftLess),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              gradient: const LinearGradient(colors: [Color(0xFF448CF7), Color(0xFF6FB9F8)],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              )
          ),
          child: Text(
            TR.current.tr_addDevicesChannelTips(vm.deviceChannelMaxCount, vm.deviceChannelRemainingCount),
            maxLines: 2,
            style: TextStyle(
                fontSize: 14.sp,
                fontWeight: kFontWeight_regular,
                color: colors.white),
          ),
        ),
        _DeviceAddTypeItem(
          imagePath: images.ic_device_add_type_platform,
          title: TR.current.tr_JFDevice,
          subTitle: TR.current.tr_JFDeviceNotes,
          onTap: () {
            vm.addJfDevice();
          },
        ),
        _DeviceAddTypeItem(
          imagePath: images.ic_device_add_type_standard,
          title: TR.current.tr_NationalStandardDevice,
          subTitle: TR.current.tr_NationalStandardDeviceNotes,
          onTap: () {
            vm.addStandardDevice();
          },
        ),
        // _DeviceAddTypeItem(
        //   imagePath: 'assets/images/ic_device_add_type_onvif.png',
        //   title: 'ONVIF 设备',
        //   subTitle: '添加 ONVIF 设备',
        //   onTap: () {
        //     DeviceAddCenter.instance.baseDeviceType = DeviceAddBaseDeviceType.onvif;
        //   },
        // ),
        // _DeviceAddTypeItem(
        //   imagePath: 'assets/images/ic_device_add_type_other.png',
        //   title: '其他设备',
        //   subTitle: '添加其他协议的设备',
        //   onTap: () {
        //     DeviceAddCenter.instance.baseDeviceType = DeviceAddBaseDeviceType.other;
        //   },
        // ),
        //功能暂时不发布
        // _DeviceAddTypeItem(
        //   imagePath: images.ic_device_add_type_ai_box,
        //   title: TR.current.tr_aiBox,
        //   subTitle: TR.current.tr_aiBoxTip,
        //   onTap: () {
        //     vm.addAIBoxDevice();
        //   },
        // ),
      ],
    );
  }
}

class _DeviceAddTypeItem extends StatefulWidget {
  final String imagePath;
  final String title;
  final String subTitle;
  final Function() onTap;
  const _DeviceAddTypeItem(
      {Key? key,
      required this.imagePath,
      required this.title,
      required this.subTitle,
      required this.onTap})
      : super(key: key);

  @override
  State<_DeviceAddTypeItem> createState() => _DeviceAddTypeItemState();
}

class _DeviceAddTypeItemState extends State<_DeviceAddTypeItem> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTap();
      },
      child: Container(
        // height: 80.w,
        margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 12.w),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          // color: kColorHexStr('#FFFFFF', alpha: 0.5),
          borderRadius: BorderRadius.all(Radius.circular(6.r)),
          border: Border.all(
            color: kColorHexStr('#E1E1E1'), // 边框颜色
            width: 0.5, // 边框宽度
          ),
        ),
        child: Row(
          children: [
            SizedBox(
                width: 49.w,
                height: 49.w,
                child: Image.asset(
                  widget.imagePath,
                )),
            SizedBox(
              width: 12.w,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: kFontWeight_Semibold,
                      color: kColorHexStr('#4C4C4C')),
                  textAlign: TextAlign.start,
                ),
                SizedBox(
                  height: 2.w,
                ),
                Text(
                  widget.subTitle,
                  style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: kFontWeight_regular,
                      color: kColorHexStr('#808080')),
                  textAlign: TextAlign.start,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
