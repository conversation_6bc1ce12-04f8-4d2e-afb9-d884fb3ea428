import 'dart:io';
import 'package:app_settings/app_settings.dart';
import 'package:bcloud/config/app_plugin.dart';
import 'package:bcloud/config/events.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/utils/download_list_util/download_list_task.dart';
import 'package:bcloud/utils/download_list_util/download_list_util.dart';
import 'package:bcloud/utils/xb_permission_util/xb_permission_util.dart';
import 'package:bcloud/widget/xb_video_player/xb_video_player.dart';
import 'package:bcloudsdk_flutter/api/media_download/cloud_video_download_controller.dart';
import 'package:bcloudsdk_flutter/media/media_player_controller.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:flutter/material.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:share_plus/share_plus.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'download_list.dart';

class DownloadListVM extends XBPageVM<DownloadList> {
  DownloadListVM({required super.context}) {
    listen<EventDownloadStateChanged>((_) => _getCompletedThumbanil());
    _getCompletedThumbanil();
  }

  _getCompletedThumbanil() async {
    List<DownloadListTask> tasks = DownloadListUtil.completedTasks;
    for (var task in tasks) {
      final img = _imgs[task.storeKey];
      if (img == null) {
        try {
          final filePath = await DownloadListUtil.filePathForTask(task);
          await VideoThumbnail.thumbnailFile(
            video: filePath,
            // thumbnailPath: (await getTemporaryDirectory()).path,
            imageFormat: ImageFormat.PNG,
            maxHeight: 128, // 生成缩略图的最大高度
            quality: 75,
          );
          final thumbnailPath = filePath.replaceAll('.mp4', '.png');
          File file = File(thumbnailPath);
          bool exists = await file.exists();
          if (exists) {
            _imgs[task.storeKey] = thumbnailPath;
          }
        } catch (e) {
          xbError("获取缩略图失败：$e");
        }
      }
    }
    notify();
  }

  int condIndex = 0;

  bool isEdit = false;

  onChangeShowStateCond(int index) {
    condIndex = index;
    notify();
  }

  bool get isCanShare => DownloadListUtil.completedSelectTasks.length == 1;

  bool get isCanDownload => DownloadListUtil.completedSelectTasks.isNotEmpty;

  onSelectedAll() {
    if (isSelectedAll) {
      if (condIndex == 0) {
        DownloadListUtil.unselectedAllDownloadingTasksForShow();
      } else {
        DownloadListUtil.unselectedAllCompletedTasks();
      }
    } else {
      if (condIndex == 0) {
        DownloadListUtil.selectedAllDownloadingTasksForShow();
      } else {
        DownloadListUtil.selectedAllCompletedTasks();
      }
    }
    notify();
  }

  onDeleteSelected() {
    if (DownloadListUtil.selectedTasks.isEmpty) {
      return;
    }
    dialog(
        title: TR.current.tr_gentleReminder,
        msg: TR.current.tr_SureDelete,
        msgStyle: TextStyle(color: colors.grey808080, fontSize: fontSizes.s15),
        btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
        btnHighLightColor: colors.blue,
        onSelected: (dlIndex) {
          if (dlIndex == 1) {
            DownloadListUtil.deleteSelectedTasks();
          }
        });
  }

  onDeleteTask(DownloadListTask task) {
    dialog(
        title: TR.current.tr_gentleReminder,
        msg: TR.current.tr_SureDelete,
        msgStyle: TextStyle(color: colors.grey808080, fontSize: fontSizes.s15),
        btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
        btnHighLightColor: colors.blue,
        onSelected: (dlIndex) {
          if (dlIndex == 1) {
            DownloadListUtil.deleteTask(task);
          }
        });
  }

  bool get isSelectedAll {
    if (condIndex == 0) {
      return DownloadListUtil.isSelectedAllDownloadingTasksForShow;
    } else {
      return DownloadListUtil.isSelectedAllCompletedTasks;
    }
  }

  edit() {
    isEdit = !isEdit;
    if (!isEdit) {
      DownloadListUtil.unselectedAll();
    }
    notify();
  }

  int get itemCount => selectedList().length;

  DownloadListTask getTask(int index) => selectedList()[index];

  List<DownloadListTask> selectedList() {
    if (condIndex == 0) {
      return DownloadListUtil.downloadingTasksForShow;
    } else {
      return DownloadListUtil.completedTasks;
    }
  }

  onChangeSelectState(int index) {
    DownloadListTask task = getTask(index);
    task.isSelected = !task.isSelected;
    notify();
  }

  final XBPreventMultiTask _multiTask =
      XBPreventMultiTask(intervalMilliseconds: 500);

  onPauseOrStart(int index) {
    _multiTask.execute(() {
      DownloadListTask task = getTask(index);
      if (task.state == CloudVideoDownloadState.downloading) {
        DownloadListUtil.pauseDownloadTask(task);
      } else {
        if (DownloadListUtil.isHasFree) {
          DownloadListUtil.executeTask(task);
        } else {
          toastFailure(TR.current.tr_maximumNameTasksDownloadLimit(
              DownloadListUtil.maxConcurrent));
        }
      }
    });
  }

  isPause(DownloadListTask task) {
    return !task.isDownloadingForShow;
  }

  ///
  Map<String, dynamic> _imgs = {};

  String image(DownloadListTask task) {
    final ret = _imgs[task.storeKey];
    if (ret == null) {
      return images.icon_no_image;
    }
    return ret;
  }

  @override
  void back<O extends Object?>([O? result]) {
    DownloadListUtil.unselectedAll();
    super.back(result);
  }

  String markImg(DownloadListTask task) {
    return task.type == MediaType.card
        ? images.icon_download_list_card
        : images.icon_download_list_cloud;
  }

  onPlay(int index) async {
    DownloadListTask task = getTask(index);
    final filePath = await DownloadListUtil.filePathForTask(task);
    push(XBVideoPlayer(
        source: filePath, type: XBVideoPlayerSourceType.localPath));
  }

  ///单个视频文件分享
  onShareTask() async {
    if (condIndex == 0 || !isCanShare) {
      return;
    }
    List<DownloadListTask> taskList = DownloadListUtil.completedSelectTasks;
    if (taskList.isEmpty) {
      return;
    }
    DownloadListTask task = taskList.first;
    if (task.state == CloudVideoDownloadState.done) {
      String filePath = await DownloadListUtil.filePathForTask(task);
      List<XFile> shareFiles = [XFile(filePath)];
      ShareParams params = ShareParams(files: shareFiles);
      ShareResult shareResult = await SharePlus.instance.share(params);
      if (shareResult.status == ShareResultStatus.success) {
        toast(TR.current.tr_ShareSuccess);
      }
    }
  }

  /// 保存视频到手机相册
  onDownload() {
    List<DownloadListTask> taskList = DownloadListUtil.completedSelectTasks;
    if (taskList.isEmpty) {
      return;
    }
    dialog(
        title: TR.current.tr_gentleReminder,
        msg: TR.current.tr_SureSaveToAlbum,
        msgStyle: TextStyle(color: colors.grey808080, fontSize: fontSizes.s15),
        btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
        btnHighLightColor: colors.blue,
        onSelected: (dlIndex) async {
          if (dlIndex == 1) {
            _download(taskList);
          }
        });
  }

  _download(List<DownloadListTask> taskList) async {
    bool success = true;
    showLoading();
    for (var task in taskList) {
      String filePath = await DownloadListUtil.filePathForTask(task);
      final saveRet = await _saveFile(filePath);
      if (!saveRet && taskList.indexOf(task) == 0) {
        success = false;
        break;
      }
    }
    hideLoading();
    if (success) {
      toastSuccess(TR.current.tr_Downloaded);
    } else {
      if (FlutterAppPlugin.isHuaweiVersion) {
        FlutterAppPlugin.showRequestPermissionDialog(4, callback: () async {
          _checkPermission(taskList);
        });
      } else {
        _checkPermission(taskList);
      }
    }
  }

  _checkPermission(List<DownloadListTask> taskList) async {
    final bool hasPermission =
        await XBPermissionUtil.checkStoragePermissionAsync();
    if (hasPermission) {
      _download(taskList);
    } else {
      dialog(
          title: TR.current.tr_gentleReminder,
          msg: TR.current.openStoragePermissionTip,
          msgStyle:
              TextStyle(color: colors.grey808080, fontSize: fontSizes.s15),
          btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
          btnHighLightColor: colors.blue,
          onSelected: (dlIndex) {
            if (dlIndex == 1) {
              AppSettings.openAppSettings();
            }
          });
    }
  }

  Future<bool> _saveFile(filePath) async {
    try {
      final result = await ImageGallerySaver.saveFile(filePath);
      final isSuccess = result['isSuccess'];
      return isSuccess;
    } catch (e) {
      return false;
    }
  }
}
