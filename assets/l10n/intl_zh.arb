{"@@locale": "zh", "local": "zh", "tr_localRecording": "本地录像", "tr_storageCardManagement": "存储卡管理", "tr_storageManagement": "存储卡管理", "tr_passengerFlowOsdSwitch": "客流OSD开关", "tr_batchSetting": "批量设置", "tr_batchSettingChannel": "批量设置通道", "tr_ErrorCode": "错误码", "tr_UnknownError": "未知错误", "tr_ErrorCode_Minus_1": "数据解析失败", "tr_ErrorCode_Minus_1000": "网络错误", "tr_ErrorCode_Minus_1001": "发送缓冲区已满", "tr_ErrorCode_Minus_1002": "网络发送失败", "tr_ErrorCode_Minus_1003": "网络接收失败", "tr_ErrorCode_Minus_1004": "网络超时", "tr_ErrorCode_Minus_1005": "没有对象", "tr_ErrorCode_Minus_1006": "创建失败", "tr_ErrorCode_Minus_1007": "连接失败", "tr_ErrorCode_Minus_1008": "超时", "tr_ErrorCode_Minus_1009": "无连接", "tr_ErrorCode_Minus_1010": "socket异常", "tr_ErrorCode_Minus_1011": "socket关闭异常", "tr_ErrorCode_Minus_1012": "创建缓存失败", "tr_ErrorCode_Minus_1013": "网络忙", "tr_ErrorCode_Minus_1014": "监听异常", "tr_ErrorCode_Minus_1015": "接收异常", "tr_ErrorCode_Minus_1016": "无缓冲区", "tr_ErrorCode_Minus_1017": "网络错误或DNS配置错误", "tr_ErrorCode_Minus_1018": "开发者账号未鉴权", "tr_ErrorCode_Minus_10000": "您的请求是非法的哦", "tr_ErrorCode_Minus_10001": "系统没有初始化", "tr_ErrorCode_Minus_10002": "参数不正确", "tr_ErrorCode_Minus_10003": "句柄无效", "tr_ErrorCode_Minus_10004": "SDK清理出错", "tr_ErrorCode_Minus_10005": "您的网络连接超时，请重试", "tr_ErrorCode_Minus_10006": "存储空间不足", "tr_ErrorCode_Minus_10007": "网络连接失败", "tr_ErrorCode_Minus_10008": "打开文件失败", "tr_ErrorCode_Minus_10009": "未知错误", "tr_ErrorCode_Minus_11000": "数据不正确，可能版本不匹配", "tr_ErrorCode_Minus_11001": "版本不支持", "tr_ErrorCode_Minus_11200": "打开通道失败", "tr_ErrorCode_Minus_11201": "关闭通道失败", "tr_ErrorCode_Minus_11202": "建立媒体子连接失败", "tr_ErrorCode_Minus_11203": "媒体子连接通讯失败", "tr_ErrorCode_Minus_11204": "视频链接达到最大", "tr_ErrorCode_Minus_11300": "无权限", "tr_ErrorCode_Minus_11301": "错误的密码", "tr_ErrorCode_Minus_11302": "用户不存在", "tr_ErrorCode_Minus_11303": "用户被锁定，请重启设备", "tr_ErrorCode_Minus_11304": "该用户不允许访问", "tr_ErrorCode_Minus_11305": "该用户已登陆", "tr_ErrorCode_Minus_11306": "该用户没有登陆", "tr_ErrorCode_Minus_11307": "设备可能不在线", "tr_ErrorCode_Minus_11308": "用户输入不合法", "tr_ErrorCode_Minus_11309": "索引重复", "tr_ErrorCode_Minus_11310": "对象不存在", "tr_ErrorCode_Minus_11311": "对象不存在", "tr_ErrorCode_Minus_11312": "对象正在使用", "tr_ErrorCode_Minus_11313": "子集超范围", "tr_ErrorCode_Minus_11314": "密码不正确", "tr_ErrorCode_Minus_11315": "密码不匹配", "tr_ErrorCode_Minus_11316": "保留帐号", "tr_ErrorCode_Minus_11317": "不支持此种加密方式登录", "tr_ErrorCode_Minus_11318": "账号密码不对", "tr_ErrorCode_Minus_11400": "保存配置后需要重启应用程序", "tr_ErrorCode_Minus_11401": "请重启设备", "tr_ErrorCode_Minus_11402": "写文件出错", "tr_ErrorCode_Minus_11403": "配置特性不支持", "tr_ErrorCode_Minus_11404": "配置校验失败", "tr_ErrorCode_Minus_11405": "配置不存在", "tr_ErrorCode_Minus_11406": "配置解析出错,可能不支持该配置", "tr_ErrorCode_Minus_11500": "暂停失败", "tr_ErrorCode_Minus_11501": "没有找到文件", "tr_ErrorCode_Minus_11502": "配置未启用", "tr_ErrorCode_Minus_11503": "视频流未开启", "tr_ErrorCode_Minus_11600": "创建连接失败", "tr_ErrorCode_Minus_11601": "连接失败", "tr_ErrorCode_Minus_11602": "域名解析失败", "tr_ErrorCode_Minus_11603": "发送数据失败", "tr_ErrorCode_Minus_11605": "服务繁忙", "tr_ErrorCode_Minus_11609": "连接受限，或访问服务器失败", "tr_ErrorCode_Minus_11612": "服务器连接数已满", "tr_ErrorCode_Minus_11700": "盗版设备", "tr_ErrorCode_Minus_20221": "验证次数超过限制，需重启设备再尝试", "tr_ErrorCode_Minus_66000": "无效登录方式", "tr_ErrorCode_Minus_70000": "透转DVR的错误值", "tr_ErrorCode_Minus_70101": "未知错误", "tr_ErrorCode_Minus_70102": "版本不支持", "tr_ErrorCode_Minus_70103": "非法请求", "tr_ErrorCode_Minus_70104": "用户已登录", "tr_ErrorCode_Minus_70105": "用户未登录", "tr_ErrorCode_Minus_70106": "用户名或密码不正确", "tr_ErrorCode_Minus_70107": "无设备功能权限", "tr_ErrorCode_Minus_70108": "超时", "tr_ErrorCode_Minus_70109": "搜索失败，未找到相应的文件", "tr_ErrorCode_Minus_70110": "搜索成功，返回所有文件", "tr_ErrorCode_Minus_70111": "搜索成功，返回部分文件", "tr_ErrorCode_Minus_70112": "用户已存在", "tr_ErrorCode_Minus_70113": "该用户不存在", "tr_ErrorCode_Minus_70114": "该用户组已经存在", "tr_ErrorCode_Minus_70115": "该用户组不存在", "tr_ErrorCode_Minus_70116": "盗版软件", "tr_ErrorCode_Minus_70117": "消息格式不正确", "tr_ErrorCode_Minus_70118": "未设置云台协议", "tr_ErrorCode_Minus_70119": "未找到录像文件", "tr_ErrorCode_Minus_70120": "配置未启用", "tr_ErrorCode_Minus_70121": "前端未连接视频源", "tr_ErrorCode_Minus_70122": "NAT链接已用尽，不允许新的NAT连接", "tr_ErrorCode_Minus_70123": "Tcp视频链接达到最大，不允许新的Tcp视频链接", "tr_ErrorCode_Minus_70124": "用户名和密码的加密算法错误", "tr_ErrorCode_Minus_70125": "创建了其它用户，不能再用admin登陆", "tr_ErrorCode_Minus_70126": "登录太频繁，稍后重试", "tr_ErrorCode_Minus_70128": "设备受限，如已购流量套餐，请重启设备。", "tr_ErrorCode_Minus_70129": "禁止远程登陆", "tr_ErrorCode_Minus_70130": "NAS地址已存在", "tr_ErrorCode_Minus_70131": "路径正在被使用，无法操作", "tr_ErrorCode_Minus_70132": "NAS已达到支持的最大值,不允许继续添加", "tr_ErrorCode_Minus_70140": "消费类产品遥控器绑定按的键错了", "tr_ErrorCode_Minus_70150": "成功，设备需要重启", "tr_ErrorCode_Minus_70153": "无SD卡", "tr_ErrorCode_Minus_70160": "视频备份失败", "tr_ErrorCode_Minus_70161": "没有录制设备或设备未录制", "tr_ErrorCode_Minus_70162": "设备正在添加过程中", "tr_ErrorCode_Minus_70163": "APS客户特殊的密码错误返回值", "tr_ErrorCode_Minus_70164": "设备空间不足", "tr_ErrorCode_Minus_70165": "设备正忙，当前未使用", "tr_ErrorCode_Minus_70184": "当前设备电量低于”实时录像模式“要求的最低值，请在设备充电后重试", "tr_ErrorCode_Minus_70202": "未登录", "tr_ErrorCode_Minus_70203": "登录设备密码错误", "tr_ErrorCode_Minus_70205": "非法用户", "tr_ErrorCode_Minus_70206": "帐户被锁定，登录错误", "tr_ErrorCode_Minus_70207": "帐户已列入黑名单", "tr_ErrorCode_Minus_70208": "用户已使用", "tr_ErrorCode_Minus_70209": "输入无效", "tr_ErrorCode_Minus_70210": "如果要添加的用户已经存在，则索引重复", "tr_ErrorCode_Minus_70211": "用于查询时对象不存在", "tr_ErrorCode_Minus_70212": "对象不存在", "tr_ErrorCode_Minus_70213": "目标正在使用中，如果组已使用，则无法删除", "tr_ErrorCode_Minus_70214": "子集超出范围", "tr_ErrorCode_Minus_70215": "密码不正确", "tr_ErrorCode_Minus_70216": "密码不匹配", "tr_ErrorCode_Minus_70217": "保留帐户", "tr_ErrorCode_Minus_70218": "系统维护期间无法登录", "tr_ErrorCode_Minus_70219": "验证次数超过限制，需重启设备再尝试", "tr_ErrorCode_Minus_70220": "答案错误", "tr_ErrorCode_Minus_70222": "验证码错误", "tr_ErrorCode_Minus_70502": "502 命令不合法", "tr_ErrorCode_Minus_70503": "对讲已经开启", "tr_ErrorCode_Minus_70504": "对讲未开启", "tr_ErrorCode_Minus_70602": "需要重新启动应用程序", "tr_ErrorCode_Minus_70603": "需要重新启动设备", "tr_ErrorCode_Minus_70604": "写入文件失败", "tr_ErrorCode_Minus_70605": "功能不支持", "tr_ErrorCode_Minus_70606": "验证失败", "tr_ErrorCode_Minus_70607": "配置解析错误", "tr_ErrorCode_Minus_70609": "配置不存在", "tr_ErrorCode_Minus_79001": "未知错误", "tr_ErrorCode_Minus_79002": "查询服务器失败", "tr_ErrorCode_Minus_79004": "离线", "tr_ErrorCode_Minus_79005": "无法连接到服务器", "tr_ErrorCode_Minus_79007": "连接数已满", "tr_ErrorCode_Minus_79008": "未连接", "tr_ErrorCode_Minus_79020": "连接超时", "tr_ErrorCode_Minus_79021": "连接服务器拒绝连接请求", "tr_ErrorCode_Minus_79022": "查询状态超时", "tr_ErrorCode_Minus_79023": "查询外网信息超时", "tr_ErrorCode_Minus_79024": "网络握手超时", "tr_ErrorCode_Minus_79025": "查询服务器失败", "tr_ErrorCode_Minus_79026": "心跳超时", "tr_ErrorCode_Minus_79027": "连接断开", "tr_ErrorCode_Minus_90000": "用户取消", "tr_ErrorCode_Minus_90001": "文件非法", "tr_ErrorCode_Minus_101": "密码不正确", "tr_ErrorCode_Minus_102": "账号不存在", "tr_ErrorCode_Minus_103": "登录超时(网络连接失败)", "tr_ErrorCode_Minus_104": "账号未登录", "tr_ErrorCode_Minus_105": "账号已登录", "tr_ErrorCode_Minus_106": "账号被列为黑名单", "tr_ErrorCode_Minus_107": "设备资源不足", "tr_ErrorCode_Minus_109": "找不到网络主机", "tr_ErrorCode_Minus_120": "设备不存在（被删除掉了）", "tr_ErrorCode_Minus_137": "设备token不合法", "tr_ErrorCode_Minus_79999": "YUV数据异常", "tr_ErrorCode_Minus_79998": "打开音频失败（设备不支持音频播放）", "tr_ErrorCode_Minus_500000": "参数编码格式错误（如要求格式为UTF8，但传入gbk）", "tr_ErrorCode_Minus_500001": "参数不是JSON格式", "tr_ErrorCode_Minus_515000": "设备离线", "tr_ErrorCode_Minus_515001": "设备没有上报过", "tr_ErrorCode_Minus_515002": "通道不符合", "tr_ErrorCode_Minus_515003": "通道不在线", "tr_ErrorCode_Minus_515004": "账号错误", "tr_ErrorCode_Minus_515100": "参数错误", "tr_ErrorCode_Minus_515101": "句柄错误", "tr_ErrorCode_Minus_515102": "api 请求失败", "tr_ErrorCode_Minus_515103": "播放类型错误", "tr_ErrorCode_Minus_515104": "向DSM服务请求设备信息失败", "tr_ErrorCode_Minus_515105": "onvif ssid还没有注册上来", "tr_ErrorCode_Minus_515201": "国标预览/回放返回Not Found， 对应sip错误码 404", "tr_ErrorCode_Minus_515202": "国标预览/回放失败", "tr_ErrorCode_Minus_515203": "国标预览/回放请求发送设备超时没有回复", "tr_ErrorCode_Minus_516100": "RTSP协议错误", "tr_ErrorCode_Minus_516101": "URL格式错误", "tr_ErrorCode_Minus_516102": "没有录像", "tr_ErrorCode_Minus_516103": "URL过期", "tr_ErrorCode_Minus_516104": "URL鉴权失败", "tr_ErrorCode_Minus_516105": "没有流量", "tr_ErrorCode_Minus_516106": "向GWM校验URL失败，通信失败", "tr_ErrorCode_Minus_516107": "播放失败，xmts通信失败", "tr_ErrorCode_Minus_516108": "查询录像失败", "tr_ErrorCode_Minus_516109": "错误的SeekTime时间", "tr_ErrorCode_Minus_516110": "没有这个url信息", "tr_ErrorCode_Minus_516111": "token解析失败", "tr_ErrorCode_Minus_516112": "payload失败", "tr_ErrorCode_Minus_516113": "更新到redis失败", "tr_ErrorCode_Minus_516114": "URL不被允许播放", "tr_ErrorCode_Minus_516115": "URL超过允许并发数", "tr_ErrorCode_Minus_90003": "功能超期", "tr_ErrorCode_Minus_90004": "达到最大连接数", "tr_ErrorCode_Minus_99975": "离线状态", "tr_ErrorCode_Minus_99976": "用户列为黑名单", "tr_ErrorCode_Minus_99977": "用户被锁定", "tr_ErrorCode_Minus_99978": "用户已在其他地方登录", "tr_ErrorCode_Minus_99979": "用户名或密码错误", "tr_ErrorCode_Minus_99980": "协议解析错误", "tr_ErrorCode_Minus_99981": "缓冲区大小不够或缓冲区满", "tr_ErrorCode_Minus_99982": "发送缓冲区已满", "tr_ErrorCode_Minus_99983": "监听服务器启动失败", "tr_ErrorCode_Minus_99984": "禁止连接外网", "tr_ErrorCode_Minus_99985": "服务器内部错误", "tr_ErrorCode_Minus_99986": "对象繁忙", "tr_ErrorCode_Minus_99987": "网络错误（发送失败）", "tr_ErrorCode_Minus_99988": "网络接受错误", "tr_ErrorCode_Minus_99989": "创建缓存失败", "tr_ErrorCode_Minus_99990": "没有找到", "tr_ErrorCode_Minus_99991": "您的网络连接超时，请重试", "tr_ErrorCode_Minus_99992": "该设备已存在", "tr_ErrorCode_Minus_99993": "网络错误", "tr_ErrorCode_Minus_99994": "不支持", "tr_ErrorCode_Minus_99995": "读取文件失败", "tr_ErrorCode_Minus_99996": "写文件失败", "tr_ErrorCode_Minus_99997": "打开文件失败", "tr_ErrorCode_Minus_99998": "创建文件失败", "tr_ErrorCode_Minus_99999": "参数错误", "tr_ErrorCode_Minus_100000": "错误", "tr_ErrorCode_Minus_200000": "无效参数", "tr_ErrorCode_Minus_200001": "用户不存在", "tr_ErrorCode_Minus_200002": "sql失败", "tr_ErrorCode_Minus_201103": "消息格式错误", "tr_ErrorCode_Minus_201111": "设备查找成功", "tr_ErrorCode_Minus_201113": "盗版软件", "tr_ErrorCode_Minus_201117": "设备连接数达到上限", "tr_ErrorCode_Minus_201121": "获取AUTHCODE有误", "tr_ErrorCode_Minus_210002": "接口验证失败", "tr_ErrorCode_Minus_210003": "参数错误", "tr_ErrorCode_Minus_210004": "手机号已被注册", "tr_ErrorCode_Minus_210005": "超出短信发送次数", "tr_ErrorCode_Minus_210008": "当前不支持高清视频，将自动切换为标清", "tr_ErrorCode_Minus_210009": "转发模式不支持高清，请升级支持DSS的设备固件", "tr_ErrorCode_Minus_210010": "发送失败，请重试", "tr_ErrorCode_Minus_210017": "120秒之内只能发送一次", "tr_ErrorCode_Minus_210106": "用户名已被注册", "tr_ErrorCode_Minus_210313": "原始密码不正确", "tr_ErrorCode_Minus_210315": "新旧密码相同，请重新更改", "tr_ErrorCode_Minus_210405": "24小时内获取验证码次数不能超过3次", "tr_ErrorCode_Minus_210414": "该手机号未注册", "tr_ErrorCode_Minus_210417": "120秒之内只能发送一次", "tr_ErrorCode_Minus_210512": "您两次输入的新密码不一致", "tr_ErrorCode_Minus_210607": "验证码错误", "tr_ErrorCode_Minus_210700": "服务器响应失败", "tr_ErrorCode_Minus_211703": "缺少上传文件", "tr_ErrorCode_Minus_212104": "服务器查询失败", "tr_ErrorCode_Minus_212402": "没有接收到上传的文件", "tr_ErrorCode_Minus_213000": "无此用户名", "tr_ErrorCode_Minus_213100": "发送邮件失败，请检查邮箱输入是否正确", "tr_ErrorCode_Minus_213108": "该邮箱已被注册", "tr_ErrorCode_Minus_213206": "用户名已被注册", "tr_ErrorCode_Minus_213207": "验证码错误", "tr_ErrorCode_Minus_213208": "邮箱已被注册", "tr_ErrorCode_Minus_213303": "参数错误", "tr_ErrorCode_Minus_213314": "邮箱不存在", "tr_ErrorCode_Minus_213316": "邮箱和用户名不匹配", "tr_ErrorCode_Minus_213407": "验证码错误", "tr_ErrorCode_Minus_213414": "邮箱不存在", "tr_ErrorCode_Minus_213514": "手机号码或邮箱不存在", "tr_ErrorCode_Minus_213600": "设备序列号在黑名单中", "tr_ErrorCode_Minus_213601": "设备序列号已存在", "tr_ErrorCode_Minus_213602": "设备序列号为空", "tr_ErrorCode_Minus_213603": "设备序列号格式不正确", "tr_ErrorCode_Minus_213604": "不存在白名单", "tr_ErrorCode_Minus_213605": "设备名不能为空", "tr_ErrorCode_Minus_213606": "设备用户名格式不正确", "tr_ErrorCode_Minus_213607": "设备密码格式不正确", "tr_ErrorCode_Minus_213608": "设备名称格式不正确，含关键字", "tr_ErrorCode_Minus_213610": "参数异常", "tr_ErrorCode_Minus_213611": "用户名不存在", "tr_ErrorCode_Minus_213612": "编辑设备信息失败", "tr_ErrorCode_Minus_213620": "开通失败", "tr_ErrorCode_Minus_213621": "没有开通云服务", "tr_ErrorCode_Minus_213630": "用户名或密码错误", "tr_ErrorCode_Minus_213700": "服务器响应失败", "tr_ErrorCode_Minus_213702": "接口验证失败", "tr_ErrorCode_Minus_213703": "参数错误", "tr_ErrorCode_Minus_213706": "用户已被注册", "tr_ErrorCode_Minus_213800": "成功，需要更新", "tr_ErrorCode_Minus_213801": "成功，已是最新，无需更新", "tr_ErrorCode_Minus_213802": "失败，无效请求", "tr_ErrorCode_Minus_213803": "失败，资源未找到", "tr_ErrorCode_Minus_213804": "失败，服务器内部错误", "tr_ErrorCode_Minus_213805": "失败，服务器暂时不可用", "tr_ErrorCode_Minus_214206": "用户名已被注册", "tr_ErrorCode_Minus_214404": "手机号码已被绑定", "tr_ErrorCode_Minus_214507": "验证码错误", "tr_ErrorCode_Minus_214608": "邮箱已被绑定", "tr_ErrorCode_Minus_214707": "验证码错误", "tr_ErrorCode_Minus_214708": "邮箱已被绑定", "tr_ErrorCode_Minus_214908": "邮箱已被注册", "tr_ErrorCode_Minus_215100": "通过XMCloud获取设备DSS信息", "tr_ErrorCode_Minus_215101": "DSS连接Hls服务器失败", "tr_ErrorCode_Minus_215102": "DSS信息格式错误", "tr_ErrorCode_Minus_215103": "获取设备DSS信息失败，请稍候重试", "tr_ErrorCode_Minus_215104": "DSS码流格式解析失败", "tr_ErrorCode_Minus_215110": "解析雄迈云返回的视频广场url失败", "tr_ErrorCode_Minus_215120": "前端未连接视频源", "tr_ErrorCode_Minus_215121": "前端未连接视频源", "tr_ErrorCode_Minus_215122": "前端不支持此种码流", "tr_ErrorCode_Minus_215124": "DSS 不能使用组合编码通道进行打开，请重新打开", "tr_ErrorCode_Minus_215130": "无效请求", "tr_ErrorCode_Minus_215131": "媒体视频链接达到最大，访问受限", "tr_ErrorCode_Minus_215140": "无效的令牌格式", "tr_ErrorCode_Minus_215141": "不匹配令牌序列号", "tr_ErrorCode_Minus_215142": "远程ip不匹配令牌ip", "tr_ErrorCode_Minus_215143": "令牌到期", "tr_ErrorCode_Minus_215144": "获取秘钥key失败", "tr_ErrorCode_Minus_215145": "令牌不符", "tr_ErrorCode_Minus_215146": "令牌数据无效格式", "tr_ErrorCode_Minus_215147": "解密秘钥数据失败", "tr_ErrorCode_Minus_215148": "Authcode不匹配", "tr_ErrorCode_Minus_215149": "更改了authcode", "tr_ErrorCode_Minus_221201": "报警授权码错误", "tr_ErrorCode_Minus_221202": "该功能不支持", "tr_ErrorCode_Minus_222400": "没有查询到当天的录像文件", "tr_ErrorCode_Minus_223000": "url为空", "tr_ErrorCode_Minus_223001": "打开失败", "tr_ErrorCode_Minus_223002": "获取流信息失败", "tr_ErrorCode_Minus_223003": "获取视频流信息失败", "tr_ErrorCode_Minus_223010": "无法获取视频流", "tr_ErrorCode_Minus_223100": "打开telnet失败", "tr_ErrorCode_Minus_225402": "服务器错误", "tr_ErrorCode_Minus_225501": "重要参数校验失败（字段缺失，类型不匹配，为空字符串）", "tr_ErrorCode_Minus_225502": "获取redis的ip，port失败", "tr_ErrorCode_Minus_225503": "redis建立连接失败", "tr_ErrorCode_Minus_225504": "redis操作失败", "tr_ErrorCode_Minus_225505": "获取mysql地址失败", "tr_ErrorCode_Minus_225506": "SQL语句输入参数校验失败，（可能存在SQL注入）", "tr_ErrorCode_Minus_225507": "SQL操作失败", "tr_ErrorCode_Minus_225508": "缩略图url与url过期时间获取失败", "tr_ErrorCode_Minus_225509": "时间格式校验失败，时间戳转化失败", "tr_ErrorCode_Minus_225510": "云存储套餐信息异常", "tr_ErrorCode_Minus_225511": "未知不合法查询类型，非MSG或VIDEO", "tr_ErrorCode_Minus_225512": "查询的开始时间与结束时间不在同一天", "tr_ErrorCode_Minus_225513": "sn格式不合法", "tr_ErrorCode_Minus_225514": "未知不合法清除类型，非（ALL，ALARM，VIDEO）", "tr_ErrorCode_Minus_225515": "未知的订阅查询协议格式", "tr_ErrorCode_Minus_225516": "非白名单内IP请求（仅针对云信息删除接口）", "tr_ErrorCode_Minus_225517": "未获取到此用户可查询的时间区域", "tr_ErrorCode_Minus_225518": "json数据格式校验失败", "tr_ErrorCode_Minus_225519": "获取消息免打扰时间段配置数据格式解析错误", "tr_ErrorCode_Minus_226003": "不能设置只读配置", "tr_ErrorCode_Minus_300000": "获取Auth <PERSON>r", "tr_ErrorCode_Minus_400000": "心跳超时", "tr_ErrorCode_Minus_400001": "文件不存在", "tr_ErrorCode_Minus_400002": "设备正在升级中", "tr_ErrorCode_Minus_400003": "服务器初始化失败", "tr_ErrorCode_Minus_400004": "获取连接类型失败", "tr_ErrorCode_Minus_400005": "查询服务器失败", "tr_ErrorCode_Minus_400006": "设备已经连接", "tr_ErrorCode_Minus_400007": "正在登录", "tr_ErrorCode_Minus_400008": "设备可能不在线，请稍候重试", "tr_ErrorCode_Minus_400009": "设备不支持", "tr_ErrorCode_Minus_400010": "没有当天图片，请切换日期", "tr_ErrorCode_Minus_400011": "断开连接失败", "tr_ErrorCode_Minus_400012": "有其它用户正在使用对讲功能，请稍候再试!", "tr_ErrorCode_Minus_400013": "有其他用户正在使用对讲功能，请稍后再试！", "tr_ErrorCode_Minus_400014": "备份到u盘失败", "tr_ErrorCode_Minus_400015": "无存储设备(u盘)或设备没在录像", "tr_ErrorCode_Minus_400017": "抓图失败", "tr_ErrorCode_Minus_400018": "超出文件大小限制", "tr_ErrorCode_Minus_400019": "文件大小校验失败", "tr_ErrorCode_Minus_400100": "对讲未开启", "tr_ErrorCode_Minus_400101": "设备存储已满", "tr_ErrorCode_Minus_400102": "获取登录加密信息未得到明确的结果（支持/不支持）", "tr_ErrorCode_Minus_400201": "内存不足", "tr_ErrorCode_Minus_400202": "升级文件格式不对", "tr_ErrorCode_Minus_400203": "某个分区升级失败", "tr_ErrorCode_Minus_400204": "硬件型号不匹配", "tr_ErrorCode_Minus_400205": "客户信息不匹配", "tr_ErrorCode_Minus_400206": "升级程序的兼容版本号比设备现在的小，不允许设备升级回老程序", "tr_ErrorCode_Minus_400207": "无效的版本", "tr_ErrorCode_Minus_400208": "升级程序里Wi-Fi驱动和设备当前在使用的Wi-Fi网卡不匹配", "tr_ErrorCode_Minus_400209": "网络出错", "tr_ErrorCode_Minus_400210": "升级程序不支持设备使用的flash", "tr_ErrorCode_Minus_400211": "升级文件被修改，不能通过外网升级", "tr_ErrorCode_Minus_400212": "升级此固件需要特殊能力支持", "tr_ErrorCode_Minus_500003": "网络错误，请稍后重试", "tr_ErrorCode_Minus_603000": "FunSDK证书合法性验证校验失败*不合法UUID或者AppKey不允许使用", "tr_ErrorCode_Minus_603001": "JSON数据格式校验失败", "tr_ErrorCode_Minus_603002": "登录用户名或密码为空", "tr_ErrorCode_Minus_603003": "登录Token为空", "tr_ErrorCode_Minus_603004": "第三方登录类型参数为空(微信--wx是Google是gg，Facebook是fb，line是line)", "tr_ErrorCode_Minus_604000": "用户名或密码错误", "tr_ErrorCode_Minus_604010": "验证码错误", "tr_ErrorCode_Minus_604011": "两次密码不一致", "tr_ErrorCode_Minus_604012": "用户名已被注册", "tr_ErrorCode_Minus_604013": "用户名为空", "tr_ErrorCode_Minus_604014": "密码为空", "tr_ErrorCode_Minus_604015": "确认密码为空", "tr_ErrorCode_Minus_604016": "手机号为空", "tr_ErrorCode_Minus_604017": "用户名格式不正确", "tr_ErrorCode_Minus_604018": "新密码不符合要求，密码8-64位必须包含数字和字母", "tr_ErrorCode_Minus_604019": "确认密码格式不正确", "tr_ErrorCode_Minus_604020": "手机号格式不正确", "tr_ErrorCode_Minus_604021": "手机号已被注册", "tr_ErrorCode_Minus_604022": "该手机号未注册", "tr_ErrorCode_Minus_604023": "邮箱已被注册", "tr_ErrorCode_Minus_604024": "邮箱不存在", "tr_ErrorCode_Minus_604026": "原始密码错误", "tr_ErrorCode_Minus_604027": "修改密码失败", "tr_ErrorCode_Minus_604029": "用户ID为空", "tr_ErrorCode_Minus_604030": "验证码错误", "tr_ErrorCode_Minus_604031": "邮箱为空", "tr_ErrorCode_Minus_604032": "邮箱格式不正确", "tr_ErrorCode_Minus_604033": "用户无权限", "tr_ErrorCode_Minus_604034": "用户未绑定", "tr_ErrorCode_Minus_604035": "用户绑定失败", "tr_ErrorCode_Minus_604036": "手机绑定失败", "tr_ErrorCode_Minus_604037": "邮箱绑定失败", "tr_ErrorCode_Minus_604038": "发送验证码超过最大次数", "tr_ErrorCode_Minus_604039": "注册失败", "tr_ErrorCode_Minus_604040": "微信已绑定用户", "tr_ErrorCode_Minus_604041": "没有权限修改用户名（仅针对生成的匿名用户修改）", "tr_ErrorCode_Minus_604042": "用户没有绑定facebook", "tr_ErrorCode_Minus_604043": "用户绑定facebook失败", "tr_ErrorCode_Minus_604044": "用户没有google绑定", "tr_ErrorCode_Minus_604045": "用户绑定google失败", "tr_ErrorCode_Minus_604046": "Line账户未绑定", "tr_ErrorCode_Minus_604047": "Line账户绑定失败", "tr_ErrorCode_Minus_604048": "用户验证码错误次数太多,验证码失效,请24小时后重试", "tr_ErrorCode_Minus_604049": "登录错误次数太多，请十分钟后重试", "tr_ErrorCode_Minus_604050": "请求太频繁，请稍后尝试", "tr_ErrorCode_Minus_604100": "设备非法不允许添加", "tr_ErrorCode_Minus_604101": "设备已经存在", "tr_ErrorCode_Minus_604102": "删除设备失败", "tr_ErrorCode_Minus_604103": "设备信息修改失败", "tr_ErrorCode_Minus_604104": "设备uuid参数异常", "tr_ErrorCode_Minus_604105": "设备用户名参数异常", "tr_ErrorCode_Minus_604106": "设备密码参数异常", "tr_ErrorCode_Minus_604107": "设备端口参数异常", "tr_ErrorCode_Minus_604108": "设备扩展字段参数异常", "tr_ErrorCode_Minus_604109": "位置错误", "tr_ErrorCode_Minus_604110": "新密码校验失败", "tr_ErrorCode_Minus_604111": "确认密码校验失败", "tr_ErrorCode_Minus_604112": "设备别名校验失败", "tr_ErrorCode_Minus_604113": "设备ip地址错误", "tr_ErrorCode_Minus_604114": "支持云存储", "tr_ErrorCode_Minus_604115": "不支持云存储", "tr_ErrorCode_Minus_604116": "将设备主账户移交给其他用户失败，检查用户是否拥有设备并且该用户拥有设备主账户权限", "tr_ErrorCode_Minus_604117": "当前账户不是当前设备的主账户", "tr_ErrorCode_Minus_604118": "设备不存在了已经被移除了", "tr_ErrorCode_Minus_604119": "其他账户已添加该设备", "tr_ErrorCode_Minus_604120": "您的账号下的设备数已经达到最大，无法再添加设备了！", "tr_ErrorCode_Minus_604124": "添加设备失败，分享的设备已经被分享者取消或者删除", "tr_ErrorCode_Minus_604126": "该设备已被绑定，需解绑后才能添加", "tr_ErrorCode_Minus_604127": "添加设备失败，请尝试其他方式", "tr_ErrorCode_Minus_604128": "添加设备失败，设备校验码非法", "tr_ErrorCode_Minus_604200": "添加授权失败", "tr_ErrorCode_Minus_604201": "修改授权失败", "tr_ErrorCode_Minus_604202": "删除授权失败", "tr_ErrorCode_Minus_604203": "单个授权同步失败(原因可能是type参数不对,或者云产品线未返回)", "tr_ErrorCode_Minus_604300": "验证码发送失败，请检查输入是否正确", "tr_ErrorCode_Minus_604301": "邮箱签名失败", "tr_ErrorCode_Minus_604302": "注销账号需要验证码", "tr_ErrorCode_Minus_604303": "今日获取邮箱链接次数已超过限制，请24小时之后再试", "tr_ErrorCode_Minus_604304": "今日获取邮箱链接次数已超过限制，请24小时之后再试", "tr_ErrorCode_Minus_604400": "短信接口验证失败，请联系我们", "tr_ErrorCode_Minus_604401": "短信接口参数错误，请联系我们", "tr_ErrorCode_Minus_604402": "24小时内获取验证码次数不能超过3次", "tr_ErrorCode_Minus_604403": "验证码发送失败，请检查输入是否正确", "tr_ErrorCode_Minus_604404": "120秒之内只能发送一次", "tr_ErrorCode_Minus_604405": "发送失败", "tr_ErrorCode_Minus_604500": "未查到用户列表或用户列表为空", "tr_ErrorCode_Minus_604502": "未查到设备列表或设备列表为空", "tr_ErrorCode_Minus_604503": "重置 app secret 失败", "tr_ErrorCode_Minus_604600": "微信报警打开失败", "tr_ErrorCode_Minus_604601": "微信报警关闭失败", "tr_ErrorCode_Minus_605000": "服务器故障", "tr_ErrorCode_Minus_605001": "证书不存在", "tr_ErrorCode_Minus_605002": "请求头信息错误", "tr_ErrorCode_Minus_605003": "证书失效", "tr_ErrorCode_Minus_605004": "生成密钥校验错误", "tr_ErrorCode_Minus_605005": "参数异常", "tr_ErrorCode_Minus_605006": "连接失败", "tr_ErrorCode_Minus_605007": "未知错误", "tr_ErrorCode_Minus_605008": "ip地址不允许接入", "tr_ErrorCode_Minus_605009": "解密错误(第三方登录code错误或者AES加解密错误)", "tr_ErrorCode_Minus_605010": "token已过期", "tr_ErrorCode_Minus_605011": "Token错误", "tr_ErrorCode_Minus_605012": "token无权限", "tr_ErrorCode_Minus_605013": "不支持", "tr_ErrorCode_Minus_605017": "消息码失效了！", "tr_ErrorCode_Minus_661412": "用户名不存在", "tr_ErrorCode_Minus_661427": "新密码格式不正确", "tr_ErrorCode_Minus_800401": "未授权", "tr_ErrorCode_Minus_800403": "禁止访问", "tr_ErrorCode_Minus_800404": "不存在", "tr_ErrorCode_Minus_800500": "服务器内部错误", "tr_ErrorCode_Minus_803004": "用户名或密码错误", "tr_ErrorCode_Minus_806002": "角色不存在，需要配置角色", "tr_ErrorCode_Minus_1239510": "对象不存在", "tr_ErrorCode_Minus_1239511": "值不存在", "tr_Input": "Input", "tr_AppWelcome": "欢迎使用蜂云SaaS", "tr_Prompt": "提示", "tr_Cancel": "取消", "tr_Confirm": "确定", "tr_LoginTips1": "我已经阅读并同意", "tr_Login": "登录", "tr_InputPhoneNumber": "请输入手机号", "tr_InputPhoneNumberEmail": "请输入手机号/邮箱", "tr_InputPassword": "请输入密码", "tr_RegisterAccount": "注册账号", "tr_ForgetPassword": "忘记密码", "tr_UserAgreement": "用户协议", "tr_PrivacyPolicy": "隐私政策", "tr_And": "和", "tr_ReviewTips1": "您已提交注册申请，请耐心等待", "tr_ReviewTips2": "工作日，我们将在 1 小时内完成审核\n非工作日，我们将在 24 小时内完成审核\n审核结果会以短信方式通知您！\n请及时关注短信通知", "tr_InputVerifyCode": "输入验证码", "tr_InputPasswordRule": "请输入密码，格式为8-16位数字和字母", "tr_InputConfirmPasswordRule": "请输入确认密码，格式为8-16位数字和字母", "tr_AccountPasswordRule": "密码为8-16位字符，需包含字母、数字和特殊字符", "tr_InputTrueName": "请输入真实姓名", "tr_InputCompanyName": "请输入单位名称", "tr_ClickToUploadTheBusinessLicense": "点击上传营业执照", "tr_Submit": "提交", "tr_TakePhoto": "拍照", "tr_Album": "相册", "tr_PasswordNotOneMatch": "密码不一致", "tr_PleaseFillInfo": "请先完善信息", "tr_PleaseAgreeAgreementAndPolicy": "请先同意用户协议与隐私政策", "tr_SendVerifyCode": "发送验证码", "tr_ErrorFormatPassword": "密码格式不正确", "tr_ErrorFormatPhoneNum": "手机号格式不正确", "tr_ResetPassword": "重置密码", "tr_ModifySuccess": "修改成功", "tr_AppPolicyTip1": "隐私保护政策概要", "tr_AppPolicyTip2": "        感谢您信任并使用蜂云！蜂云非常重视您的隐私保护和个人信息保护。在您使用蜂云服务之前，请认真阅读", "tr_AppPolicyTip3": "的全部条款，同意并接受全部条款后开始使用我们的服务。\n        我们将按照法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。", "tr_AgreeAndContinue": "同意并继续", "tr_Reject": "拒绝", "tr_PersonManage": "用户管理", "tr_RoleManage": "角色管理", "tr_HelpAndFeedback": "帮助与反馈", "tr_Tool": "工具", "tr_About": "关于", "tr_Account": "账号", "tr_AccountSet": "个人设置", "tr_PersonInfo": "个人信息", "tr_Avatar": "头像", "tr_Name": "姓名", "tr_Mail": "邮箱", "tr_BindPhone": "绑定手机", "tr_ModifyPassword": "修改密码", "tr_AccountSafe": "账号与安全", "tr_AccountCancellation": "注销账号", "tr_AccountCancellation1": "账户注销", "tr_Logout": "退出登录", "tr_VersionUpdate": "版本更新", "tr_ImportantClause": "特别声明", "tr_ImportantClauseContent": "杭州杰峰科技有限公司在此声明，您通过本软件参与的商业活动，与Apple inc.无关", "tr_ContactUs": "联系我们", "tr_ContactUsContent": "感谢您使用我司的产品和服务，若您在使用产品或服务过程中有任何问题，意见或建议，请将您的反馈意见发送至**************，我们会在第一时间给您回复，感谢您的配合.", "tr_NowLatestVersion": "最新版本", "tr_OfficialWebsite": "官网", "tr_AllRightsReserved": "版权所有：杭州杰峰科技", "tr_ConfirmTips": "确定退出登录？", "tr_Done": "完成", "tr_PleaseInputOldPwd": "请输入旧密码", "tr_PleaseInputNewPwd": "请输入新密码", "tr_PleaseInputNewPwdCon": "请确认新密码", "tr_IdCheck": "身份验证", "tr_CheckCurrentPhone": "检测到您当前绑定的手机号为：", "tr_Next": "下一步", "tr_BindNewPhone": "绑定新手机", "tr_BindEmail": "绑定邮箱", "tr_DearUser": "尊敬的用户：", "tr_DearUserDetail": "您的账号【{phone}】注销后，您将无法再以该账号登录并使用【蜂云平台】的相关产品与服务，并自动退出您在【蜂云平台】加入的企业/组织，您的账号在【蜂云平台】的所有数据信息将被清空，且无法恢复。\n如有疑问，请联系蜂云客服邮箱：<EMAIL>", "@tr_DearUserDetail": {"placeholders": {"phone": {}}}, "tr_ConfirmCancellation": "确认注销", "tr_CancellationProtocol": "《蜂云SaaS账号注销协议》", "tr_PleaseInputNewPhone": "请输入新的手机号", "tr_PleaseInputNewMail": "请输入新的邮箱", "tr_PleaseAgreeProtocol": "请先同意协议", "tr_Cancellation": "注销", "tr_CancellationSuccess": "注销成功", "tr_CannotChangeSamePhone": "新手机号不能和原手机号一致", "tr_CannotChangeSameEmail": "新邮箱不能和原邮箱一致", "tr_CheckCurrentEmail": "检测到您当前绑定的邮箱为：", "tr_FileFormatterNotSupport": "不支持的文件格式", "tr_AddNode": "添加节点", "tr_AddDevice": "添加设备", "tr_EnterNoteNickName": "请输入节点名称", "tr_CommonSave": "保存", "tr_AddSuccess": "添加成功", "tr_DeviceAdd": "设备添加", "tr_Node": "节点", "tr_JFDevice": "杰峰设备", "tr_JFDeviceNotes": "添加杰峰协议的设备", "tr_PlatformDevice": "平台设备", "tr_PlatformDeviceNotes": "添加平台协议的设备", "tr_AddJFDevice": "设备添加", "tr_NationalStandardDevice": "国标设备", "tr_NationalStandardDeviceNotes": "添加国标协议的设备", "tr_ScanJFDevice": "扫描可用设备", "tr_AddDeviceByManual": "手动添加", "tr_AddDeviceByWIFI": "WiFi配网", "tr_AddDeviceByWIFINotes": "支持二维码配网和快速配网", "tr_AddRecordDevice": "网线直连添加", "tr_AddRecordDeviceNotes": "适用于带网线插口的设备", "tr_Add4GDevice": "4G摄像机", "tr_Add4GDeviceNotes": "扫描设备二维码直接添加", "tr_common_close": "关闭", "tr_common_GoSetting": "前往开启", "tr_LocalNetworkDisable": "本地网络权限未开启", "tr_LocalNetworkDisableNotes": "未开启本地网络权限，您将无法对设备进行添加操作", "tr_OperationByInstructions": "按照说明书，给摄像机上电，确保摄像机正常开机", "tr_RestoreFactorySettings": "初始化设备", "tr_RestoreFactorySettingsTip": "如果听到设备提示\"开始快速配置\"或\"等待连接\",点击\"下一步\"", "tr_RestoreFactorySettingsNotes": "如果没有听到，请长按设备SET/RESET按键约6秒，待听到\"恢复出厂设置，请勿断电\"后松开；待设备恢复出厂设置后，重新连接", "tr_RestoreFactorySettingsNotes2": "按键位置具体查看机身标识或者说明书", "tr_NetworkConfiguration": "网络配置", "tr_NetworkConfigurationDeclare": "设备暂不支持5G WiFi,仅支持2.4G WiFi", "tr_ChooseNetwork": "请选择设备连接的无线网络", "tr_EnterWiFiPassword": "请输入WiFi密码", "tr_ChoosePrettyWiFi": "请选择信号较强的WiFi，可快速让设备联网", "tr_OprationsOfNetworkConfiguration": "· 请将二维码朝向设备镜头;\n· 保持25-35公分的距离，等待扫描;\n· 听到正在配置WiFi提示音后移开手机;\n· 听到配置成功提示音表示设备WiFi配置已完成", "tr_DeviceScanTips": "快速配网和扫码配网同时开启中，请查看说明书是否支持扫码配网，如果设备不支持扫码配网，无需进行二维码对码", "tr_ModifyDeviceNickName": "修改设备名称", "tr_ModifyDevicePassword": "重设密码", "tr_DeviceSN": "序列号", "tr_ModifyDevicePasswordNotes": "为了您的设备安全，请修改默认密码", "tr_RulesOfDevicePassword": "密码长度8-64位，由字母和数字组成", "tr_FailedSetDevicePassword": "若密码设置不成功，请长按设备后方的reset键，进行恢复出厂默认。恢复出厂设置后，需要重新添加设备，重新设置密码。", "tr_CheckDeviceLoginInfo": "设备用户名和密码", "tr_DeviceLoginName": "设备登录名", "tr_DevicePassword": "请输入密码", "tr_ResetDevice": "若忘记密码，请恢复出厂设置重新添加", "tr_AddRecordNotes": "注： \n· 确保设备已上电 \n· 网线与设备网线连接口连接", "tr_SCanQRCode": "扫码识别", "tr_AccessNationalStandardDevice": "国标设备接入", "tr_AccessProtocol": "接入协议", "tr_AccessType": "接入类型", "tr_EncodingMethod": "编码方式", "tr_EncodingMethodRandom": "随机编码", "tr_EncodingMethodNationalStandard": "国标编码", "tr_GenerateIDNum": "生成ID数", "tr_AccessPassword": "接入密码", "tr_GoToSetting": "去设置", "tr_Common_Random": "随机", "tr_Common_Custom": "自定义", "tr_AccessTips": "注意:\n国标协议暂不支持H.265编码，请确保设备码流为H.264设备需使用GB28181-2016协议，才能支持TCP。", "tr_StandardAccessTips": "添加国际设备，需要将接入ID、SIP服务器IP、SIP服务器端口等信息配置到待接入设备的后台指定位置。在生成接入id详情中可查看对应信息。", "tr_GenerateID": "生成接入ID", "tr_ReviewAccess": "查看接入", "tr_NationalStandardConfiguration": "国标编码配置", "tr_Common_AdministrativeArea": "行政区域", "tr_Common_GrassrootsUnitNum": "基层单位编号", "tr_IndustryCode": "行业编码", "tr_Common_PlsFillInfo": "请选择", "tr_TypeCode": "类型编码", "tr_NetworkIdentification": "网络标识", "tr_AccessIds": "接入ID", "tr_Common_export": "导出", "tr_NodeName": "节点名称", "tr_TypePre": "类型:", "tr_Common_All": "全部", "tr_StatusPre": "状态:", "tr_Status": "状态", "tr_UnUsed": "未使用", "tr_Used": "已使用", "tr_Common_Password": "密码", "tr_View": "查看", "tr_Detailed": "详细信息", "tr_ServiceParams": "业务参数", "tr_AccessNode": "接入节点", "tr_CancellationValidityPeriod": "注册有效期", "tr_HeartbeatCycle": "心跳周期", "tr_ServerParams": "服务器参数", "tr_SIPServerId": "SIP服务器ID", "tr_SIPServerDomain": "SIP服务器域", "tr_SIPServerAddress": "SIP服务器地址", "tr_SIPServerPort": "SIP服务器端口", "tr_DeviceParams": "设备参数", "tr_HoldOnForSearhingDevices": "正在搜索附近设备，请等待...", "tr_OrganizationStructure": "组织结构", "tr_Common_Search": "搜索", "tr_NodeEdit": "编辑节点", "tr_DeviceEdit": "编辑设备", "tr_Common_ContentPairingFailed": "暂无搜索结果", "tr_Common_Device": "设备", "tr_Common_Edit": "编辑", "tr_Common_Delete": "删除", "tr_Common_DeleteSuccess": "删除成功", "tr_Common_InputPassword": "请输入登录密码", "tr_MoveDevicesToRootNode": "若有设备，删除节点后，设备自动归属到根节点下", "tr_MoveDevicesToRootNVRNode": "删除NVR，将同步删除下属所有设备，是否确认删除？", "tr_MoveDevicesToCenterSeverNode": "删除中心服务器，将同步删除所属通道和节点，是否确认删除？", "tr_NameContentUnchange": "输入名称一致", "tr_Common_Monitor": "监控", "tr_Common_Function": "功能", "tr_Common_Message": "消息", "tr_Common_Mine": "我的", "tr_PlayUrlIsInvalid": "播放地址为空", "tr_OprationDeny": "该节点下存在子节点，请先删除子节点", "tr_MakeSureDeleteNode": "确认删除该节点?", "tr_MakeSureDeleteDevice": "确定删除设备?", "tr_AddNodeTips": "当前节点下暂无设备与子节点请点击右上角添加按钮添加设备与子节点", "tr_InputNodeNickName": "请输入名称", "tr_InputDeviceNickName": "请输入设备名称", "tr_InputDeviceNickNameForSearch": "请输入设备名称搜索", "tr_AddDeviceFailed": "添加失败", "tr_CheckAddDeviceFailed": "查询不到设备，请重置设备后重新添加", "tr_InvalidDevice": "设备序列号不合法或者设备已经被添加过", "tr_WiFiConfigurationRecommendation": "1. 如果家庭为双频路由器，请检查摄像连接的WiFi是否是5GHz频段的WiFi，请切换连接2.4GHz的WiFi\n\n2. 配置摄像机时建议距离路由器不要太远。\n\n3. 建议连接非桥接的WiFi，因为桥接的原因可能导致您的网络非常不稳定。", "tr_GotIt": "我知道了", "tr_TheRestrictionsOnWiFi": "设备对WiFi有什么要求？", "tr_InvalidData": "无效数据", "tr_Permission_Location": "定位", "tr_Permission_LocalNetwork": "本地网络", "tr_RequestPermission": "为了您能正常添加设备，需要【{permission}】权限", "tr_TurnOnWiFi": "为了您能正常添加设备，需要打开WiFi", "tr_GenerateChannels": "生成通道数", "tr_InputAccessPassword": "请先设置接入密码", "tr_Common_Channel": "通道", "tr_channelParams": "通道参数", "tr_CopySuccess": "已经复制到粘贴板", "tr_Common_ChooseAdministrativeArea": "行政区域选择", "tr_Recording": "已开启录像", "tr_FailedRecord": "录像失败", "tr_SaveRecordFileSuccess": "录像保存成功", "tr_SaveRecordFileFailed": "录像保存失败", "tr_SnapSuccess": "截图成功", "tr_SnapFailed": "截图失败", "tr_PTZ": "云台", "tr_Preset": "预置点", "tr_Playback": "回放", "tr_Common_More": "更多", "tr_Common_Voice": "声音", "tr_Common_Record": "录像", "tr_Common_Snap": "截图", "tr_Common_definition": "清晰度", "tr_Common_Reset": "重置", "tr_Common_Add": "添加", "tr_Common_SelectAll": "全选", "tr_Common_Rename": "重命名", "tr_AddPreset": "新建预置点", "tr_SetPresetName": "请输入预置点名称", "tr_PresetNameExisted": "预置点名称已经存在，请不要重复命名", "tr_EditPreset": "编辑预置点", "tr_CommandSuccess": "操作成功", "tr_Common_Loading": "正在加载中...", "tr_Loading": "加载中...", "tr_Common_Disconnect": "断开连接", "tr_Permission_Camera": "请打开相机权限！", "tr_Permission_Album": "请允许访问相册！", "tr_QRcodeInvalid": "未能识别图片中的二维码信息，请重新选择图片或者重试！", "tr_SelectAll": "全选", "tr_Share": "分享", "tr_Download": "下载", "tr_Delete": "删除", "tr_AlarmMessage": "报警消息", "tr_NoData": "暂无数据", "tr_SelectItemTips": "请先选择想要操作的文件", "tr_ShareTips1": "1.视频图片不能组合分享\n2.视频每次只能分享一个", "tr_ShareTips2": "仅支持分享一个文件", "tr_ShareTips3": "视频需要下载,请在下载完成后再分享", "tr_ShareTipsImageMaxNum": "图片分享单次不能超过5张", "tr_ShareSuccess": "分享成功", "tr_ShareFailure": "分享失败", "tr_PleaseAllowVisitAllPhoto": "请先允许访问所有相册图片", "tr_SureSaveToAlbum": "确定保存选中文件到相册?", "tr_Downloaded": "已下载", "tr_SureItemDelete": "确定删除选中项目?", "tr_SuccessfullyDeleted": "删除成功", "tr_NOAlarmMessage": "暂无告警", "tr_LoadedFail": "加载失败", "tr_AlarmAI": "AI告警", "tr_AlarmDevice": "设备告警", "tr_AlarmTotalNum": "告警总数", "tr_Today": "今日", "tr_Device": "设备", "tr_ToDoList": "待办事项", "tr_MessageCenter": "消息中心", "tr_AllProtocols": "所有协议", "tr_AllTypes": "所有类型", "tr_Mon": "一", "tr_Tues": "二", "tr_Wed": "三", "tr_Thur": "四", "tr_Fri": "五", "tr_Sat": "六", "tr_Sun": "日", "tr_AllDate": "全部日期", "tr_AccessIdPwdTips": "密码长度8-64位，由字母和数字组成", "tr_AccessPwdConfig": "接入密码设置", "tr_SelectNode": "节点选择", "tr_DownloadSuccess": "下载成功", "tr_downloadFailed": "下载失败", "tr_NoMedia": "暂无视频资源", "tr_RecordCard": "本地回放", "tr_RecordCloud": "云回放", "tr_AddDepartment": "新增部门", "tr_EditDepartment": "编辑部门", "tr_EnterNoteDepartment": "请输入部门名称", "tr_MakeSureDeleteDepartment": "确定删除部门?", "tr_AddDepartmentTips": "当前部门下暂无子部门,请点击右上角添加按钮添加部门", "tr_AddDepartmentTipsSimple": "当前部门下暂无子部门", "tr_ChooseDepartment": "选择部门", "tr_AlarmSetting": "报警设置", "tr_NormalSetting": "设置", "tr_DepartmentManage": "部门管理", "tr_NoRole": "暂无角色", "tr_ViewDetail": "查看详情", "tr_SelectRole": "选择角色", "tr_Phone": "手机", "tr_PleaseInputDes": "请输入描述", "tr_PleaseInputName": "请输入姓名", "tr_RoleName": "角色名称", "tr_RoleDes": "角色描述", "tr_BasicInfo": "基本信息", "tr_AddRole": "添加角色", "tr_RoleDetail": "角色详情", "tr_NORoleTips": "暂无角色\n请点击右上角添加按钮添加", "tr_Department": "部门", "tr_PwdCon": "确认密码", "tr_PleaseInputPwdCon": "请输入确认密码", "tr_PhoneNum": "手机号", "tr_PleaseInputMail": "请输入邮箱", "tr_Role": "角色", "tr_DeviceResource": "设备资源", "tr_PersonDetail": "用户详情", "tr_AddPerson": "添加新用户", "tr_CheckView": "查看", "tr_InputPwd": "输入密码", "tr_SureDelete": "确认删除", "tr_SelectDeviceResource": "选择设备资源", "tr_PleaseSelect": "请选择", "tr_DeviceName": "设备名称", "tr_DeviceInfo": "设备信息", "tr_DeleteDevice": "删除设备", "tr_DeleteDeviceTips": "摄像机删除后，将清除云端动态产生的照片和视频", "tr_DeviceState": "设备状态", "tr_DeviceStateOnline": "在线", "tr_DeviceStateOffline": "离线", "tr_DeviceStateUnRegister": "未注册", "tr_DeviceNode": "所属节点", "tr_DeviceUuid": "设备序列号", "tr_DeviceAccessChannelNum": "接入通道数", "tr_DeviceAccessIDPwd": "接入ID密码", "tr_SIPServerIP": "SIP服务器IP", "tr_SIPServerDomain2": "SIP域", "tr_DeviceChannelNum": "通道号", "tr_CloudRecord": "云回放", "tr_SuperAdmin": "超级管理员", "tr_NOPersonTips": "暂无用户\n请点击右上角添加按钮添加", "tr_AccountPwdRuleSimple": "8-16位数字和字母", "tr_GenIdNumTips": "生成数必须大于0", "tr_GenIdNumTips1": "生成通道数必须不能超过128", "tr_Template": "考评模板", "tr_VideoSpotCheckInspection": "视频抽查巡检", "tr_OnSiteInspection": "现场巡检", "tr_ShakeToInspect": "摇一摇巡检", "tr_InspectionPlan": "巡检计划", "tr_InspectAccordingToPlan": "巡检任务", "tr_InspectionRecord": "巡检记录", "tr_AlgorithmConfiguration": "算法配置", "tr_AlgorithmAlarm": "算法告警", "tr_Record": "记录", "tr_Algorithm": "算法", "tr_Function": "功能", "tr_Inspection": "巡检", "tr_createTemplate": "新建考评模板", "tr_pleaseEnterTemplateName": "请输入模板名称", "tr_pleaseEntereLigibilityScore": "请输入合格分数线", "tr_presetEvaluationTemplate": "预设考评模板", "tr_assessmentCategory": "考评类", "tr_assessmentItem": "考评项", "tr_totalScore": "总分", "tr_passingScore": "合格线", "tr_displayArrangement": "陈列摆放", "tr_createEvaluationClass": "新建考评类", "tr_score": "分", "tr_item": "项", "tr_createEvaluationItem": "新建考评项", "tr_pleaseEnterTheEvaluationClassName": "请输入考评类名称", "tr_noEvaluationItemsTemporarily": "暂无考评项", "tr_description": "描述", "tr_scoreValue": "分值", "tr_pleaseEnterTheContentOfTheEvaluationItem": "请输入考评项内容", "tr_pleaseEnterTheScoreOfTheEvaluationItem": "请输入考评项分数", "tr_referencePicture": "参考图片", "tr_templateDetails": "模板详情", "tr_collapse": "收起", "tr_templateName": "模板名称", "tr_expand": "展开", "tr_pleaseSetEvaluationItems": "请设置考评项", "tr_inspectionTemplateSelection": "巡检模板选择", "tr_inspectionTemplate": "巡检模板", "tr_inspectionResult": "巡检结果", "tr_useTemplate": "使用模板", "tr_gentleReminder": "温馨提示", "tr_deviceSelection": "设备选择", "tr_qualified": "合格", "tr_unqualified": "不合格", "tr_alreadyTheFirstOne": "已经是第一个", "tr_alreadyTheLastOne": "已经是最后一个", "tr_loadingDataPleaseWait": "正在加载数据，请稍候", "tr_thereAreStillAssessmentItemsWithoutResultsSet": "还有考评项未设置结果", "tr_selectEvaluationCategory": "选择考评类", "tr_scoresCanBeOnlyEnteredInNumbers": "分数只能输入数字", "tr_screenshotFailedPleaseRetry": "截图失败，请重试", "tr_deviceIsNotInPlaybackStateCannotTakeScreenshots": "设备不在播放状态，无法截图", "tr_deviceIsNotInPlaybackStateCannotChangeSpeed": "设备不在播放状态，无法改变播放速度", "tr_createEvent": "创建事件", "tr_getScore": "得分", "tr_scoringRate": "得分率", "tr_scoringItem": "评分项", "tr_overdue": "逾期", "tr_inInspection": "巡检中", "tr_completed": "已完成", "tr_inspectionCompletionTime": "巡检完成时间", "tr_inspectionCompletion": "巡检完成", "tr_inspector": "巡检人", "tr_chooseInspector": "选择巡检人", "tr_inspectionPerson": "巡检人员", "tr_pleaseChooseInspectionPerson": "请选择巡检人员", "tr_inspectionTime": "巡检时间", "tr_uncompleted": "未完成", "tr_setDemandNumChannels": "设置接入通道数", "tr_authorizedNumChannels": "授权通道数", "tr_usedNumChannels": "已使用通道数", "tr_inputNumChannels": "您的NVR需要几个通道？", "tr_pleaseEnterTheInspectionNameToSearch": "请输入巡检名称搜索", "tr_assessmentCategoryDetail": "考评类详情", "tr_cancelAllSelections": "取消全选", "tr_areYouSureToDeleteThisTemplate": "确认删除该模板吗", "tr_thereMustBeAtLeastOneEvaluationItem": "至少要有一个考评项", "tr_atLeastOneEvaluationClassIsRequired": "至少要有一个考评类", "tr_pressAgainToExitTheApplication": "再按一次退出应用", "tr_recordingSchedule": "录像计划", "tr_upgradePackage": "升级套餐", "tr_skyCloudStorage": "天云存储", "tr_startTime": "开始时间", "tr_recordingTime": "录像时间", "tr_recordingStartTime": "录像开始时间", "tr_recordingEndTime": "录像结束时间", "tr_recordingPlanStartTime": "录像计划开始时间", "tr_recordingPlanEndTime": "录像计划结束时间", "tr_viewRecordingSchedule": "录像详情", "tr_createCloudStorageRecordingPlan": "创建云存储录像计划", "tr_selectDevice": "选择设备", "tr_remainingPath": "剩余路", "tr_overwrite": "覆盖", "tr_doNotOverwrite": "不覆盖", "tr_okayIKnow": "好的，我知道了", "tr_openPackage": "开通套餐", "tr_youHaveNotYetOpenedTheCloudStorageRecordingPackageAuthorization": "您还未开通云存储录像套餐授权", "tr_selectStoragePackage": "选择存储套餐", "tr_cover_record_plan_tip": "检测到所选设备已有录像计划，是否覆盖？", "tr_record_plan_not_enough_tip": "您的云存储录像套餐授权路数不足，请联系**************开通更多授权。", "tr_record_plan_upgrade_tip": "请联系**************升级套餐。", "tr_running": "运行中", "tr_videoCloudBase": "视频云底座", "tr_goToSelectMultipleChoice": "去选择（可多选）", "tr_InspectionAnalysis": "巡检分析", "tr_skyCloudStorageLoopRecording": "天云存储循环录像", "tr_areYouSureToDeleteThisPlan": "确认删除该计划吗", "tr_pleaseSelectTheDeviceFirst": "请先选择设备", "tr_pleaseSelectTheRecordingPlan": "请选择录像计划", "tr_remainingRoad": "剩余{name}路", "tr_recordingHasStopped": "录像停止", "tr_allDay": "全天", "tr_isTheRecordingPlanTurnedOn": "是否开启录像计划", "tr_confirmClosingTheRecordingPlan": "确认关闭录像计划", "tr_failedToGetUrlPleaseTryAgain": "获取url失败，请重试", "tr_youDoNotHavePermissionToHandleThisTask": "您无权限处理此任务", "tr_noProcessingPermissionForTheTimeBeing": "暂无处理权限", "tr_pendingInspection": "待巡检", "tr_pendingRectification": "待整改", "tr_pendingAcceptance": "待验收", "tr_taskIssuanceTime": "任务下发时间", "tr_issuanceTime": "下发时间", "tr_processingDeadline": "处理时限", "tr_rectificationDeadline": "截止时间", "tr_pendingLevel": "待办等级", "tr_general": "一般", "tr_minor": "轻微", "tr_severe": "严重", "tr_taskSubmissionTime": "任务提交时间", "tr_submissionTime": "提交时间", "tr_aPatrolTaskHasBeenAssignedToYou": "为您分配了巡检任务", "tr_submitRectificationTaskToYou": "提交的事件", "tr_aRectificationTaskHasBeenAssignedToYou": "为您分配了整改任务", "tr_rectificationCompletedLookingForwardToYourAcceptance": "整改完成，期待您验收", "tr_rectificationSubmissionTime": "整改提交时间", "tr_copyTo": "抄送人", "tr_returnTime": "打回时间", "tr_filter": "筛选", "tr_isItOverdue": "是否逾期", "tr_eventLevel": "事件等级", "tr_notYet": "无", "tr_overdueYet": "已逾期", "tr_rectificationTime": "整改时间", "tr_problemDescription": "问题描述", "tr_pleaseEnter": "请输入", "tr_assignedTo": "指派给", "tr_ccTo": "抄送给", "tr_days": "天", "tr_hours": "小时", "tr_assignedPersonnel": "指派人员", "tr_editImage": "编辑图片", "tr_mineRectify": "我的整改", "tr_mineInitiate": "我发起", "tr_mineDuplicate": "抄送我", "tr_mineAcceptance": "我验收", "tr_EventCenter": "事件中心", "tr_currentFlowPackage": "当前流量包", "tr_flowExpirationTime": "流量包到期时间：", "tr_usageRatio": "使用占比", "tr_flowTotal": "总共", "tr_flowUse": "已用", "tr_flowRemaining": "剩余", "tr_flowRemainingTip": "想要续费或扩容？请联系**************进行续费！", "tr_flow_statistics": "流量统计", "tr_accountAuthorization": "子账号授权", "tr_deviceAuthorization": "设备接入授权", "tr_cloudStorageAuthorization": "云录像授权", "tr_algorithmAuthorization": "算法授权", "tr_buy_connect_email_tip": "请联系**************续费。", "tr_bcloud_saas_activity": "蜂云 SAAS 套餐优惠", "tr_buy_saas": "立即购买", "tr_remaining_flow": "剩余流量", "tr_authorization_statistics": "授权统计", "tr_renewal": "续费", "tr_maturity": "到期", "tr_bcloud_saas_shopping_tip": "蜂云 SAAS 套餐优惠，欢迎联系**************购买。", "tr_pleaseEnterText": "请输入文字", "tr_pleaseSelectAssignee": "请选择指派人", "tr_problemDescriptionCannotBeEmpty": "问题描述不能为空", "tr_dataNotSavedContinueReturnWillClearData": "数据还没有保存，继续返回将清空数据，确认返回吗？", "tr_forever": "永久", "tr_titleImprovements": "整改进展", "tr_waitingMineRectify": "待我整改", "tr_waitingMineAcceptance": "待我验收", "tr_rectificationStatus": "整改状态", "tr_passed": "已通过", "tr_goRectification": "去整改", "tr_goAcceptance": "去验收", "tr_titleViewPassedItems": "已通过验收项", "tr_rectify": "整改", "tr_acceptance": "验收", "tr_acceptancePassed": "验收通过", "tr_beforeRectification": "整改前", "tr_afterRectification": "整改后", "tr_mRectificationTasks": "的整改任务", "tr_acceptanceAndSubmissionTip": "验收提交前，请判断整改项是否通过", "tr_submittedSuccessfully": "提交成功", "tr_yes": "是", "tr_no": "否", "tr_underRectification": "整改中", "tr_underAcceptance": "验收中", "tr_determineBeforeAcceptanceAndSubmission": "检测到有整改未截图，是否继续提交？", "tr_titleWorkbenchApplication": "功能", "tr_enableLocationOrGrantLocationPermissions": "请开启定位或授予定位权限", "tr_imageInspection": "图片巡检", "tr_capturePlan": "抓拍计划", "tr_eventStatus": "事件状态", "tr_captureDate": "抓拍日期", "tr_imageInspectionPlan": "抓拍计划", "tr_addImageInspectionPlan": "添加抓拍计划", "tr_editImageInspectionPlan": "编辑抓拍计划", "tr_planName": "计划名称", "tr_pleaseEntryPlanName": "请输入计划名称", "tr_executionCycle": "执行周期", "tr_executionTime": "执行时间", "tr_pleaseChooseExecutionTime": "请选择执行时间", "tr_inspectionDevice": "巡检设备", "tr_eventDetails": "事件详情", "tr_rectificationItem": "整改项", "tr_rectificationPerson": "整改人", "tr_Common_Workbench": "工作台", "tr_upcomingTasks": "待办任务", "tr_commonApplications": "常用功能", "tr_moreApplications": "更多功能", "tr_addApplications": "添加常用", "tr_workbenchApplicationsConfiguration": "配置功能", "tr_dragToSort": "按住拖动排序", "tr_notSubmitted": "未提交", "tr_submitted": "已提交", "tr_submittedEvent": "已提交事件", "tr_today": "今天", "tr_last3Days": "近3天", "tr_last7Days": "近7天", "tr_last30Days": "近30天", "tr_everyDay": "每天", "tr_everyMonthNameDay": "每月:{name}", "tr_everyWeekNameDay": "每周:{name}", "tr_dayNo": "号", "tr_selectTemplate": "请选择模板", "tr_monday": "周一", "tr_tuesday": "周二", "tr_wednesday": "周三", "tr_thursday": "周四", "tr_friday": "周五", "tr_saturday": "周六", "tr_sunday": "周日", "tr_yesterday": "昨天", "tr_theDayBeforeYesterday": "前天", "tr_thisRecordHasNoImage": "该条记录没有图片", "tr_pleaseSelectRectificationItem": "请选择整改项", "tr_confirmDeletion": "确定删除？", "tr_subTitlePermission": "权限", "tr_titleEventAnalysis": "事件分析", "tr_subDate": "日期", "tr_employeeRanking": "员工排名", "tr_unitTimes": "单位：次", "tr_unqualifiedCameraRanking": "不合格点位排名", "tr_numberInspections": "巡检次数", "tr_proportionUnqualifiedInspections": "巡检不合格占比", "tr_eventTrends": "事件趋势", "tr_passedNumberTimes": "通过验收次数", "tr_timeOverdueTimes": "时间逾期次数", "tr_itemOther": "其他", "tr_problemFound": "发现问题", "tr_completeRectification": "完成整改", "tr_rectificationOverdue": "整改逾期", "tr_completeAcceptance": "完成验收", "tr_completeRectificationTimes": "完成整改次数", "tr_rectificationOverdueTimes": "整改逾期次数", "tr_completeAcceptanceTimes": "完成验收次数", "tr_completeRectificationSort": "完成整改次数排名", "tr_rectificationOverdueSort": "整改逾期次数排名", "tr_completeAcceptanceSort": "完成验收次数排名", "tr_eventLevelProportion": "事件等级占比", "tr_rankingOfCameraIncidents": "摄像头事件数排名", "tr_workbenchHelloTip": "Hello", "tr_workbenchSubTip": "欢迎使用蜂云 SaaS App", "tr_workbenchExpireDaysTip": "平台{days}天后到期，请联系 <EMAIL> 续费/扩容", "tr_platformParsing": "平台解析", "tr_deviceReporting": "设备上报", "tr_alarmDescription": "报警描述", "tr_alarmTime": "告警时间", "tr_deviceTree": "设备树", "tr_relatedPerson": "关联人", "tr_similarity": "相似度", "tr_video": "视频", "tr_image": "图片", "tr_centralServer": "中心服务器", "tr_centralServerPlatform": "中心服务器（平台）", "tr_requestNotificationLocation": "获取位置信息权限使用说明：用于访问确切位置信息、搜索摄像机等设备场景", "tr_requestNotificationCamera": "相机权限使用说明：用于拍照、录制视频等场景", "tr_requestNotificationPhoto": "读取存储卡权限使用说明：用于读取存储卡上的照片、媒体内容和文件等场景", "tr_requestNotificationStory": "访问存储权限使用说明：用于修改或删除存储卡上的照片、媒体内容和文件场景", "tr_serverPortNum": "SIP服务器端口", "tr_serverActualAccessChannelRate": "接入通道数/申请通道数", "tr_success": "成功", "tr_failure": "失败", "tr_exeSync": "同步", "tr_syncInterceptTitle": "授权拦截详情", "tr_DeviceChannelID": "通道ID", "tr_time_c": "次", "tr_passRate": "合格率", "tr_amapTitle": "电子地图", "tr_deviceList": "查看列表", "tr_deviceSum": "总设备数", "tr_deviceOnLine": "在线设备", "tr_latitude": "纬度", "tr_longitude": "经度", "tr_liveStreaming": "直播", "tr_surrounding": "周边", "tr_aiInspection": "AI 巡检", "tr_notEffective": "未生效", "tr_paused": "已暂停", "tr_expired": "已过期", "tr_configureAlgorithm": "配置算法", "tr_createAIInspectionPlan": "创建 AI 巡检计划", "tr_acceptancePerson": "验收人", "tr_associatedAlgorithm": "关联算法", "tr_pleaseSelectAlgorithm": "请选择算法", "tr_selectAlgorithm": "选择算法", "tr_parameterConfiguration": "参数配置", "tr_AIDetailInspection": "AI 巡检详情", "tr_detailInspection": "巡检详情", "tr_AIPlanInspectionEdit": "AI 巡检计划编辑", "tr_searchAlgorithmByName": "请输入算法名称搜索", "tr_titleDeviceList": "设备列表", "tr_noDevice": "暂无设备", "tr_selectAcceptancePerson": "选择验收人", "tr_pleaseSelectAcceptancePerson": "请选择验收人", "tr_selectPerson": "选择人员", "tr_goToSelect": "前往选择", "tr_belongDevice": "所属设备", "tr_selectRectificationPerson": "选择整改人", "tr_pleaseSelectRectificationPerson": "请选择整改人", "tr_someOptionsUnSelect": "还有未选项", "tr_Common_Store": "商城", "tr_createStore": "创建门店", "tr_manageStore": "门店管理", "tr_totalNameWay": "共{name}路", "tr_usedNameWay": "已用{name}路", "tr_surplusNameWay": "还剩{name}路", "tr_organization": "组织", "tr_typeNormal": "标准", "tr_typeSatellite": "卫星", "tr_chooseAtLeastOneAlgorithm": "最少选择一个算法", "tr_devicesInTotal": "共{quantity}台设备", "tr_pleaseEntryPlanNameForSearch": "请输入计划名称搜索", "tr_chooseAtMostNameAlgorithm": "最多选择{name}个算法", "tr_detectExistingInspectionPlan": "检测到巡检计划已有对应计划任务，请选择是否覆盖原有AI巡检计划任务？", "tr_setTime": "时间设置", "tr_endTime": "结束时间", "tr_startTimeMustBeBeforeEndTime": "开始时间必须小于结束时间", "tr_inspectionProblemDescription": "巡检问题描述", "tr_detectionFrequency": "检测频率", "tr_alarmParameters": "告警参数", "tr_confidenceLevel": "置信度", "tr_duration": "持续时长", "tr_resetDraw": "重画", "tr_exitDraw": "退出", "tr_detectionArea": "检测区域", "tr_queryNoDevicesTip": "区域内未找到设备，请重画区域", "tr_useCase": "使用情况", "tr_normal": "正常", "tr_normalProportion": "正常占比", "tr_nameAbnormalRoutes": "{name}路异常", "tr_algorithmList": "算法列表", "tr_searchHitText": "请输入门店名称搜索", "tr_delMoreStore": "您确定要删除选中的门店吗？", "tr_delOneStore": "您确定要删除此门店吗？", "tr_algorithParamsDoNotPairedYet": "算法参数未配置", "tr_algorithParamsHavePaired": "算法参数已配置", "tr_pairTheParams": "配置参数", "tr_analysisRight": "解析正常", "tr_analysisError": "解析异常", "tr_storeName": "门店名称", "tr_storeNameHit": "请输入门店名称", "tr_storeAddress": "门店地址", "tr_storeAddressHit": "请输入地址", "tr_asyncStoreLatLng": "门店经纬度设置到设备", "tr_choice": "去选择", "tr_btnCreateStore": "设定门店", "tr_analysisStop": "解析停止", "tr_storeDevices": "门店设备", "tr_storeChannelDevices": "共{channels}台设备", "tr_createStoreSuccessTip": "创建门店成功", "tr_titleDeviceNodeResources": "设备节点资源", "tr_rootNode": "根节点", "tr_searchStoreAddress": "去搜索门店地址", "tr_AIInitiate": "AI发起", "tr_eventSource": "事件来源", "tr_rectificationComplete": "整改完成", "tr_pendingForAcceptance": "待验收详情", "tr_initiateTime": "发起时间", "tr_deadlineTime": "截止时间", "tr_initiatePerson": "发起人", "tr_chooseInitiatePerson": "选择发起人", "tr_watchRectifyTask": "等待整改", "tr_watchCopyTask": "查看抄送任务", "tr_pendingPersonRectification": "待“{name}”整改", "tr_pendingPersonAcceptance": "待“{name}”验收", "tr_personAcceptancePassed": "”{name}”验收通过", "tr_rectificationInstructions": "整改说明", "tr_acceptanceInstructions": "验收说明", "tr_moreInstructionsMaxHundred": "请输入说明，最多100字", "tr_autoCertification": "自动留证", "tr_titleAIAlgorithm": "AI算法", "tr_algorithmElectricBikeDetection": "电动车检测", "tr_algorithmFaceMaskDetection": "口罩检测", "tr_aiInspectionSource": "AI巡检", "tr_personInspectionSource": "人工巡检", "tr_hasStopped": "已停止", "tr_timeFilter": "时间筛选", "tr_time": "时间", "tr_pleaseSelectStartTime": "请选择开始时间", "tr_pleaseSelectEndTime": "请选择结束时间", "tr_searchHistory": "搜索历史", "tr_searchRegion": "搜地区", "tr_searchRegionNoData": "未搜索到地名信息", "tr_searchRegionNoAddressData": "未搜索到附近地址信息", "tr_searchStoreAddressNoAddressData": "请搜索或定位门店地址获取位置信息", "tr_searchStoreAddressAddressInfo": "请搜索门店地址位置信息", "tr_selectDeviceNode": "请选择节点", "tr_inputStoreName": "请输入门店名称", "tr_searchDevice": "搜设备", "tr_pleaseInputRegionName": "请输入地区名称搜索", "tr_pleaseInputDeviceName": "请输入设备名称搜索", "tr_pleaseSelectCityRegionName": "请先选择城市或地区", "tr_select": "选择", "tr_isQualified": "是否合格", "tr_inspectionMethod": "巡检方式", "tr_spotCheck": "抽查", "tr_task": "任务", "tr_chooseAtMostNamePerson": "最多选择{name}个人员", "tr_applicationClassification": "应用分类", "tr_AppName": "蜂云SaaS", "tr_SpotCheckInspection": "抽查巡检", "tr_envSwitching": "环境切换", "tr_envTest": "测试", "tr_envTestB": "测试B", "tr_envPreRelease": "预发", "tr_envRelease": "正式", "tr_taskName": "任务名称", "tr_associatedTemplate": "关联模板", "tr_inspectionScope": "巡检范围", "tr_notOverdueYet": "未逾期", "tr_inProgress": "进行中", "tr_inspectionStatus": "巡检状态", "tr_algorithmName": "算法名称", "tr_inputApplicationName": "请输入应用名称", "tr_appPatrolIndex": "智慧巡检", "tr_appVideoCloudBase": "视频基座", "tr_appSmartCloudStore": "智慧云店", "tr_appAlgorithmCenter": "算法中心", "tr_appEnterpriseManagement": "企业管理", "tr_appAccessAuthority": "权限管理", "tr_appDealCenter": "待办中心", "tr_appSmartStore": "门店列表", "tr_appAlgorithmConfig": "算法配置", "tr_appAlgorithmList": "云端算法", "tr_appDeviceSideAlgorithm": "端侧算法", "tr_appFacialManagement": "人脸管理", "tr_appVideoSurveillance": "视频监控", "tr_appVideoRecord": "录像管理", "tr_appDevicesTree": "设备管理", "tr_appPatrolIndexFlow": "巡检流程", "tr_levelSetEvaluationTitle": "设置考评模板", "tr_levelInspectionTitle": "巡检", "tr_levelInspectionDataTitle": "巡检数据", "tr_levelEventTitle": "事件数据", "tr_addDevicesChannelTips": "设备通道接入授权{maxChannel}路，剩余{remainChannel}路。", "tr_taskExecutionTime": "任务执行时间", "tr_taskExecutionMaximum": "最多可设置{max}个", "tr_existed": "已存在", "tr_pleaseSetExecutionTime": "请设置一个执行时间", "tr_addTime": "添加时间{time}", "tr_errorConvertingImage": "转换图片出错", "tr_triangle": "三角形", "tr_quadrilateral": "四边形", "tr_pentagon": "五边形", "tr_hexagon": "六边形", "tr_deviceHasBeenDeleted": "设备已删除", "tr_hasBeenDeleted": "已删除", "tr_incompleteDeviceInfo": "设备信息不完善", "tr_lackNecessaryDeviceInfo": "缺少必要的设备信息", "tr_accountAddInfoTips": "当前设备已经被当前账户或者其他账户添加,请勿重复添加。", "tr_channelUnreasonable": "输入路数不合理，请重新输入", "tr_channelsInsufficient": "您的设备通道授权数不足", "tr_setUp": "已设置", "tr_noInputParameter": "没有输入参数", "tr_china": "中国", "tr_NormalMessage": "消息", "tr_chooseDeviceMaxCount": "最多可选{deviceMaxCount}个资源", "tr_choseDeviceCount": "已选资源", "tr_choseDeviceResource": "已选资源: ", "tr_channelsInsufficientRet": "套餐剩余路数不足，请联系售后人员进行扩容再操作。", "tr_insufficientAuthorization": "授权不足", "tr_insufficientChannels": "申请不足", "tr_insufficientApplicationChannels": "申请通道数不足", "tr_numberChannel": "通道号：", "tr_chooseSyncChannels": "请选择需要同步授权的通道", "tr_hasNoAuthorization": "无权限操作", "tr_channelNum": "通道数", "tr_detail": "详情", "tr_InitiatePersonRectificationTasks": "“{name}”的整改任务", "tr_lastWeek": "上周", "tr_thisWeek": "本周", "tr_thisMonth": "本月", "tr_lastMonth": "上月", "tr_personInitiate": "人工发起", "tr_startDate": "起始日期", "tr_endDate": "终止日期", "tr_eventInitiateType": "事件发起类型", "tr_allPerson": "全部人员", "tr_everyMonth": "每月", "tr_everyWeek": "每周", "tr_numberFailures": "不合格次数", "tr_proportion": "所占比例", "tr_frequency": "次数", "tr_inspectionCategory": "所属巡检类", "tr_totalNumberFailures": "不合格总次数", "tr_proportionValue": "占比", "tr_highFrequencyQuestion": "高频问题", "tr_numberHighFrequencyQuestion": "高频问题对应次数", "tr_chooseInspectionDate": "请选择巡检日期", "tr_taskExecutionDate": "任务执行日期", "tr_chooseInspectionPeriodDate": "请选择{period}巡检日期", "tr_ranking": "排名", "tr_overdueRate": "逾期率", "tr_fraction": "分", "tr_completionTime": "完成时间", "tr_plannedInspection": "计划巡检", "tr_inspectionPlanSettings": "巡检计划设置", "tr_planValidityPeriod": "计划有效期", "tr_inspected": "已巡检", "tr_processor": "处理人", "tr_pleaseSelectInspectionTemplate": "请选择巡检模板", "tr_pleaseSelectInspectionDevice": "请选择巡检设备", "tr_createInspectionPlan": "创建巡检计划", "tr_inspectionPlanDetail": "巡检计划详情", "tr_validityPeriod": "有效期", "tr_selectMultipleChoices": "去选择(可多选)", "tr_optionalFields": "非必填项", "tr_inputValidityPeriod": "输入时效", "tr_processingTime": "处理时效", "tr_makeCopy": "抄送", "tr_DeviceStateChangeOnline": "上线", "tr_DeviceStateChangeOffline": "下线", "tr_aiAlarm": "AI报警", "tr_deviceAlarm": "设备报警", "tr_viewUserInfoOperation": "查看管理用户信息及相关操作", "tr_viewUserRoleInfoOperation": "查看管理人员角色权限信息及相关操作", "tr_viewDepartmentInfoOperation": "查看部门结构及相关操作", "tr_memberManagerDesc": "查看平台及非平台成员信息及相关操作", "tr_platform": "平台", "tr_noPresetPoint": "暂无预置点", "tr_play": "播放", "tr_year": "年", "tr_month": "月", "tr_day": "日", "tr_datePicker": "日期选择", "tr_stopTime": "终止时间", "tr_morning": "上午", "tr_afternoon": "下午", "tr_cannotLaterStopTime": "开始时间不能晚于终止时间", "tr_maximumTimeSupport": "时间最多支持{day}天", "tr_refreshHeaderReleaseText": "松开手刷新", "tr_refreshHeaderRefreshingText": "刷新中", "tr_refreshHeaderCompleteText": "刷新完成", "tr_refreshHeaderFailedText": "刷新失败", "tr_refreshHeaderIdleText": "下拉刷新", "tr_refreshFooterLoadingText": "加载中...", "tr_refreshFooterNoDataText": "没有更多了", "tr_refreshFooterFailedText": "加载失败", "tr_refreshFooterIdleText": "上拉加载", "tr_refreshFooterCanLoadingText": "松手开始加载数据", "tr_insufficientAuthorizationRoutes": "授权路数不足", "tr_deviceIsOffline": "设备已离线", "tr_dasProtocol": "DAS协议", "tr_notSupported": "不支持", "tr_support": "支持", "tr_psdKeyVerify": "密钥验证", "tr_deviceDetails": "设备详情", "tr_deviceAndEvidence": "设备及留证", "tr_autoEvidenceTime": "自动留证时间", "tr_setDetectionFrequency": "设置检测频率", "tr_searchDeviceNoData": "未搜索到对应设备信息", "tr_faceDatabase": "人脸库", "tr_searchFaceHitText": "请输入用户名/手机号搜索", "tr_addFacialUser": "添加新用户", "tr_shakeForInspection": "摇一摇巡检", "tr_shake": "摇一摇", "tr_platformUser": "平台用户", "tr_facialImage": "人脸图", "tr_switchPlatform": "转平台", "tr_switchPlatformTip": "以下为非平台用户，可转为平台用户", "tr_delChooseUserInfoTip": "您确定要删除选中的用户吗？", "tr_commonPhone": "手机号码", "tr_pleaseInputPhone": "请输入手机号码", "tr_commonEmail": "电子邮箱", "tr_pleaseInputEmail": "请输入电子邮箱", "tr_userPermissionConfig": "用户权限配置", "tr_pleaseInputTwicePsd": "请再次输入密码", "tr_PersonDetailEdit": "用户详情编辑", "tr_choseDepartmentResource": "已选部门", "tr_shakeToSearchMonitoring": "摇一摇搜索监控", "tr_searchingMonitoringDevicePleaseWait": "正在搜索监控设备，请稍等片刻...", "tr_passengerFlowStatistics": "客流统计", "tr_passengerFlowPeerConfiguration": "客流统计配置", "tr_passengerFlowStatisticsData": "客流统计数据", "tr_thisYear": "本年", "tr_dataOverview": "数据概览", "tr_storeConversionRate": "进店转化率", "tr_storeEntryRate": "进店率", "tr_totalStoreEntryRateProportion": "总进店率占比", "tr_yearOnYear": "同比", "tr_chainRatio": "环比", "tr_storeTraffic": "进店客流", "tr_passByTraffic": "过店客流", "tr_outStoreTraffic": "出店客流", "tr_totalTraffic": "总客流", "tr_pleaseChooseRole": "请选择角色", "tr_pleaseChooseDevice": "请选择设备", "tr_userType": "用户类型", "tr_userTypeNotPlatformTip": "非平台用户(仅用于人脸库管理)", "tr_userTypePlatformTip": "平台用户(仅用于人脸库管理)", "tr_notPlatformUser": "非平台用户", "tr_departmentStructure": "组织结构", "tr_pleaseChooseDepartment": "请选择部门", "tr_uploadFaceImage": "上传人脸", "tr_uploadFaceImageBtn": "点击拍摄/上传", "tr_switchPlatformSuccessfully": "转平台成功", "tr_pleaseUploadFaceImage": "请拍摄或上传人脸照片", "tr_pleaseChooseUserType": "请选择用户类型", "tr_checkEmailNotValid": "邮箱格式不正确", "tr_entryPeak": "进店峰值", "tr_entryTrough": "进店谷值", "tr_deviceIdIsEmpty": "设备id为空", "tr_versionUpdateFindLatestVersion": "发现新版本", "tr_versionUpdateLatestDownload": "更新版本", "tr_versionUpdateVersionLatestTip": "已经是最新版本", "tr_person": "人", "tr_storeTrafficStatistics": "门店客流统计", "tr_showLanguage": "显示语言", "tr_languageSimpleChinese": "简体中文", "tr_languageUSEnglish": "English", "tr_setAMapParam": "地图设置", "tr_setSystemPermission": "系统权限", "tr_versionUpdateContent": "更新内容", "tr_versionUpdateCancel": "暂不升级", "tr_versionUpdateConfirm": "立即升级", "tr_flowStatisticsConfigTip": "1、摄像头建议略大于45°俯拍视角，对着店外。\n2、框选区域为店外区域，箭头一侧内为店内。\n3、框选区域可拖动调整大小、位置。", "tr_phoneLocation": "跟随手机定位", "tr_permissionLocationTitle": "访问位置信息", "tr_permissionLocationSubtitle": "用于实时定位、设备配网时获取WiFi信息", "tr_permissionStorageTitle": "访问存储", "tr_permissionStorageSubtitle": "用于保存设备端图片、视频等功能", "tr_permissionCameraTitle": "访问相机", "tr_permissionCameraSubtitle": "用于拍照、扫描二维码等功能", "tr_permissionAudioTitle": "访问麦克风", "tr_permissionAudioSubtitle": "用于设备语音对讲等功能", "tr_Permission_Audio": "请允许访问麦克风！", "tr_refreshText": "刷新", "tr_searchNoAddressData": "请搜索或定位获取位置信息", "tr_verifyCodeLogin": "验证码登录", "tr_changeLanguage": "语言切换", "tr_confirmChangeLanguage": "是否确定切换成：", "tr_IPC": "IPC", "tr_NVR": "NVR", "tr_DVR": "DVR", "tr_deviceSource": "设备来源", "tr_peakCloudDirectConnection": "蜂云直连", "tr_longPressToShowDetails": "长按显示详情", "tr_faceSimilarity": "人脸相似度", "tr_associatedFace": "关联人脸", "tr_allFacesUnderDefaultAccount": "默认账号下全部人脸", "tr_chooseAtMostNameDepartment": "最多选择{name}个部门", "tr_deviceConfigNoLatLng": "设备未配置经纬度坐标", "tr_getNoLatLng": "未获取经纬度坐标", "tr_noStoragePermissionPleaseRetryAfterOpening": "没有存储权限，请打开权限后重试", "openPhotoPermissionTip": "请在系统设置中为当前应用打开相册访问权限", "openCameraPermissionTip": "请在系统设置中为当前应用打开相机访问权限", "openStoragePermissionTip": "请在系统设置中为当前应用打开存储权限", "openLocationPermissionTip1": "请在系统设置中为当前应用打开定位权限", "openLocationPermissionTip": "需要您授予定位权限以读取当前连接的WiFi，请在系统设置中为当前应用打开定位权限。如果不授权，请手动输入", "tr_chooseDeptMaxCount": "最多可选{maxCount}个部门", "tr_storeHasNoPassengerFlowCameraPleaseAddFirst": "门店无客流统计摄像头，请先添加客流统计摄像头。", "tr_passengerFlowDataInformationClearsEveryDayAt24": "客流数据信息每日24:00清零", "tr_flowDeviceTip": "仅对门店下支持客流统计的设备做数据分析。不支持客流统计的设备不做统计。", "tr_allSources": "所有来源", "tr_allAlarms": "所有报警", "tr_imageStyleTip": "支持扩展名: jpeg、jpg、bmp格式；\n图片限制: 10MB；\n建议分辨率: 626*413（2寸标准证件照）", "tr_dioException_cancel": "请求已被取消，请重新请求", "tr_dioException_timeout": "连接超时", "tr_dioException_unknown": "网络异常，请检查网络请求", "tr_dioException_connectionError": "网络未连接,请检查网络", "tr_dioException_badCertificate": "错误证书，请重新检查证书", "tr_dioException_badResponse400": "参数错误", "tr_dioException_badResponse401": "没有权限", "tr_dioException_badResponse403": "服务器拒绝执行", "tr_dioException_badResponse404": "无法连接服务器", "tr_dioException_badResponse405": "请求方法被禁止", "tr_dioException_badResponse500": "服务器内部错误", "tr_dioException_badResponse502": "无效的请求", "tr_dioException_badResponse503": "服务器挂了", "tr_dioException_badResponse504": "服务器无法在规定的时间内获得想要的响应", "tr_dioException_badResponse505": "不支持请求所使用的HTTP版本", "tr_pleaseSelectDepartmentToAssociateFaces": "请选择需要关联人脸的部门", "tr_errorMixFormatPassword": "请输入8-16位含数字、字母和特殊字符", "tr_passwordManage": "密码管理", "tr_pwdConfig": "密码设置", "tr_setPwdConfig": "设置密码", "tr_cancellationAccountAgreement": "账户注销协议", "tr_setDuration": "设置持续时长", "tr_type": "类型", "tr_rectificationScope": "整改范围", "tr_tryCaptureAction": "重新抓拍", "tr_failPassed": "不通过", "tr_successPassed": "通过", "tr_goPassedItems": "验收通过项", "tr_deviceInspectionAnalysis": "设备巡检分析", "tr_storeInspectionAnalysis": "门店巡检分析", "tr_delMoreAiAlarmMsg": "您确定要删除选中的AI告警消息吗？", "tr_delOneAiAlarmMsg": "您确定要删除此AI告警消息吗？", "tr_storeQuestionRanking": "门店发现问题排名", "tr_playbackFailedPleaseRetry": "播放失败，请重试", "tr_numberQuestions": "问题次数", "tr_problemChain": "问题环比", "tr_selectedTime": "已选时间", "tr_selectStores": "请选择门店", "tr_titleSelectStore": "选择门店", "tr_selectDeviceNodeResources": "请选择设备节点", "tr_safetyManagement": "安全管理", "tr_videoWatermark": "视频水印", "tr_watermarkSettings": "水印设置", "tr_watermarkSettingsTip": "开启后视频播放、下载、截图都会携带水印", "tr_exemptFromWatermarkWhitelist": "免水印白名单", "tr_watermarkContent": "水印内容", "tr_watermarkContentTip": "回显自定义的水印内容", "tr_customContent": "自定义内容", "tr_notYetSetCustomWatermarkContentAreYouSureToReturn": "还未设置自定义水印内容，确定返回？", "tr_namePlusAccount": "姓名+账号", "tr_algorithmConfigDescription": "关于算法配置的相关描述", "tr_alarmSetting": "告警设置", "tr_delMoreDeviceAlarmMsg": "您确定要删除选中的设备告警消息吗？", "tr_delOneDeviceAlarmMsg": "您确定要删除此设备告警消息吗？", "tr_alarmAIEdit": "AI告警编辑", "tr_alarmDeviceEdit": "设备告警编辑", "tr_permissionNotificationTitle": "通知管理权限", "tr_permissionNotificationSubtitle": "用于APP进行消息推送通知", "tr_confidence_tip": "置信度越低，识别精准度越低，识别的结果越多\n置信度越高，识别的结果越少，识别精准度越高", "tr_confidence_tip_1": "1.值越小区分人行越严格；当前推荐阈值6.8%\n2.若一天内频繁调整阈值，对不同阈值下的去重效果会有影响；\n3.不建议调整阈值，若要调整阈值，尽量一天调整一次", "tr_duration_tip": "检测的行为时间大于持续时长时，才产生告警", "tr_tapToAdd": "点击添加", "tr_storeEventRanking": "门店事件排名", "tr_storeEventCount": "事件数", "tr_storeOverdueCount": "逾期数", "tr_userHasBeenDeleted": "用户已删除", "tr_playerError": "播放器错误", "tr_playbackCompleted": "已完成播放", "tr_watermarkContentCanNotBeEmpty": "水印内容不能为空", "tr_retrying": "重试中...", "tr_retryingNameCount": "正在进行第{name}次重试", "tr_storeAcceptanceCount": "验收数", "tr_arithmeticChannelNotEnoughTip": "“{name}”算法，路数不足", "tr_insufficientTip": "请联系**************扩容", "tr_overdueTimes": "逾期次数", "tr_canNotEditAreaTip": "设备异常，无法保存区域。", "tr_aiPlatformAlarm": "AI平台报警", "tr_aiDeviceAlarm": "AI设备报警", "tr_permissionStatement": "权限声明", "tr_addBatches": "批量添加", "tr_lANEquipment": "局域网设备", "tr_addBatchesDevices": "批量添加设备", "tr_addDiscoveredBatchesDevices": "批量添加局域网搜索到的设备", "tr_waitDiscoveredDevices": "扫描到可用设备才能执行添加操作", "tr_deviceAdded": "设备已添加", "tr_inputDevicePsd": "请输入设备密码", "tr_psdVerificationFail": "密码校验失败", "tr_addDeviceDialogTip1": "若忘记密码，请恢复出厂设置重新添加！", "tr_addDeviceDialogTip2": "设备开机超过1小时，无法登录设备，请恢复出厂设置重新添加！", "tr_ensure": "确认", "tr_returnHomePage": "回到首页", "tr_psdVerification": "密码校验", "tr_addException": "添加异常", "tr_thisCompanyAddExceptions": "设备已被本企业添加，请联系管理员确认。若有需要，可联系管理员将设备分配给您。", "tr_otherCompanyAddExceptions": "设备已被其他企业添加，若是您的设备，请联系客服处理。", "tr_addFailed": "添加失败", "tr_deviceNotFound": "查询不到设备，请重置设备后重新添加", "tr_stopNetConfigure": "当前正在快速配网和扫描配网中，您确定要中断配网吗？", "tr_twiceAddDevice": "继续添加", "tr_extendRole": "继承角色", "tr_newRole": "新建角色", "tr_nationalStandardDeviceAccess": "国标接入", "tr_nationalStandardAccessManagement": "国标接入管理", "tr_deviceStateRegister": "已注册", "tr_authorizeIntercept": "授权拦截", "tr_accessStatus": "接入状态", "tr_onlineStatus": "在线状态", "tr_allStatus": "所有状态", "tr_allDevices": "所有设备", "tr_sceneInspection": "现场巡检", "tr_beginSceneInspection": "开始现场巡检", "tr_inspectionConfirm": "巡检确认", "tr_bandwidthStatistics": "带宽统计", "tr_realTimeBandwidth": "实时带宽", "tr_bandwidthCap": "带宽上限", "tr_clearAllMessage": "一键清除", "tr_clearAllMessageTip": "请输入本账号登录密码验证删除", "tr_clearMessageContent": "确认一键删除所有报警消息？", "tr_pleaseInputNameForSearch": "请输入名称搜索", "tr_OnSiteInspectionDetail": "现场巡检详情", "tr_inspectionAddress": "巡检地址", "tr_signInImage": "签到图片", "tr_inspectionNode": "巡检节点", "tr_currentLocation": "当前位置", "tr_takePhotoForSignin": "拍照签到", "tr_takePhotoForSigninTip": "请拍摄现场照片打卡认证", "tr_commitSceneInspectionResult": "提交巡店结果", "tr_turnOn": "开启", "tr_turnOff": "关闭", "tr_locating": "正在定位", "tr_qualifiedItem": "合格项", "tr_unqualifiedItem": "不合格项", "tr_remark": "备注", "tr_alarmSwitch": "消息接收", "tr_alarmSwitchTip": "关闭后，手机将无法接收报警推送消息", "tr_exitLoginTip": "您的账号已在其他设备登录，若不是您的操作，请重新登录并修改密码", "tr_exitKnow": "知道了", "tr_taskCreationTime": "任务创建时间", "tr_failedToCreateOnsiteInspection": "创建现场巡检失败", "tr_imageRequiredForNonCompliance": "不合格必须有图片", "tr_emptyImage": "图片为空", "tr_continueInspectionOutsideStoreRange": "您未在门店范围内，是否继续巡检？", "tr_outOfRange": "超出范围", "tr_patrolStoreDetails": "巡店详情", "tr_overallScore": "综合得分", "tr_pageStay": "页面停留", "tr_signInInfo": "签到信息", "tr_signOutInfo": "签出信息", "tr_gettingVideoStream": "正在获取视频流，请稍候", "tr_noAvailableDevices": "无可用设备", "tr_location": "位置", "tr_deviceTag": "设备标签", "tr_inspectionDeviceOfflineOrDeleted": "巡检设备离线或已删除，无法进行巡检", "tr_accessInfo": "接入信息", "tr_locationInfo": "位置信息", "tr_deviceCapacity": "设备能力", "tr_channelInfo": "通道信息", "tr_abnormalDevice": "异常设备", "tr_abnormalDeviceTip": "以下为异常设备，离线或无法打开视频，不参与巡检操作", "tr_resetFlowInfo": "客流数据信息每日24:00清零", "tr_deviceSideFlowTip": "按目标区域实时统计画面内进出店的人员数量", "tr_fullScreen": "全屏", "tr_switchingDevicesPleaseWait": "正在切换设备，请稍候", "tr_reason": "原因", "tr_deviceExceptionDuringDetectionNotParticipatingInInspection": "检测过程中出现异常的设备，不参与巡检", "tr_creator": "创建人", "tr_chooseCreator": "选择创建人", "tr_createOnsiteInspection": "创建现场巡检", "tr_createImageInspectionPlan": "创建抓拍计划", "tr_resetNode": "默认", "tr_flowExpired": "（已过期）", "tr_detailUuid": "序列号", "tr_inputChannels": "请输入通道数", "tr_notSupportPassengerFlow": "暂无支持客流统计的设备", "tr_invalidSVData": "无效sn，请扫描正确二维码", "tr_deviceOffline": "设备不在线", "tr_allNode": "全部节点", "tr_enterInspectionItemNameToSearch": "请输入巡检项名称搜索", "tr_uploadingImagePleaseWait": "正在上传图片，请稍候", "tr_videoPlayAddress": "视频播放地址", "tr_downloadList": "下载列表", "tr_downloadManagement": "下载管理", "tr_downloading": "下载中", "tr_allStores": "所有门店", "tr_viewDownloadList": "查看下载列表", "tr_videoDuration": "视频时长", "tr_playAddress": "播放地址", "tr_playAddressRtsp": "RTSP播放地址", "tr_playAddressFlv": "FLV播放地址", "tr_playAddressHls": "HLS播放地址", "tr_playAddressRtmp": "RTMP播放地址", "tr_playAddressExpiration": "过期时间", "tr_watchConcurrency": "观看并发数", "tr_generatePlayAddress": "生成播放地址", "tr_startDownloadTip": "下载任务添加成功，请到下载管理中查看", "tr_ensureDeviceOnline": "请确认设备是否在线", "tr_InputFrequency": "请输入观看并发数", "tr_checkFrequency": "观看并发数不合理", "tr_list": "列表", "tr_alarmType": "报警类型", "tr_nodeInfo": "节点信息", "tr_targetInformation": "目标信息", "tr_detectionBox": "检测框", "tr_targetBox": "目标框", "tr_endSidePassengerBox": "端侧客流检测框", "tr_taskExists": "已经有相同任务，无需重复添加", "tr_cancelTaskTip": "该操作会取消正在下载的任务（需从头开始下载），是否继续？", "tr_downloadCanceled": "下载取消", "tr_getDownloadLinkFailed": "获取下载链接失败", "tr_transcodingInProgress": "转码中", "tr_deleteFailed": "删除失败", "tr_deleteFailedDownloadingTask": "正在下载的任务删除失败", "tr_stopFailedDownloadingTask": "正在下载的任务停止失败", "tr_partialDeleteFailedTasks": "部分任务删除失败", "tr_maximumNameTasksDownloadLimit": "最多只能同时下载{name}个任务", "tr_downloadChannelOccupied": "下载通道被占用", "tr_replayChannelOccupied": "回放通道被占用", "tr_localPlaybackDownloadConflict": "同一个设备本地回放预览和下载不可同时进行，继续播放将暂停下载", "tr_deviceInUseByAnotherUser": "此设备已经有其他人在预览或下载，请稍候重试", "tr_pleaseExitReplayPageAndRetry": "请退出回放页面后再进行重试", "tr_continue": "继续", "tr_noVideosToDownloadAdjustRegion": "无可下载视频，请调整下载区域", "tr_channelOccupied": "通道被占用", "tr_customTime": "自定义时间", "tr_allDayRunning": "全天运行", "tr_customTimePeriod": "自定义时间段", "tr_setUpToNameTimePeriods": "最多可设置{name}个时间段", "tr_overlapWithExistingTimePeriods": "选择的时间段和已有的时间段有重合", "tr_Permission_Bluetooth": "蓝牙", "tr_trafficExcess": "流量已超额", "tr_runtime": "运行时间", "tr_defaultAllDayRunning": "默认全天运行", "tr_supportedCentralAlgorithms": "支持的中心算法", "tr_algorithmInvocationStatistics": "算法调用统计", "tr_customerSegmentationAnalysis": "客群分析", "tr_ageGroupDistribution": "年龄段占比", "tr_genderDistribution": "性别占比", "tr_malePercentageName": "男性占比:{name}%", "tr_femalePercentageName": "女性占比:{name}%", "tr_algorithmStatistics": "算法统计", "tr_AIInspectionStatistics": "AI巡检统计", "tr_children": "儿童", "tr_youth": "青年", "tr_youthMiddleAged": "中青年", "tr_middleAged": "中年", "tr_elderly": "老年", "tr_supportedEdgeAlgorithms": "支持的端侧算法", "tr_btEquipment": "蓝牙设备", "tr_enterStoreBatch": "进店批次", "tr_enterStoreBatchInterval": "进店批次间隔", "tr_pleaseEnterContinuousDetectionCount": "请输入连续检测次数", "tr_btConnectNetwork": "设备连接无线网络", "tr_tr_btConnectNetworkConfiguration": "连接过程大概需要1-2分钟，请您稍等片刻。", "tr_tr_btConnectNetworkConfigurationTip": "路由器、手机和设备尽量靠近...", "tr_CheckAddBTDeviceFailed": "设备校验失败，请在设备端确认。如需继续添加，可将新的设备重置再试一试。", "tr_alarmCallCount": "告警调用次数", "tr_alarmGeneratedCount": "产生告警次数", "tr_inspectionCallCount": "巡检调用次数", "tr_inspectionAlarmCount": "巡检告警次数", "tr_totalCallCount": "总调用次数", "tr_totalAlarmCount": "总告警次数", "tr_btConnectNetworkConfiguration": "连接过程大概需要1-2分钟，请您稍等片刻。", "tr_btConnectNetworkConfigurationTip": "路由器、手机和设备尽量靠近...", "tr_stopBTNetConfigure": "确定要中断蓝牙配网？", "tr_retAddDeviceFailed": "1. 请插好电源和数据线，确保设备上电并已经正常运行；\n\n2. 请将设备，手机和路由器尽量靠近（1米以内的范围内），确保信号接收成功；\n\n3. 请确保输入的路由器账号密码是正确的；\n\n4. 可将设备SET/RESET键，将设备恢复出厂设置后重新配网连接。", "tr_retryBTConnectFailed": "无法连接蓝牙设备，请重试！", "tr_retry": "重试", "tr_failConnectNetwork": "设备联网遇到问题？", "tr_algorithmAndDevice": "算法和设备", "tr_automaticallyInitiateRectificationEvent": "自动发起整改事件", "tr_aiInspectionPlan": "AI巡检计划", "tr_pleaseSelectAlgorithmAndDeviceFirst": "请先选择算法和设备", "tr_passScore": "合格分数线", "tr_deleteFromCurrentPlan": "从当前计划中删除", "tr_deleteFromOriginalPlan": "从原计划中删除", "tr_arithmeticOverTip": "以上设备和算法与其他计划中重叠，您可在当前计划中删除或直接删除原计划中的设备和算法。", "tr_deviceAndAlgorithmConfiguration": "设备和算法配置", "tr_exitBTDistributeFail": "密码错误", "tr_exitBTDistributeFailSub": "密码错误，请重置设备，恢复出厂设置后重新添加", "tr_openBTDevice": "请开启蓝牙，才能搜索到设备", "tr_durationEqualsContinuousDetectionCountTimesDetectionFrequency": "时长为连续检测次数*检测频率", "tr_detectionFrequencySmartScheduling": "检测频率智能调度", "tr_whenTriggerCondition": "当（触发条件）", "tr_continuousDetection": "连续检测", "tr_timesNoWarning": "次无警告", "tr_increaseDetectionFrequency": "检测频率提升", "tr_detectionFrequencyUpperLimit": "检测频率上限为", "tr_thenExecuteAction": "就（执行动作）", "tr_afterAlarmContinueDetectionFrequencyReturnsToDefault": "告警产生后，继续检测（检测频率回到默认）", "tr_afterAlarmCurrentRunningTimeNoDetection": "告警产生后，当前运行时间不检测", "tr_afterAlarmNoDetectionForRestOfDay": "告警产生后，当天不检测", "tr_delSelectedAiInspection": "您确定要删除选中的巡检计划吗？", "tr_deviceAndAlgorithm": "设备和算法", "tr_notStart": "未开始", "tr_dayMark": "号", "tr_thisDeviceHasNoConfiguredAlgorithm": "该设备暂无配置算法", "tr_detectionFrequencyRangeIs": "检测频率设置范围为：", "tr_detectionFrequencyUpperLimitRangeIs": "检测频率上限设置范围为：", "tr_configurationNotSavedDoYouWantToReturn": "配置还未保存，是否返回？", "tr_resetDeviceNode": "回到默认", "tr_exitBTConnectTimeout": "连接超时", "tr_exitBTConnectTimeoutSub": "连接超时，请重置设备，恢复出厂设置后重新添加", "tr_pleaseEntryProblemDescribe": "请输入问题描述", "tr_captureRecord": "抓拍记录", "tr_pleaseSelectPlan": "请选择计划", "tr_someDevicesHaveNoConfiguredAlgorithm": "还有设备未配置算法", "tr_passFail": "未通过", "tr_ErrorCode_Minus_4101": "设备暂时无法播放", "tr_appHumanoidLibrary": "人形库", "tr_searchNameHitText": "请输入名称搜索", "tr_gatewayType": "网关类型", "tr_gatewaySN": "网关序列号", "tr_gatewayStatus": "网关状态", "tr_pcGateway": "PC网关", "tr_serverGateway": "服务器网关", "tr_nodeAddRefuseMessage": "NVR，平台，ONVIF下不可设置节点，不可添加设备", "tr_allAlgorithms": "全部算法", "tr_callStatistics": "调用统计", "tr_usedAlgorithms": "已用算法", "tr_singlePerson": "单人", "tr_multiplePeople": "多人", "tr_twoPeople": "双人", "tr_address": "地址", "tr_storeManager": "店长", "tr_setStoreManagerHit": "请选择设置店长", "tr_nearbyLocation": "附近位置", "tr_nearbyLocationNoData": "未查询到相关附近位置信息", "tr_noAlgorithmData": "无算法数据", "tr_callCount": "调用次数", "tr_alarmCount": "告警次数", "tr_msgCall": "消息调用", "tr_msgAlarm": "消息告警", "tr_inspectionCall": "巡检调用", "tr_inspectionAlarm": "巡检告警", "tr_statisticsTime": "统计时间", "tr_rectificationEvent": "整改事件", "tr_acceptanceEvent": "验收事件", "tr_expansionChannel": "扩展通道", "tr_channelStartStop": "通道启停", "tr_channelStartStopTitle": "通道启停", "tr_deactivate": "停用", "tr_enable": "启用", "tr_channelStartStopTip": "可选择通道停用，停用后，设备将不在设备列表中显示。", "tr_channelStatusStop": "已停用", "tr_channelStatusStart": "已使用", "tr_sixSNCode": "6位序列号", "tr_inputSixSNCode": "请输入6位序列号编码", "tr_sixSNCodeTips": "长度6位，由数字组成", "tr_cloudTraffic": "云端客流", "tr_deviceTraffic": "端侧客流", "tr_storeFlow": "门店客流", "tr_pleaseSaveCustomAreaFirst": "请先保存自定义区域", "tr_modifyStore": "门店信息", "tr_onlyCloudStatisticsTip": "仅对所选门店下支持云端客流统计的设备做数据统计", "tr_onlyDeviceStatisticsTip": "仅对所选门店下支持设备端客流统计的设备做数据统计", "tr_setNoHit": "暂未设置", "tr_protocolChooseTip": "网络环境弱时，建议优先TCP", "tr_visibleRangeDept": "可见部门", "tr_chooseVisibleRangeDept": "选择可见部门", "tr_pleaseSelectDept": "请先选择部门", "tr_visibleDeptTip": "以上为所属部门，所属部门默认为可见部门", "tr_switchingStreamPleaseWait": "正在切换主辅码流，请稍候", "tr_notSupportAuxiliaryStream": "不支持辅码流播放", "tr_allAssessmentItems": "所有考评项", "tr_text": "文字", "tr_assessmentReferenceImage": "考评参考图", "tr_notSupportClientFlowDeviceOnlyTip": "端侧客流设备、端云结合客流设备不支持配置云端精准客流，已置灰显示。", "tr_onlySupportClientFlowDeviceTip": "非端云结合客流设备不支持配置端云结合精准客流，已置灰显示。", "tr_userHasPeriod": "用户有效期", "tr_userForeverPeriod": "永久有效", "tr_pleaseChooseValidityPeriod": "请选择有效期", "tr_scanDeviceQRCodeTip": "扫描设备上的二维码", "tr_changeNetAddDeviceTip": "找不到二维码？请使用局域网添加设备", "tr_selectAll": "选择全部", "tr_selectMultiple": "选择多个", "tr_selectPersonnel": "请选择人员", "tr_preciseTrafficFlow": "精准客流", "tr_unableToGetImage": "无法获取图片", "tr_selectableRangeIsInspectorEquipmentPermissions": "可选范围为巡检人所拥有设备权限资源", "tr_canOnlyDrawOneArea": "只能绘制一个区域", "tr_this_week": "本周", "tr_this_month": "本月", "tr_this_year": "本年", "tr_instructionsForAccessing": "国标接入说明", "tr_setNationalStandardConfiguration": "设置国标编码配置", "tr_deny": "拒绝", "tr_accept": "接受", "tr_googlePermissionTips": "蜂云SaaS应用程序收集位置数据，收集照片、视频、相机录像媒体内容或文件数据，即使在应用程序关闭或未使用时也能以读取实时位置数据；识别照片、视频媒体内容或文件信息数据。", "tr_appDeviceAttribute": "设备属性", "tr_deviceTags": "设备标签", "tr_bindTag": "打标签", "tr_clickEditText": "点击修改文字", "tr_crop": "裁剪", "tr_deviceTagsCategory": "设备标签分类", "tr_currentTagHasNoDevice": "当前标签下没有任何设备", "tr_deviceNewCategory": "新增分类", "tr_deviceNewTag": "新增标签", "tr_deviceCreateCategory": "创建分类", "tr_deviceCreateTag": "创建标签", "tr_pleaseTagsCategoryName": "请输入标签分类名称", "tr_selectTagsCategory": "请选择标签分类", "tr_chooseTagsCategory": "筛选标签分类", "tr_searchDeviceTags": "搜索设备标签", "tr_commonBingSuccess": "绑定成功", "tr_pleaseChooseDeviceTags": "请选择设备标签", "tr_delDeviceTags": "您确定要删除选中的标签信息吗？", "tr_talk": "对讲", "tr_onlySupportBroadDeviceTip": "不支持广播的设备和离线的设备已置灰显示。", "tr_tagHasNoDeviceTags": "暂未添加任何设备标签", "tr_deviceNewSuccessfully": "新增成功", "tr_editDeviceTags": "编辑名称", "tr_broadcast": "广播", "tr_delDeviceTagsCategory": "标签分类删除后，分类及分类所属的标签将同步删除。", "tr_connecting": "连接中", "tr_broadcasting": "广播中", "tr_deviceBusy": "设备占线", "tr_broadcastException": "广播异常", "tr_memberManagement": "成员管理", "tr_scanOnlyQRCodeTip": "二维码放入框内扫描", "tr_qrScan": "扫一扫", "tr_scanLogin": "登录确认", "tr_scanWebLogin": "桌面版登录确认", "tr_scanLoginSuccess": "扫码登录成功", "tr_pleaseInputNamePhoneEmail": "请输入用户名/手机号/邮箱搜索", "tr_addChooseManual": "手动添加", "tr_addChooseContacts": "通讯录导入", "tr_addChooseFaceDatabase": "人脸库导入", "tr_addChooseBatch": "批量添加", "tr_commonModify": "修改", "tr_pleaseChoosePersons": "请选择用户人员", "tr_cancelLogin": "取消登录", "tr_chooseMaxCount": "最多可选{maxCount}个用户", "tr_chooseMember": "选择成员", "tr_batchEdit": "批量编辑", "tr_batchModify": "批量修改", "tr_parentCategory": "上级分类", "tr_disable": "禁用", "tr_checkEnable": "是否启用", "tr_pleaseChooseRoles": "请选择角色", "tr_noMicrophonePermissionTip": "没有麦克风权限，请在系统设置中打开麦克风权限", "tr_isNotPlayingTip": "未在播放状态，请稍候重试", "tr_disabled": "禁用中", "tr_active": "启用中", "tr_pleaseChooseTagDevices": "请选择标签下设备", "tr_delTagDevices": "您确定要从标签下移除选中的设备吗？", "tr_remove": "移除", "tr_disableSuccessful": "禁用成功", "tr_enabledSuccessful": "启用成功", "tr_scanReLoginTip": "请重新扫码登录", "tr_confirmRemove": "确认移除", "tr_removeSuccessfully": "移除成功", "tr_pleaseTagsName": "请输入标签名称", "tr_pleaseCategoryName": "请输入分类名称", "tr_agree": "同意", "tr_selectCategory": "已选分类", "tr_hasSelectDeviceNode": "请选择一个节点", "tr_secondaryVerification": "二次校验", "tr_emailActivation": "邮箱激活", "tr_verificationActivation": "验证码激活", "tr_currentAccount": "当前账号为：", "tr_checkAccountLogin": "二次校验登录", "tr_notActivated": "未激活", "tr_modifyAccessIdPwd": "修改接入ID密码", "tr_statistics": "数据统计", "tr_modifyStoreName": "修改门店名称", "tr_allDevicesTotal": "全部({quantity}台)", "tr_precisionDevicesTotal": "精准客流({quantity}台)", "tr_passengerFlowDevicesTotal": "端侧客流({quantity}台)", "tr_passengerFlowDevices": "客流设备", "tr_pleaseChooseDate": "请选择日期", "tr_dateChoose": "日期选择", "tr_flowTypeVisitedStore": "进过店", "tr_flowTypeEnterStore": "进店", "tr_flowTypePassingStore": "过店", "tr_selectTime": "选择时间", "tr_nodeAddress": "节点位置", "tr_nodeSyncAddress": "同步到设备", "tr_colorCast": "偏色", "tr_pureBlackWhite": "纯黑/纯白", "tr_screenGlitch": "花屏", "tr_blur": "模糊", "tr_selectAtLeastOneParameter": "至少要选择一个参数", "tr_noPreciseTrafficFlowDevice": "暂无精准客流设备", "tr_noDeviceTrafficDevice": "暂无端侧客流设备", "tr_runtimePoint": "运行时间点", "tr_deviceNotAddedRuntimePoint": "还有设备未添加运行时间点", "tr_storeEntry": "进过店", "tr_enterStore": "进店", "tr_passByStore": "过店", "tr_excludeDeliveryCourier": "去除外卖员、快递员", "tr_searchJFDevice": "扫描发现设备", "tr_namePleaseRetry": "{name}，请重试", "tr_incorrectPassword": "密码不正确", "tr_accountNotExist": "账号不存在", "tr_loginTimeoutNetworkConnectionFailed": "登录超时 (网络连接失败)", "tr_accountNotLoggedIn": "账号未登录", "tr_accountLoggedIn": "账号已登录", "tr_accountBlacklisted": "账号被列为黑名单", "tr_insufficientDeviceResources": "设备资源不足", "tr_networkHostNotFound": "找不到网络主机", "tr_deviceNotExistDeleted": "设备不存在（被删除掉了）", "tr_deviceTokenInvalid": "设备token不合法", "tr_talkChannelOccupied": "对讲通道被占用", "tr_normalOnline": "正常在线", "tr_deviceNeverReported": "设备没有上报过", "tr_channelMismatch": "通道不符合", "tr_channelOffline": "通道不在线", "tr_accountError": "账号错误", "tr_passwordError": "密码错误", "tr_loginFailed": "登入失败", "tr_parameterError": "参数错误", "tr_handleError": "句柄错误", "tr_apiRequestFailed": "api 请求失败", "tr_playbackTypeError": "播放类型错误", "tr_requestDeviceInfoFromDSMFailed": "向DSM服务请求设备信息失败", "tr_onvifSSIDNotRegistered": "onvif ssid还没有注册上来", "tr_previewPlaybackServerGatewayCannotProcess": "预览/回放由于设备超负载或维护问题，server或gateway不能处理请求", "tr_previewPlaybackDeviceReturnedNotFound": "预览/回放设备返回Not Found", "tr_previewPlaybackFailed": "预览/回放失败", "tr_previewPlaybackRequestTimeoutNoResponse": "预览/回放请求发送设备超时没有回复", "tr_rtspProtocolError": "RTSP协议错误", "tr_urlFormatError": "URL格式错误", "tr_noRecording": "无录像", "tr_urlExpired": "URL过期", "tr_urlAuthenticationFailed": "URL鉴权失败", "tr_noTraffic": "无流量", "tr_urlVerificationFailedGwmCommunicationFailed": "向gwm校验url失败，通信失败", "tr_playbackFailedXmtsCommunicationFailed": "播放失败，xmts通信失败", "tr_queryRecordingFailed": "查询录像失败", "tr_invalidSeekTime": "错误的seektime时间", "tr_noConfigInfoFoundInRedis": "在redis里没找到配置信息", "tr_tokenParsingFailed": "token解析失败", "tr_payloadFailed": "payload失败", "tr_updateRedisFailed": "更新到redis失败", "tr_urlNotAllowedToPlay": "URL不被允许播放", "tr_urlExceedsAllowedConcurrency": "URL超过允许并发数", "tr_networkException": "网络异常", "tr_pleaseRetryLater": "请稍后重试", "tr_searchJFStatusRefresh": "正在搜索设备，请等待…", "tr_searchJFStatusResultFront": "发现{quantity}款设备，", "tr_searchJFStatusResultBehind": "查看详情", "tr_searchJFStatusFailureFront": "未扫描到设备，请", "tr_searchJFStatusFailureBehind": "重新扫描", "tr_scoreCannotBeEmpty": "分数不能为空", "tr_modifyStoreAddress": "修改门店地址", "tr_onlineOfflineAlarm": "上下线告警", "tr_selectEvaluationTemplate": "选择考评模板", "tr_customTemplate": "自定义模板", "tr_syncSubordinateChannels": "同步修改下属通道", "tr_accountAuthorizationStatistics": "子账号授权{total}个，已分配{allocatedNum}个，还剩{unallocatedNum}个。", "tr_deviceNotPlay": "设备不在播放状态", "tr_deviceCamera": "摄像头", "tr_AddDirectNetConnectionNotes": "注： \n1. 确保设备已上电 \n2. 网线与设备网线连接口连接，DVR/NVR接入有线网络。设备启动成功后尝试连接网络，听到设备联网成功，表示联网完成", "tr_chooseDeviceType": "选择设备类型", "tr_scanQRNetworkCodeTips": "扫描设备上的二维码，找不到\n请使用局域网进行设备添加", "tr_snAdd": "序列号添加", "tr_networkAdd": "局域网添加", "tr_pleaseInputDeviceAccount": "请输入设备账号", "tr_pleaseInputSN": "请输入序列号（S/N）", "tr_pleaseInputDevicePassword": "请输入设备密码", "tr_openBlueTooth": "请打开蓝牙及相关权限", "tr_openWiFi": "请打开WiFi", "tr_openLocalNetwork": "请开启本地网络权限", "tr_executeOpen": "去开启", "tr_openBTPermission": "想访问你的蓝牙权限", "tr_openBTPermissionContent": "请开启手机蓝牙，用以扫描附近的蓝牙设备", "tr_titleCollect": "收藏", "tr_recordingType": "录像类型", "tr_eventRecording": "事件录像", "tr_allDayRecording": "全天录像", "tr_collectDevices": "收藏设备", "tr_activate": "激活", "tr_switchPhone": "切换手机号", "tr_switchEmail": "切换邮箱", "tr_heatMap": "热力图", "tr_dataDetails": "数据详情", "tr_notConfiguredHeatArea": "未配置热力区域", "tr_storeDataOverview": "门店数据概览", "tr_mostPopulatedArea": "最受欢迎区域", "tr_leastPopulatedArea": "最不受欢迎区域", "tr_totalPeople": "总人数", "tr_areaTrafficStatistics": "区域客流统计", "tr_trafficTrend": "客流趋势", "tr_areaName": "区域名称", "tr_numberOfPeopleEntering": "进入人数", "tr_averageStayDuration": "平均停留时长", "tr_stayRate": "停留率", "tr_storeFloorPlan": "门店平面图", "tr_trafficHeatZone": "客流热区", "tr_noFloorPlanConfigured": "暂未配置平面设计图", "tr_goConfigure": "去配置", "tr_floorPlan": "平面图", "tr_uploadStoreFloorPlan": "请上传门店平面图", "tr_storeHeatMapConfiguration": "门店热力图配置", "tr_addHeatZone": "添加热区", "tr_nodeCount": "共{nodeCount}个节点", "tr_heatAreaConfiguration": "热力区域配置", "tr_noStoresAvailable": "暂无门店", "tr_storeDeviceInformation": "门店设备信息", "tr_mostPreferredStayArea": "最感兴趣区域", "tr_leastPreferredStayArea": "最不感兴趣区域", "tr_totalStayDuration": "总停留时长", "tr_heatDataDetails": "热力数据详情", "tr_addHeatArea": "添加热力区", "tr_pleaseEnterAreaName": "请输入区域名称", "tr_searchNodeDevice": "输入节点或设备名称", "tr_dataDashboard": "数据看板", "tr_storeFlowRanking": "门店客流排名", "tr_storeInspectionCompletionRate": "巡店任务完成率", "tr_storeInspectionCoverageRate": "巡店覆盖率", "tr_storeInspectionCoverageDetail": "巡检覆盖情况", "tr_passengerFlowInfo": "客流数", "tr_area": "区域", "tr_device": "设备", "tr_numberOfEntries": "进入人次", "tr_clickToEditStoreFloorPlan": "点击修改门店平面图", "tr_longPressToEditStoreFloorPlan": "长按修改门店平面图", "tr_heatArea": "热力区域", "tr_pleaseSelectArea": "请选择区域", "tr_incompleteConfiguration": "未完成配置", "tr_pleaseSelectAtLeastOneArea": "请至少选择一个区域", "tr_storeInspectionOverview": "巡店概览", "tr_passengerFlowData": "客流数据", "tr_store": "门店", "tr_numberOverdueEvents": "逾期事件数", "tr_numberIssuedTasks": "下发任务数", "tr_numberCompletedTasks": "完成任务数", "tr_numberOverdueTasks": "逾期任务数", "tr_completionRate": "完成率", "tr_deviceDimension": "设备维度", "tr_storeDimension": "门店维度", "tr_deviceEventAnalysis": "设备事件分析", "tr_storeEventAnalysis": "门店事件分析", "tr_fetchingImageDimensionsPleaseRetry": "正在获取图片尺寸，请稍候重试", "tr_deviceDataOverview": "设备数据概览", "tr_accountActivation": "账号激活", "tr_deviceInspectionCompletionRate": "巡检任务完成率", "tr_storeResources": "门店资源", "tr_pleaseSelectStore": "请选择门店", "tr_nameDevices": "{name}个设备", "tr_onlySelectStoresWithOnlineDevices": "只能选择有在线设备的门店", "tr_completionRateExplain": "任务完成率仅针对任务巡检有效。", "tr_numberInspectionTasks": "巡检任务数", "tr_numberInspectionTasksCompleted": "巡检完成任务数", "tr_inspectionCompletionRate": "巡检完成率", "tr_allInspectors": "全部巡检人", "tr_inspectionProgress": "巡检进度", "tr_inspectionStores": "巡检门店", "tr_continueInspection": "继续巡检", "tr_storesPendingInspectionContinue": "本次抽查巡检中还有门店未巡检，是否继续巡检？", "tr_randomShake": "随机摇", "tr_shakeDevice": "摇设备", "tr_shakeStore": "摇门店", "tr_inspectionDimension": "巡检维度", "tr_coverage": "覆盖情况", "tr_inspectionTasks": "巡检任务", "tr_numberStoresInspected": "巡检门店数", "tr_storeDetail": "门店详情", "tr_chooseStoreMaxCount": "最多可选{name}个门店", "tr_PleaseConfirmPwd": "请确认密码", "tr_pendingInspectionImages": "待巡检图片", "tr_allPendingInspectionImagesProcessed": "所有待巡检图片已处理完成", "tr_operationSuccessfulProceedingToNext": "操作成功，即将进行下一个", "tr_onlySupportOnlineDeviceTip": "只能选择在线设备", "tr_allDevicesAbnormal": "设备全部异常", "tr_storeNoPermission": "门店没权限", "tr_storeDeleted": "门店被删除", "tr_noDevicesUnderStore": "无可用设备", "tr_allDevicesOfflineUnderStore": "门店下设备全离线", "tr_storeExceptionInspectionNotPossible": "存在门店异常无法巡检", "tr_noStoresAvailableForInspection": "无可巡检门店", "tr_noTrafficHeatZoneCamerasInStoreContactB2bEmail": "当前门店无客流热区摄像头，您可联系**************邮箱联系采购", "tr_delMoreUserPerson": "您确定要删除选中的用户吗？", "tr_delTheUserPerson": "您确定要删除此用户吗？", "tr_noCorrespondingRecords": "没有对应记录", "tr_enterStoreCount": "进店人数", "tr_storeCoverageRateExplain": "统计时间内巡检门店数/所有门店", "tr_hasChooseNode": "当前节点下无法添加节点，请选择一个节点", "tr_aiInspectionRecords": "AI巡检记录", "tr_trafficHeatZoneAlgorithmNotEnabledContactB2b": "暂未开通客流热区算法，无法进行配置，请联系**************开通", "tr_addHeatAreaTip": "（1）点击+在平面图上划定区域；\n（2）关联此区域的监控设备；\n（3）配置并关联设备上热力区；", "tr_refreshed": "已刷新", "tr_alarmDetail": "告警详情", "tr_searchDeviceNameSN": "输入设备名称/序列号", "tr_associateHumanShape": "关联人形", "tr_AddChildDeviceNodeTips": "当前节点下暂无设备与子节点", "tr_storeAndAlgorithm": "门店和算法", "tr_encodingSettings": "编码设置", "tr_encodingSoftSolution": "软解", "tr_encodingHardSolution": "硬解", "tr_encodingSwitching": "软硬自动切换", "tr_algorithmAnomaly": "算法异常", "tr_detectionCount": "检测次数", "tr_rectifyCount": "已提交整改次数", "tr_deviceOfflineOrAbnormalNoSnapshotGenerated": "设备离线或异常，未生成抓拍图", "tr_pendingSubmission": "待提交", "tr_aiInspectionRecordDetail": "AI巡检记录详情", "tr_viewEvent": "查看事件", "tr_autoSubmitEvent": "已自动提交事件", "tr_event": "事件", "tr_HDStream": "高清", "tr_SDStream": "流畅", "tr_transparentAudioTemplate": "透传去音频模板", "tr_transparentTransmission": "透传", "tr_rectificationTimeMustBeGreaterThanOneMinute": "整改时间必须大于1分钟", "tr_imageGenerationFailed": "生成图片失败", "tr_GBCascade": "国标级联", "tr_GBCascadeAdd": "新建国标级联", "tr_GBCascadeDetail": "国标级联详情", "tr_GBCascadeEdit": "国标级联详情编辑", "tr_transcodingTemplate": "转码模版", "tr_bitStream": "码流", "tr_previewAddress": "直播地址", "tr_playbackAddress": "回放地址", "tr_to": "至", "tr_globalPlaybackPeriod": "全局回放时段", "tr_aboutDevice": "关于设备", "tr_networkMode": "网络模式", "tr_localIP": "本地IP", "tr_deviceVersion": "设备版本", "tr_firmwareVersion": "固件版本", "tr_releaseDate": "发布日期", "tr_deviceTimezone": "设备时区", "tr_deviceTime": "设备时间", "tr_restartDevice": "重启设备", "tr_deviceConfiguration": "设备配置", "tr_imageFlip": "图像翻转", "tr_speakerVolume": "喇叭音量", "tr_pictureColorShift": "画面偏色", "tr_advancedSettings": "高级设置", "tr_verticalFlip": "上下翻转", "tr_horizontalFlip": "左右翻转", "tr_fullFlip": "上下左右翻转", "tr_irLensReverse": "IR镜片反序", "tr_audioVideoEncodingConfig": "音视频编码配置", "tr_imageConfig": "图像配置", "tr_wideDynamicConfig": "宽动态配置", "tr_enableHighQualityImageUnderHighContrast": "开启后，在高对比度光线下拍摄高质量的图像", "tr_deviceSN": "设备SN", "tr_mainStream": "主码流", "tr_resolution": "分辨率", "tr_frameRateFPS": "帧率(FPS)", "tr_bitRateControlFPS": "码流控制(FPS)", "tr_variableBitRate": "可变码流", "tr_limitedBitRate": "限定码流", "tr_imageQuality": "画质", "tr_bitRateValue": "码流值", "tr_IFrameInterval": "I帧间隔", "tr_audio": "音频", "tr_encoderStaticConfig": "编码器静态配置", "tr_smartEncoding": "Smart编码", "tr_subStream": "辅码流", "tr_osdSettings": "OSD设置", "tr_channelTitle": "通道标题", "tr_titleContent": "标题内容", "tr_titleTime": "标题时间", "tr_GBCascadeChannelTips": "国标级联授权{maxChannel}路，还剩{remainChannel}路", "tr_GBCascadeAuthorization": "国标级联授权", "tr_thisPlatformName": "本平台名称", "tr_channelCount": "{count}路", "tr_pleaseInputPlatformNameIDHit": "请输入上级平台名称/接入ID", "tr_parentPlatformName": "上级平台", "tr_platformName": "本级平台", "tr_pushChannel": "推送路数", "tr_updateTime": "更新时间", "tr_creationTime": "创建时间", "tr_SIPServerNumber": "SIP服务器号", "tr_signalingTransmission": "信令传输", "tr_registrationSwitch": "注册开关", "tr_registrationCycle": "注册周期(秒)", "tr_heartbeatPeriod": "心跳周期(秒)", "tr_parentPlatformNameHint": "1-20个字，中英文数字均可", "tr_accessIdHint": "20个数字", "tr_accessPsdHint": "英文、数字、特殊符号", "tr_registrationCycleHint": "范围在3600-86400秒", "tr_heartbeatPeriodHint": "范围在30-3600秒", "tr_delOneDeviceGBCascade": "您确定要删除此国标级联吗？", "tr_east": "东", "tr_west": "西", "tr_sipServerPortHint": "端口号为1-5位的数字", "tr_registrationSwitchExplanation": "开启状态下，表示与上级平台通信中，不可编辑；关闭注册开关，可编辑国标级联信息", "tr_pendingEvent": "待提交事件", "tr_pleaseSelectTime": "请选择时间", "tr_HDAddress": "高清地址", "tr_SDAddress": "流畅地址", "tr_recordCardAddress": "本地回放地址", "tr_recordCloudAddress": "云回放地址", "tr_pleaseSelectPlaybackTimeError": "目前回放时段是不可跨天，请重新选择时间段", "tr_belongStore": "所属门店", "tr_pleaseUnbindStoreId": "请先解除以下云店绑定后再删除", "tr_belongNode": "所属节点", "tr_verification": "去校验", "tr_psdVerificationTip": "验证当前默认密码是否正确，若不正确可输入密码校验", "tr_executeVerification": "校验", "tr_verificationSuccess": "校验成功", "tr_deviceOfflineEdit": "设备离线不可编辑", "tr_deviceAnomaly": "设备异常", "tr_pleaseUnbindHasChildren": "请先删除节点及下属节点的设备", "tr_inputParentPlatName": "请输入上级平台名称", "tr_inputPlatformName": "请输入本级平台名称", "tr_inputAccessId": "请输入接入ID", "tr_inputAccessIdNumHint": "接入ID为20个数字", "tr_inputAccessPsd": "请输入接入密码", "tr_inputAccessPsdHint": "接入密码为8～16位英文、数字、特殊符号", "tr_inputSIPServerNumber": "请输入SIP服务器号", "tr_inputSIPServerNumberHint": "SIP服务器号为20个数字", "tr_inputSIPServerIP": "请输入SIP服务器IP", "tr_inputSIPServerPort": "请输入SIP服务器端口", "tr_inputSIPServerPortHint": "SIP服务器端口为1-5位的数字", "tr_inputRegistrationCycle": "请输入注册周期(秒)", "tr_inputHeartbeatPeriod": "请输入心跳周期(秒)", "tr_inputRegistrationCycleHint": "注册周期的范围为3600～86400秒", "tr_inputHeartbeatPeriodHint": "心跳周期的范围为60～3600秒", "tr_inputIPV4": "请输入正确的IPV4", "tr_pleaseSelectPassOrFail": "请选择合格或者不合格", "tr_newPassword": "新密码", "tr_passwordModify": "密码修改", "tr_checkPasswordErrorHint": "设备密码错误，请密码校验", "tr_GBCascadePush": "推 送", "tr_GBCascadeViewSIPID": "查看SIP ID", "tr_GBCascadeAuthorizationRoutes": "授权路数不足，请扩容", "tr_channelName": "通道名称", "tr_channelSIPID": "本平台SIP ID", "tr_pushSIPID": "推送 SIP ID", "tr_pleaseInputChannelNamePushSIPHint": "请输入通道名称/推送SIP ID", "tr_alarmTimeInterval": "报警时间间隔", "tr_sensitivity": "灵敏度", "tr_alarmVoice": "报警语音", "tr_smartAlarm": "智能报警", "tr_humanDetection": "人形检测", "tr_motionDetection": "移动侦测", "tr_humanDetectionTip": "默认开启人形检测，人形检测与移动侦测二选一", "tr_deviceAlertSound": "设备警戒音", "tr_trackingTrajectory": "跟踪轨迹", "tr_humanDetectionMarking": "视频中出现人形时会对人形做画框或划线标记", "tr_displayAlarmRules": "显示告警规则", "tr_videoDisplayAlarmRules": "视频中显示告警规则", "tr_ruleConfiguration": "规则配置", "tr_alarmRuleConfiguration": "告警规则配置", "tr_messageNotificationInterval": "消息通知间隔", "tr_alarmPeriod": "报警时段", "tr_high": "高", "tr_medium": "中", "tr_low": "低", "tr_allDayAlarm": "全天报警", "tr_GBCascadeChannelSIPID": "{platformId}平台SIP ID查看", "tr_alertSound": "警戒音", "tr_loopAlarmSound": "循环报警音", "tr_secondsPerLoop": "秒循环一次", "tr_aiInspectionAnalysis": "AI巡检分析", "tr_noDeviceNode": "暂无设备节点", "tr_alarmRate": "告警率", "tr_detectionTrend": "检测趋势", "tr_keyIssues": "突出问题", "tr_issueLocations": "问题点位", "tr_issueDistributionPeriod": "问题分布时段", "tr_keyIssueDetails": "突出问题详情", "tr_statisticalPeriod": "统计周期", "tr_issueDetails": "问题详情", "tr_viewIssueDetails": "查看问题详情", "tr_choseChannelCount": "已选通道", "tr_channelResources": "通道资源", "tr_chooseChannels": "请选择通道", "tr_alertLine": "警戒线", "tr_alertZone": "警戒区域", "tr_executionDate": "执行日期", "tr_customTimeSetting": "自定义时间设置", "tr_globalSetting": "全局设置", "tr_timePeriod": "时间段", "tr_selectTimePeriod": "选择时间段", "tr_intervalRangeTip": "间隔为0-600秒，推荐30秒", "tr_inspectionItem": "巡检项", "tr_durationCannotBeLessThanDetectionFrequency": "持续时长不允许小于检测频率", "tr_alarmCountRatio": "告警次数占比", "tr_alarmRateRanking": "告警率排名", "tr_alarmCountRanking": "告警次数排名", "tr_notModifiable": "不可修改", "tr_pleaseEnterBitrate": "请输入码流值", "tr_videoBitrateKbps": "视频码流(kbps)，码流值取值范围0~8192", "tr_pleaseEnterIFrameInterval": "请输入I帧间隔", "tr_iFrameIntervalRange": "I帧间隔，取值范围1~12", "tr_upgradeFirmwareTip": "此设备有新的设备固件可升级，是否升级？", "tr_firmwareUpgrading": "升级中", "tr_firmwareUpgradeFailure": "升级失败", "tr_firmwareUpgradeSuccess": "升级成功", "tr_firmwareUpgradingTip": "升级过程中不能进行其他操作，请勿中途退出或按home键", "tr_firmwareUpgradeFailureTip": "升级失败，请重试", "tr_firmwareUpgradeSuccessTip": "升级成功，等待设备重启", "tr_deviceUpgrade": "设备升级", "tr_deviceExecuteUpgrade": "请点击更新设备", "tr_deviceUpgradeVersion": "已经最新版本", "tr_orgAddDeviceNodeTips": "无设备和子节点\n请点击右上角'+'按钮，添加设备和子节点", "tr_deduplication": "去重数", "tr_accountLocked": "账号已锁定", "tr_noObject": "无对象", "tr_fileDeletionFailed": "文件未删除成功", "tr_noSdCardOrHardDrive": "无SD卡或硬盘", "tr_noRecordingDeviceOrNotRecording": "无录像设备或设备未进行录像", "tr_userNoQueryPermission": "此用户无查询权限", "tr_userNotBoundToFacebook": "用户未绑定facebook", "tr_userNotBoundToGoogle": "用户未绑定google", "tr_iFrameNotFound": "未找到i帧", "tr_deviceNoStreamOver2s": "设备无流超2s", "tr_deviceNoStreamOver20s": "设备无流超20s", "tr_dataSourceFetchFailed": "获取数据源失败", "tr_urlConcurrencyLimited": "URL并发受限", "tr_waitForGwmValidationTimeout": "等待gwm的校验结果超时", "tr_httpParsingError": "Http解析错误", "tr_clientNotSupportedUseChrome": "客户端不支持，请使用chrome", "tr_iFrameNoSps": "I帧无SPS", "tr_protocolMessageParsingError": "协议报文解析错误", "tr_noFrameDataReceivedDuringConnection": "连接期间没有收到帧数据", "tr_noFrameDataReceived2sBeforeDisconnection": "断开前2秒内没有收到帧数据", "tr_noVideoFrameDataReceived2sBeforeDisconnection": "断开前2秒内没有收到视频帧数据", "tr_actualVideoFrameRateTooLow": "实际视频帧帧率太小", "tr_accountOverdueAccessFailed": "账户欠费，访问流量服务失败", "tr_serviceValidationException": "服务校验时服务异常", "tr_serverValidationTimeout": "服务器校验时服务出现超时", "tr_localNetworkExceptionDuringServiceValidation": "服务校验时本机网络出现异常", "tr_passengerCloudFlowTip": "进店客流：从外进入店内的客流\n出店客流：由内走出店外的客流\n过店客流：过而不入店的客流\n总客流 = 进店客流 + 过店客流\n进店率 = 进店客流 / 总客流 * 100% = 进店客流 / (进店客流 + 过店客流) * 100%", "tr_default": "默认", "tr_smartAlert": "智能警戒", "tr_smartAlertTip": "配置相关参数，设备可触发人形检测和移动侦测", "tr_thinkingPleaseWait": "正在思考中，请稍后再试", "tr_thinking": "思考中", "tr_thinkingFailed": "思考失败", "tr_manualInspectionAnalysisInspectionCount": "以下是人工巡检分析巡检次数数据", "tr_totalCount": "总次数", "tr_passedCount": "合格次数", "tr_failedCount": "不合格次数", "tr_manualInspectionAnalysisPassRate": "以下是人工巡检分析巡检合格率数据", "tr_coverageRate": "覆盖率", "tr_manualInspectionAnalysisEmployeeRankingByInspectionCount": "以下是人工巡检分析巡检次数员工排名数据", "tr_employeeName": "员工姓名", "tr_manualInspectionAnalysisEmployeeRankingByFindings": "以下是人工巡检分析发现次数员工排名数据", "tr_manualInspectionAnalysisEmployeeRankingByOverdue": "以下是人工巡检分析逾期次数员工排名数据", "tr_manualInspectionAnalysisStoreRankingByFindings": "以下是人工巡检分析发现问题门店排名数据", "tr_findingsCount": "发现问题次数", "tr_manualInspectionAnalysisFailedInspectionRatio": "以下是人工巡检分析巡检不合格占比数据", "tr_inspectionItemName": "巡检项名称", "tr_manualInspectionAnalysisFailedPointRanking": "以下是人工巡检分析不合格点位排名数据", "tr_manualInspectionAnalysisTaskCompletionRate": "以下是人工巡检分析巡店任务完成率数据", "tr_inspectionMethods": "巡检方式总共有“巡检任务,抽查巡检,图片巡检,视频巡检,摇一摇巡检,现场巡检”这几种", "tr_manualInspectionAnalysisCoverageByTaskCompletionRate": "以下是人工巡检分析任务完成率巡检覆盖情况数据", "tr_inspectionType": "巡检类型", "tr_manualInspectionAnalysisTaskCompletionRateData": "以下是人工巡检分析巡检任务完成率数据", "tr_aiInspectionAnalysisAlarmCount": "以下是AI巡检分析告警次数数据", "tr_aiInspectionAnalysisInspectionCount": "以下是AI巡检分析巡检次数数据", "tr_aiInspectionAnalysisAlarmRate": "以下是AI巡检分析告警率数据", "tr_aiInspectionAnalysisAlarmCountByInspectionItem": "以下是AI巡检分析巡检项告警次数占比数据", "tr_aiInspectionAnalysisAlarmRateRankingByInspectionItem": "以下是AI巡检分析巡检项告警率排名数据", "tr_aiInspectionAnalysisAlarmCountRanking": "以下是AI巡检分析-告警次数排名数据", "tr_aiInspectionAnalysisAlarmRateRanking": "告警率排名数据", "tr_aiInspectionAnalysisInspectionItemDistributionByTime": "以下是AI巡检分析巡检项分布时段数据", "tr_pleaseClick": "请点击", "tr_selectOrAddConversation": "选择一个会话或者添加会话", "tr_history": "历史记录", "tr_enterTitleToSearch": "请输入标题名称搜索", "tr_sendMessageToDeepseek": "给 deepseek 发送消息", "tr_noResendBeforeReply": "回复完成之前不允许再次发送消息", "tr_copySuccess": "复制成功", "tr_manualInspectionCoverageData": "以下是人工巡检分析巡检覆盖率数据", "tr_belowIsAiInspectionAnalysis": "以下是AI巡检分析", "tr_alarmCountRankingData": "告警次数排名数据", "tr_deepseekSuffiex": "针对以上巡检分析数据，有什么优化建议？请使用报表格式回答，请使用中文回答。", "tr_selectUsefulWiFi": "请选择可用的WiFi", "tr_chooseAvailableWiFi": "WiFi连接不通畅，请重新选择可用的WiFi", "tr_videoBlocking": "视频遮挡", "tr_videoBlockingTip": "摄像头画面被物体遮挡时，触发报警", "tr_firmwareManage": "固件管理", "tr_PleaseInputContent": "请输入内容", "tr_chooseFirmwareFile": "请选择固件升级文件", "tr_chooseExpandListMode": "设备已切换列表展示", "tr_chooseExpandGridMode": "设备已切换宫格展示", "tr_deepSeekStatistics": "Deepseek调用统计", "tr_deepSeekStatisticsTip": "请到巡检分析或事件分析页面查看具体功能", "tr_occlusionAlarm": "遮挡告警", "tr_videoOcclusionAlarm": "视频遮挡告警", "tr_deviceMaintenance": "设备维护", "tr_StorageCardSetting": "存储卡设置", "tr_selectLocalFileUpgrade": "选本地文件升级", "tr_NoSDCardInserted": "未插入SD卡，无法录像", "tr_StorageCardInfo": "存储卡信息", "tr_Capacity": "容量", "tr_Remaining": "剩余", "tr_Images": "图片", "tr_Videos": "视频", "tr_FormatStorageCard": "存储卡格式化", "tr_Format": "格式化", "tr_Formatting": "格式化中...", "tr_RecordingManagement": "录像管理", "tr_RecordingSwitch": "录像开关", "tr_AlarmRecording": "报警录像", "tr_AlarmRecordingTip": "仅在检测到报警时录像，录像时间长", "tr_AllDayRecording": "全天录像", "tr_AllDayRecordingTip": "全天24小时不间断录像", "tr_RecordingSegment": "录像段", "tr_RecordingSegmentTip": "单个录像文件最长的时间", "tr_AlertPreRecordTime": "告警预录时段", "tr_SelectTime": "选择时间", "tr_autoRestart": "自动重启", "tr_never": "从不", "tr_timePoint": "时间点", "tr_hour": "时", "tr_minute": "分", "tr_second": "秒", "tr_factoryReset": "恢复出厂设置", "tr_resetDevice": "重置设备", "tr_resetDeviceSuccess": "重置设备成功", "tr_resetDeviceFailure": "重置设备失败", "tr_restartDeviceSuccess": "重启设备成功", "tr_restartDeviceFailure": "重启设备失败", "tr_latestUpgradeVersion": "已经最新版本", "tr_AlertPreRecordTimeTip": "范围为0-30秒", "tr_Monday": "周一", "tr_Tuesday": "周二", "tr_Wednesday": "周三", "tr_Thursday": "周四", "tr_Friday": "周五", "tr_Saturday": "周六", "tr_Sunday": "周日", "tr_deepseekSuffiex2": "针对以上事件分析数据，有什么优化建议？请使用报表格式回答，请使用中文回答。", "tr_staffCompleteRectificationSort": "员工完成整改次数排名", "tr_staffOverdueRectificationSort": "员工整改逾期次数排名", "tr_staffAcceptanceRectificationSort": "员工完成验收次数排名", "tr_eventCountRanking": "事件数排名", "tr_overdueCountRanking": "逾期数排名", "tr_acceptanceCountRanking": "验收数排名", "tr_formatStorageCardTip": "格式化存储卡后，设备将离线并重新加载存储卡，整个过程需要持续几分钟，请确认是否继续？", "tr_heptagon": "七边形", "tr_octagon": "八边形", "tr_titleReset": "确认恢复出厂设置", "tr_titleRestart": "确认重启设备", "tr_subTitleReset": "设备恢复出厂设置后，导致设备无法连接WIFI，相关功能无法正常运行", "tr_subTitleRestart": "设备重启后需要稍作等候，功能即可恢复正常", "tr_thinkingFailedTip": "服务繁忙，请稍后再试", "tr_stopRecording": "停止录像", "tr_loopRecording": "循环录像", "tr_recordingFull": "录像满时", "tr_uploadUpgradeFail": "固件上传升级失败", "tr_unnormal": "异常", "tr_layers": "图层", "tr_drawCircle": "画圈", "tr_locate": "定位", "tr_planType": "计划类型", "tr_videoInspection": "视频巡检", "tr_storeSelfInspection": "门店自检", "tr_supervisionInspection": "督导巡店", "tr_multiIPC": "多目IPC", "tr_channelNumbers": "通道数量", "tr_IPCType": "IPC类型", "tr_channelOne": "通道1", "tr_channelTwo": "通道2", "tr_channelThree": "通道3", "tr_channelFour": "通道4", "tr_storeManagerSign": "店长签字", "tr_storeInspectorSign": "巡店人签字", "tr_sign": "点击签名", "tr_resign": "重签", "tr_clear": "清除", "tr_pleaseSign": "请签名", "tr_inspectionRetPreview": "巡店结果预览", "tr_myInitiated": "我发起的", "tr_taskIssued": "任务下发", "tr_multi": "多目", "tr_channelStatus": "通道状态", "tr_channelSN": "通道序列号", "tr_channelLabel": "通道标签", "tr_channelNumInfo": "通道{serialNum}信息", "tr_inspectionWatermark": "巡检水印", "tr_inspectionRecordWatermark": "巡检记录水印", "tr_inspectionRecordWatermarkTip": "开启后，巡检记录页面会携带水印", "tr_formatSuccess": "格式化成功", "tr_signTip": "签名处", "tr_maxDurationCannotBeGreaterThan": "最大时长不能超过{name}", "tr_sixSNNationsCodeTips": "长度6位，由数字组成，序列号之间不能相同", "tr_channelSNExistSameCodeTips": "与通道{channel}的序列号相同，请修改序列号编码", "tr_PTZResetSuccessful": "云台重置成功", "tr_PTZResetFailed": "云台重置失败", "tr_privacyPolicyUpdate": "隐私协议更新", "tr_privacyPolicyUpdateTip": "感谢您信任并使用蜂云SaaS的产品和服务。为了更好地维护用户的利益，我们对《隐私政策》进行了更新，特向您推送本提示。请仔细阅读并充分理解相关条款。您点击“同意”，即表示您已阅读并同意更新后的《隐私政策》，蜂云SaaS将尽全力保障您的合法权益。", "tr_privacyPolicyFrontTip": "查看完整版", "tr_endTimeCannotBeGreaterThanName": "结束时间不能超过{name}", "tr_UserServiceAgreement": "用户服务协议", "tr_holidaySelection": "节假日选择", "tr_holiday": "节假日", "tr_selectYearAndMonth": "选择年月", "tr_selectMonth": "选择月", "tr_selectYear": "选择年", "tr_inStoreCustomerGroup": "进店顾客组", "tr_threePeople": "三人", "tr_channelManagement": "通道管理", "tr_channelDetails": "通道详情", "tr_deduplicationAfter": "去重后", "tr_inStoreCustomerGroupTip": "进店客流： 去重后，顾客从先经过检测区域，再经过进店客流线，作为进店客流\n单人进店（顾客组）： 去重后，单人进店并且放置时间间隔两后与另其他人进店，作为单人进店\n双人进店（顾客组）： 去重后，2人进店间隔时间相手预设的时间作为同一批次\n三人进店（顾客组）： 去重后，3人进店间隔时间相手预设的时间作为同一批次 20%\n多人进店（顾客组）： 去重后，3人以上进店间隔时间相手预设的时间作为同一批次\n进店客流： 去重后，经过任意非进店客流线的，一条对应测线，作为出店客流\n总客流： 进店客流+出店客流\n进店率： 进店客流/总客流100%", "tr_inStoreCustomerGroupTable": "进店顾客组统计表格", "tr_singlePersonGroup": "单人进店（顾客组）", "tr_doublePersonGroup": "双人进店（顾客组）", "tr_threePersonGroup": "三人进店（顾客组）", "tr_manyPersonGroup": "多人进店（顾客组）", "tr_statisticalDimension": "统计维度", "tr_storeTrafficNoDeduplication": "进店客流(未去重)", "tr_passByTrafficNoDeduplication": "过店客流(未去重)", "tr_outStoreTrafficNoDeduplication": "出店客流(未去重)", "tr_totalTrafficNoDeduplication": "总客流(未去重)", "tr_psdCheckFailureTip": "如下密码校验失败，可手动输入正确的密码", "tr_psdCheckSuccessTip": "校验成功！如下为通道的账号密码", "tr_psdCheckNoticeTip": "注意：设备离线不允许修改密码！", "tr_verificationFail": "校验失败", "tr_inputAccount": "请输入账号", "tr_addChannel": "添加通道", "tr_channelAdd": "通道添加", "tr_JFProtocol": "杰峰协议", "tr_onvifProtocol": "onvif协议", "tr_addChannelTips": "设备通道接入授权{maxChannel}路，剩余{remainChannel}路，请联系**************拓展授权。", "tr_batchAddChannelTips": "批量添加选择的设备密码必须一致，否则会添加失败，密码不一致的设备请单个添加。", "tr_searchChannelTips": "搜索到如下设备", "tr_deviceUsername": "设备用户名", "tr_devicePassword": "设备密码", "tr_inputAccountPassword": "输入账号密码", "tr_accountPassword": "账号密码", "tr_recordingSetting": "录像设置", "tr_searchJFDeviceTip": "NVR和摄像头要在同一局域网内", "tr_searchNoDeviceTip": "未检测到设备或设备已添加", "tr_searchingDeviceTip": "设备扫描中，请等待...", "tr_globalConfiguration": "全局配置", "tr_createActivityTime": "创建活动时间", "tr_activityName": "活动名称", "tr_pleaseEnterActivityName": "请输入活动名称", "tr_pleaseSelectActivityDate": "请选择活动日期", "tr_noChannel": "暂无通道", "tr_customerGroupEntryInterval": "进店顾客组间隔", "tr_aiBox": "AI盒子", "tr_aiBoxTip": "添加AI Box设备", "tr_inStoreCustomerCount": "进店顾客数", "tr_aiBoxAdd": "AI盒子添加", "tr_aiBoxAddSNTip": "请输入序列号", "tr_aiBoxAddNameTip": "请输入设备名称（默认名称设备序列号）", "tr_notAddChannel": "暂不添加通道", "tr_searchAiBoxDeviceTip": "设备和盒子要在同一局域网内", "tr_serialNumberHasNotExistConnect": "序列号不存在/设备未联网", "tr_subAccountActivation": "子账户激活", "tr_companyList": "企业列表", "tr_lastLogin": "上次选择", "tr_assignedToYou": "为您分配了", "tr_juvenile": "少年", "tr_storeTrafficSettings": "门店客流设置", "tr_staffNumber": "店员人数", "tr_setStaffNumber": "设置店员人数", "tr_staffDeduplication": "店员去重", "tr_manuallySetStaffNumberRange": "手动设置店员人数范围区间", "tr_pleaseSetStaffNumberRange": "请设置店员人数范围区间", "tr_manuallySetStaffNumber": "手动设置店员人数", "tr_setStaffDeduplicationRules": "设置店员去重规则，条件可任意组合", "tr_visitCount": "访问次数", "tr_multipleVisitsPerDayConsideredStaff": "一天进店多次认为是店员", "tr_arrivalDepartureTime": "到店/离店时间点", "tr_beforeAfterBusinessHoursConsideredStaff": "营业时间前后{name}分钟进店/离店，认为是店员", "tr_storeEntryExitConsideredStaff": "在营业时间前后{name}分钟，进店离店认为是店员", "tr_nameMinutesOrMore": "{name}分钟以上", "tr_nameHoursOrMore": "{name}小时以上", "tr_dailyTotalStayDuration": "一天停留总时长", "tr_stayDuration": "店内停留时长", "tr_setSingleAndTotalStayDuration": "设置单次停留时长和总停留时长", "tr_meetAboveConditionsConsideredStaff": "满足以上{name}认为是店员", "tr_setBusinessHours": "设置营业时间", "tr_businessHours": "营业时间", "tr_deviceRunTimeGreaterThanStoreHours": "设备运行时间要大于门店营业时间", "tr_setStaffArrivalDepartureTime": "设置店员到店/离店时间", "tr_beforeAfterBusinessHours": "营业时间前后", "tr_note": "备注：", "tr_anyCondition": "任一条件", "tr_allConditions": "所有条件", "tr_singleStayDuration": "单次停留时长", "tr_staffDeduplicationRules": "店员去重规则", "tr_conditionsCanBeCombined": "条件可任意组合", "tr_singleStayDurationAboveMinutes": "单次停留时长{name}分钟以上", "tr_totalStayDurationAboveHours": "总停留时长{name}小时以上", "tr_isStaff": "是店员", "tr_meetAbove": "满足以上", "tr_consideredAsStaff": "认为是店员", "tr_configuration": "配置", "tr_statistics1": "统计", "tr_todayTraffic": "今日客流", "tr_pleaseEnterVisitCount": "请输入访问次数", "tr_edgeComputing": "边缘计算", "tr_pleaseEnterStaffNumber": "请输入店员人数", "tr_pleaseEnterCorrectRange": "请输入正确的范围", "tr_staffFilter": "店员过滤", "tr_staffFilterRules": "店员过滤规则", "tr_passengerFlowStatisticsTip": "客流统计：统计维度总客度\n\n1. 总客流\n2. 进店客流\n3. 过店客流\n4. 出店客流\n5. 进店顾客数（去重后）\n6. 出店顾客数（去重后）\n7. 过店顾客数（去重后）\n8. 总顾客数（去重后）\n\n进店峰值：统计时段内，去重后的进店峰值\n\n进店合值：统计时段内，去重后的进店合值", "tr_storeConversionRateTip": "进店转化率\n1. 进店转化率=统计时段内未去重进店客流/统计时段内未去重总客流\n2. 总进店率比：未去重进店客流/未去重总客流（进店客流+过店客流）\n3. 进店顾客组：统计时间内，单人/双人/三人/多人批次个数/总批次个数", "tr_customerSegmentationAnalysisTip": "客群分析：基于进店去重后的数据统计分析\n1. 进店人员年龄段：各年龄段数据/全部进店去重后的数据\n2. 进店人员性别：统计时间段男/女去重后的个数/统计时间段去重后的进店数", "tr_inStoreBatchRatio": "进店批次占比", "tr_male": "男", "tr_female": "女", "tr_aiBoxChannelConfigLimitTip": "AI盒子所有通道已配置8路算法，无法配置更多算法。", "tr_deduplicationTip": "门店下有多台客流设备，会自动进行合并去重，若开启店员去重，将基于门店去重！", "tr_softwareVersion": "软件版本", "tr_softwareVersionReleaseDate": "软件版本发布日期", "tr_supportAlgorithms": "支持算法"}