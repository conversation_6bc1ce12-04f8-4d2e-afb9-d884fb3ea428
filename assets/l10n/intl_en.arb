{"@@locale": "en", "local": "en", "tr_localRecording": "Local recording", "tr_storageCardManagement": "Storage card management", "tr_storageManagement": "Storage management", "tr_passengerFlowOsdSwitch": "Passenger flow osd switch", "tr_batchSetting": "Batch setting", "tr_batchSettingChannel": "Batch setting channel", "tr_ErrorCode": "Error code", "tr_UnknownError": "Unknown error", "tr_ErrorCode_Minus_1": "Data parsing failed", "tr_ErrorCode_Minus_1000": "Network error", "tr_ErrorCode_Minus_1001": "Sending buffer is full", "tr_ErrorCode_Minus_1002": "Network sending failed", "tr_ErrorCode_Minus_1003": "Network receiving failed", "tr_ErrorCode_Minus_1004": "Network timeout", "tr_ErrorCode_Minus_1005": "No object", "tr_ErrorCode_Minus_1006": "Fail to creat", "tr_ErrorCode_Minus_1007": "Connecting failed", "tr_ErrorCode_Minus_1008": "Timeout", "tr_ErrorCode_Minus_1009": "No connection", "tr_ErrorCode_Minus_1010": "Socket abnormal", "tr_ErrorCode_Minus_1011": "Close socket abnormal", "tr_ErrorCode_Minus_1012": "Creating cache failed", "tr_ErrorCode_Minus_1013": "Network busy", "tr_ErrorCode_Minus_1014": "Listening abnormal", "tr_ErrorCode_Minus_1015": "Receiving abnormal", "tr_ErrorCode_Minus_1016": "No buffer", "tr_ErrorCode_Minus_1017": "Network error or DNS setting wrong", "tr_ErrorCode_Minus_1018": "Developer account hasn't been authorised", "tr_ErrorCode_Minus_10000": "Your request is illegal", "tr_ErrorCode_Minus_10001": "System is not initialised", "tr_ErrorCode_Minus_10002": "Incorrect parameters", "tr_ErrorCode_Minus_10003": "Invalid handle", "tr_ErrorCode_Minus_10004": "SDK cleaning error", "tr_ErrorCode_Minus_10005": "Your network connection timed out,please try again", "tr_ErrorCode_Minus_10006": "Insufficient storage space", "tr_ErrorCode_Minus_10007": "Network connection failed", "tr_ErrorCode_Minus_10008": "Failed to open the file", "tr_ErrorCode_Minus_10009": "Unknown error", "tr_ErrorCode_Minus_11000": "The dta is incorrect,the version may not match", "tr_ErrorCode_Minus_11001": "The version does't support", "tr_ErrorCode_Minus_11200": "Failed to open the channel", "tr_ErrorCode_Minus_11201": "Failed to close the channel", "tr_ErrorCode_Minus_11202": "Failed to creat media sub-connection", "tr_ErrorCode_Minus_11203": "Media sub-connection communication failed", "tr_ErrorCode_Minus_11204": "Video link reaches the maximum", "tr_ErrorCode_Minus_11300": "No permission", "tr_ErrorCode_Minus_11301": "Wrong password", "tr_ErrorCode_Minus_11302": "User does not exist", "tr_ErrorCode_Minus_11303": "The user is locked,please reboot device", "tr_ErrorCode_Minus_11304": "The user is not allowed to access", "tr_ErrorCode_Minus_11305": "The user has logged in", "tr_ErrorCode_Minus_11306": "The user hasn't logged in", "tr_ErrorCode_Minus_11307": "The device may be not online", "tr_ErrorCode_Minus_11308": "User input is illegal", "tr_ErrorCode_Minus_11309": "Duplicate index", "tr_ErrorCode_Minus_11310": "Object does not exist", "tr_ErrorCode_Minus_11311": "Object does not exist", "tr_ErrorCode_Minus_11312": "Object is being used", "tr_ErrorCode_Minus_11313": "Subset hyperrange", "tr_ErrorCode_Minus_11314": "Incorrect password", "tr_ErrorCode_Minus_11315": "Password doesn't match", "tr_ErrorCode_Minus_11316": "Keep this account", "tr_ErrorCode_Minus_11317": "This encrypted login is not supported", "tr_ErrorCode_Minus_11318": "Incorrect account password", "tr_ErrorCode_Minus_11400": "You need to restart the application after saving the configuration", "tr_ErrorCode_Minus_11401": "Please reboot the device", "tr_ErrorCode_Minus_11402": "Error in writing documents", "tr_ErrorCode_Minus_11403": "Configuration features are not supported", "tr_ErrorCode_Minus_11404": "Configuration verification failed", "tr_ErrorCode_Minus_11405": "Configuration doesn't exist", "tr_ErrorCode_Minus_11406": "Error in configuration parsing, and the configuration may not be supported", "tr_ErrorCode_Minus_11500": "Pause failed", "tr_ErrorCode_Minus_11501": "No file found", "tr_ErrorCode_Minus_11502": "Configuration is not enabled", "tr_ErrorCode_Minus_11503": "Video streaming is not opened", "tr_ErrorCode_Minus_11600": "Failed to creat connection", "tr_ErrorCode_Minus_11601": "Connection failed", "tr_ErrorCode_Minus_11602": "Domain name parsing failed", "tr_ErrorCode_Minus_11603": "Failed to send data", "tr_ErrorCode_Minus_11605": "Busy service", "tr_ErrorCode_Minus_11609": "Connection is limited, or failed to access the server", "tr_ErrorCode_Minus_11612": "The number of server connection is full", "tr_ErrorCode_Minus_11700": "Pirated device", "tr_ErrorCode_Minus_20221": "If the number of verification exceeds the limitation, you need to reboot the device and try again", "tr_ErrorCode_Minus_66000": "Invalid login method", "tr_ErrorCode_Minus_70000": "Error value of translitring DVR", "tr_ErrorCode_Minus_70101": "Unknown error", "tr_ErrorCode_Minus_70102": "Version doesn't support", "tr_ErrorCode_Minus_70103": "Illegal request", "tr_ErrorCode_Minus_70104": "The user has logged in", "tr_ErrorCode_Minus_70105": "The user hasn't logged in", "tr_ErrorCode_Minus_70106": " Incorrect username or password", "tr_ErrorCode_Minus_70107": "No device function permission", "tr_ErrorCode_Minus_70108": "Timeout", "tr_ErrorCode_Minus_70109": "Searching failed,the corresponding file was not found", "tr_ErrorCode_Minus_70110": "Searching successfully,return all files", "tr_ErrorCode_Minus_70111": "Searching successfully,return some files", "tr_ErrorCode_Minus_70112": "User already exists", "tr_ErrorCode_Minus_70113": "The user does not exist", "tr_ErrorCode_Minus_70114": "This user group already exists", "tr_ErrorCode_Minus_70115": "This group doesn't exist", "tr_ErrorCode_Minus_70116": "Pirated software", "tr_ErrorCode_Minus_70117": "Incorrect message format", "tr_ErrorCode_Minus_70118": "The cloud platform protocol is not set", "tr_ErrorCode_Minus_70119": "No record file", "tr_ErrorCode_Minus_70120": "Configuration is not enabled", "tr_ErrorCode_Minus_70121": "Video resource not connected to the frond end ", "tr_ErrorCode_Minus_70122": "NAT links has been exhausted,and the new NAT connections are not allowed", "tr_ErrorCode_Minus_70123": "Tcp video links are up to the limit, new TCP video link is not allowed", "tr_ErrorCode_Minus_70124": "Incorrect encryption algorithm for username and password", "tr_ErrorCode_Minus_70125": "Other users have been created, and you can no longer log in with admin", "tr_ErrorCode_Minus_70126": "Log in too frequently,try again later", "tr_ErrorCode_Minus_70128": "The device is limited. If you have purchased a traffic package,please reboot the device.", "tr_ErrorCode_Minus_70129": "Remote login is prohibited", "tr_ErrorCode_Minus_70130": "NAS address already exists", "tr_ErrorCode_Minus_70131": "The path is being used, can not be operated", "tr_ErrorCode_Minus_70132": "NAS has reached the maximum supported value, and it is not allowed to continue to be added", "tr_ErrorCode_Minus_70140": "The key pressed in the remote control binding of cunsumer products is wrong", "tr_ErrorCode_Minus_70150": "Succeed,device needs to be restarted", "tr_ErrorCode_Minus_70153": "No SD card", "tr_ErrorCode_Minus_70160": "Video backup failed", "tr_ErrorCode_Minus_70161": "Not record device or device doesnt record", "tr_ErrorCode_Minus_70162": "The device is being added", "tr_ErrorCode_Minus_70163": "Error value of APS customer special password return", "tr_ErrorCode_Minus_70164": "Insufficient device space", "tr_ErrorCode_Minus_70165": "The device is busy, and currently unused.", "tr_ErrorCode_Minus_70184": "The device current power is lower than minimum required for ”real-time recording mode“,please try again after the device is charged", "tr_ErrorCode_Minus_70202": "Not logged in", "tr_ErrorCode_Minus_70203": "Incorrect login password", "tr_ErrorCode_Minus_70205": "Illegal user", "tr_ErrorCode_Minus_70206": "Account is locked, login error", "tr_ErrorCode_Minus_70207": "The account has been blackedlisted", "tr_ErrorCode_Minus_70208": "User is used", "tr_ErrorCode_Minus_70209": "Invalid input", "tr_ErrorCode_Minus_70210": "If the user to be added already exists,the index is duplicated", "tr_ErrorCode_Minus_70211": "The object doesn't exist when querying", "tr_ErrorCode_Minus_70212": "Object does not exist", "tr_ErrorCode_Minus_70213": "The object is in use, if the group has been used,it can not be deleted", "tr_ErrorCode_Minus_70214": "The subset is out of scope", "tr_ErrorCode_Minus_70215": "Incorrect password", "tr_ErrorCode_Minus_70216": "Password doesn't match", "tr_ErrorCode_Minus_70217": "Reserve account", "tr_ErrorCode_Minus_70218": "Unable to log in during system maintenance", "tr_ErrorCode_Minus_70219": "The number of verifications exsceeds the limit,you need to reboot the device and try again", "tr_ErrorCode_Minus_70220": "Incorrect answer", "tr_ErrorCode_Minus_70222": "Verification code error", "tr_ErrorCode_Minus_70502": "502 command is illegal", "tr_ErrorCode_Minus_70503": "Talk is enabled", "tr_ErrorCode_Minus_70504": "Talk is not enabled", "tr_ErrorCode_Minus_70602": "The application needs to be restarted", "tr_ErrorCode_Minus_70603": "The device needs to be restarted", "tr_ErrorCode_Minus_70604": "Fail to write the file", "tr_ErrorCode_Minus_70605": "Function is not supported", "tr_ErrorCode_Minus_70606": "Verification failed", "tr_ErrorCode_Minus_70607": "Configuration parsing error", "tr_ErrorCode_Minus_70609": "Configuration doesn't exist", "tr_ErrorCode_Minus_79001": "Unknown error", "tr_ErrorCode_Minus_79002": "Query server failed", "tr_ErrorCode_Minus_79004": "Offline", "tr_ErrorCode_Minus_79005": "Unable to connect to the server", "tr_ErrorCode_Minus_79007": "The number of connections is full", "tr_ErrorCode_Minus_79008": "Unconnected", "tr_ErrorCode_Minus_79020": "Connection timeout", "tr_ErrorCode_Minus_79021": "Connecting server request is refused", "tr_ErrorCode_Minus_79022": "Query status timeout", "tr_ErrorCode_Minus_79023": "Query WAN information tieout", "tr_ErrorCode_Minus_79024": "Network handshake timeout", "tr_ErrorCode_Minus_79025": "Query server failed", "tr_ErrorCode_Minus_79026": "Heartbeat timeout", "tr_ErrorCode_Minus_79027": "Connection is disconnected", "tr_ErrorCode_Minus_90000": "User cancellation", "tr_ErrorCode_Minus_90001": "File is illegal", "tr_ErrorCode_Minus_101": "Incorrect password", "tr_ErrorCode_Minus_102": "Account does not exist", "tr_ErrorCode_Minus_103": "Login timeout (network connection failed)", "tr_ErrorCode_Minus_104": "Account not logged in", "tr_ErrorCode_Minus_105": "Account already logged in", "tr_ErrorCode_Minus_106": "Account is blacklisted", "tr_ErrorCode_Minus_107": "Insufficient device resources", "tr_ErrorCode_Minus_109": "Network host not found", "tr_ErrorCode_Minus_120": "Device does not exist (has been deleted)", "tr_ErrorCode_Minus_137": "Device token is invalid", "tr_ErrorCode_Minus_79999": "YUV data error", "tr_ErrorCode_Minus_79998": "Failed to open audio (device does not support audio playback)", "tr_ErrorCode_Minus_500000": "Parameter encoding format error (e.g., UTF8 required but GBK provided)", "tr_ErrorCode_Minus_500001": "Parameter is not in JSON format", "tr_ErrorCode_Minus_515000": "Device offline", "tr_ErrorCode_Minus_515001": "Device has not reported", "tr_ErrorCode_Minus_515002": "Channel mismatch", "tr_ErrorCode_Minus_515003": "Channel offline", "tr_ErrorCode_Minus_515004": "Account error", "tr_ErrorCode_Minus_515100": "Parameter error", "tr_ErrorCode_Minus_515101": "Handle error", "tr_ErrorCode_Minus_515102": "API request failed", "tr_ErrorCode_Minus_515103": "Play type error", "tr_ErrorCode_Minus_515104": "Failed to request device information from DSM service", "tr_ErrorCode_Minus_515105": "ONVIF SSID not registered yet", "tr_ErrorCode_Minus_515201": "GB preview/playback returned Not Found, corresponding to SIP error code 404", "tr_ErrorCode_Minus_515202": "GB preview/playback failed", "tr_ErrorCode_Minus_515203": "GB preview/playback request timeout, device did not respond", "tr_ErrorCode_Minus_516100": "RTSP protocol error", "tr_ErrorCode_Minus_516101": "URL format error", "tr_ErrorCode_Minus_516102": "No recording", "tr_ErrorCode_Minus_516103": "URL expired", "tr_ErrorCode_Minus_516104": "URL authentication failed", "tr_ErrorCode_Minus_516105": "No traffic", "tr_ErrorCode_Minus_516106": "Failed to verify URL with GWM, communication failed", "tr_ErrorCode_Minus_516107": "Playback failed, XMTS communication failed", "tr_ErrorCode_Minus_516108": "Query recording failed", "tr_ErrorCode_Minus_516109": "Invalid SeekTime", "tr_ErrorCode_Minus_516110": "URL information not found", "tr_ErrorCode_Minus_516111": "Token parsing failed", "tr_ErrorCode_Minus_516112": "Payload failed", "tr_ErrorCode_Minus_516113": "Failed to update to Redis", "tr_ErrorCode_Minus_516114": "URL not allowed to play", "tr_ErrorCode_Minus_516115": "URL exceeds allowed concurrency", "tr_ErrorCode_Minus_90003": "Function expired", "tr_ErrorCode_Minus_90004": "Reach the maximum connection number", "tr_ErrorCode_Minus_99975": "Offline status", "tr_ErrorCode_Minus_99976": "The user is blacklisted", "tr_ErrorCode_Minus_99977": "The user is locked", "tr_ErrorCode_Minus_99978": "The user has logged in elsewhere", "tr_ErrorCode_Minus_99979": "Incorrect username or password", "tr_ErrorCode_Minus_99980": "Protocol parsing error", "tr_ErrorCode_Minus_99981": "The buffer size is not enough or buffer is full", "tr_ErrorCode_Minus_99982": "The sending buffer is full", "tr_ErrorCode_Minus_99983": "Listening server startup failed", "tr_ErrorCode_Minus_99984": "Connecting WAN is forbidden", "tr_ErrorCode_Minus_99985": "Server internal error", "tr_ErrorCode_Minus_99986": "Object busy", "tr_ErrorCode_Minus_99987": "Network error（sending failed）", "tr_ErrorCode_Minus_99988": "Network reception error", "tr_ErrorCode_Minus_99989": "Fail to creat cache", "tr_ErrorCode_Minus_99990": "Not found", "tr_ErrorCode_Minus_99991": "Your network connection timed out,please try again", "tr_ErrorCode_Minus_99992": "The device already exists", "tr_ErrorCode_Minus_99993": "Network error", "tr_ErrorCode_Minus_99994": "Not supported", "tr_ErrorCode_Minus_99995": "Failed to read the file", "tr_ErrorCode_Minus_99996": "Failed to write the file", "tr_ErrorCode_Minus_99997": "Failed to open the file", "tr_ErrorCode_Minus_99998": "Failed to creat the file", "tr_ErrorCode_Minus_99999": "Parameter error", "tr_ErrorCode_Minus_100000": "Error", "tr_ErrorCode_Minus_200000": "Invalid parameter", "tr_ErrorCode_Minus_200001": "User doesn;t exist", "tr_ErrorCode_Minus_200002": "Sql failed", "tr_ErrorCode_Minus_201103": "Message format error", "tr_ErrorCode_Minus_201111": "Device is searched successfully", "tr_ErrorCode_Minus_201113": "Pirated software", "tr_ErrorCode_Minus_201117": "The number of device connection has reached the uppper limit", "tr_ErrorCode_Minus_201121": "Obtaining  AUTHCODE incorrectly", "tr_ErrorCode_Minus_210002": "Interface verification failed", "tr_ErrorCode_Minus_210003": "Parameter error", "tr_ErrorCode_Minus_210004": "Phone number has been registered", "tr_ErrorCode_Minus_210005": "Sending text message exceeds the limited number ", "tr_ErrorCode_Minus_210008": "HD video is not supported at present, it will switch to SD automatically", "tr_ErrorCode_Minus_210009": "Forwarding mode doesn't support HD, please upgrade the firmware that supports DSS", "tr_ErrorCode_Minus_210010": "Sending failed,please try again", "tr_ErrorCode_Minus_210017": "Sending only once in 120 seconds", "tr_ErrorCode_Minus_210106": "Username has been registered", "tr_ErrorCode_Minus_210313": "The origional password is incorrect", "tr_ErrorCode_Minus_210315": "The old and new passwords are same, please change again.", "tr_ErrorCode_Minus_210405": "Verification code within 24 hours can not exceed 3 times", "tr_ErrorCode_Minus_210414": "This phone number is not registered", "tr_ErrorCode_Minus_210417": "Sending only once within 120 seconds", "tr_ErrorCode_Minus_210512": "Passwords you sent twice doesn't match", "tr_ErrorCode_Minus_210607": "Verification code error", "tr_ErrorCode_Minus_210700": "Server responding failed", "tr_ErrorCode_Minus_211703": "Missing upload file", "tr_ErrorCode_Minus_212104": "Server query failed", "tr_ErrorCode_Minus_212402": "The uloaded file hasn't been received", "tr_ErrorCode_Minus_213000": "No this username", "tr_ErrorCode_Minus_213100": "Sending email failed,please check email input is correct or not", "tr_ErrorCode_Minus_213108": "This email has been registered", "tr_ErrorCode_Minus_213206": "Username has been registered", "tr_ErrorCode_Minus_213207": "Verification code error", "tr_ErrorCode_Minus_213208": "Email has been registered", "tr_ErrorCode_Minus_213303": "Parameter error", "tr_ErrorCode_Minus_213314": "The email doesn't exist", "tr_ErrorCode_Minus_213316": "Email and username don't match", "tr_ErrorCode_Minus_213407": "Verification code error", "tr_ErrorCode_Minus_213414": "<PERSON><PERSON><PERSON> doesn't exist", "tr_ErrorCode_Minus_213514": "Phone number or email doesn't exist", "tr_ErrorCode_Minus_213600": "Device serial number is in the blacklist", "tr_ErrorCode_Minus_213601": "Device serial number already exists", "tr_ErrorCode_Minus_213602": "Device serial number is empty", "tr_ErrorCode_Minus_213603": "Device serial number format is incorrect", "tr_ErrorCode_Minus_213604": "There is no whitelist", "tr_ErrorCode_Minus_213605": "The device name cannot be empty", "tr_ErrorCode_Minus_213606": "The device username format is incorrect", "tr_ErrorCode_Minus_213607": "The device password format is incorrect", "tr_ErrorCode_Minus_213608": "The device name format is incorrect and contains keywords", "tr_ErrorCode_Minus_213610": "Parameter error", "tr_ErrorCode_Minus_213611": "Username does not exist", "tr_ErrorCode_Minus_213612": "Failed to edit device information", "tr_ErrorCode_Minus_213620": "Activation failed", "tr_ErrorCode_Minus_213621": "The cloud service is not activated", "tr_ErrorCode_Minus_213630": "Incorrect username or password", "tr_ErrorCode_Minus_213700": "Server responding failed", "tr_ErrorCode_Minus_213702": "Interface verification failed", "tr_ErrorCode_Minus_213703": "Parameter error", "tr_ErrorCode_Minus_213706": "The user has been registered", "tr_ErrorCode_Minus_213800": "Successful, needs to be updated", "tr_ErrorCode_Minus_213801": "Success, it is the latest, no need to update", "tr_ErrorCode_Minus_213802": "Failed, invalid request", "tr_ErrorCode_Minus_213803": "Failed, resource not found", "tr_ErrorCode_Minus_213804": "Failed, internal server error", "tr_ErrorCode_Minus_213805": "Failed, the server is temporarily unavailable", "tr_ErrorCode_Minus_214206": "Username has been registered", "tr_ErrorCode_Minus_214404": "Phone number has been bound", "tr_ErrorCode_Minus_214507": "Verification code error", "tr_ErrorCode_Minus_214608": "The email address has been bound", "tr_ErrorCode_Minus_214707": "Verification code error", "tr_ErrorCode_Minus_214708": "The email address has been bound", "tr_ErrorCode_Minus_214908": "The email address has been registered", "tr_ErrorCode_Minus_215100": "Get device DSS information through XMCloud", "tr_ErrorCode_Minus_215101": "DSS failed to connect to Hls server", "tr_ErrorCode_Minus_215102": "DSS information format error", "tr_ErrorCode_Minus_215103": "Failed to obtain device DSS information, please try again later", "tr_ErrorCode_Minus_215104": "DSS code stream format parsing failed", "tr_ErrorCode_Minus_215110": "Failed to parse the video square URL returned by Xi<PERSON>ma<PERSON> Cloud", "tr_ErrorCode_Minus_215120": "The front end is not connected to the video source", "tr_ErrorCode_Minus_215121": "The front end is not connected to the video source", "tr_ErrorCode_Minus_215122": "The front end does not support this code stream", "tr_ErrorCode_Minus_215124": "DSS cannot be opened using the combined encoding channel, please reopen", "tr_ErrorCode_Minus_215130": "Invalid request", "tr_ErrorCode_Minus_215131": "The media video link has reached the maximum limit and access is limited", "tr_ErrorCode_Minus_215140": "Invalid token format", "tr_ErrorCode_Minus_215141": "Does not match token serial number", "tr_ErrorCode_Minus_215142": "The remote ip does not match the token ip", "tr_ErrorCode_Minus_215143": "Token expired", "tr_ErrorCode_Minus_215144": "Failed to obtain the key", "tr_ErrorCode_Minus_215145": "Token does not match", "tr_ErrorCode_Minus_215146": "Invalid token data format", "tr_ErrorCode_Minus_215147": "Failed to decrypt key data", "tr_ErrorCode_Minus_215148": "Authcode does not match", "tr_ErrorCode_Minus_215149": "Authcode is changed", "tr_ErrorCode_Minus_221201": "Alarm authorization code error", "tr_ErrorCode_Minus_221202": "This function is not supported", "tr_ErrorCode_Minus_222400": "No recording files of the day were found", "tr_ErrorCode_Minus_223000": "url is empty", "tr_ErrorCode_Minus_223001": "Open failed", "tr_ErrorCode_Minus_223002": "Failed to obtain stream information", "tr_ErrorCode_Minus_223003": "Failed to obtain video stream information", "tr_ErrorCode_Minus_223010": "Unable to obtain video stream", "tr_ErrorCode_Minus_223100": "Failed to open telnet", "tr_ErrorCode_Minus_225402": "Server error", "tr_ErrorCode_Minus_225501": "Important parameter verification failed (field missing, type mismatch, empty string)", "tr_ErrorCode_Minus_225502": "Getting redis ip, port failed", "tr_ErrorCode_Minus_225503": "redis failed to establish connection", "tr_ErrorCode_Minus_225504": "redis operation failed", "tr_ErrorCode_Minus_225505": "Failed to obtain mysql address", "tr_ErrorCode_Minus_225506": "SQL statement input parameter verification failed (SQL injection may exist)", "tr_ErrorCode_Minus_225507": "SQL operation failed", "tr_ErrorCode_Minus_225508": "Failed to obtain thumbnail URL and URL expiration time", "tr_ErrorCode_Minus_225509": "Time format verification failed, timestamp conversion failed", "tr_ErrorCode_Minus_225510": "Cloud storage package information is abnormal", "tr_ErrorCode_Minus_225511": "Unknown illegal query type, not MSG or VIDEO", "tr_ErrorCode_Minus_225512": "The start time and end time of the query are not on the same day", "tr_ErrorCode_Minus_225513": "The sn format is illegal", "tr_ErrorCode_Minus_225514": "Unknown illegal clearing type, not (ALL, ALARM, VIDEO)", "tr_ErrorCode_Minus_225515": "Unknown subscription query protocol format", "tr_ErrorCode_Minus_225516": "IP request not in the whitelist (only for cloud information deletion interface)", "tr_ErrorCode_Minus_225517": "The time area that this user can query has not been obtained", "tr_ErrorCode_Minus_225518": "JSON data format verification failed", "tr_ErrorCode_Minus_225519": "Error in getting message do-not-disturb time period and parsing configuration data format ", "tr_ErrorCode_Minus_226003": "Cannot set read-only configuration", "tr_ErrorCode_Minus_300000": "<PERSON> Auth Error", "tr_ErrorCode_Minus_400000": "Heartbeat timeout", "tr_ErrorCode_Minus_400001": "File does not exist", "tr_ErrorCode_Minus_400002": "The device is being upgraded", "tr_ErrorCode_Minus_400003": "Server initialization failed", "tr_ErrorCode_Minus_400004": "Failed to obtain connection type", "tr_ErrorCode_Minus_400005": "Failed to query the server", "tr_ErrorCode_Minus_400006": "The device is already connected", "tr_ErrorCode_Minus_400007": "logging in", "tr_ErrorCode_Minus_400008": "The device may not be online, please try again later", "tr_ErrorCode_Minus_400009": "<PERSON><PERSON> doesn't support", "tr_ErrorCode_Minus_400010": "There is no picture of the day, please switch the date", "tr_ErrorCode_Minus_400011": "Disconnecting failed", "tr_ErrorCode_Minus_400012": "Other users are using the talk function, please try again later!", "tr_ErrorCode_Minus_400013": "Other users are using the talk function, please try again later!", "tr_ErrorCode_Minus_400014": "Backup to USB disk failed", "tr_ErrorCode_Minus_400015": "No storage device (USB disk) or the device is not recording", "tr_ErrorCode_Minus_400017": "Capture failed", "tr_ErrorCode_Minus_400018": "File size limit exceeded", "tr_ErrorCode_Minus_400019": "File size verification failed", "tr_ErrorCode_Minus_400100": "talk is not enabled", "tr_ErrorCode_Minus_400101": "Device storage is full", "tr_ErrorCode_Minus_400102": "No clear result was obtained for obtaining login encryption information (supported/unsupported)", "tr_ErrorCode_Minus_400201": "Insufficient memory", "tr_ErrorCode_Minus_400202": "The upgrade file format is incorrect", "tr_ErrorCode_Minus_400203": "A certain partition failed to upgrade", "tr_ErrorCode_Minus_400204": "Hardware model does not match", "tr_ErrorCode_Minus_400205": "Customer information does not match", "tr_ErrorCode_Minus_400206": "The compatible version number of the upgrade firmware is smaller than the current version of the device, and the device is not allowed to be upgraded back to the old firmware", "tr_ErrorCode_Minus_400207": "Invalid version", "tr_ErrorCode_Minus_400208": "The Wi-Fi driver in the upgrade firmware does not match the Wi-Fi network card currently used by the device", "tr_ErrorCode_Minus_400209": "Network error", "tr_ErrorCode_Minus_400210": "The upgrade firmware does not support the flash used by the device", "tr_ErrorCode_Minus_400211": "The upgrade file has been modified and cannot be upgraded through the external network", "tr_ErrorCode_Minus_400212": "Upgrading this firmware requires special capability support", "tr_ErrorCode_Minus_500003": "Network error, please try again later", "tr_ErrorCode_Minus_603000": "FunSDK certificate validity verification failed *Illegal UUID or AppKey is not allowed to be used", "tr_ErrorCode_Minus_603001": "JSON data format verification failed", "tr_ErrorCode_Minus_603002": "The login username or password is empty", "tr_ErrorCode_Minus_603003": "Login Token is empty", "tr_ErrorCode_Minus_603004": "The third-party login type parameter is empty (WeChat--wx means Google means gg, Faceboo means fb, line means line)", "tr_ErrorCode_Minus_604000": "Incorrect username or password", "tr_ErrorCode_Minus_604010": "Verification code error", "tr_ErrorCode_Minus_604011": "Two passwords don't match", "tr_ErrorCode_Minus_604012": "Username has been registered", "tr_ErrorCode_Minus_604013": "Username is empty", "tr_ErrorCode_Minus_604014": "Password is empty", "tr_ErrorCode_Minus_604015": "Confirming password is empty", "tr_ErrorCode_Minus_604016": "Mobile number is empty", "tr_ErrorCode_Minus_604017": "Incorrect username format", "tr_ErrorCode_Minus_604018": "New password does not meet requirements. Passwords with 8-64 bits must contain numbers and letters", "tr_ErrorCode_Minus_604019": "Confirm password format incorrect", "tr_ErrorCode_Minus_604020": "Phone number format incorrect", "tr_ErrorCode_Minus_604021": "The phone number has been registered", "tr_ErrorCode_Minus_604022": "The phone number is not registered", "tr_ErrorCode_Minus_604023": "The email has been registered", "tr_ErrorCode_Minus_604024": "Email does not exist", "tr_ErrorCode_Minus_604026": "Original password error", "tr_ErrorCode_Minus_604027": "Modify password failed", "tr_ErrorCode_Minus_604029": "UserID is empty", "tr_ErrorCode_Minus_604030": "Verification code error", "tr_ErrorCode_Minus_604031": "Email is empty", "tr_ErrorCode_Minus_604032": "Email format is incorrect", "tr_ErrorCode_Minus_604033": "User not have authority", "tr_ErrorCode_Minus_604034": "User unbond", "tr_ErrorCode_Minus_604035": "User binding failed", "tr_ErrorCode_Minus_604036": "Mobile binding failed", "tr_ErrorCode_Minus_604037": "Email binding failed", "tr_ErrorCode_Minus_604038": "Sending verification code exceeds the maximum numbers of times", "tr_ErrorCode_Minus_604039": "register failed", "tr_ErrorCode_Minus_604040": "WeChat has bound users", "tr_ErrorCode_Minus_604041": "No permission to modify username (only for generated anonymous users)", "tr_ErrorCode_Minus_604042": "User not bindfacebook", "tr_ErrorCode_Minus_604043": "User binding facebook failed", "tr_ErrorCode_Minus_604044": "User not bind google", "tr_ErrorCode_Minus_604045": "User binding google failed", "tr_ErrorCode_Minus_604046": "Line account unbound", "tr_ErrorCode_Minus_604047": "Line account binding failed", "tr_ErrorCode_Minus_604048": "Too many user verification code errors, the verification code is invalid. Please try again in 24 hours", "tr_ErrorCode_Minus_604049": "Too many login errors, please try again in ten minutes", "tr_ErrorCode_Minus_604050": "Request too frequent, please try again later", "tr_ErrorCode_Minus_604100": "Illegal device not allowed to be added", "tr_ErrorCode_Minus_604101": "Device already exist", "tr_ErrorCode_Minus_604102": "Delete device failed", "tr_ErrorCode_Minus_604103": "Device info modify failed", "tr_ErrorCode_Minus_604104": "Device uuid parameter abnormal", "tr_ErrorCode_Minus_604105": "Device user parameter abnormal", "tr_ErrorCode_Minus_604106": "Device password parameter abnormal", "tr_ErrorCode_Minus_604107": "Device port parameter abnormal", "tr_ErrorCode_Minus_604108": "Device extension field parameter exception", "tr_ErrorCode_Minus_604109": "Wrong position", "tr_ErrorCode_Minus_604110": "New password verification failed", "tr_ErrorCode_Minus_604111": "Confirmed password verification failed", "tr_ErrorCode_Minus_604112": "Device another name verification failed", "tr_ErrorCode_Minus_604113": "Device ip address error", "tr_ErrorCode_Minus_604114": "Support cloud storage", "tr_ErrorCode_Minus_604115": "Not support cloud storage", "tr_ErrorCode_Minus_604116": "Transferring the device master account to another user failed. Check if the user owns the device and if they have the device master account permission", "tr_ErrorCode_Minus_604117": "Current account is not the main account of the current device", "tr_ErrorCode_Minus_604118": "The device not  exists and has been removed", "tr_ErrorCode_Minus_604119": "The device has been added to another account", "tr_ErrorCode_Minus_604120": "Device number under your account has been reached most, no more devices can be added!", "tr_ErrorCode_Minus_604124": "Add device failed, the shared device has been cancelled or deleted by the sharer", "tr_ErrorCode_Minus_604126": "The device has been bound and needs to be unbound before it can be added", "tr_ErrorCode_Minus_604127": "Add device failed, please try other methods", "tr_ErrorCode_Minus_604128": "Add device failed, device verification code illegal", "tr_ErrorCode_Minus_604200": "Add authorization failed", "tr_ErrorCode_Minus_604201": "Modify authorization failed", "tr_ErrorCode_Minus_604202": "Delete authorization failed", "tr_ErrorCode_Minus_604203": "Single authorization synchronization failed (possibly due to incorrect type parameters or cloud product line not returning)", "tr_ErrorCode_Minus_604300": "Verification code send failed.pls check whether input correct", "tr_ErrorCode_Minus_604301": "Email signature failed", "tr_ErrorCode_Minus_604302": "Logout account requires a verification code", "tr_ErrorCode_Minus_604303": "The number of times to obtain email links has exceeded the limit today. Please try again in 24 hours", "tr_ErrorCode_Minus_604304": "The number of times to obtain email links has exceeded the limit today. Please try again in 24 hours", "tr_ErrorCode_Minus_604400": "SMS interface verified failed, please contact us", "tr_ErrorCode_Minus_604401": "SMS interface parameter error, please contact us", "tr_ErrorCode_Minus_604402": "The number of times to obtain the verification code within 24 hours cannot exceed 3 times", "tr_ErrorCode_Minus_604403": "Verification code send failed, please check whether input correct", "tr_ErrorCode_Minus_604404": "Can only send once within 120 seconds", "tr_ErrorCode_Minus_604405": "Send failed", "tr_ErrorCode_Minus_604500": "Not find user list or user list is empty", "tr_ErrorCode_Minus_604502": "Not find device list or device list is empty", "tr_ErrorCode_Minus_604503": "Reset app secret failed", "tr_ErrorCode_Minus_604600": "WeChat alarm failed to open", "tr_ErrorCode_Minus_604601": "WeChat alarm failed to close", "tr_ErrorCode_Minus_605000": "Server failure", "tr_ErrorCode_Minus_605001": "Certification not exist", "tr_ErrorCode_Minus_605002": "Request header information error", "tr_ErrorCode_Minus_605003": "Certificate Invalidation", "tr_ErrorCode_Minus_605004": "Generate key verification error", "tr_ErrorCode_Minus_605005": "Parameter error", "tr_ErrorCode_Minus_605006": "Connect failed", "tr_ErrorCode_Minus_605007": "Unknow error", "tr_ErrorCode_Minus_605008": "ip address not allowed for access", "tr_ErrorCode_Minus_605009": "Decryption error (third-party login code error or AES encryption and decryption error)", "tr_ErrorCode_Minus_605010": "token expired", "tr_ErrorCode_Minus_605011": "Token error", "tr_ErrorCode_Minus_605012": "tokennot have authority", "tr_ErrorCode_Minus_605013": "Not support", "tr_ErrorCode_Minus_605017": "Message code invalidation!", "tr_ErrorCode_Minus_661412": "Account name not exist", "tr_ErrorCode_Minus_661427": "New password format not correct", "tr_ErrorCode_Minus_800401": "Unauthorized", "tr_ErrorCode_Minus_800403": "Prohibit access", "tr_ErrorCode_Minus_800404": "Not exist", "tr_ErrorCode_Minus_800500": "Server internal error", "tr_ErrorCode_Minus_803004": "Account or password error", "tr_ErrorCode_Minus_806002": "The role does not exist, it needs to be configured", "tr_ErrorCode_Minus_1239510": "Object not exist", "tr_ErrorCode_Minus_1239511": "Value not exist", "tr_Input": "Input", "tr_AppWelcome": "Welcome to use BcloudSaaS", "tr_Prompt": "Prompt", "tr_Cancel": "Cancel", "tr_Confirm": "Confirm", "tr_LoginTips1": "Already read and agree", "tr_Login": "<PERSON><PERSON>", "tr_InputPhoneNumber": "Please input mobile number", "tr_InputPhoneNumberEmail": "Please input mobile number/email", "tr_InputPassword": "Please enter password", "tr_RegisterAccount": "Register account", "tr_ForgetPassword": "Forgot password", "tr_UserAgreement": "User protocol", "tr_PrivacyPolicy": "Privacy policy", "tr_And": "And", "tr_ReviewTips1": "You already submitted your registration application, please be patient and wait", "tr_ReviewTips2": "On weekdays, we will complete the audit within 1 hour. \nOn non weekdays, we will complete the audit within 24 hours. \nThe audit results will be notified to you via SMS! \nPlease pay attention to SMS notifications in a timely manner", "tr_InputVerifyCode": "Enter verification code", "tr_InputPasswordRule": "Please enter password", "tr_InputConfirmPasswordRule": "Please enter confirm password", "tr_AccountPasswordRule": "The password is 8-16 characters and must contain letters, numbers and special characters", "tr_InputTrueName": "Please enter real name", "tr_InputCompanyName": "Please enter the company name", "tr_ClickToUploadTheBusinessLicense": "Click to upload business license", "tr_Submit": "Submit", "tr_TakePhoto": "Take photo", "tr_Album": "Album", "tr_PasswordNotOneMatch": "Password inconsist", "tr_PleaseFillInfo": "Please complete the information firstly", "tr_PleaseAgreeAgreementAndPolicy": "Please agree to the user agreement and privacy policy first", "tr_SendVerifyCode": "Send verification code", "tr_ErrorFormatPassword": "Password format incorrect", "tr_ErrorFormatPhoneNum": "Mobile number formagt is incorrect", "tr_ResetPassword": "Reset password", "tr_ModifySuccess": "Modify success", "tr_AppPolicyTip1": "Summary of Privacy Protection Policy", "tr_AppPolicyTip2": "Thank you for trusting and using Bcloud! Bcloud attaches great importance to your privacy and personal information protection. Before using the Bcloud service, please read carefully", "tr_AppPolicyTip3": "We agree and accept all terms and conditions before starting to use your services.\n        e will take corresponding security measures in accordance with legal and regulatory requirements, and do our best to protect the security and controllability of your personal information.", "tr_AgreeAndContinue": "Agree to continue", "tr_Reject": "Reject", "tr_PersonManage": "User management", "tr_RoleManage": "Role management", "tr_HelpAndFeedback": "Help and feedback", "tr_Tool": "Tool", "tr_About": "About", "tr_Account": "Account", "tr_AccountSet": "Persoal setting", "tr_PersonInfo": "Personal info", "tr_Avatar": "Head sculpture", "tr_Name": "Name", "tr_Mail": "Email", "tr_BindPhone": "Bind phone", "tr_ModifyPassword": "Modify password", "tr_AccountSafe": "Account and safety", "tr_AccountCancellation": "Cancel account", "tr_AccountCancellation1": "Cancel account", "tr_Logout": "Log out", "tr_VersionUpdate": "Version update", "tr_ImportantClause": "Important clause", "tr_ImportantClauseContent": "Hangzhou JFTECH Technology Co., Ltd. hereby declares that the commercial activities you participate in through this software are not related to Apple Inc", "tr_ContactUs": "Contact us", "tr_ContactUsContent": "Thank you for using our products and services. If you have any questions, comments or suggestions during the use of our products or services, please send your <NAME_EMAIL> We will reply to you as soon as possible. Thank you for your cooperation", "tr_NowLatestVersion": "Latest version", "tr_OfficialWebsite": "Official website", "tr_AllRightsReserved": "Copyright：Hangzhou JFTECH", "tr_ConfirmTips": "Confirm to logout?", "tr_Done": "Done", "tr_PleaseInputOldPwd": "Please input the old password", "tr_PleaseInputNewPwd": "Please input the new password", "tr_PleaseInputNewPwdCon": "Please confirm new password", "tr_IdCheck": "ID Authentication", "tr_CheckCurrentPhone": "Detected that the phone number you are currently bound to is:", "tr_Next": "Next", "tr_BindNewPhone": "Bind to new mobile phone", "tr_BindEmail": "Bind email", "tr_DearUser": "Dear user：", "tr_DearUserDetail": "After your account【{phone}】cancellation, you can not login using that account and use【Bcloud platform】related products and service,and automatically exit the enterprise/organization you joined on the Bcloud Platform. All data information of your account on the Bcloud platform will be cleared and cannot be restored.\n If you have any questions, please contact Bcloud customer service email: <EMAIL>", "@tr_DearUserDetail": {"placeholders": {"phone": {}}}, "tr_ConfirmCancellation": "Confirm cancellation", "tr_CancellationProtocol": "《BcloudSaaS Account Cancellation Agreement》", "tr_PleaseInputNewPhone": "Please input new phone number", "tr_PleaseInputNewMail": "Please input new email", "tr_PleaseAgreeProtocol": "Please agree protocol firstly", "tr_Cancellation": "Cancellation", "tr_CancellationSuccess": "Cancel success", "tr_CannotChangeSamePhone": "Nw phone number cannot be same as the original phone number", "tr_CannotChangeSameEmail": "New email cannot be same as the original email", "tr_CheckCurrentEmail": "Detected that the email you are currently bound to is：", "tr_FileFormatterNotSupport": "Unsupported file format", "tr_AddNode": "Add node", "tr_AddDevice": "Add device", "tr_EnterNoteNickName": "Add node name", "tr_CommonSave": "Save", "tr_AddSuccess": "Add success", "tr_DeviceAdd": "Add device", "tr_Node": "Node", "tr_JFDevice": "JFTECH device", "tr_JFDeviceNotes": "Add JFTECH protocol device", "tr_PlatformDevice": "Platform device", "tr_PlatformDeviceNotes": "Add a platform protocol device", "tr_AddJFDevice": "Device add", "tr_NationalStandardDevice": "National standard device", "tr_NationalStandardDeviceNotes": "Add national standard device", "tr_ScanJFDevice": "Scan available devices", "tr_AddDeviceByManual": "Add device manually", "tr_AddDeviceByWIFI": "WiFi distribution network", "tr_AddDeviceByWIFINotes": "Supports QR code distribution and fast distribution", "tr_AddRecordDevice": "Add by direct network cable connection", "tr_AddRecordDeviceNotes": "Suitable for devices with Ethernet cable sockets", "tr_Add4GDevice": "4G camera", "tr_Add4GDeviceNotes": "Add by scan the device QR code", "tr_common_close": "Close", "tr_common_GoSetting": "Go to enable", "tr_LocalNetworkDisable": "Local network permissions not enabled", "tr_LocalNetworkDisableNotes": "Not enable local network permissions, you will not be able to add devices", "tr_OperationByInstructions": "According to the instructions, power on the camera to ensure it starts up normally", "tr_RestoreFactorySettings": "Initialize device", "tr_RestoreFactorySettingsTip": "If you hear the device prompt\"Start Quick Configuration\"or\"Waiting for Connection\",Click\"next\"", "tr_RestoreFactorySettingsNotes": "If not，please long press device SET/RESET button for 6 seconds untill hearing\"Restoring to factory,please do not power off\"；After device restoring finished, connect again", "tr_RestoreFactorySettingsNotes2": "Please check the device body or refering to manual for specific button positions", "tr_NetworkConfiguration": "Network configuration", "tr_NetworkConfigurationDeclare": "Device not support 5G WiFi, only support 2.4G WiFi", "tr_ChooseNetwork": "Please choose the WIFI network", "tr_EnterWiFiPassword": "Please enter WIFI password", "tr_ChoosePrettyWiFi": "Please choose a WiFi with strong signal that can quickly connect devices to the internet", "tr_OprationsOfNetworkConfiguration": "· Please put the QR code towards the camera lens;\n· Maintain a distance of 25-35 centimeters and wait for scanning;\n· Remove the phone after hearing the WiFi configuration prompt sound;\n·Hearing a successful configuration prompt indicates that the device's WiFi configuration has been completed", "tr_DeviceScanTips": "Quick network configuration and QR code configuration are both enabled. Please check if the manual supports QR code configuration. If the device does not support QR code configuration, there is no need to perform QR code matching", "tr_ModifyDeviceNickName": "Modify device name", "tr_ModifyDevicePassword": "Modify device password", "tr_DeviceSN": "Serial number", "tr_ModifyDevicePasswordNotes": "For the security of your device, please change the default password", "tr_RulesOfDevicePassword": "Password length 8-64 bits, consisting of letters and numbers", "tr_FailedSetDevicePassword": "If set password failed, please long press the reset button on the back of the device to restore the factory default. After restoring the factory settings, it is necessary to add the device again and reset the password.", "tr_CheckDeviceLoginInfo": "Device name and password", "tr_DeviceLoginName": "Device login name", "tr_DevicePassword": "Please input password", "tr_ResetDevice": "If forgot password, please restore to default and add again", "tr_AddRecordNotes": "NOTE： \n· Mare sure to power on device \n· Connect network cable and device network cable", "tr_SCanQRCode": "Scan QR code", "tr_AccessNationalStandardDevice": "National device access", "tr_AccessProtocol": "Access protocol", "tr_AccessType": "Access type", "tr_EncodingMethod": "Encode method", "tr_EncodingMethodRandom": "Random encode", "tr_EncodingMethodNationalStandard": "National encode", "tr_GenerateIDNum": "Generate ID number", "tr_AccessPassword": "Access password", "tr_GoToSetting": "Go to setting", "tr_Common_Random": "Random", "tr_Common_Custom": "Custom", "tr_AccessTips": "NOTE:\nNational standard protocol currently does not support H.265 encoding. Please ensure that the device's bitstream is H.264. The device needs to use the GB28181-2016 protocol to support TCP.", "tr_StandardAccessTips": "To add an international device, it is necessary to configure the access ID, SIP server IP, SIP server port, and other information to the designated location in the background of the device to be connected. You can view the corresponding information in the generated access ID details.", "tr_GenerateID": "Generate access ID", "tr_ReviewAccess": "Review access", "tr_NationalStandardConfiguration": "National standard encode configuration", "tr_Common_AdministrativeArea": "Administrative Area", "tr_Common_GrassrootsUnitNum": "Grassroots unit number", "tr_IndustryCode": "Industry code", "tr_Common_PlsFillInfo": "Please choose", "tr_TypeCode": "Type code", "tr_NetworkIdentification": "_Network Identificatio", "tr_AccessIds": "Access ID", "tr_Common_export": "Export", "tr_NodeName": "Node name", "tr_TypePre": "Type:", "tr_Common_All": "All", "tr_StatusPre": "Status:", "tr_Status": "Status", "tr_UnUsed": "Unused", "tr_Used": "Used", "tr_Common_Password": "Password", "tr_View": "View", "tr_Detailed": "Detailed info", "tr_ServiceParams": "Service parameter", "tr_AccessNode": "Access Node", "tr_CancellationValidityPeriod": "THE TERM OF VALIDITY", "tr_HeartbeatCycle": "Heartbeat Cycle", "tr_ServerParams": "Server parameter", "tr_SIPServerId": "SIP Server ID", "tr_SIPServerDomain": "SIP Server domain", "tr_SIPServerAddress": "SIP Service address", "tr_SIPServerPort": "SIP Server port", "tr_DeviceParams": "Device parameter", "tr_HoldOnForSearhingDevices": "Searching for nearby devices, please wait...", "tr_OrganizationStructure": "Device tree", "tr_Common_Search": "Search", "tr_NodeEdit": "Edit node", "tr_DeviceEdit": "Edit device", "tr_Common_ContentPairingFailed": "Searched nothing", "tr_Common_Device": "<PERSON><PERSON>", "tr_Common_Edit": "Edit", "tr_Common_Delete": "Delete", "tr_Common_DeleteSuccess": "Delete success", "tr_Common_InputPassword": "Please input login password", "tr_MoveDevicesToRootNode": "If there is a device, after deleting the node, the device will automatically belong to the root node", "tr_MoveDevicesToRootNVRNode": "Deleting the NVR will simultaneously delete all subordinate devices. Do you confirm the deletion?", "tr_MoveDevicesToCenterSeverNode": "Deleting the central server will simultaneously delete its channels and nodes. Do you confirm the deletion?", "tr_NameContentUnchange": "Enter consistent names", "tr_Common_Monitor": "Monitor", "tr_Common_Function": "Function", "tr_Common_Message": "Message", "tr_Common_Mine": "Mine", "tr_PlayUrlIsInvalid": "The play address is empty", "tr_OprationDeny": "There are child nodes under this node, please delete the child nodes first", "tr_MakeSureDeleteNode": "Confirm to delete node?", "tr_MakeSureDeleteDevice": "Confirm to delete the device?", "tr_AddNodeTips": "There are currently no devices or child nodes under the current node. Please click the add button in the upper right corner to add devices and child nodes", "tr_InputNodeNickName": "Please input name", "tr_InputDeviceNickName": "Please input device name", "tr_InputDeviceNickNameForSearch": "Please input device name to search", "tr_AddDeviceFailed": "Add failed", "tr_CheckAddDeviceFailed": "The device cannot be found. Please reset the device and add it again.", "tr_InvalidDevice": "The device serial number is invalid or the device has already been added", "tr_WiFiConfigurationRecommendation": "1. If the home is a dual band router, please check if the WiFi connected to the camera is in the 5GHz frequency band, and switch to a 2.4GHz WiFi connection.\n\n2. It is recommended not to be too far from the router when configuring the camera. \n\n3. It is recommended to connect to non bridging WiFi, as bridging may cause your network to be very unstable.", "tr_GotIt": "Got it", "tr_TheRestrictionsOnWiFi": "What are the WiFi requirements for devices？", "tr_InvalidData": "Invalid data", "tr_Permission_Location": "Position", "tr_Permission_LocalNetwork": "Local network", "tr_RequestPermission": "In order to add device normally, need【{permission}】permission", "tr_TurnOnWiFi": "In order to add device normally，need enable WiFi", "tr_GenerateChannels": "Generate channel number", "tr_InputAccessPassword": "Please set access password", "tr_Common_Channel": "Channel", "tr_channelParams": "Channel parameter", "tr_CopySuccess": "Copied to the clipboard", "tr_Common_ChooseAdministrativeArea": "Administrative Region Selection", "tr_Recording": "Already enabled record", "tr_FailedRecord": "Record failed", "tr_SaveRecordFileSuccess": "Save record success", "tr_SaveRecordFileFailed": "Save record faield", "tr_SnapSuccess": "Snapshot success", "tr_SnapFailed": "Snapshot failed", "tr_PTZ": "PTZ", "tr_Preset": "Preset", "tr_Playback": "Playback", "tr_Common_More": "More", "tr_Common_Voice": "Voice", "tr_Common_Record": "Record", "tr_Common_Snap": "Snapshot", "tr_Common_definition": "Definition", "tr_Common_Reset": "Reset", "tr_Common_Add": "Add", "tr_Common_SelectAll": "Select all", "tr_Common_Rename": "<PERSON><PERSON>", "tr_AddPreset": "Create new preset", "tr_SetPresetName": "Please input preset name", "tr_PresetNameExisted": "Preset name already exist, do not use same name", "tr_EditPreset": "Edit preset", "tr_CommandSuccess": "Operation Successful", "tr_Common_Loading": "Loading...", "tr_Loading": "Loading...", "tr_Common_Disconnect": "Disconnect", "tr_Permission_Camera": "Please allow Camera Access!", "tr_Permission_Album": "Please Allow Access to Photos!", "tr_QRcodeInvalid": "Unable to recognize QR code information in the image. Please choose another image or retry.", "tr_SelectAll": "Select All", "tr_Share": "Share", "tr_Download": "Download", "tr_Delete": "Detele", "tr_AlarmMessage": "Alarm Message", "tr_NoData": "No Data Available", "tr_SelectItemTips": "Please select the file you want to operate on first.", "tr_ShareTips1": "1.Video and image cannot be combined for sharing.\n2.Only one video can be shared at a time.", "tr_ShareTips2": "Only one file can be shared.", "tr_ShareTips3": "Video needs to be downloaded. Please share after the download is complete.", "tr_ShareTipsImageMaxNum": "Maximum 5 images can be shared at once.", "tr_ShareSuccess": "Share Successful", "tr_ShareFailure": "Share Failed", "tr_PleaseAllowVisitAllPhoto": "Please allow access to all photos in album first.", "tr_SureSaveToAlbum": "Confirm saving selected files to album?", "tr_Downloaded": "Downloaded", "tr_SureItemDelete": "Are you sure you want to delete the selected item?", "tr_SuccessfullyDeleted": "Deletion Successful", "tr_NOAlarmMessage": "No alarms found", "tr_LoadedFail": "Failed to load", "tr_AlarmAI": "AI alarm", "tr_AlarmDevice": "Device alarm", "tr_AlarmTotalNum": "Total alarms", "tr_Today": "Today", "tr_Device": "<PERSON><PERSON>", "tr_ToDoList": "To Do items", "tr_MessageCenter": "Message", "tr_AllProtocols": "All Protocols", "tr_AllTypes": "All Types", "tr_Mon": "Mon", "tr_Tues": "<PERSON><PERSON>", "tr_Wed": "Wed", "tr_Thur": "<PERSON><PERSON>", "tr_Fri": "<PERSON><PERSON>", "tr_Sat": "Sat", "tr_Sun": "Sun", "tr_AllDate": "All Date", "tr_AccessIdPwdTips": "Password length should be 8-64 characters, consisting of letters and numbers", "tr_AccessPwdConfig": "Access Password Configuration", "tr_SelectNode": "Node Selection", "tr_DownloadSuccess": "Download Successful", "tr_downloadFailed": "Download failed", "tr_NoMedia": "No video resources available", "tr_RecordCard": "Local Playback", "tr_RecordCloud": "Cloud Playback", "tr_AddDepartment": "Add Department", "tr_EditDepartment": "Edit Department", "tr_EnterNoteDepartment": "Enter Department Name", "tr_MakeSureDeleteDepartment": "Are you sure you want to delete the department?", "tr_AddDepartmentTips": "There are currently no sub-departments under this department. Please click the add button in the top right corner to add a department.", "tr_AddDepartmentTipsSimple": "There are currently no sub-departments under this department.", "tr_ChooseDepartment": "Select Department", "tr_AlarmSetting": "Alarm Setting", "tr_NormalSetting": "Setting", "tr_DepartmentManage": "Department Management", "tr_NoRole": "No roles available", "tr_ViewDetail": "View Details", "tr_SelectRole": "Select Role", "tr_Phone": "Phone", "tr_PleaseInputDes": "Please enter description", "tr_PleaseInputName": "Please enter name", "tr_RoleName": "Role Name", "tr_RoleDes": "Role Description", "tr_BasicInfo": "Basic Information", "tr_AddRole": "Add Role", "tr_RoleDetail": "Role Details", "tr_NORoleTips": "No roles available\nPlease click the add button in the top right corner to add", "tr_Department": "Department", "tr_PwdCon": "Confirm Password", "tr_PleaseInputPwdCon": "Please enter confirm password", "tr_PhoneNum": "Phone Number", "tr_PleaseInputMail": "Please enter email", "tr_Role": "Role", "tr_DeviceResource": "Device Resource", "tr_PersonDetail": "User Details", "tr_AddPerson": "Add New User", "tr_CheckView": "View", "tr_SureDelete": "Confirm Deletion", "tr_InputPwd": "Enter Password", "tr_SelectDeviceResource": "Select Device Resource", "tr_PleaseSelect": "Please Select", "tr_DeviceName": "Device Name", "tr_DeviceInfo": "Device Information", "tr_DeleteDevice": "Delete Device", "tr_DeleteDeviceTips": "After deleting the camera, any photos and videos generated in the cloud will also be cleared.", "tr_DeviceState": "Device State", "tr_DeviceStateOnline": "Online", "tr_DeviceStateOffline": "Offline", "tr_DeviceStateUnRegister": "Unregistered", "tr_DeviceNode": "Node", "tr_DeviceUuid": "Device SN", "tr_DeviceAccessChannelNum": "Access Channels", "tr_DeviceAccessIDPwd": "Access ID Password", "tr_SIPServerIP": "SIP Server IP", "tr_SIPServerDomain2": "SIP Domain", "tr_DeviceChannelNum": "Channel Number", "tr_CloudRecord": "Cloud Playback", "tr_SuperAdmin": "Super Admin", "tr_NOPersonTips": "No users available \n Please click the add button in the top right corner to add", "tr_AccountPwdRuleSimple": "8-16 characters, letters and numbers", "tr_GenIdNumTips": "Generated number must be greater than 0", "tr_GenIdNumTips1": "Number of channels must not exceed 128", "tr_Template": "Template", "tr_VideoSpotCheckInspection": "Video Spot Check Inspection", "tr_OnSiteInspection": "On-Site Inspection", "tr_ShakeToInspect": "Shake to Inspect", "tr_InspectionPlan": "Inspection plan", "tr_InspectAccordingToPlan": "Task check", "tr_InspectionRecord": "Inspection record", "tr_AlgorithmConfiguration": "Set algorithm", "tr_AlgorithmAlarm": "Algorithm Alarm", "tr_Record": "Record", "tr_Algorithm": "Algorithm", "tr_Function": "Function", "tr_Inspection": "Inspection", "tr_createTemplate": "Create Template", "tr_pleaseEnterTemplateName": "Please enter template name", "tr_pleaseEntereLigibilityScore": "Please enter eligibility score", "tr_presetEvaluationTemplate": "Preset Evaluation Template", "tr_assessmentCategory": "Assessment Category", "tr_assessmentItem": "Assessment Item", "tr_totalScore": "Total Score", "tr_passingScore": "Passing Score", "tr_displayArrangement": "Display Arrangement", "tr_createEvaluationClass": "Create Evaluation Class", "tr_score": "Score", "tr_item": "<PERSON><PERSON>", "tr_createEvaluationItem": "Create Evaluation Item", "tr_pleaseEnterTheEvaluationClassName": "Please enter evaluation class name", "tr_noEvaluationItemsTemporarily": "No evaluation items available", "tr_description": "Describe", "tr_scoreValue": "Score", "tr_pleaseEnterTheContentOfTheEvaluationItem": "Please enter the content of the evaluation item", "tr_pleaseEnterTheScoreOfTheEvaluationItem": "Please enter the score of the evaluation item", "tr_referencePicture": "Reference Picture", "tr_templateDetails": "Template Details", "tr_collapse": "Collapse", "tr_templateName": "Template Name", "tr_expand": "Expand", "tr_pleaseSetEvaluationItems": "Please set evaluation items", "tr_inspectionTemplateSelection": "Inspection Template Selection", "tr_inspectionTemplate": "Template", "tr_inspectionResult": "Inspection Result", "tr_useTemplate": "Use Template", "tr_gentleReminder": "Gen<PERSON> Reminder", "tr_deviceSelection": "Device Selection", "tr_qualified": "Qualified", "tr_unqualified": "Unqualified", "tr_alreadyTheFirstOne": "Already the first one", "tr_alreadyTheLastOne": "Already the last one", "tr_loadingDataPleaseWait": "Loading data, please wait", "tr_thereAreStillAssessmentItemsWithoutResultsSet": "There are still evaluation items without results set", "tr_selectEvaluationCategory": "Select Evaluation Category", "tr_scoresCanBeOnlyEnteredInNumbers": "Scores can only be entered in numbers", "tr_screenshotFailedPleaseRetry": "Screenshot failed, please retry", "tr_deviceIsNotInPlaybackStateCannotTakeScreenshots": "<PERSON><PERSON> is not in playback state, cannot take screenshots", "tr_deviceIsNotInPlaybackStateCannotChangeSpeed": "Devi<PERSON> is not in playback state, cannot change playback speed", "tr_createEvent": "Create event", "tr_getScore": "Get Score", "tr_scoringRate": "Scoring Rate", "tr_scoringItem": "Scoring Item", "tr_overdue": "Overdue", "tr_inInspection": "In Inspection", "tr_completed": "Completed", "tr_inspectionCompletionTime": "Completion Time", "tr_inspectionCompletion": "Inspection Completion", "tr_inspector": "Inspector", "tr_chooseInspector": "Select Inspector", "tr_inspectionPerson": "Inspector", "tr_pleaseChooseInspectionPerson": "Please select inspection personnel", "tr_inspectionTime": "Time", "tr_uncompleted": "Uncompleted", "tr_setDemandNumChannels": "Set Demand Num Channels", "tr_authorizedNumChannels": "Authorized Num Channels", "tr_usedNumChannels": "Used Num Channels", "tr_inputNumChannels": "How many channels does your NVR need?", "tr_pleaseEnterTheInspectionNameToSearch": "Please enter the inspection name to search", "tr_assessmentCategoryDetail": "Evaluation Category Detail", "tr_cancelAllSelections": "Deselect All", "tr_areYouSureToDeleteThisTemplate": "Are you sure you want to delete this template?", "tr_thereMustBeAtLeastOneEvaluationItem": "There must be at least one evaluation item", "tr_atLeastOneEvaluationClassIsRequired": "At least one evaluation class is required", "tr_pressAgainToExitTheApplication": "Press again to exit the application", "tr_recordingSchedule": "Recording Plan", "tr_upgradePackage": "Upgrade Package", "tr_skyCloudStorage": "Sky Cloud Storage", "tr_startTime": "Start Time", "tr_recordingTime": "Recording Time", "tr_recordingStartTime": "Recording Start Time", "tr_recordingEndTime": "Recording End Time", "tr_recordingPlanStartTime": "Recording Plan Start Time", "tr_recordingPlanEndTime": "Recording Plan End Time", "tr_viewRecordingSchedule": "Video recording", "tr_createCloudStorageRecordingPlan": "Create Cloud Storage Recording Plan", "tr_selectDevice": "Select Device", "tr_remainingPath": "Remaining channels", "tr_overwrite": "Overwrite", "tr_doNotOverwrite": "Do Not Overwrite", "tr_okayIKnow": "Okay, I Know", "tr_openPackage": "Activate Package", "tr_youHaveNotYetOpenedTheCloudStorageRecordingPackageAuthorization": "You have not yet opened the cloud storage recording package authorization", "tr_selectStoragePackage": "Select Storage Package", "tr_cover_record_plan_tip": "Detected that the selected device already has a recording plan. Do you want to overwrite it?", "tr_record_plan_not_enough_tip": "Your cloud storage recording package authorization channels are insufficient. <NAME_EMAIL> to authorize more channels.", "tr_record_plan_upgrade_tip": "<NAME_EMAIL> to upgrade your package.", "tr_running": "Running", "tr_videoCloudBase": "Video Cloud Base", "tr_goToSelectMultipleChoice": "Go to Select (Multiple possible)", "tr_InspectionAnalysis": "Inspection analysis", "tr_skyCloudStorageLoopRecording": "Sky Cloud Storage Loop Recording", "tr_areYouSureToDeleteThisPlan": "Are you sure you want to delete this plan?", "tr_pleaseSelectTheDeviceFirst": "Please select device first", "tr_pleaseSelectTheRecordingPlan": "Please select the Recording plan", "tr_remainingRoad": "Remaining {name} channels", "tr_recordingHasStopped": "Stop recording", "tr_allDay": "All Day", "tr_isTheRecordingPlanTurnedOn": "Enable Recording Plan?", "tr_confirmClosingTheRecordingPlan": "Confirm disabling Recording Plan.", "tr_failedToGetUrlPleaseTryAgain": "Failed to obtain url, please try again", "tr_youDoNotHavePermissionToHandleThisTask": "You do not have permission to process this task", "tr_noProcessingPermissionForTheTimeBeing": "No processing permission", "tr_pendingInspection": "To be inspected", "tr_pendingRectification": "To be rectified", "tr_pendingAcceptance": "To be accepted", "tr_taskIssuanceTime": "Task issue time", "tr_issuanceTime": "Issue time", "tr_processingDeadline": "Processing Deadline", "tr_rectificationDeadline": "Deadline", "tr_pendingLevel": "Pending Priority Level", "tr_general": "General", "tr_minor": "Minor", "tr_severe": "Serious", "tr_taskSubmissionTime": "Task Submission Time", "tr_submissionTime": "Submission Time", "tr_aPatrolTaskHasBeenAssignedToYou": "Assigned an inspection task to you", "tr_submitRectificationTaskToYou": "submitted events", "tr_aRectificationTaskHasBeenAssignedToYou": "Assigned a rectification task to you", "tr_rectificationCompletedLookingForwardToYourAcceptance": "The rectification is complete, awaiting acceptance", "tr_rectificationSubmissionTime": "Rectification Submission Time", "tr_copyTo": "Copy to ", "tr_returnTime": "Return time", "tr_filter": "Filter", "tr_isItOverdue": "Whether to overdue", "tr_eventLevel": "Event level", "tr_notYet": "None", "tr_overdueYet": "Overdue", "tr_rectificationTime": "Rectification time", "tr_problemDescription": "Problem description", "tr_pleaseEnter": "Please enter", "tr_assignedTo": "Assigned to", "tr_ccTo": "Copy to ", "tr_days": "Days", "tr_hours": "Hour", "tr_assignedPersonnel": "Assigned Personnel", "tr_editImage": "Edit Image", "tr_mineRectify": "My Rectify", "tr_mineInitiate": "My Initiated", "tr_mineDuplicate": "Copy to me", "tr_mineAcceptance": "My Acceptance", "tr_EventCenter": "Event center", "tr_currentFlowPackage": "Current Data Package", "tr_flowExpirationTime": "Expiration Time：", "tr_usageRatio": "Usage Ratio", "tr_flowTotal": "Total", "tr_flowUse": "Used", "tr_flowRemaining": "Remaining", "tr_flowRemainingTip": "To renew or expand? <NAME_EMAIL> for renewal!", "tr_flow_statistics": "Data statistics", "tr_accountAuthorization": "Sub Account Authorization", "tr_deviceAuthorization": "Device Authorization", "tr_cloudStorageAuthorization": "Cloud Storage Authorization", "tr_algorithmAuthorization": "Algorithm Authorization", "tr_buy_connect_email_tip": "<NAME_EMAIL> for renewal!", "tr_bcloud_saas_activity": "BcloudSaaS package discount", "tr_buy_saas": "Buy Now", "tr_remaining_flow": "Remaining Data", "tr_authorization_statistics": "Authorization Statistics", "tr_renewal": "Renewal", "tr_maturity": "Expired", "tr_bcloud_saas_shopping_tip": "BcloudSaaS package discount, <NAME_EMAIL> for buy! ", "tr_pleaseEnterText": "Please Enter Text", "tr_pleaseSelectAssignee": "Please select assignee.", "tr_problemDescriptionCannotBeEmpty": "Problem description cannot be empty", "tr_dataNotSavedContinueReturnWillClearData": "The data has not been saved yet. Continuing to return will clear the data. Are you sure you want to return?", "tr_forever": "Permanent", "tr_titleImprovements": "Rectification Progress", "tr_waitingMineRectify": "To be rectified", "tr_waitingMineAcceptance": "Waiting for my acceptance", "tr_rectificationStatus": "Rectification Status", "tr_passed": "Passed", "tr_goRectification": "Go to Rectification", "tr_goAcceptance": "Go to Acceptance", "tr_titleViewPassedItems": "View Passed Items", "tr_rectify": "Rectification", "tr_acceptance": "Acceptance", "tr_acceptancePassed": "Acceptance passed", "tr_beforeRectification": "Before Rectification", "tr_afterRectification": "After Rectification", "tr_mRectificationTasks": " Rectification Tasks", "tr_acceptanceAndSubmissionTip": "Before submitting for acceptance, please determine whether the rectification item has passed.", "tr_submittedSuccessfully": "Submission successful.", "tr_yes": "Yes", "tr_no": "No", "tr_underRectification": "In rectification", "tr_underAcceptance": "In Acceptance", "tr_determineBeforeAcceptanceAndSubmission": "Before submitting for acceptance, please assess whether the rectification item has passed", "tr_titleWorkbenchApplication": "Function", "tr_enableLocationOrGrantLocationPermissions": "Please allow location or grant location permissions.", "tr_imageInspection": "Photo check", "tr_capturePlan": "Capture Plan", "tr_eventStatus": "Event Status", "tr_captureDate": "Capture Date", "tr_imageInspectionPlan": "Capture plan", "tr_addImageInspectionPlan": "Add Image Inspection Plan", "tr_editImageInspectionPlan": "Edit Image Inspection Plan", "tr_planName": "Plan Name", "tr_pleaseEntryPlanName": "Please Enter Plan Name", "tr_executionCycle": "Period", "tr_executionTime": "Time", "tr_pleaseChooseExecutionTime": "Please select Execution Time", "tr_inspectionDevice": "<PERSON><PERSON>", "tr_eventDetails": "Event Details", "tr_rectificationItem": "Rectification Item", "tr_rectificationPerson": "Rectifier", "tr_Common_Workbench": "Workbench", "tr_upcomingTasks": "To Do items", "tr_commonApplications": "Common Functions", "tr_moreApplications": "More features", "tr_addApplications": "Add as common", "tr_workbenchApplicationsConfiguration": "Configuration function", "tr_dragToSort": "Drag to sort", "tr_notSubmitted": "Unsubmitted", "tr_submitted": "Submitted", "tr_submittedEvent": "Submitted Event", "tr_today": "Today", "tr_last3Days": "Last 3 Days", "tr_last7Days": "Last 7 Days", "tr_last30Days": "Last 30 Days", "tr_everyDay": "Everyday", "tr_everyMonthNameDay": "Monthly:{name}", "tr_everyWeekNameDay": "Weekly:{name}", "tr_dayNo": "No.", "tr_selectTemplate": "Select Template", "tr_monday": "Mon", "tr_tuesday": "<PERSON><PERSON>", "tr_wednesday": "Wed", "tr_thursday": "<PERSON><PERSON>", "tr_friday": "<PERSON><PERSON>", "tr_saturday": "Sat", "tr_sunday": "Sun", "tr_yesterday": "Yesterday", "tr_theDayBeforeYesterday": "Day before yesterday", "tr_thisRecordHasNoImage": "This record does not have any images.", "tr_pleaseSelectRectificationItem": "Please Select Rectification Item", "tr_confirmDeletion": "Are you sure to delete?", "tr_subTitlePermission": "Permission", "tr_titleEventAnalysis": "Event analysis", "tr_subDate": "Date", "tr_employeeRanking": "Employee Ranking", "tr_unitTimes": "Unit: times", "tr_unqualifiedCameraRanking": "Unqualified points", "tr_numberInspections": "Number of Inspections", "tr_proportionUnqualifiedInspections": "Proportion of Unqualified Inspections", "tr_eventTrends": "Event Trends", "tr_passedNumberTimes": "Passed Times", "tr_timeOverdueTimes": "Overdue Times", "tr_itemOther": "Other", "tr_problemFound": "Problem Found", "tr_completeRectification": "Complete Rectification", "tr_rectificationOverdue": "Rectification Overdue", "tr_completeAcceptance": "Complete Acceptance", "tr_completeRectificationTimes": "Complete Rectification Times", "tr_rectificationOverdueTimes": "Rectification Overdue Times", "tr_completeAcceptanceTimes": "Complete Acceptance Times", "tr_completeRectificationSort": "Complete Rectification Sort", "tr_rectificationOverdueSort": "Rectification Overdue Sort", "tr_completeAcceptanceSort": "Complete Acceptance Sort", "tr_eventLevelProportion": "Event Level Proportion", "tr_rankingOfCameraIncidents": "Ranking of Camera Incidents", "tr_workbenchHelloTip": "Hello", "tr_workbenchSubTip": "Welcome to use BcloudSaaS App", "tr_workbenchExpireDaysTip": "Platform will expire in {days} days. <NAME_EMAIL> for renewal/expansion.", "tr_platformParsing": "Platform Parsing", "tr_deviceReporting": "Device Reporting", "tr_alarmDescription": "Alarm Description", "tr_alarmTime": "Alarm Time", "tr_deviceTree": "<PERSON>ce Tree", "tr_relatedPerson": "Related Person", "tr_similarity": "Similarity", "tr_video": "Video", "tr_image": "Image", "tr_centralServer": "Central Server", "tr_centralServerPlatform": "Central Server (Platform)", "tr_requestNotificationLocation": "Explanation for Location Permission: Used for accessing precise location information, searching for cameras and other device scenes", "tr_requestNotificationCamera": "Explanation for Camera Permission: Used for taking photos, recording videos, etc.", "tr_requestNotificationPhoto": "Explanation for Storage Card Permission: Used for reading photos, media content, and files on the storage card", "tr_requestNotificationStory": "Explanation for Storage Access Permission: Used for modifying or deleting photos, media content, and files on the storage card", "tr_serverPortNum": "SIP Server Port", "tr_serverActualAccessChannelRate": "Actual Access Channels/Requested Channels", "tr_success": "Success", "tr_failure": "Failure", "tr_exeSync": "Sync", "tr_syncInterceptTitle": "Authorization Intercept Details", "tr_DeviceChannelID": "Channel ID", "tr_time_c": "Times", "tr_passRate": "Pass Rate", "tr_amapTitle": "Emap", "tr_deviceList": "View list", "tr_deviceSum": "Total Devices", "tr_deviceOnLine": "Online Devices", "tr_latitude": "latitude", "tr_longitude": "longitude", "tr_liveStreaming": "Live show", "tr_surrounding": "Surrounding", "tr_aiInspection": "AI Inspection", "tr_notEffective": "Not effective", "tr_paused": "Paused", "tr_expired": "Expired", "tr_configureAlgorithm": "Algorithm", "tr_createAIInspectionPlan": "Create an AI inspection plan", "tr_acceptancePerson": "Acceptor", "tr_associatedAlgorithm": "Correlation algorithm", "tr_pleaseSelectAlgorithm": "Please select algorithm", "tr_selectAlgorithm": "select algorithm", "tr_parameterConfiguration": "Parameter configuration", "tr_AIDetailInspection": "AI Inspection details", "tr_detailInspection": "Inspection details", "tr_AIPlanInspectionEdit": "AI Inspection plan Editor", "tr_searchAlgorithmByName": "Enter the algorithm name to search", "tr_titleDeviceList": "Device list", "tr_noDevice": "No device", "tr_selectAcceptancePerson": "Select the acceptor", "tr_pleaseSelectAcceptancePerson": "Select the acceptor", "tr_selectPerson": "Select", "tr_goToSelect": "Go to select", "tr_belongDevice": "Device to which it belongs", "tr_selectRectificationPerson": "Choose a rectification person", "tr_pleaseSelectRectificationPerson": "Choose a rectification person", "tr_someOptionsUnSelect": "There is not selected item", "tr_Common_Store": "Store", "tr_createStore": "Create a store", "tr_manageStore": "Store management", "tr_totalNameWay": "Total {name} channels", "tr_usedNameWay": "Used {name} channels", "tr_surplusNameWay": "Left {name}channels", "tr_organization": "Organization", "tr_typeNormal": "Standard", "tr_typeSatellite": "Satellite", "tr_chooseAtLeastOneAlgorithm": "Choose at least one algorithm", "tr_devicesInTotal": "Total {quantity} devices", "tr_pleaseEntryPlanNameForSearch": "Please enter the plan name to search", "tr_chooseAtMostNameAlgorithm": "Max select {name} algorithm", "tr_detectExistingInspectionPlan": "It is detected that the inspection plan already has a corresponding planned task, please choose whether to overwrite the original AI inspection plan task?", "tr_setTime": "Time settings", "tr_endTime": "End time", "tr_startTimeMustBeBeforeEndTime": "The start time must be less than the end time", "tr_inspectionProblemDescription": "Inspection problem description", "tr_detectionFrequency": "Detection frequency", "tr_alarmParameters": "Alarm parameters", "tr_confidenceLevel": "Confidence", "tr_duration": "Duration", "tr_resetDraw": "<PERSON><PERSON>", "tr_exitDraw": "Exit", "tr_detectionArea": "Detection area", "tr_queryNoDevicesTip": "No device was found in the area, please redraw the area", "tr_useCase": "Usage", "tr_normal": "Normal", "tr_normalProportion": "Normal proportion", "tr_nameAbnormalRoutes": "{name} channel exception", "tr_algorithmList": "Algorithm list", "tr_searchHitText": "Please enter the store name to search", "tr_delMoreStore": "Are you sure you want to delete the selected store?", "tr_delOneStore": "Are you sure you want to delete the selected store?？", "tr_algorithParamsDoNotPairedYet": "Algorithm parameters are not configured", "tr_algorithParamsHavePaired": "Algorithm parameters are configured", "tr_pairTheParams": "Configuration parameters", "tr_analysisRight": "Normal analysis", "tr_analysisError": "Parse exception", "tr_storeName": "Store name", "tr_storeNameHit": "Please enter store name", "tr_storeAddress": "Store address", "tr_storeAddressHit": "Please enter store address", "tr_asyncStoreLatLng": "Store latitude and longitude are set to the device", "tr_choice": "Go to select", "tr_btnCreateStore": "Set up a store", "tr_analysisStop": "Analysis stopped", "tr_storeDevices": "Store devices", "tr_storeChannelDevices": "Total {channels} devices", "tr_createStoreSuccessTip": "Successfully created a store", "tr_titleDeviceNodeResources": "Device node resources", "tr_rootNode": "Root node", "tr_searchStoreAddress": "Go search for store address", "tr_AIInitiate": "AI initiate", "tr_eventSource": "Source of the event", "tr_rectificationComplete": "Passed", "tr_pendingForAcceptance": "Details pending acceptance", "tr_initiateTime": "Initiation time", "tr_deadlineTime": "Deadline", "tr_initiatePerson": "Sponsor", "tr_chooseInitiatePerson": "Choose a sponsor", "tr_watchRectifyTask": "View rectification tasks", "tr_watchCopyTask": "View CC tasks", "tr_pendingPersonRectification": "“{name}“ to be rectified", "tr_pendingPersonAcceptance": "“{name}“ to be accepted", "tr_personAcceptancePassed": "“{name}“ acceptance has passed", "tr_rectificationInstructions": "Remarks", "tr_acceptanceInstructions": "Acceptance instructions", "tr_moreInstructionsMaxHundred": "Please enter a description, up to 100 words", "tr_autoCertification": "Automatic record retention", "tr_titleAIAlgorithm": "AI algorithm", "tr_algorithmElectricBikeDetection": "Electric vehicle inspection", "tr_algorithmFaceMaskDetection": "Mask detection", "tr_aiInspectionSource": "AI check", "tr_personInspectionSource": "Manual inspection", "tr_hasStopped": "Stopped", "tr_timeFilter": "Time filter", "tr_time": "Time", "tr_pleaseSelectStartTime": "Please select the start time", "tr_pleaseSelectEndTime": "Please select the end time", "tr_searchHistory": "Search history", "tr_searchRegion": "Search area", "tr_searchRegionNoData": "Place name information not searched", "tr_searchRegionNoAddressData": "No nearby address information was searched", "tr_searchStoreAddressNoAddressData": "Please search or locate the store address for location information", "tr_searchStoreAddressAddressInfo": "Please search for store address and location information", "tr_selectDeviceNode": "Please select node", "tr_inputStoreName": "Please enter the store name", "tr_searchDevice": "Search device", "tr_pleaseInputRegionName": "Please enter the region name to search", "tr_pleaseInputDeviceName": "Please enter the device name to search", "tr_pleaseSelectCityRegionName": "Please select the city or region first", "tr_select": "Select", "tr_isQualified": "Whether it is qualified", "tr_inspectionMethod": "Inspection method", "tr_spotCheck": "Spot check", "tr_task": "Task", "tr_chooseAtMostNamePerson": "Choose at most {name} person", "tr_applicationClassification": "Application classification", "tr_AppName": "BcloudSaaS", "tr_SpotCheckInspection": "Video check", "tr_envSwitching": "Environment switching", "tr_envTest": "Test", "tr_envTestB": "Test B", "tr_envPreRelease": "Advance", "tr_envRelease": "formal", "tr_taskName": "Task name", "tr_associatedTemplate": "Association template", "tr_inspectionScope": "Scope of inspection", "tr_notOverdueYet": "Not overdue", "tr_inProgress": "Running", "tr_inspectionStatus": "Inspection status", "tr_algorithmName": "Algorithm name", "tr_inputApplicationName": "Please enter the application name", "tr_appPatrolIndex": "Inspection", "tr_appVideoCloudBase": "Video base", "tr_appSmartCloudStore": "Cloud store", "tr_appAlgorithmCenter": "Algorithm", "tr_appEnterpriseManagement": "Enterprise", "tr_appAccessAuthority": "Permission", "tr_appDealCenter": "To Do items", "tr_appSmartStore": "Store list", "tr_appAlgorithmConfig": "Set algorithm", "tr_appAlgorithmList": "Cloud algorithm", "tr_appDeviceSideAlgorithm": "Device-side algorithm", "tr_appFacialManagement": "Face database", "tr_appVideoSurveillance": "Video", "tr_appVideoRecord": "Video recording", "tr_appDevicesTree": "Device management", "tr_appPatrolIndexFlow": "Inspection process", "tr_levelSetEvaluationTitle": "Set up an evaluation template", "tr_levelInspectionTitle": "Inspection", "tr_levelInspectionDataTitle": "Inspection data", "tr_levelEventTitle": "Event data", "tr_addDevicesChannelTips": "The device channel is authorized {maxChannel} channels，remain {remainChannel}channels", "tr_taskExecutionTime": "Task execution time", "tr_taskExecutionMaximum": "Can be set up to {max}", "tr_existed": "Already exists", "tr_pleaseSetExecutionTime": "Please set an execution time", "tr_addTime": "Add time {time}", "tr_errorConvertingImage": "Error converting image", "tr_triangle": "Triangle", "tr_quadrilateral": "Quadrilateral", "tr_pentagon": "Pentagonal", "tr_hexagon": "Hexagon", "tr_deviceHasBeenDeleted": "<PERSON>ce deleted", "tr_hasBeenDeleted": "Deleted", "tr_incompleteDeviceInfo": "Imperfect device information", "tr_lackNecessaryDeviceInfo": "Lack of necessary device information", "tr_accountAddInfoTips": "The current device has been added by the current account or other accounts, please do not add it repeatedly.", "tr_channelUnreasonable": "The number of input channels is unreasonable, please re-enter", "tr_channelsInsufficient": "Your device channel authorization number is insufficient", "tr_setUp": "Set up", "tr_noInputParameter": "No input parameters", "tr_china": "China", "tr_NormalMessage": "Message", "tr_chooseDeviceMaxCount": "Max can select {deviceMaxCount} resources", "tr_choseDeviceCount": "Selected resources", "tr_choseDeviceResource": "Selected resources: ", "tr_channelsInsufficientRet": "The remaining number of channels in the package is insufficient, please contact the after-sales staff to expand.", "tr_insufficientAuthorization": "Insufficient authorization", "tr_insufficientChannels": "Insufficient application", "tr_insufficientApplicationChannels": "Insufficient application", "tr_numberChannel": "Channel No：", "tr_chooseSyncChannels": "Please select the channel that needs synchronization authorization", "tr_hasNoAuthorization": "No permission to operate", "tr_channelNum": "Number of channels", "tr_detail": "Detail", "tr_InitiatePersonRectificationTasks": "“{name}”'s rectification tasks", "tr_lastWeek": "Last week", "tr_thisWeek": "This week", "tr_thisMonth": "This month", "tr_lastMonth": "Last month", "tr_personInitiate": "Manually initiated", "tr_startDate": "Start date", "tr_endDate": "End date", "tr_eventInitiateType": "Event initiation type", "tr_allPerson": "All personnel", "tr_everyMonth": "Every month", "tr_everyWeek": "Every week", "tr_numberFailures": "Number of nonconformities", "tr_proportion": "Proportion", "tr_frequency": "Number of times", "tr_inspectionCategory": "Belongs to the inspection type", "tr_totalNumberFailures": "Total number of nonconformities", "tr_proportionValue": "proportion", "tr_highFrequencyQuestion": "High frequency problem", "tr_numberHighFrequencyQuestion": "High frequency issues (freq.）", "tr_chooseInspectionDate": "Please select the inspection date", "tr_taskExecutionDate": "Task execution date", "tr_chooseInspectionPeriodDate": "Please select {period} inspection date", "tr_ranking": "ranking", "tr_overdueRate": "Overdue rate", "tr_fraction": "score", "tr_completionTime": "Completion time", "tr_plannedInspection": "Planned inspection", "tr_inspectionPlanSettings": "Inspection plan setting", "tr_planValidityPeriod": "Plan validity period", "tr_inspected": "Checked", "tr_processor": "Handler", "tr_pleaseSelectInspectionTemplate": "Please select the inspection template", "tr_pleaseSelectInspectionDevice": "Please select the inspection device", "tr_createInspectionPlan": "Create inspection plan", "tr_inspectionPlanDetail": "Inspection plan details", "tr_validityPeriod": "Validity", "tr_selectMultipleChoices": "Go to select (Multiple choices available)", "tr_optionalFields": "Not required", "tr_inputValidityPeriod": "Input time limit", "tr_processingTime": "Processing time", "tr_makeCopy": "CC", "tr_DeviceStateChangeOnline": "Online", "tr_DeviceStateChangeOffline": "Offline", "tr_aiAlarm": "AI Alarm", "tr_deviceAlarm": "Device alarm", "tr_viewUserInfoOperation": "View and manage user information and related operations", "tr_viewUserRoleInfoOperation": "View management role authority information and related operations", "tr_viewDepartmentInfoOperation": "View department structure and related operations", "tr_memberManagerDesc": "View platform and non platform members info,relevant operations", "tr_platform": "Platform", "tr_noPresetPoint": "No preset point", "tr_play": "Play", "tr_year": "Year", "tr_month": "Month", "tr_day": "Date", "tr_datePicker": "Select date", "tr_stopTime": "End time", "tr_morning": "Morning", "tr_afternoon": "Afternoon", "tr_cannotLaterStopTime": "The start time cannot be later than the end time", "tr_maximumTimeSupport": "Time supports up to {day} days", "tr_refreshHeaderReleaseText": "Release your hand to refresh", "tr_refreshHeaderRefreshingText": "Refreshing", "tr_refreshHeaderCompleteText": "Refresh is complete", "tr_refreshHeaderFailedText": "Refresh failed", "tr_refreshHeaderIdleText": "Drop down to refresh", "tr_refreshFooterLoadingText": "Loading...", "tr_refreshFooterNoDataText": "No more", "tr_refreshFooterFailedText": "Loading failed", "tr_refreshFooterIdleText": "Pull-up loading", "tr_refreshFooterCanLoadingText": "Release to start loading data", "tr_insufficientAuthorizationRoutes": "Insufficient number of authorized channels", "tr_deviceIsOffline": "The device is offline", "tr_dasProtocol": "DAS protocol", "tr_notSupported": "Not supported", "tr_support": "Support", "tr_psdKeyVerify": "Key verification", "tr_deviceDetails": "Device details", "tr_deviceAndEvidence": "Device and records", "tr_autoEvidenceTime": "Automatic retention of recording time", "tr_setDetectionFrequency": "Set the detection frequency", "tr_searchDeviceNoData": "No corresponding device information was searched", "tr_faceDatabase": "Face library", "tr_searchFaceHitText": "Please enter username/phone to search", "tr_addFacialUser": "Add a new user", "tr_shakeForInspection": "Shake check", "tr_shake": "Shake", "tr_platformUser": "Platform users", "tr_facialImage": "Face image", "tr_switchPlatform": "Switch the platform", "tr_switchPlatformTip": "The following are non-platform users, you can switch it to platform users", "tr_delChooseUserInfoTip": "Are you sure you want to delete the selected user?", "tr_commonPhone": "Phone number", "tr_pleaseInputPhone": "Please enter phone number", "tr_commonEmail": "Email", "tr_pleaseInputEmail": "Please enter email", "tr_userPermissionConfig": "User permission configuration", "tr_pleaseInputTwicePsd": "Please enter the password again", "tr_PersonDetailEdit": "Edit the user details", "tr_choseDepartmentResource": "Selected department", "tr_shakeToSearchMonitoring": "Shake it to search for the camera", "tr_searchingMonitoringDevicePleaseWait": "Searching for camera, please wait a moment...", "tr_passengerFlowStatistics": "Passenger flow statistics", "tr_passengerFlowPeerConfiguration": "Settings", "tr_passengerFlowStatisticsData": "Passenger flow statistics data", "tr_thisYear": "This year", "tr_dataOverview": "Data Overview", "tr_storeConversionRate": "In store conversion rate", "tr_storeEntryRate": "Store entry rate", "tr_totalStoreEntryRateProportion": "The proportion of total store entry rate", "tr_yearOnYear": "YoY", "tr_chainRatio": "MoM", "tr_storeTraffic": "Inbound customer flow", "tr_passByTraffic": "Customer flow through the store", "tr_outStoreTraffic": "Outbound customer flow", "tr_totalTraffic": "Total passenger flow", "tr_pleaseChooseRole": "Please select a role", "tr_pleaseChooseDevice": "Please select a device", "tr_userType": "User type", "tr_userTypeNotPlatformTip": "Non platform users (for facial database management only)", "tr_userTypePlatformTip": "Platform users (for facial database management only)", "tr_notPlatformUser": "Non platform users", "tr_departmentStructure": "Organization", "tr_pleaseChooseDepartment": "Please select department", "tr_uploadFaceImage": "Upload Face", "tr_uploadFaceImageBtn": "Click to take photos/upload", "tr_switchPlatformSuccessfully": "Successfully transferred to platform", "tr_pleaseUploadFaceImage": "Please take or upload a facial photo", "tr_pleaseChooseUserType": "Please select user type", "tr_checkEmailNotValid": "Incorrect email format", "tr_entryPeak": "Peak in store", "tr_entryTrough": "Valley in store", "tr_deviceIdIsEmpty": "Device ID is empty", "tr_versionUpdateFindLatestVersion": "Discovering new versions", "tr_versionUpdateLatestDownload": "Updated version", "tr_versionUpdateVersionLatestTip": "It's the latest version", "tr_person": "person", "tr_storeTrafficStatistics": "Store passenger flow statistics", "tr_showLanguage": "Display language", "tr_languageSimpleChinese": "Simple Chinese", "tr_languageUSEnglish": "English", "tr_setAMapParam": "Map settings", "tr_setSystemPermission": "System permissions", "tr_versionUpdateContent": "Update content", "tr_versionUpdateCancel": "Do not upgrade", "tr_versionUpdateConfirm": "upgrade now", "tr_flowStatisticsConfigTip": "1. The camera is recommended to be slightly larger than a 45 ° overhead viewing angle, facing outside the store.\n2. The selected area is the outside area of the store, and the inside area on the arrow side is the inside area of the store.\n3. The selection area can be dragged to adjust its size and position.", "tr_phoneLocation": "Follow mobile positioning", "tr_permissionLocationTitle": "Accessing location information", "tr_permissionLocationSubtitle": "Used for real-time positioning and obtaining WiFi information during device networking", "tr_permissionStorageTitle": "Accessing storage", "tr_permissionStorageSubtitle": "Used for saving device side images, videos, and other functions", "tr_permissionCameraTitle": "Access Camera", "tr_permissionCameraSubtitle": "Used for taking photos,scanning QR codes and other functions", "tr_permissionAudioTitle": "Access microphone", "tr_permissionAudioSubtitle": "Used for device voice intercom and other functions", "tr_Permission_Audio": "Please allow access to the microphone!", "tr_refreshText": "Refresh", "tr_searchNoAddressData": "Please search or locate to obtain location information", "tr_verifyCodeLogin": "Verification code login", "tr_changeLanguage": "Language switching", "tr_confirmChangeLanguage": "Are you sure to switch to:", "tr_IPC": "IPC", "tr_NVR": "NVR", "tr_DVR": "DVR", "tr_deviceSource": "Device source", "tr_peakCloudDirectConnection": "Bcloud connection", "tr_longPressToShowDetails": "Long press to display details", "tr_faceSimilarity": "Facial similarity", "tr_associatedFace": "Associate Face", "tr_allFacesUnderDefaultAccount": "All faces under default account", "tr_chooseAtMostNameDepartment": "Select a maximum of {name} departments", "tr_deviceConfigNoLatLng": "The device has not set coordinates", "tr_getNoLatLng": "No coordinates obtained", "tr_noStoragePermissionPleaseRetryAfterOpening": "No storage permission, please enable and try again", "openPhotoPermissionTip": "Please open album access permissions in system settings", "openCameraPermissionTip": "Please open camera access permissions in system settings", "openStoragePermissionTip": "Please open storage permissions in system settings", "openLocationPermissionTip1": "Please open location permissions in system settings", "openLocationPermissionTip": "Please grant location permission to read the currently connected WiFi, and enable location permission in the system settings. If not authorized, please manually enter", "tr_chooseDeptMaxCount": "Up to {maxCount} departments can be selected", "tr_storeHasNoPassengerFlowCameraPleaseAddFirst": "The store does not have a passenger flow statistics camera. Please add a passenger flow statistics camera first.", "tr_passengerFlowDataInformationClearsEveryDayAt24": "Passenger flow data information is cleared to zero at 24:00 every day", "tr_flowDeviceTip": "Only perform data analysis on devices that support customer flow statistics under the store. Devices that do not support passenger flow statistics will not be counted.", "tr_allSources": "All sources", "tr_allAlarms": "All alarms", "tr_imageStyleTip": "Support extensions: jpeg, jpg, bmp formats;\nImage limit: 10MB;\nSuggested resolution: 626 * 413 (2-inch standard ID photo)", "tr_dioException_cancel": "Request has been canceled, please request again", "tr_dioException_timeout": "Connection timed out", "tr_dioException_unknown": "Network unknown, please check the network request", "tr_dioException_connectionError": "Network is not connected, please check the network", "tr_dioException_badCertificate": "Wrong certificate, please recheck the certificate", "tr_dioException_badResponse400": "Bad Request", "tr_dioException_badResponse401": "Unauthorized", "tr_dioException_badResponse403": "Forbidden", "tr_dioException_badResponse404": "Not Found", "tr_dioException_badResponse405": "Request Method Not Allowed", "tr_dioException_badResponse500": "Internal Server Error", "tr_dioException_badResponse502": "Bad Gateway", "tr_dioException_badResponse503": "Service Unavailable", "tr_dioException_badResponse504": "Gateway Timeout", "tr_dioException_badResponse505": "HTTP Version Not Supported", "tr_pleaseSelectDepartmentToAssociateFaces": "Please select the department to which the face needs to be associated", "tr_errorMixFormatPassword": "Please enter 8-16 digits including numbers, letters and special characters", "tr_passwordManage": "Password management", "tr_pwdConfig": "Password setting", "tr_setPwdConfig": "Set password", "tr_cancellationAccountAgreement": "Account cancellation agreement", "tr_setDuration": "Set duration", "tr_type": "Type", "tr_rectificationScope": "Rectification scope", "tr_tryCaptureAction": "Capture again", "tr_failPassed": "Fail", "tr_successPassed": "Pass", "tr_goPassedItems": "Acceptance items", "tr_deviceInspectionAnalysis": "Device inspection analysis", "tr_storeInspectionAnalysis": "Store inspection analysis", "tr_delMoreAiAlarmMsg": "Are you sure you want to delete the selected AI alarm messages？", "tr_delOneAiAlarmMsg": "Are you sure you want to delete one selected AI alarm message？", "tr_storeQuestionRanking": "Ranking of problems found in stores", "tr_playbackFailedPleaseRetry": "Playback failed please retry", "tr_numberQuestions": "Number of questions", "tr_problemChain": "Problem chain", "tr_selectedTime": "Selected time", "tr_selectStores": "Please select stores", "tr_titleSelectStore": "Select store", "tr_selectDeviceNodeResources": "Please select the device node", "tr_videoWatermark": "Video Watermark", "tr_watermarkSettings": "Watermark Settings", "tr_watermarkSettingsTip": "Once activated, video playback, download, and screenshot will all carry a watermark.", "tr_exemptFromWatermarkWhitelist": "Exempt From Watermark list", "tr_watermarkContent": "Watermark content", "tr_watermarkContentTip": "Echo back the custom watermark content", "tr_customContent": "Custom Content", "tr_notYetSetCustomWatermarkContentAreYouSureToReturn": "The custom watermark content has not been set yet, are you sure you want to go back?", "tr_namePlusAccount": "Name Plus Account", "tr_safetyManagement": "Safety management", "tr_algorithmConfigDescription": "Related descriptions about algorithm configuration", "tr_alarmSetting": "Alarm setting", "tr_delMoreDeviceAlarmMsg": "Are you sure you want to delete the selected device alarm messages？", "tr_delOneDeviceAlarmMsg": "Are you sure you want to delete one selected device alarm message？", "tr_alarmAIEdit": "AI alarm edit", "tr_alarmDeviceEdit": "Device alarm edit", "tr_permissionNotificationTitle": "Notification management permission", "tr_permissionNotificationSubtitle": "Used for APP to send message push notifications", "tr_confidence_tip": "The lower the confidence level, the lower the accuracy of recognition and the more results being recognized.\nThe higher the confidence level, the fewer results being recognized, but the accuracy of recognition is higher.", "tr_confidence_tip_1": "\t1.\tThe smaller the value, the stricter the distinction between individuals; the current recommended threshold is 6.8%.\n2.\tFrequent threshold adjustments within a day can affect deduplication effectiveness under different thresholds.\n3.\tIt is not recommended to adjust the threshold frequently. If adjustment is necessary, try to do it only once a day.", "tr_duration_tip": "An alarm is generated only when the detection behavior time exceeds the duration", "tr_tapToAdd": "Tap to add", "tr_storeEventRanking": "Store event ranking", "tr_storeEventCount": "Event count", "tr_storeOverdueCount": "Overdue count", "tr_userHasBeenDeleted": "User has been deleted", "tr_playerError": "Player error", "tr_playbackCompleted": "Playback completed", "tr_watermarkContentCanNotBeEmpty": "Watermark content can not be empty", "tr_retrying": "Retrying...", "tr_retryingNameCount": "The {name} times retry is in progress", "tr_storeAcceptanceCount": "Acceptance count", "tr_arithmeticChannelNotEnoughTip": "“{name}”arithmetic，channel insufficient", "tr_insufficientTip": "contact <EMAIL> to extend", "tr_overdueTimes": "Overdue times", "tr_canNotEditAreaTip": "The equipment is malfunctioning, unable to save the region.", "tr_aiPlatformAlarm": "AI platform alarm", "tr_aiDeviceAlarm": "AI device alarm", "tr_permissionStatement": "Permission statement", "tr_addBatches": "Add in batches", "tr_lANEquipment": "LAN equipment", "tr_addBatchesDevices": "Add devices in batches", "tr_addDiscoveredBatchesDevices": "Add devices discovered by LAN in batches", "tr_waitDiscoveredDevices": "Scan for available devices to perform the add operation", "tr_deviceAdded": "<PERSON><PERSON> added", "tr_inputDevicePsd": "Please enter device password", "tr_psdVerificationFail": "Password verification failed", "tr_addDeviceDialogTip1": "If you forget your password, please restore the factory settings and add it again!", "tr_addDeviceDialogTip2": "The device has been powered on for more than 1 hour and cannot log in to the device. Please restore the factory settings and add it again!", "tr_ensure": "Confirm", "tr_returnHomePage": "Return to home page", "tr_psdVerification": "Password verification", "tr_addException": "Add exception", "tr_thisCompanyAddExceptions": "The device has been added by this company, please contact the administrator to confirm. If necessary, you can contact the administrator to assign the device to you.", "tr_otherCompanyAddExceptions": "The device has been added by another company. If it is your device, please contact customer service.", "tr_addFailed": "Add failed", "tr_deviceNotFound": "The device cannot be found, please reset the device and add it again.", "tr_stopNetConfigure": "Currently, the network is being configured quickly and scanned. Are you sure you want to interrupt the network configuration?", "tr_twiceAddDevice": "Keep adding", "tr_extendRole": "extend role", "tr_newRole": "new role", "tr_nationalStandardDeviceAccess": "Add GB28181", "tr_nationalStandardAccessManagement": "Access ID", "tr_deviceStateRegister": "Registered", "tr_authorizeIntercept": "Authorized interception", "tr_accessStatus": "Access status", "tr_onlineStatus": "Online status", "tr_allStatus": "All status", "tr_allDevices": "All devices", "tr_sceneInspection": "Scene inspection", "tr_beginSceneInspection": "Begin scene inspection", "tr_inspectionConfirm": "Inspection confirm", "tr_bandwidthStatistics": "Bandwidth statistics", "tr_realTimeBandwidth": "Real time bandwidth", "tr_bandwidthCap": "Bandwidth cap", "tr_clearAllMessage": "Clear all messages", "tr_clearAllMessageTip": "Please enter the login password of this account to verify and delete", "tr_clearMessageContent": "Are you sure you want to delete all alarm messages?", "tr_pleaseInputNameForSearch": "Please input name for search", "tr_OnSiteInspectionDetail": "Scene inspection detail", "tr_inspectionAddress": "Inspection address", "tr_signInImage": "Sign in image", "tr_inspectionNode": "Inspection node", "tr_currentLocation": "Current location", "tr_takePhotoForSignin": "Take photo for sign in", "tr_takePhotoForSigninTip": "Please take scene photo for sign in", "tr_commitSceneInspectionResult": "Commit scene inspection result", "tr_turnOn": "Turn on", "tr_turnOff": "Turn off", "tr_locating": "Locating", "tr_qualifiedItem": "Qualified item", "tr_unqualifiedItem": "Unqualified item", "tr_remark": "Remark", "tr_alarmSwitch": "Message reception", "tr_alarmSwitchTip": "After closing, the mobile phone will not be able to receive alarm push messages.", "tr_exitLoginTip": "Your account has been logged in on other devices. If it was not your operation, please log in again and change your password.", "tr_exitKnow": "Knew", "tr_taskCreationTime": "Task creation time", "tr_failedToCreateOnsiteInspection": "Failed to create onsite inspection", "tr_imageRequiredForNonCompliance": "Image required for non compliance", "tr_emptyImage": "Empty image", "tr_continueInspectionOutsideStoreRange": "Continue inspection outside storeRange", "tr_outOfRange": "Out of range", "tr_patrolStoreDetails": "Patrol store details", "tr_overallScore": "Overall score", "tr_pageStay": "Page stay", "tr_signInInfo": "Sign in info", "tr_signOutInfo": "Sign out info", "tr_gettingVideoStream": "Getting video stream", "tr_noAvailableDevices": "No available devices", "tr_location": "Location", "tr_deviceTag": "Device tag", "tr_inspectionDeviceOfflineOrDeleted": "Inspection device offline or deleted", "tr_accessInfo": "Access information", "tr_locationInfo": "Location information", "tr_deviceCapacity": "Device capacity", "tr_channelInfo": "Channel information", "tr_abnormalDevice": "Abnormal device", "tr_abnormalDeviceTip": "The following are abnormal devices, offline or unable to open video, and do not participate in inspection operations", "tr_resetFlowInfo": "Passenger flow data information is cleared at 24:00 every day", "tr_deviceSideFlowTip": "Real-time statistics of the number of people entering and exiting the store based on the target area", "tr_fullScreen": "Full screen", "tr_switchingDevicesPleaseWait": "Switching devices please wait", "tr_reason": "Reason", "tr_deviceExceptionDuringDetectionNotParticipatingInInspection": "Device exception during detection not participating in inspection", "tr_creator": "Creator", "tr_chooseCreator": "Choose creator", "tr_createOnsiteInspection": "Create onsite inspection", "tr_createImageInspectionPlan": "Create image inspection plan", "tr_resetNode": "Reset node", "tr_flowExpired": "(Expired)", "tr_detailUuid": "Serial number", "tr_inputChannels": "Please enter the number of channels", "tr_notSupportPassengerFlow": "There is no device that supports passenger flow counting", "tr_invalidSVData": "Invalid sn, please scan the correct QR code", "tr_deviceOffline": "Device offline", "tr_allNode": "All nodes", "tr_enterInspectionItemNameToSearch": "Enter inspection item name to search", "tr_uploadingImagePleaseWait": "Uploading image please wait", "tr_videoPlayAddress": "Video play address", "tr_downloadList": "Download list", "tr_downloadManagement": "Download management", "tr_downloading": "Downloading", "tr_allStores": "All stores", "tr_viewDownloadList": "View download list", "tr_videoDuration": "Video duration", "tr_playAddress": "Play address", "tr_playAddressRtsp": "RTSP play address", "tr_playAddressFlv": "FLV play address", "tr_playAddressHls": "HLS play address", "tr_playAddressRtmp": "RTMP play address", "tr_playAddressExpiration": "Expiration", "tr_watchConcurrency": "Concurrent viewing", "tr_generatePlayAddress": "Generate play address", "tr_startDownloadTip": "Download task added successfully, please view in download manager", "tr_ensureDeviceOnline": "Please confirm whether the device is online", "tr_InputFrequency": "Please enter the number of concurrent viewers", "tr_checkFrequency": "The number of concurrent viewers is unreasonable", "tr_list": "List", "tr_alarmType": "Alarm type", "tr_nodeInfo": "Node Information", "tr_targetInformation": "Target Information", "tr_detectionBox": "Detection box", "tr_targetBox": "Target box", "tr_endSidePassengerBox": "End-side passenger flow detection frame", "tr_taskExists": "The task has already been assigned, there's no need for duplication.", "tr_cancelTaskTip": "This action will cancel the ongoing download (requiring it to start from the beginning). Do you wish to continue?", "tr_downloadCanceled": "Download canceled.", "tr_getDownloadLinkFailed": "Failed to retrieve the download link.", "tr_transcodingInProgress": "Transcoding in progress.", "tr_deleteFailed": "Delete failed", "tr_deleteFailedDownloadingTask": "Delete failed downloading task", "tr_stopFailedDownloadingTask": "Stop failed downloading task", "tr_partialDeleteFailedTasks": "Partial delete failed tasks", "tr_maximumNameTasksDownloadLimit": "Maximum {name} tasks download limit", "tr_downloadChannelOccupied": "Download channel occupied", "tr_replayChannelOccupied": "Replay channel occupied", "tr_localPlaybackDownloadConflict": "Local playback preview and download cannot be performed simultaneously on the same device. Continuing playback will pause the download.", "tr_deviceInUseByAnotherUser": "This device is currently being used for preview or download by another user. Please try again later.", "tr_pleaseExitReplayPageAndRetry": "Please exit the playback page and try again.", "tr_continue": "Continue", "tr_noVideosToDownloadAdjustRegion": "No downloadable videos available. Please adjust the download area.", "tr_channelOccupied": "Channel occupied", "tr_customTime": "Custom time", "tr_allDayRunning": "All day running", "tr_customTimePeriod": "Custom time period", "tr_setUpToNameTimePeriods": "Set up to {name} time periods", "tr_overlapWithExistingTimePeriods": "Overlap with wxisting time periods", "tr_Permission_Bluetooth": "Bluetooth", "tr_trafficExcess": "Traffic excess", "tr_runtime": "Runtime", "tr_defaultAllDayRunning": "Set to run continuously by default.", "tr_supportedCentralAlgorithms": "Supported core algorithms", "tr_algorithmInvocationStatistics": "Algorithm invocation statistics", "tr_customerSegmentationAnalysis": "Customer group analysis", "tr_ageGroupDistribution": "Age group proportion", "tr_genderDistribution": "Gender ratio", "tr_malePercentageName": "Male percentage:{name}%", "tr_femalePercentageName": "Female percentage:{name}%", "tr_algorithmStatistics": "Algorithm statistics", "tr_AIInspectionStatistics": "AI inspection statistics", "tr_children": "Children", "tr_youth": "Youth", "tr_youthMiddleAged": "Young and middle-aged", "tr_middleAged": "MiddleAged", "tr_elderly": "Elderly", "tr_supportedEdgeAlgorithms": "Supported edge algorithms", "tr_btEquipment": "Bluetooth devices", "tr_enterStoreBatch": "Enter store batch", "tr_enterStoreBatchInterval": "Enter store batch interval", "tr_pleaseEnterContinuousDetectionCount": "Please enter continuous detection count", "tr_btConnectNetwork": "Device connected to wireless network", "tr_tr_btConnectNetworkConfiguration": "The connection process may take 1-2 minutes, please wait for a moment.", "tr_tr_btConnectNetworkConfigurationTip": "Keep routers, mobile phones and devices as close as possible...", "tr_CheckAddBTDeviceFailed": "Device verification failed, please confirm on the device. If you need to continue adding, reset the new device and try again.", "tr_alarmCallCount": "Alarm call count", "tr_alarmGeneratedCount": "Alarm generated count", "tr_inspectionCallCount": "Inspection call count", "tr_inspectionAlarmCount": "Inspection alarm count", "tr_totalCallCount": "Total call count", "tr_totalAlarmCount": "Total alarm count", "tr_btConnectNetworkConfiguration": "The connection process may take 1-2 minutes, please wait for a moment.", "tr_btConnectNetworkConfigurationTip": "Keep routers, mobile phones and devices as close as possible...", "tr_stopBTNetConfigure": "Are you sure you want to terminate Bluetooth pairing?", "tr_retAddDeviceFailed": "1. Please plug in the power and data cables to ensure that the device is powered on and running normally;\n\n2. Please keep the device, mobile phone and router as close as possible (within 1 meter) to ensure successful signal reception;\n\n3. Please make sure that the router account and password you enter are correct;\n\n4. You can press the SET/RESET button on the device to restore the device to factory settings and then reconnect to the network.", "tr_retryBTConnectFailed": "Unable to connect to Bluetooth device, please try again!", "tr_retry": "Retry", "tr_failConnectNetwork": "Networking issues", "tr_algorithmAndDevice": "Algorithm and device", "tr_automaticallyInitiateRectificationEvent": "Automatically initiate rectification event", "tr_aiInspectionPlan": "AI inspection plan", "tr_pleaseSelectAlgorithmAndDeviceFirst": "Please select algorithm and device first", "tr_passScore": "Passing score", "tr_deleteFromCurrentPlan": "Delete from current plan", "tr_deleteFromOriginalPlan": "Delete from original plan", "tr_arithmeticOverTip": "The aforementioned equipment and algorithms overlap with those in other plans. You may remove them from the current plan or directly delete them from the original plan.", "tr_deviceAndAlgorithmConfiguration": "Device and algorithm configuration", "tr_exitBTDistributeFail": "Wrong password", "tr_exitBTDistributeFailSub": "The password is wrong. Please reset the device and restore it to factory settings before adding it again.", "tr_openBTDevice": "Please turn on Bluetooth to search for the device", "tr_durationEqualsContinuousDetectionCountTimesDetectionFrequency": "The duration is equal to the number of consecutive detections multiplied by the detection frequency.", "tr_detectionFrequencySmartScheduling": "The detection frequency is intelligently scheduled.", "tr_whenTriggerCondition": "When (trigger condition) is met.", "tr_continuousDetection": "Continuous detection", "tr_timesNoWarning": "No warnings", "tr_increaseDetectionFrequency": "Increase detection frequency.", "tr_detectionFrequencyUpperLimit": "The maximum detection frequency is", "tr_thenExecuteAction": "Just (perform the action)", "tr_afterAlarmContinueDetectionFrequencyReturnsToDefault": "After an alarm is triggered, continue detection (with the detection frequency returning to the default).", "tr_afterAlarmCurrentRunningTimeNoDetection": "After an alarm is triggered, do not perform detection during the current runtime.", "tr_afterAlarmNoDetectionForRestOfDay": "After an alarm is triggered, do not perform detection for the rest of the day.", "tr_delSelectedAiInspection": "Are you sure you want to delete the selected inspection plan?", "tr_deviceAndAlgorithm": "Device and algorithms", "tr_notStart": "Not started", "tr_dayMark": "", "tr_thisDeviceHasNoConfiguredAlgorithm": "No algorithms are currently configured for this device.", "tr_detectionFrequencyRangeIs": "Detection frequency range is：", "tr_detectionFrequencyUpperLimitRangeIs": "Detection frequency upper limit range is：", "tr_configurationNotSavedDoYouWantToReturn": "Configuration not saved do you want to return?", "tr_resetDeviceNode": "Reset default", "tr_exitBTConnectTimeout": "Connection timed out", "tr_exitBTConnectTimeoutSub": "Connection timed out, please reset the device, restore factory settings and add it again", "tr_pleaseEntryProblemDescribe": "Please entry problem describe", "tr_captureRecord": "Capture record", "tr_pleaseSelectPlan": "Please select plan", "tr_someDevicesHaveNoConfiguredAlgorithm": "Some devices have no configured algorithm", "tr_passFail": "Failed", "tr_ErrorCode_Minus_4101": "The device is temporarily unable to play", "tr_appHumanoidLibrary": "Humanoid library", "tr_searchNameHitText": "Please enter the name to search", "tr_gatewayType": "Gateway type", "tr_gatewaySN": "Gateway serial number", "tr_gatewayStatus": "Gateway Status", "tr_pcGateway": "PC Gateway", "tr_serverGateway": "Server Gateway", "tr_nodeAddRefuseMessage": "NVR, platform, nodes cannot be set under ONVIF, and devices cannot be added", "tr_allAlgorithms": "All algorithms", "tr_callStatistics": "Call statistics", "tr_usedAlgorithms": "Used algorithms", "tr_singlePerson": "Single person", "tr_multiplePeople": "Multiple people", "tr_twoPeople": "Two people", "tr_address": "Address", "tr_storeManager": "Store Manager", "tr_setStoreManagerHit": "Please select Set Store Manager", "tr_nearbyLocation": "Nearby location", "tr_nearbyLocationNoData": "No relevant nearby location information was found", "tr_noAlgorithmData": "No algorithm data", "tr_callCount": "Call count", "tr_alarmCount": "Alarm count", "tr_msgCall": "Msg call", "tr_msgAlarm": "Msg alarm", "tr_inspectionCall": "Inspection call", "tr_inspectionAlarm": "Inspection alarm", "tr_statisticsTime": "Statistics time", "tr_rectificationEvent": "Rectification event", "tr_acceptanceEvent": "Acceptance event", "tr_expansionChannel": "Expansion", "tr_channelStartStop": "Start/Stop", "tr_channelStartStopTitle": "Channel start and stop", "tr_deactivate": "Deactivate", "tr_enable": "Enable", "tr_channelStartStopTip": "You can choose to disable the channel. After deactivation, the device will not be displayed in the device list.", "tr_channelStatusStop": "Terminated", "tr_channelStatusStart": "Used", "tr_sixSNCode": "6-digit serial number", "tr_inputSixSNCode": "Please enter the 6-digit serial number", "tr_sixSNCodeTips": "Length 6 digits, consisting of numbers", "tr_cloudTraffic": "Cloud traffic", "tr_deviceTraffic": "Device traffic", "tr_storeFlow": "Store flow", "tr_pleaseSaveCustomAreaFirst": "Please save custom area first", "tr_modifyStore": "Store Information", "tr_onlyCloudStatisticsTip": "Only perform data statistics on devices that support cloud-based traffic counting for the selected stores.", "tr_onlyDeviceStatisticsTip": "Only perform data statistics on devices that support device-end traffic counting for the selected stores.", "tr_setNoHit": "Not set yet", "tr_protocolChooseTip": "When the network is poor, prioritize TCP", "tr_visibleRangeDept": "Visible Department", "tr_chooseVisibleRangeDept": "Select Visible Departments", "tr_pleaseSelectDept": "Please select departments first", "tr_visibleDeptTip": "The above are the departments to which they belong. The departments to which they belong are visible departments by default.", "tr_switchingStreamPleaseWait": "Switching the main and auxiliary streams, please wait", "tr_notSupportAuxiliaryStream": "Does not support auxiliary stream play", "tr_allAssessmentItems": "All assessment items", "tr_text": "Text", "tr_assessmentReferenceImage": "Assessment reference image", "tr_notSupportClientFlowDeviceOnlyTip": "Edge side passenger flow device、edge cloud combined passenger flow device not support configure cloud precise passenger flow,already disabled display.", "tr_onlySupportClientFlowDeviceTip": "Not edge cloud combined passenger flow device not support configuring end cloud combination precise guest flow", "tr_userHasPeriod": "User validity period", "tr_userForeverPeriod": "Permanent validity", "tr_pleaseChooseValidityPeriod": "Please select the validity period", "tr_scanDeviceQRCodeTip": "Scan the QR code on your device", "tr_changeNetAddDeviceTip": "Can't find the QR code? Please use the local area network to add the device", "tr_selectAll": "Select all", "tr_selectMultiple": "Select multiple", "tr_selectPersonnel": "Select personnel", "tr_preciseTrafficFlow": "Precise traffic flow", "tr_unableToGetImage": "Unable to getImage", "tr_selectableRangeIsInspectorEquipmentPermissions": "Selectable range is inspector equipment permissions", "tr_canOnlyDrawOneArea": "Can only draw one area", "tr_this_week": "Week", "tr_this_month": "Month", "tr_this_year": "Year", "tr_instructionsForAccessing": "National Standard Access Instructions", "tr_setNationalStandardConfiguration": "Set national standard encoding configuration", "tr_deny": "<PERSON><PERSON>", "tr_accept": "Accept", "tr_googlePermissionTips": "BcloudSaaS App collects location data, collects photos, videos, and camera-recorded media content or file data，to enable identification of real-time positioning coordinate and address, identify photos, video media content, or file information data, even when the app is closed or not in use.", "tr_appDeviceAttribute": "Device attribute", "tr_deviceTags": "Device tags", "tr_bindTag": "Bind tag", "tr_clickEditText": "Click to edit text content", "tr_crop": "Crop", "tr_deviceTagsCategory": "Device tags category", "tr_currentTagHasNoDevice": "There are no devices under the current label", "tr_deviceNewCategory": "Add new category", "tr_deviceNewTag": "Add tags", "tr_deviceCreateCategory": "Create a category", "tr_deviceCreateTag": "Create tags", "tr_pleaseTagsCategoryName": "Please enter a label category name", "tr_selectTagsCategory": "Please select a tag category", "tr_chooseTagsCategory": "Filter tag categories", "tr_searchDeviceTags": "Search device tags", "tr_commonBingSuccess": "Binding successful", "tr_pleaseChooseDeviceTags": "Please select device tags", "tr_delDeviceTags": "Are you sure you want to delete the selected tag information?", "tr_talk": "Talk", "tr_onlySupportBroadDeviceTip": "Devices not supporting broadcast and devices offline are grayed out", "tr_tagHasNoDeviceTags": "No device tags have been added yet", "tr_deviceNewSuccessfully": "Added successfully", "tr_editDeviceTags": "Edit Name", "tr_broadcast": "Broadcast", "tr_delDeviceTagsCategory": "After a tag category is deleted, the category and its tags will be deleted simultaneously.", "tr_connecting": "Connecting", "tr_broadcasting": "Broadcasting", "tr_deviceBusy": "DeviceBusy", "tr_broadcastException": "Broadcast exception", "tr_memberManagement": "Member management", "tr_scanOnlyQRCodeTip": "Place the QR code into the frame and scan", "tr_qrScan": "<PERSON><PERSON>", "tr_scanLogin": "Login confirmation", "tr_scanWebLogin": "Desktop version login confirmation", "tr_scanLoginSuccess": "Scan the QR code to login successfully", "tr_pleaseInputNamePhoneEmail": "Please enter username/phone number/email to search", "tr_addChooseManual": "Manually add", "tr_addChooseContacts": "Contacts import", "tr_addChooseFaceDatabase": "Face database import", "tr_addChooseBatch": "<PERSON><PERSON> Add", "tr_commonModify": "Modify", "tr_pleaseChoosePersons": "Please select user personnel", "tr_cancelLogin": "Cancel login", "tr_chooseMaxCount": "Max can select {maxCount} users", "tr_chooseMember": "Select a member", "tr_batchEdit": "Batch edit", "tr_batchModify": "Batch modification", "tr_parentCategory": "Parent category", "tr_disable": "Disable", "tr_checkEnable": "Is it enabled", "tr_pleaseChooseRoles": "Please select roles", "tr_noMicrophonePermissionTip": "No microphone permission, please open microphone permission in system settings.", "tr_isNotPlayingTip": "Not in play state, please try again later.", "tr_disabled": "Disabled", "tr_active": "Active", "tr_pleaseChooseTagDevices": "Please select the device under the tag", "tr_delTagDevices": "Are you sure you want to remove the selected device under the tag?", "tr_remove": "Remove", "tr_disableSuccessful": "Disable successfully", "tr_enabledSuccessful": "Enabled successfully", "tr_scanReLoginTip": "Please scan the QR code again to login", "tr_confirmRemove": "Confirm remove", "tr_removeSuccessfully": "Removed successfully", "tr_pleaseTagsName": "Please enter a label name", "tr_pleaseCategoryName": "Please enter a category name", "tr_agree": "Agree", "tr_selectCategory": "Selected category", "tr_hasSelectDeviceNode": "Please select a node", "tr_secondaryVerification": "Secondary verification", "tr_emailActivation": "Email activation", "tr_verificationActivation": "Verification code activation", "tr_currentAccount": "Current account:", "tr_checkAccountLogin": "Second verification login", "tr_notActivated": "Not activated", "tr_modifyAccessIdPwd": "Modify access ID password", "tr_statistics": "Statistics", "tr_modifyStoreName": "Modify store name", "tr_allDevicesTotal": "Total ({quantity} devices)", "tr_precisionDevicesTotal": "Precise traffic flow ({quantity} devices)", "tr_passengerFlowDevicesTotal": "Device traffic ({quantity} devices)", "tr_passengerFlowDevices": "Passenger flow equipment", "tr_pleaseChooseDate": "Please choose date", "tr_dateChoose": "Date choose", "tr_flowTypeVisitedStore": "Visited the store", "tr_flowTypeEnterStore": "Enter the store", "tr_flowTypePassingStore": "Passing the store", "tr_selectTime": "Select time", "tr_nodeAddress": "Node location", "tr_nodeSyncAddress": "Sync to device", "tr_colorCast": "Color cast", "tr_pureBlackWhite": "Pure black/white", "tr_screenGlitch": "Screen glitch", "tr_blur": "Blur", "tr_selectAtLeastOneParameter": "Please select at least one parameter", "tr_noPreciseTrafficFlowDevice": "No precise traffic flow devices", "tr_noDeviceTrafficDevice": "No device traffic devices", "tr_runtimePoint": "Runtime point", "tr_deviceNotAddedRuntimePoint": "Some device not added runtime point", "tr_storeEntry": "Store entry", "tr_enterStore": "Enter store", "tr_passByStore": "Pass by store", "tr_excludeDeliveryCourier": "Exclude delivery/courier", "tr_searchJFDevice": "Scan and discover devices", "tr_namePleaseRetry": "{name}，pleaseRetry", "tr_incorrectPassword": "Incorrect password", "tr_accountNotExist": "Account does not exist", "tr_loginTimeoutNetworkConnectionFailed": "Login timed out (network connection failed)", "tr_accountNotLoggedIn": "Account not logged in", "tr_accountLoggedIn": "Account logged in", "tr_accountBlacklisted": "Account blacklisted", "tr_insufficientDeviceResources": "Insufficient device resources", "tr_networkHostNotFound": "Network host not found", "tr_deviceNotExistDeleted": "Device does not exist (deleted)", "tr_deviceTokenInvalid": "Device token invalid", "tr_talkChannelOccupied": "Talk channel occupied", "tr_normalOnline": "Normal online", "tr_deviceNeverReported": "<PERSON>ce never reported", "tr_channelMismatch": "Channel mismatch", "tr_channelOffline": "Channel offline", "tr_accountError": "Account error", "tr_passwordError": "Password error", "tr_loginFailed": "<PERSON><PERSON> failed", "tr_parameterError": "Parameter error", "tr_handleError": "Handle error", "tr_apiRequestFailed": "API request failed", "tr_playbackTypeError": "Playback type error", "tr_requestDeviceInfoFromDSMFailed": "Request device info from DSM failed", "tr_onvifSSIDNotRegistered": "ONVIF SSID not registered", "tr_previewPlaybackServerGatewayCannotProcess": "Preview/playback server or gateway cannot process due to device overload or maintenance", "tr_previewPlaybackDeviceReturnedNotFound": "Preview/playback device returned Not Found", "tr_previewPlaybackFailed": "Preview/playback failed", "tr_previewPlaybackRequestTimeoutNoResponse": "Preview/playback request timeout, no response", "tr_rtspProtocolError": "RTSP protocol error", "tr_urlFormatError": "URL format error", "tr_noRecording": "No recording", "tr_urlExpired": "URL expired", "tr_urlAuthenticationFailed": "URL authentication failed", "tr_noTraffic": "No traffic", "tr_urlVerificationFailedGwmCommunicationFailed": "URL verification failed, GWM communication failed", "tr_playbackFailedXmtsCommunicationFailed": "Playback failed, XMTS communication failed", "tr_queryRecordingFailed": "Query recording failed", "tr_invalidSeekTime": "Invalid seek time", "tr_noConfigInfoFoundInRedis": "No configuration information found in Redis", "tr_tokenParsingFailed": "Token parsing failed", "tr_payloadFailed": "Payload failed", "tr_updateRedisFailed": "Update Redis failed", "tr_urlNotAllowedToPlay": "URL not allowed to play", "tr_urlExceedsAllowedConcurrency": "URL exceeds allowed concurrency", "tr_networkException": "Network exception", "tr_pleaseRetryLater": "Please retry later", "tr_searchJFStatusRefresh": "Searching for devices, please wait...", "tr_searchJFStatusResultFront": "{quantity} devices found, ", "tr_searchJFStatusResultBehind": "check the details", "tr_searchJFStatusFailureFront": "No device was scanned, please ", "tr_searchJFStatusFailureBehind": "rescan", "tr_scoreCannotBeEmpty": "Score cannot be empty", "tr_modifyStoreAddress": "Modify store address", "tr_onlineOfflineAlarm": "Online offline alarm", "tr_selectEvaluationTemplate": "Select evaluation template", "tr_customTemplate": "Custom template", "tr_syncSubordinateChannels": "Synchronize modification of subordinate channels", "tr_accountAuthorizationStatistics": "{total} sub-accounts are authorized, {allocatedNum} have been allocated, and {unallocatedNum} are left.", "tr_deviceNotPlay": "The device is not playing", "tr_deviceCamera": "IPC", "tr_AddDirectNetConnectionNotes": "NOTE： \n1.Make sure the device is powered on \n2.Connect the network cable to the network cable port of the device, and the DVR/NVR is connected to the wired network. After the device is successfully started, try to connect to the network. When you hear the device successfully connected to the network, it means that the network is connected", "tr_chooseDeviceType": "Select device type", "tr_scanQRNetworkCodeTips": "Scan the QR code on the device, but can't find it, please use LAN to add devices", "tr_snAdd": "SN Add", "tr_networkAdd": "LAN Add", "tr_pleaseInputDeviceAccount": "Please enter the device account", "tr_pleaseInputSN": "Please enter the serial number", "tr_pleaseInputDevicePassword": "Please enter the device password", "tr_openBlueTooth": "Please turn on Bluetooth and related permissions", "tr_openWiFi": "Please turn on WiFi", "tr_openLocalNetwork": "Please enable local network permission", "tr_executeOpen": "Go to open", "tr_openBTPermission": " want to access your Bluetooth permissions", "tr_openBTPermissionContent": "Please turn on your phone's Bluetooth to scan nearby Bluetooth devices", "tr_titleCollect": "Collect", "tr_recordingType": "Recording type", "tr_eventRecording": "Event recording", "tr_allDayRecording": "All-day recording", "tr_collectDevices": "Collection devices", "tr_activate": "Activate", "tr_switchPhone": "Switch phone number", "tr_switchEmail": "Switch email", "tr_heatMap": "Heat map", "tr_dataDetails": "Data details", "tr_notConfiguredHeatArea": "Not configured heat area", "tr_storeDataOverview": "Store data overview", "tr_mostPopulatedArea": "Most populated area", "tr_leastPopulatedArea": "Least populated area", "tr_totalPeople": "Total people", "tr_areaTrafficStatistics": "Area traffic statistics", "tr_trafficTrend": "Traffic trend", "tr_areaName": "Area name", "tr_numberOfPeopleEntering": "Number of people entering", "tr_averageStayDuration": "Average stay duration", "tr_stayRate": "Stay rate", "tr_storeFloorPlan": "Store floor plan", "tr_trafficHeatZone": "Traffic heat zone", "tr_noFloorPlanConfigured": "No floor plan configured", "tr_goConfigure": "Go configure", "tr_floorPlan": "Floor plan", "tr_uploadStoreFloorPlan": "Please upload store floor plan", "tr_storeHeatMapConfiguration": "Store heat map configuration", "tr_addHeatZone": "Add heat zone", "tr_nodeCount": "Total {nodeCount} nodes", "tr_heatAreaConfiguration": "Heat area configuration", "tr_noStoresAvailable": "No stores available", "tr_storeDeviceInformation": "Store device information", "tr_mostPreferredStayArea": "Most preferred stay area", "tr_leastPreferredStayArea": "Least preferred stay area", "tr_totalStayDuration": "Total stay duration", "tr_heatDataDetails": "Heat data details", "tr_addHeatArea": "Add heat area", "tr_pleaseEnterAreaName": "Please enter area name", "tr_searchNodeDevice": "Enter the node or device name", "tr_dataDashboard": "Data dashboard", "tr_storeFlowRanking": "Store passenger flow ranking", "tr_storeInspectionCompletionRate": "Store inspection completion rate", "tr_storeInspectionCoverageRate": "Store inspection coverage rate", "tr_storeInspectionCoverageDetail": "Inspection coverage", "tr_passengerFlowInfo": "Passenger flow", "tr_area": "Area", "tr_device": "<PERSON><PERSON>", "tr_numberOfEntries": "Number of entries", "tr_clickToEditStoreFloorPlan": "Click to edit store floor plan", "tr_longPressToEditStoreFloorPlan": "Long press to edit store floor plan", "tr_heatArea": "Heat area", "tr_pleaseSelectArea": "Please select area", "tr_incompleteConfiguration": "Incomplete configuration", "tr_pleaseSelectAtLeastOneArea": "Please select at least one area", "tr_storeInspectionOverview": "Store inspection overview", "tr_passengerFlowData": "Passenger flow data", "tr_store": "Store", "tr_numberOverdueEvents": "Number of overdue events", "tr_numberIssuedTasks": "Number of tasks issued", "tr_numberCompletedTasks": "Number of completed tasks", "tr_numberOverdueTasks": "Number of overdue tasks", "tr_completionRate": "Completion Rate", "tr_deviceDimension": "Device Dimension", "tr_storeDimension": "Store Dimension", "tr_deviceEventAnalysis": "Device event analysis", "tr_storeEventAnalysis": "Store event analysis", "tr_fetchingImageDimensionsPleaseRetry": "Fetching image dimensions, please retry later", "tr_deviceDataOverview": "Device data overview", "tr_accountActivation": "Account activation", "tr_deviceInspectionCompletionRate": "Inspection task completion rate", "tr_storeResources": "Store resources", "tr_pleaseSelectStore": "Please select store", "tr_nameDevices": "{name} devices", "tr_onlySelectStoresWithOnlineDevices": "Only select stores with online devices", "tr_completionRateExplain": "The task completion rate is only valid for task inspections.", "tr_numberInspectionTasks": "Number of inspection tasks", "tr_numberInspectionTasksCompleted": "Number of inspection tasks completed", "tr_inspectionCompletionRate": "Inspection completion rate", "tr_allInspectors": "All inspectors", "tr_inspectionProgress": "Inspection progress", "tr_inspectionStores": "Inspection stores", "tr_continueInspection": "Continue inspection", "tr_storesPendingInspectionContinue": "There are stores pending inspection in this random check. Do you want to continue?", "tr_randomShake": "Random shake", "tr_shakeDevice": "Shake device", "tr_shakeStore": "Shake store", "tr_inspectionDimension": "Inspection dimension", "tr_coverage": "Coverage", "tr_inspectionTasks": "Inspection tasks", "tr_numberStoresInspected": "Number of stores inspected", "tr_storeDetail": "Store Details", "tr_chooseStoreMaxCount": "Max can select {name} stores", "tr_PleaseConfirmPwd": "Please confirm password", "tr_pendingInspectionImages": "Pending inspection images", "tr_allPendingInspectionImagesProcessed": "All pending inspection images have been processed", "tr_operationSuccessfulProceedingToNext": "Operation successful, proceeding to the next", "tr_onlySupportOnlineDeviceTip": "Only select online devices", "tr_allDevicesAbnormal": "All devices abnormal", "tr_storeNoPermission": "Store no permission", "tr_storeDeleted": "Store deleted", "tr_noDevicesUnderStore": "No available devices", "tr_allDevicesOfflineUnderStore": "All devices offline under store", "tr_storeExceptionInspectionNotPossible": "Store exception, inspection not possible", "tr_noStoresAvailableForInspection": "No stores available for inspection", "tr_noTrafficHeatZoneCamerasInStoreContactB2bEmail": "No traffic heat zone cameras in the current store. You <NAME_EMAIL> for procurement.", "tr_delMoreUserPerson": "Are you sure you want to delete the selected users?", "tr_delTheUserPerson": "Are you sure you want to delete this user？", "tr_noCorrespondingRecords": "No corresponding records", "tr_enterStoreCount": "Enter store count", "tr_storeCoverageRateExplain": "Number of stores inspected during the statistical period / all stores", "tr_hasChooseNode": "You cannot add a node under the current node, Please select a node", "tr_aiInspectionRecords": "AI inspection records", "tr_trafficHeatZoneAlgorithmNotEnabledContactB2b": "The traffic heat zone algorithm is not enabled and cannot be configured. <NAME_EMAIL> to enable it.", "tr_addHeatAreaTip": "(1)Click + to draw an area on the floor plan;\n(2)Associate the monitoring device of this area;\n(3)Configure and associate the heat area on the device.", "tr_refreshed": "Refreshed", "tr_alarmDetail": "Alarm details", "tr_searchDeviceNameSN": "Enter device name/serial number", "tr_associateHumanShape": "Associate human shape", "tr_AddChildDeviceNodeTips": "There are no devices or sub-nodes under the current node", "tr_storeAndAlgorithm": "Stores and algorithms", "tr_encodingSettings": "Encoding settings", "tr_encodingSoftSolution": "Soft solution", "tr_encodingHardSolution": "Hard solution", "tr_encodingSwitching": "Automatic switching between soft and hard solutions", "tr_algorithmAnomaly": "Algorithm anomaly", "tr_detectionCount": "Detection count", "tr_rectifyCount": "Rectify count", "tr_deviceOfflineOrAbnormalNoSnapshotGenerated": "Device offline or abnormal, no snapshot generated", "tr_pendingSubmission": "Pending submission", "tr_aiInspectionRecordDetail": "AI inspection record detail", "tr_viewEvent": "View Event", "tr_autoSubmitEvent": "Event automatically submitted", "tr_event": "Event", "tr_HDStream": "HD", "tr_SDStream": "SD", "tr_transparentAudioTemplate": "Transparent audio template", "tr_transparentTransmission": "Transparent transmission", "tr_rectificationTimeMustBeGreaterThanOneMinute": "Rectification time must be greater than 1 minute", "tr_imageGenerationFailed": "Image generation failed", "tr_GBCascade": "GB Cascade", "tr_GBCascadeAdd": "New GB Cascade", "tr_GBCascadeDetail": "GB Cascade detail", "tr_GBCascadeEdit": "GB Cascade detail edit", "tr_transcodingTemplate": "Transcoding template", "tr_bitStream": "Bit Stream", "tr_previewAddress": "Preview address", "tr_playbackAddress": "Playback address", "tr_to": "to", "tr_globalPlaybackPeriod": "Global playback period", "tr_aboutDevice": "About device", "tr_networkMode": "Network mode", "tr_localIP": "Local IP", "tr_deviceVersion": "Device version", "tr_firmwareVersion": "Firmware version", "tr_releaseDate": "Release date", "tr_deviceTimezone": "Device timezone", "tr_deviceTime": "Device time", "tr_restartDevice": "Restart device", "tr_deviceConfiguration": "Device configuration", "tr_imageFlip": "Image flip", "tr_speakerVolume": "Speaker volume", "tr_pictureColorShift": "Picture color shift", "tr_advancedSettings": "Advanced settings", "tr_verticalFlip": "Vertical flip", "tr_horizontalFlip": "Horizontal flip", "tr_fullFlip": "Full flip", "tr_irLensReverse": "IR lens reverse", "tr_audioVideoEncodingConfig": "Audio-video encoding config", "tr_imageConfig": "Image config", "tr_wideDynamicConfig": "Wide dynamic config", "tr_enableHighQualityImageUnderHighContrast": "Enable high-quality image under high contrast lighting", "tr_deviceSN": "Device SN", "tr_mainStream": "Main stream", "tr_resolution": "Resolution", "tr_frameRateFPS": "Frame rate (FPS)", "tr_bitRateControlFPS": "Bit rate control (FPS)", "tr_variableBitRate": "Variable bit rate", "tr_limitedBitRate": "Limited bit rate", "tr_imageQuality": "Image quality", "tr_bitRateValue": "Bit rate value", "tr_IFrameInterval": "I-frame interval", "tr_audio": "Audio", "tr_encoderStaticConfig": "Encoder static config", "tr_smartEncoding": "Smart encoding", "tr_subStream": "Sub stream", "tr_osdSettings": "OSD settings", "tr_channelTitle": "Channel title", "tr_titleContent": "Title content", "tr_titleTime": "Title time", "tr_GBCascadeChannelTips": "GB cascade authorization {maxChannel} channels, {remainChannel} channels left", "tr_GBCascadeAuthorization": "GB cascade authorization", "tr_thisPlatformName": "This platform name", "tr_channelCount": "{count} channels", "tr_pleaseInputPlatformNameIDHit": "Please enter the parent platform name/access ID", "tr_parentPlatformName": "Parent platform name", "tr_platformName": "level platform", "tr_pushChannel": "Push channel", "tr_updateTime": "Update time", "tr_creationTime": "Creation time", "tr_SIPServerNumber": "SIP server number", "tr_signalingTransmission": "Signaling transmission", "tr_registrationSwitch": "Registration switch", "tr_registrationCycle": "Registration cycle (seconds)", "tr_heartbeatPeriod": "Heartbeat period (seconds)", "tr_parentPlatformNameHint": "1-20 characters, both Chinese and English numbers are acceptable", "tr_accessIdHint": "20 numbers", "tr_accessPsdHint": "English, numbers, special symbols", "tr_registrationCycleHint": "Range 3600-86400 seconds", "tr_heartbeatPeriodHint": "Range 30-3600 seconds", "tr_delOneDeviceGBCascade": "Are you sure you want to delete this GB cascade?", "tr_east": "East", "tr_west": "West", "tr_sipServerPortHint": "The port number is a 1-5 digit number", "tr_registrationSwitchExplanation": "When it is turned on, it means that it is communicating with the upper-level platform and cannot be edited; when the registration switch is turned off, the national standard cascade information can be edited", "tr_pendingEvent": "Pending event", "tr_pleaseSelectTime": "Please select a time", "tr_HDAddress": "HD Address", "tr_SDAddress": "SD Address", "tr_recordCardAddress": "Local Playback Address", "tr_recordCloudAddress": "Cloud Playback Address", "tr_pleaseSelectPlaybackTimeError": "The current playback period cannot cross days. Please reselect the time period", "tr_belongStore": "Store to which it belongs", "tr_pleaseUnbindStoreId": "Please unbind the following cloud stores before deleting them", "tr_belongNode": "Node to which it belongs", "tr_verification": "Go to verify", "tr_psdVerificationTip": "Verify whether the current default password is correct. If incorrect, enter the password to verify.", "tr_executeVerification": "verify", "tr_verificationSuccess": "Verification successful", "tr_deviceOfflineEdit": "The device is offline and cannot be edited", "tr_deviceAnomaly": "Device anomaly", "tr_pleaseUnbindHasChildren": "Please delete the node and its subordinate nodes' devices first", "tr_inputParentPlatName": "Please enter the name of the parent platform", "tr_inputPlatformName": "Please enter the platform name", "tr_inputAccessId": "Please enter the access ID", "tr_inputAccessIdNumHint": "The access ID is 20 digits", "tr_inputAccessPsd": "Please enter the access password", "tr_inputAccessPsdHint": "The access password is 8 to 16 characters in English, numbers, and special symbols", "tr_inputSIPServerNumber": "Please enter the SIP server number", "tr_inputSIPServerNumberHint": "The SIP server number is 20 digits", "tr_inputSIPServerIP": "Please enter the SIP server IP", "tr_inputSIPServerPort": "Please enter the SIP server port", "tr_inputSIPServerPortHint": "The SIP server port is a 1-5 digit number", "tr_inputRegistrationCycle": "Please enter the registration period (seconds)", "tr_inputHeartbeatPeriod": "Please enter the heartbeat period (seconds)", "tr_inputRegistrationCycleHint": "The registration period ranges from 3600 to 86400 seconds", "tr_inputHeartbeatPeriodHint": "The heartbeat cycle ranges from 60 to 3600 seconds", "tr_inputIPV4": "Please enter the correct IPV4", "tr_pleaseSelectPassOrFail": "Please select pass or fail", "tr_newPassword": "New Password", "tr_passwordModify": "Password modify", "tr_checkPasswordErrorHint": "The device password is incorrect, please verify the password", "tr_GBCascadePush": "<PERSON><PERSON>", "tr_GBCascadeViewSIPID": "View SIP ID", "tr_GBCascadeAuthorizationRoutes": "Insufficient authorized routes, please expand", "tr_channelName": "Channel Name", "tr_channelSIPID": "SIP ID of the platform", "tr_pushSIPID": "Push SIP ID", "tr_pleaseInputChannelNamePushSIPHint": "Please enter the channel name/push SIP ID", "tr_alarmTimeInterval": "Alarm time interval", "tr_sensitivity": "Sensitivity", "tr_alarmVoice": "Alarm voice", "tr_smartAlarm": "Smart alarm", "tr_humanDetection": "Human detection", "tr_motionDetection": "Motion detection", "tr_humanDetectionTip": "Default enable human detection，human detection or motion detection, choose one", "tr_deviceAlertSound": "Device alert sound", "tr_trackingTrajectory": "Tracking trajectory", "tr_humanDetectionMarking": "When a human figure appears in the video, it will be marked with a box or line", "tr_displayAlarmRules": "Display alarm rules", "tr_videoDisplayAlarmRules": "Show alarm rules in video", "tr_ruleConfiguration": "Rule configuration", "tr_alarmRuleConfiguration": "Alarm rule configuration", "tr_messageNotificationInterval": "Message notification interval", "tr_alarmPeriod": "Alarm period", "tr_high": "High", "tr_medium": "Medium", "tr_low": "Low", "tr_allDayAlarm": "All-day alarm", "tr_GBCascadeChannelSIPID": "{platformId} Platform SIP ID View", "tr_alertSound": "Alert sound", "tr_loopAlarmSound": "Loop alarm sound", "tr_secondsPerLoop": "Loop every second", "tr_aiInspectionAnalysis": "AI Inspection analysis", "tr_noDeviceNode": "No device node", "tr_alarmRate": "Alarm rate", "tr_detectionTrend": "Detection trend", "tr_keyIssues": "Key issues", "tr_issueLocations": "Issue locations", "tr_issueDistributionPeriod": "Issue distribution period", "tr_keyIssueDetails": "Key issue details", "tr_statisticalPeriod": "Statistical period", "tr_issueDetails": "Issue details", "tr_viewIssueDetails": "View issue details", "tr_choseChannelCount": "Selected Channel", "tr_channelResources": "Channel resources", "tr_chooseChannels": "Please select channels", "tr_alertLine": "Alert line", "tr_alertZone": "Alert zone", "tr_executionDate": "Execution date", "tr_customTimeSetting": "Custom time setting", "tr_globalSetting": "Global setting", "tr_timePeriod": "Time period", "tr_selectTimePeriod": "Select time period", "tr_intervalRangeTip": "Interval range: 0-600 seconds, recommended 30 seconds", "tr_inspectionItem": "Inspection item", "tr_durationCannotBeLessThanDetectionFrequency": "Duration cannot be less than detection frequency", "tr_alarmCountRatio": "Alarm count ratio", "tr_alarmRateRanking": "Alarm rate ranking", "tr_alarmCountRanking": "Alarm count ranking", "tr_notModifiable": "Not modifiable", "tr_pleaseEnterBitrate": "Please enter bitrate", "tr_videoBitrateKbps": "Video bitrate (kbps), value range 0~8192", "tr_pleaseEnterIFrameInterval": "Please enter I-frame interval", "tr_iFrameIntervalRange": "I-frame interval, value range 1~12", "tr_upgradeFirmwareTip": "This device has new device firmware that can be upgraded. Do you want to upgrade?", "tr_firmwareUpgrading": "Upgrading", "tr_firmwareUpgradeFailure": "Upgrade Failed", "tr_firmwareUpgradeSuccess": "Upgrade Successfully", "tr_firmwareUpgradingTip": "No other operations can be performed during the upgrade process, Please do not exit or press the home button during the upgrade", "tr_firmwareUpgradeFailureTip": "Upgrade failed, please try again", "tr_firmwareUpgradeSuccessTip": "Upgrade successful, waiting for the device to restart", "tr_deviceUpgrade": "Device Upgrade", "tr_deviceExecuteUpgrade": "Please click Update Device", "tr_deviceUpgradeVersion": "Already the latest version", "tr_orgAddDeviceNodeTips": "No devices and sub-nodes\nPlease click the '+' button in the upper right corner to add devices and sub-nodes", "tr_deduplication": "Deduplication", "tr_accountLocked": "Account locked", "tr_noObject": "No object", "tr_fileDeletionFailed": "File deletion failed", "tr_noSdCardOrHardDrive": "No SD card or hard drive", "tr_noRecordingDeviceOrNotRecording": "No recording device or device not recording", "tr_userNoQueryPermission": "User has no query permission", "tr_userNotBoundToFacebook": "User not bound to Facebook", "tr_userNotBoundToGoogle": "User not bound to Google", "tr_iFrameNotFound": "I-frame not found", "tr_deviceNoStreamOver2s": "Device has no stream for over 2s", "tr_deviceNoStreamOver20s": "Device has no stream for over 20s", "tr_dataSourceFetchFailed": "Failed to fetch data source", "tr_urlConcurrencyLimited": "URL concurrency limited", "tr_waitForGwmValidationTimeout": "Waiting for GWM validation result timed out", "tr_httpParsingError": "HTTP parsing error", "tr_clientNotSupportedUseChrome": "Client not supported, please use Chrome", "tr_iFrameNoSps": "I-frame has no SPS", "tr_protocolMessageParsingError": "Protocol message parsing error", "tr_noFrameDataReceivedDuringConnection": "No frame data received during connection", "tr_noFrameDataReceived2sBeforeDisconnection": "No frame data received 2 seconds before disconnection", "tr_noVideoFrameDataReceived2sBeforeDisconnection": "No video frame data received 2 seconds before disconnection", "tr_actualVideoFrameRateTooLow": "Actual video frame rate too low", "tr_accountOverdueAccessFailed": "Account overdue, access to streaming service failed", "tr_serviceValidationException": "Service validation exception", "tr_serverValidationTimeout": "Server validation timed out", "tr_localNetworkExceptionDuringServiceValidation": "Local network exception during service validation", "tr_passengerCloudFlowTip": "In-store customer flow: customer flow entering the store from outside\nOut-store customer flow: customer flow walking out of the store from inside\nPassing-store customer flow: customer flow passing by without entering the store\nTotal customer flow = in-store customer flow + passing-store customer flow\nEntry rate = in-store customer flow / total customer flow * 100% = in-store customer flow / (in-store customer flow + passing-store customer flow) * 100%", "tr_default": "<PERSON><PERSON><PERSON>", "tr_smartAlert": "<PERSON> Alert", "tr_smartAlertTip": "By configuring relevant parameters, the device can trigger human detection and motion detection", "tr_thinkingPleaseWait": "Thinking, please wait", "tr_thinking": "Thinking", "tr_thinkingFailed": "Thinking failed", "tr_manualInspectionAnalysisInspectionCount": "Manual inspection analysis - inspection count data", "tr_totalCount": "Total count", "tr_passedCount": "Passed count", "tr_failedCount": "Failed count", "tr_manualInspectionAnalysisPassRate": "Manual inspection analysis - pass rate data", "tr_coverageRate": "Coverage rate", "tr_manualInspectionAnalysisEmployeeRankingByInspectionCount": "Manual inspection analysis - employee ranking by inspection count", "tr_employeeName": "Employee name", "tr_manualInspectionAnalysisEmployeeRankingByFindings": "Manual inspection analysis - employee ranking by findings", "tr_manualInspectionAnalysisEmployeeRankingByOverdue": "Manual inspection analysis - employee ranking by overdue count", "tr_manualInspectionAnalysisStoreRankingByFindings": "Manual inspection analysis - store ranking by findings", "tr_findingsCount": "Findings count", "tr_manualInspectionAnalysisFailedInspectionRatio": "Manual inspection analysis - failed inspection ratio", "tr_inspectionItemName": "Inspection item name", "tr_manualInspectionAnalysisFailedPointRanking": "Manual inspection analysis - failed point ranking", "tr_manualInspectionAnalysisTaskCompletionRate": "Manual inspection analysis - store inspection task completion rate", "tr_inspectionMethods": "Inspection methods include: 'Inspection tasks, Spot checks, Image inspections, Video inspections, Shake inspections, On-site inspections'", "tr_manualInspectionAnalysisCoverageByTaskCompletionRate": "Manual inspection analysis - task completion rate and coverage data", "tr_inspectionType": "Inspection type", "tr_manualInspectionAnalysisTaskCompletionRateData": "Manual inspection analysis - task completion rate data", "tr_aiInspectionAnalysisAlarmCount": "AI inspection analysis - alarm count data", "tr_aiInspectionAnalysisInspectionCount": "AI inspection analysis - inspection count data", "tr_aiInspectionAnalysisAlarmRate": "AI inspection analysis - alarm rate data", "tr_aiInspectionAnalysisAlarmCountByInspectionItem": "AI inspection analysis - alarm count percentage by inspection item", "tr_aiInspectionAnalysisAlarmRateRankingByInspectionItem": "AI inspection analysis - alarm rate ranking by inspection item", "tr_aiInspectionAnalysisAlarmCountRanking": "The following is AI inspection analysis-alarm number ranking data", "tr_aiInspectionAnalysisAlarmRateRanking": "Alarm rate ranking", "tr_aiInspectionAnalysisInspectionItemDistributionByTime": "AI inspection analysis - inspection item distribution by time", "tr_pleaseClick": "Please click", "tr_selectOrAddConversation": "Select a conversation or add one", "tr_history": "History", "tr_enterTitleToSearch": "Enter title name to search", "tr_sendMessageToDeepseek": "Send message to deepseek", "tr_noResendBeforeReply": "No resending before reply is completed", "tr_copySuccess": "Copy successful", "tr_manualInspectionCoverageData": "The following is the manual inspection analysis coverage data", "tr_belowIsAiInspectionAnalysis": "The following is the AI inspection analysis", "tr_alarmCountRankingData": "Alarm count ranking data", "tr_deepseekSuffiex": "What optimization suggestions do you have for the above inspection analysis data? Please respond in report format and use English.", "tr_selectUsefulWiFi": "Please select an available WiFi", "tr_chooseAvailableWiFi": "The WiFi connection is not smooth. Please reselect an available WiFi.", "tr_videoBlocking": "Video Blocking", "tr_videoBlockingTip": "When the camera image is blocked by an object, an alarm is triggered", "tr_firmwareManage": "Firmware Management", "tr_PleaseInputContent": "Please input content", "tr_chooseFirmwareFile": "Please select the firmware upgrade file", "tr_chooseExpandListMode": "Device has switched list display", "tr_chooseExpandGridMode": "Device has switched to grid display", "tr_deepSeekStatistics": "Deepseek calling statistics", "tr_deepSeekStatisticsTip": "Please go to the inspection analysis or event analysis page to view specific functions", "tr_occlusionAlarm": "Occlusion alarm", "tr_videoOcclusionAlarm": "Video occlusion alarm", "tr_deviceMaintenance": "Device maintenance", "tr_StorageCardSetting": "Storage card setting", "tr_selectLocalFileUpgrade": "Select Local File Upgrade", "tr_NoSDCardInserted": "No SD card inserted, cannot record", "tr_StorageCardInfo": "Storage card information", "tr_Capacity": "Capacity", "tr_Remaining": "Remaining", "tr_Images": "Images", "tr_Videos": "Videos", "tr_FormatStorageCard": "Format storage card", "tr_Format": "Format", "tr_Formatting": "Formatting...", "tr_RecordingManagement": "Recording management", "tr_RecordingSwitch": "Recording switch", "tr_AlarmRecording": "Alarm recording", "tr_AlarmRecordingTip": "Only record when an alarm is detected, long recording time", "tr_AllDayRecording": "All-day recording", "tr_AllDayRecordingTip": "All-day recording, 24 hours non-stop recording", "tr_RecordingSegment": "Recording segment", "tr_RecordingSegmentTip": "The maximum duration of a single recording file", "tr_AlertPreRecordTime": "Alert pre-record time", "tr_SelectTime": "Select time", "tr_autoRestart": "auto restart", "tr_never": "Never", "tr_timePoint": "time point", "tr_hour": "Hour", "tr_minute": "Minute", "tr_second": "Second", "tr_factoryReset": "Factory reset", "tr_resetDevice": "Reset device", "tr_resetDeviceSuccess": "Reset device successfully", "tr_resetDeviceFailure": "Reset device failed", "tr_restartDeviceSuccess": "Restart device successfully", "tr_restartDeviceFailure": "Restart device failed", "tr_latestUpgradeVersion": "The latest version", "tr_AlertPreRecordTimeTip": "Range 1-30 seconds", "tr_Monday": "Monday", "tr_Tuesday": "Tuesday", "tr_Wednesday": "Wednesday", "tr_Thursday": "Thursday", "tr_Friday": "Friday", "tr_Saturday": "Saturday", "tr_Sunday": "Sunday", "tr_deepseekSuffiex2": "What optimization suggestions do you have for the above event analysis data? Please respond in report format and use English.", "tr_staffCompleteRectificationSort": "Employee complete rectification count ranking", "tr_staffOverdueRectificationSort": "Employee overdue rectification count ranking", "tr_staffAcceptanceRectificationSort": "Employee complete acceptance count ranking", "tr_eventCountRanking": "Event count ranking", "tr_overdueCountRanking": "Overdue count ranking", "tr_acceptanceCountRanking": "Acceptance count ranking", "tr_formatStorageCardTip": "Formatting the storage card will make the device offline and reload the storage card, which will take several minutes, please confirm whether to continue?", "tr_heptagon": "Heptagon", "tr_octagon": "Octagon", "tr_titleReset": "Confirm factory reset", "tr_titleRestart": "Confirm to restart the device", "tr_subTitleReset": "After the device is restored to factory settings, the device cannot connect to WIFI and related functions cannot operate normally", "tr_subTitleRestart": "After the device restarts, you need to wait for a while before the function returns to normal", "tr_thinkingFailedTip": "Service busy, please try again later", "tr_stopRecording": "Stop recording", "tr_loopRecording": "Loop recording", "tr_recordingFull": "Recording full", "tr_uploadUpgradeFail": "Firmware upload and upgrade failed", "tr_unnormal": "Abnormal", "tr_layers": "Layers", "tr_drawCircle": "Circle", "tr_locate": "Locate", "tr_planType": "Plan type", "tr_videoInspection": "Video inspection", "tr_storeSelfInspection": "Store self-inspection", "tr_supervisionInspection": "Supervision inspection", "tr_multiIPC": "Multi-port IPC", "tr_channelNumbers": "Number of channels", "tr_IPCType": "IPC Type", "tr_channelOne": "Channel 1", "tr_channelTwo": "Channel 2", "tr_channelThree": "Channel 3", "tr_channelFour": "Channel 4", "tr_storeManagerSign": "Store manager sign", "tr_storeInspectorSign": "Store inspector sign", "tr_sign": "Click to sign", "tr_resign": "Resign", "tr_clear": "Clear", "tr_pleaseSign": "Please sign", "tr_inspectionRetPreview": "Inspection result preview", "tr_myInitiated": "My initiated", "tr_taskIssued": "Task issued", "tr_multi": "Multi-port", "tr_channelStatus": "Channel status", "tr_channelSN": "Channel SN", "tr_channelLabel": "Channel Label", "tr_channelNumInfo": "Channel {serialNum} information", "tr_inspectionWatermark": "Inspection watermark", "tr_inspectionRecordWatermark": "Inspection record watermark", "tr_inspectionRecordWatermarkTip": "After enabling, the inspection record page will carry a watermark", "tr_formatSuccess": "Format successful", "tr_signTip": "Sign area", "tr_maxDurationCannotBeGreaterThan": "Maximum duration cannot be greater than {name}", "tr_sixSNNationsCodeTips": "The length is 6 digits, consisting of numbers, and the serial numbers cannot be the same.", "tr_channelSNExistSameCodeTips": "Same as the serial number of channel {channel}, please modify the serial number code", "tr_PTZResetSuccessful": "PTZ reset successful", "tr_PTZResetFailed": "PTZ reset failed", "tr_privacyPolicyUpdate": "Privacy Policy Update", "tr_privacyPolicyUpdateTip": "Thank you for trusting and using the products and services of BcloudSaaS. In order to better protect the interests of users, we have updated the Privacy Policy and hereby send you this reminder. Please read carefully and fully understand the relevant terms. If you click \"Agree\", it means that you have read and agreed to the updated Privacy Policy, and BcloudSaaS will do its best to protect your legal rights and interests.", "tr_privacyPolicyFrontTip": "View full version", "tr_endTimeCannotBeGreaterThanName": "End time cannot be greater than {name}", "tr_UserServiceAgreement": "User Service Agreement", "tr_holidaySelection": "Holiday selection", "tr_holiday": "Holiday", "tr_selectYearAndMonth": "Select year and month", "tr_selectMonth": "Select month", "tr_selectYear": "Select year", "tr_inStoreCustomerGroup": "In-store customer group", "tr_threePeople": "Three people", "tr_channelManagement": "Channel Management", "tr_channelDetails": "Channel Details", "tr_deduplicationAfter": "Deduplication after", "tr_inStoreCustomerGroupTip": "In-store customer flow: after deduplication, customers who pass through the detection area first and then through the in-store customer line are counted as in-store customer flow\nSingle-person entry (customer group): after deduplication, single-person entry and placement time interval of two seconds after another person's entry are counted as single-person entry\nDouble-person entry (customer group): after deduplication, two people's entry time interval is set as the same batch\nThree-person entry (customer group): after deduplication, three people's entry time interval is set as the same batch 20%\nMultiple-person entry (customer group): after deduplication, three or more people's entry time interval is set as the same batch\nIn-store customer flow: after deduplication, any non-in-store customer line is counted as out-store customer flow\nTotal customer flow: in-store customer flow + out-store customer flow\nIn-store rate: in-store customer flow / total customer flow * 100% = in-store customer flow / (in-store customer flow + out-store customer flow) * 100%", "tr_inStoreCustomerGroupTable": "In-store customer group statistics table", "tr_singlePersonGroup": "Single-person entry (customer group)", "tr_doublePersonGroup": "Double-person entry (customer group)", "tr_threePersonGroup": "Three-person entry (customer group)", "tr_manyPersonGroup": "Multiple-person entry (customer group)", "tr_statisticalDimension": "Statistical dimension", "tr_storeTrafficNoDeduplication": "In-store customer flow (not deduplicated)", "tr_passByTrafficNoDeduplication": "Pass-by customer flow (not deduplicated)", "tr_outStoreTrafficNoDeduplication": "Out-store customer flow (not deduplicated)", "tr_totalTrafficNoDeduplication": "Total customer flow (not deduplicated)", "tr_psdCheckFailureTip": "If the following password verification fails, you can manually enter the correct password", "tr_psdCheckSuccessTip": "Verification successful! The following is the channel account and password", "tr_psdCheckNoticeTip": "Note: Password modification is not allowed when the device is offline!", "tr_verificationFail": "Verification failed", "tr_inputAccount": "Please enter account", "tr_addChannel": "Add Channel", "tr_channelAdd": "Channel add", "tr_JFProtocol": "JFTECH Protocol", "tr_onvifProtocol": "ONVIF Protocol", "tr_addChannelTips": "Device channel access authorization {maxChannel} paths, remaining {remainChannel} paths, <NAME_EMAIL> to expand authorization.", "tr_batchAddChannelTips": "The passwords of the devices selected for batch adding must be consistent, otherwise the addition will fail. Devices with inconsistent passwords should be added individually.", "tr_searchChannelTips": "Searched devices", "tr_deviceUsername": "<PERSON><PERSON>", "tr_devicePassword": "<PERSON>ce Password", "tr_inputAccountPassword": "Enter account and password", "tr_accountPassword": "Account and password", "tr_recordingSetting": "Recording setting", "tr_searchJFDeviceTip": "NVR and cameras must be in the same LAN", "tr_searchNoDeviceTip": "The device was not detected or has already been added", "tr_searchingDeviceTip": "Scanning devices, please wait...", "tr_globalConfiguration": "Global configuration", "tr_createActivityTime": "Create activity time", "tr_activityName": "Activity name", "tr_pleaseEnterActivityName": "Please enter activity name", "tr_pleaseSelectActivityDate": "Please select activity date", "tr_noChannel": "No Channel", "tr_customerGroupEntryInterval": "Customer group entry interval", "tr_aiBox": "AI Box", "tr_aiBoxTip": "Add AI Box device", "tr_inStoreCustomerCount": "In-store customer count", "tr_aiBoxAdd": "AI box added", "tr_aiBoxAddSNTip": "Please enter device SN", "tr_aiBoxAddNameTip": "Please enter device name", "tr_notAddChannel": "Do not add channels yet", "tr_searchAiBoxDeviceTip": "The device and the box must be in the same LAN", "tr_serialNumberHasNotExistConnect": "The serial number does not exist/the device is not connected to the Internet", "tr_subAccountActivation": "Subaccount Activation", "tr_companyList": "Company List", "tr_lastLogin": "Last Selection", "tr_assignedToYou": " assigned to you ", "tr_juvenile": "Juvenile", "tr_storeTrafficSettings": "Store traffic settings", "tr_staffNumber": "Staff number", "tr_setStaffNumber": "Set staff number", "tr_staffDeduplication": "Staff deduplication", "tr_manuallySetStaffNumberRange": "Manually set staff number range", "tr_pleaseSetStaffNumberRange": "Please set staff number range", "tr_manuallySetStaffNumber": "Manually set staff number", "tr_setStaffDeduplicationRules": "Set staff deduplication rules, conditions can be combined", "tr_visitCount": "Visit count", "tr_multipleVisitsPerDayConsideredStaff": "Multiple visits per day considered staff", "tr_arrivalDepartureTime": "Arrival departure time", "tr_beforeAfterBusinessHoursConsideredStaff": "Before after business hours {name} minutes considered staff", "tr_storeEntryExitConsideredStaff": "Entry and exit within {name} minutes before and after business hours are considered staff", "tr_nameMinutesOrMore": "{name} minutes or more", "tr_nameHoursOrMore": "{name} hours or more", "tr_dailyTotalStayDuration": "Daily total stay duration", "tr_stayDuration": "Stay duration", "tr_setSingleAndTotalStayDuration": "Set single and total stay duration", "tr_meetAboveConditionsConsideredStaff": "Meet above {name} considered staff", "tr_setBusinessHours": "Set business hours", "tr_businessHours": "Business hours", "tr_deviceRunTimeGreaterThanStoreHours": "Device run time greater than store hours", "tr_setStaffArrivalDepartureTime": "Set staff arrival departure time", "tr_beforeAfterBusinessHours": "Before after business hours", "tr_note": "Note:", "tr_anyCondition": "Any condition", "tr_allConditions": "All conditions", "tr_singleStayDuration": "Single stay duration", "tr_staffDeduplicationRules": "Staff deduplication rules", "tr_conditionsCanBeCombined": "Conditions can be combined", "tr_singleStayDurationAboveMinutes": "Single stay duration above {name} minutes", "tr_totalStayDurationAboveHours": "Total stay duration above {name} hours", "tr_isStaff": "Is staff", "tr_meetAbove": "Meet above", "tr_consideredAsStaff": "Considered as staff", "tr_configuration": "Configuration", "tr_statistics1": "Statistics", "tr_todayTraffic": "Today traffic", "tr_pleaseEnterVisitCount": "Please enter visit count", "tr_edgeComputing": "Edge computing", "tr_pleaseEnterStaffNumber": "Please enter staff number", "tr_pleaseEnterCorrectRange": "Please enter correct range", "tr_staffFilter": "Staff filter", "tr_staffFilterRules": "Staff filter rules", "tr_passengerFlowStatisticsTip": "Customer Traffic Statistics: Statistical Dimensions Total Customer Volume\n\n1. Total customer traffic\n2. Store entry traffic\n3. Pass-by traffic\n4. Store exit traffic\n5. Store entry customers (deduplicated)\n6. Store exit customers (deduplicated)\n7. Pass-by customers (deduplicated)\n8. Total customers (deduplicated)\n\nStore entry peak: The peak of store entry within the statistical period\n\nStore entry value: The value of store entry within the statistical period", "tr_storeConversionRateTip": "Store conversion rate\n1. Store conversion rate=Number of customers entering the store/Total number of customers entering the store\n2. Total store entry rate: Number of customers entering the store/Total number of customers entering the store (Store entry traffic+Pass-by traffic)\n3. Store entry customer group: Number of customers entering the store (single person/batch of two people/batch of three people/batch of multiple people)/Total number of customers entering the store (single person/batch of two people/batch of three people/batch of multiple people)", "tr_customerSegmentationAnalysisTip": "Customer segmentation analysis: Based on the data statistics after deduplication of customers entering the store\n1. Age of customers entering the store: Data of each age group/Total data of customers entering the store after deduplication\n2. Gender of customers entering the store: Number of men/Number of women in the statistical period after deduplication/Total number of customers entering the store after deduplication", "tr_inStoreBatchRatio": "In-store batch ratio", "tr_male": "Male", "tr_female": "Female", "tr_aiBoxChannelConfigLimitTip": "All channels of the AI box have been configured with 8 algorithms, and no more algorithms can be configured.", "tr_deduplicationTip": "Multiple passenger flow devices under the store will automatically merge and deduplicate, and if staff deduplication is enabled, it will be based on the store deduplication!", "tr_softwareVersion": "Software Version", "tr_softwareVersionReleaseDate": "Software Build Time", "tr_supportAlgorithms": "Support algorithms"}