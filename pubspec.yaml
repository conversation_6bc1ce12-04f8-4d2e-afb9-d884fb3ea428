name: bcloud
description: A new Flutter project.

publish_to: 'none'

version: 2.14.0+1

environment:
  sdk: '>=2.19.6 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  xb_scaffold: ^0.1.14
  #SDK
  bcloudsdk_flutter:
    # path: ../bcloudsdk_plugin
    # path: ./sdkModules/bcloudsdk_plugin
    git:
      url: http://**********/BeCloud/bcloudsdk_plugin.git
      ref: '23e3e79'
  cupertino_icons: ^1.0.2
  #文件处理
  path_provider: ^2.0.7
  #本地缓存
  shared_preferences: ^2.2.0
  #网络图片
  cached_network_image: ^3.3.1
  #状态管理
  provider: ^6.0.5
  #国际化
  intl: any
  flutter_localizations:
    sdk: flutter
  #图片展示
  photo_view: ^0.15.0
  #版本信息获取
  package_info_plus: ^8.3.0
  #设备信息
  device_info_plus: ^11.3.0
  dio: ^5.2.0+1
  #svg
  flutter_svg: ^2.0.7
  #屏幕适配
  flutter_screenutil: ^5.9.0
  #键盘操作
  flutter_keyboard_visibility: ^6.0.0
  #权限管理
  permission_handler: ^12.0.1
  #webview
  webview_flutter: ^4.2.2
  #图片保存到系统相册
  # image_gallery_saver: ^2.0.1
  #验证码输入框
  pinput: ^5.0.1
  #图片选择
  image_picker: ^1.0.1
  #网络请求
  retrofit: ^4.0.3
  #网络日志打印
  pretty_dio_logger: any
  #系统分享
  #share_plus: ^11.0.0
  #url重定向
  url_launcher: ^6.1.14
  #侧滑删除
  flutter_slidable: ^4.0.0
  #二维码扫描
  # qr_code_scanner: ^1.0.1
  #扫描本地图片
  # scan: ^1.6.0
  #生成二维码
  qr_flutter: ^4.1.0
  #gif动画
  lottie: ^3.3.0
  #网络检测
  connectivity_plus: ^6.1.4
  #视频缩略图生成
  video_thumbnail: ^0.5.3
  #wifi检测
  network_info_plus: ^6.1.4
  #打开文件
  open_file: ^3.3.1
  #屏幕亮度
  screen_brightness: ^2.1.5
  #开发调试工具
  flutter_ume_plus: ^3.0.0
  # flutter_ume_kit_ui_plus: ^2.0.0
  flutter_ume_kit_device_plus: ^2.0.0
  flutter_ume_kit_perf_plus: ^2.0.0
  flutter_ume_kit_show_code_plus: ^3.0.0
  flutter_ume_kit_console_plus: ^2.0.3
  flutter_ume_kit_dio_plus: ^3.4.0
  #华为推送
  # huawei_push: ^6.13.0+300
  #事件通知
  event_bus: ^2.0.0
  #百分百进度
  percent_indicator: ^4.2.3
  #日志打印
  logger: ^2.3.0
  image_cropper: ^9.1.0
  card_swiper: ^3.0.1
  dash_painter: ^1.0.2
  intl_utils: ^2.8.5
  flutter_draggable_gridview_haoxin: ^1.0.2
  # 卡尺
  xb_time_ruler: ^0.0.20
  # xb_time_ruler:
  #   path: ../../github/xb_time_ruler
  # 图表
  xb_chart: ^0.0.41
  date_format: ^2.0.7
  #高德地图
  # amap_flutter_map: ^3.0.0
  # amap_flutter_location: ^3.0.0
  # amap_flutter_base: ^3.0.0
  scrollable_positioned_list: ^0.3.8
  time: ^2.1.4
  lpinyin: ^2.0.3
  app_tracking_transparency: ^2.0.4
  sensors_plus: ^6.1.1
  just_audio: ^0.10.4
  flutter_app_update: ^3.0.4
  vibration: ^3.1.3
  app_settings: ^6.1.1
  kumi_popup_window: ^2.0.1
  archive: ^3.4.10
  encrypt: ^5.0.3
  flutter_image_compress: ^2.4.0
  # 华为扫码
  flutter_scankit: ^2.0.4
  video_player: ^2.8.5
  flutter_blue_plus: ^1.32.8
  # 上下拉刷新
  xb_refresh: ^0.0.18
  # xb_refresh: 
  #   path: ../../github/xb_refresh
  #rx
  rxdart: ^0.28.0
  syncfusion_flutter_pdfviewer: ^29.1.38
  #通讯录
  flutter_native_contact_picker: ^0.0.8
  xb_image_editor: ^0.0.14
  xb_calendar: ^0.0.13
  # receive_sharing_intent: ^1.8.1
  markdown_widget: ^2.3.2+7

dependency_overrides:
  archive: ^4.0.7
  intl: ^0.17.0
  vm_service: ^7.1.1
  sqflite: 2.3.3+1
  win32_registry: 1.1.2
  url_launcher_ios:
     git:
        url: https://github.com/huisedediao/url_launcher_ios.git
        path: url_launcher_ios-6.2.4
  xb_scaffold: 0.1.49
  # xb_scaffold:
  #    path: ../../github/xb_scaffold
  flutter_ume_kit_ui_plus: 
     git:
        url: https://github.com/huisedediao/flutter_ume_kit_ui_plus.git
        path: flutter_ume_kit_ui_plus-2.0.0
    # path: ../../github/flutter_ume_kit_ui_plus/flutter_ume_kit_ui_plus-2.0.0
  wakelock: 
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: wakelock-0.6.2
      ref: 'f5eae10'
  win32: 
    git:
      url: https://github.com/huisedediao/win32.git
      path: win32-5.2.0
    # path: ../../github/win32/win32-5.2.0
  amap_flutter_base: 
    git:
      url: http://**********/BeCloud/amap_flutter.git
      path: amap_flutter_base-3.0.0
      ref: 'eefec47'
    # path: ../../github/amap_flutter/amap_flutter_base-3.0.0
  amap_flutter_map: 
    git:
      url: http://**********/BeCloud/amap_flutter.git
      path: amap_flutter_map-3.0.0
      ref: 'c31812a'
    # path: ../../github/amap_flutter/amap_flutter_map-3.0.0
  amap_flutter_location: 
    git:
      url: http://**********/BeCloud/amap_flutter.git
      path: amap_flutter_location-3.0.0
      ref: '46a0056'
  device_info: 
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: device_info-2.0.3
      ref: 'a4fd44e2'
  huawei_push:
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: huawei_push-6.13.0+300
      ref: '03b19a8'
  image_gallery_saver:
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: image_gallery_saver-2.0.3
      ref: '6fd90b0'
  qr_code_scanner:
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: qr_code_scanner-1.0.1
      ref: '7281801'
  scan:
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: scan-1.6.0
      ref: '24df932'
  receive_sharing_intent:
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: receive_sharing_intent-1.8.1
      ref: 'd33f870'
  share_plus:
    git:
      url: http://**********/BeCloud/adaptpackage.git
      path: share_plus-11.0.0
      ref: '89e42e4'

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner:
  flutter_gen_runner: ^5.8.0
  retrofit_generator: ^8.2.1

#图片资源生成工具,使用查看 README.md
flutter_gen:
  output: lib/generated/
  integrations:
    flutter_svg: true
  colors:
    enabled: false
    outputs:
      class_name: NamedColors
    inputs:
      - assets/color/colors.xml


flutter:
  uses-material-design: true
  #本地图片资源
  assets:
    - assets/audios/
    - assets/data/
    - assets/config/
    - assets/images/
    - assets/images/common/
    - assets/images/setting/
    - assets/images/gif/

#国际化,使用查看 README.md
flutter_intl:
  enabled: true
  arb_dir: assets/l10n
  class_name: TR